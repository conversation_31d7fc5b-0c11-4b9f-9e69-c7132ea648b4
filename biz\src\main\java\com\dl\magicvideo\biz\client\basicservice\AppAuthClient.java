package com.dl.magicvideo.biz.client.basicservice;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.client.basicservice.dto.AppAuthInfoDTO;
import com.dl.magicvideo.biz.client.basicservice.intercepter.BasicServiceInterceptor;
import com.dl.magicvideo.biz.client.basicservice.param.AppTokenParamDTO;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-09 20:18
 */
@BaseRequest(interceptor = BasicServiceInterceptor.class)
public interface AppAuthClient {

    @Post("/internal/app/auth/checktoken")
    ResultModel<AppAuthInfoDTO> checkToken(@JSONBody AppTokenParamDTO paramDTO);

}
