package com.dl.magicvideo.biz.manager.oplog;

import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.basicservice.dto.OpLogDTO;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogAddReq;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogPageReq;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:48
 */
public interface OpLogManager {

    /**
     * 新增操作日志
     *
     * @param req
     */
    void newOpLog(OpLogAddReq req);

    /**
     * 分页查询操作日志
     *
     * @param req
     * @return
     */
    ResultPageModel<OpLogDTO> page(OpLogPageReq req);
}
