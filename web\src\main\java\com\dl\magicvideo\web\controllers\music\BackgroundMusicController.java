package com.dl.magicvideo.web.controllers.music;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.music.param.BackgroundMusicAiChooseStyleParam;
import com.dl.magicvideo.web.controllers.music.param.BackgroundMusicPageParam;
import com.dl.magicvideo.web.controllers.music.vo.BackgroundMusicAiChooseStyleVO;
import com.dl.magicvideo.web.controllers.music.vo.BackgroundMusicVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/background/music")
public class BackgroundMusicController {

    @Autowired
    private BackgroundMusicProcess backgroundMusicProcess;

    @PostMapping("/list")
    public ResultModel<List<BackgroundMusicVO>> list(@RequestBody BackgroundMusicPageParam param) {
        return backgroundMusicProcess.list(param);
    }

    @PostMapping("/page")
    @ApiOperation("音乐分页")
    public ResultPageModel<BackgroundMusicVO> page(@RequestBody BackgroundMusicPageParam param) {
        return backgroundMusicProcess.page(param);
    }

    @ApiOperation("ai选择背景音风格")
    @PostMapping("/aichoosestyle")
    public ResultModel<BackgroundMusicAiChooseStyleVO> aiChooseStyle(
            @RequestBody BackgroundMusicAiChooseStyleParam param) {
        return backgroundMusicProcess.aiChooseStyle(param);
    }
}
