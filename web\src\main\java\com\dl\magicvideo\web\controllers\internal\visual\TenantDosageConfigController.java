package com.dl.magicvideo.web.controllers.internal.visual;

import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.account.trial.emuns.TenantDosageEnum;
import com.dl.magicvideo.biz.dal.account.trial.po.TenantDosageConfigPO;
import com.dl.magicvideo.biz.manager.account.trial.TenantDosageConfigManager;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.visual.param.DosageConfigDetailParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.DosageConfigUpdateParam;
import com.dl.magicvideo.web.controllers.internal.visual.vo.TenantDosageConfigVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @describe: DeliveryPlanInternalController
 * @author: zhousx
 * @date: 2023/9/5 11:41
 */
@RestController
@RequestMapping("/visual/internal/tenant")
public class TenantDosageConfigController extends AbstractController {

    @Resource
    private TenantDosageConfigManager tenantDosageConfigManager;

    @Resource
    private HostTimeIdg hostTimeIdg;

    @PostMapping("/dosageConfig")
    public ResultModel<TenantDosageConfigVO> dosageConfig(@RequestBody @Validated DosageConfigDetailParam param) {
        TenantDosageConfigVO vo = new TenantDosageConfigVO();
        vo.setTenantCode(param.getTenantCode());
        List<TenantDosageConfigPO> list = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, param.getTenantCode()).list();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                if (Objects.equals(TenantDosageEnum.DIGITAL_MAN.getType(), e.getConfigType())) {
                    vo.setDigitalManBalance(e.getBalance());
                }
                if (Objects.equals(TenantDosageEnum.VIDEO.getType(), e.getConfigType())) {
                    vo.setVideoBalance(e.getBalance());
                }
                if (Objects.equals(TenantDosageEnum.TTS.getType(), e.getConfigType())) {
                    vo.setTtsBalance(e.getBalance());
                }
            });
        } else {
            List<TenantDosageConfigPO> poList = new ArrayList<>();
            TenantDosageConfigPO po = new TenantDosageConfigPO();
            long id1 = hostTimeIdg.generateId().longValue();
            po.setId(id1);
            po.setTenantCode(param.getTenantCode());
            po.setConfigType(TenantDosageEnum.DIGITAL_MAN.getType());
            po.setBalance(Const.MINUS_ONE_LONG);
            poList.add(po);

            TenantDosageConfigPO po2 = new TenantDosageConfigPO();
            long id2 = hostTimeIdg.generateId().longValue();
            po2.setId(id2);
            po2.setTenantCode(param.getTenantCode());
            po2.setConfigType(TenantDosageEnum.VIDEO.getType());
            po2.setBalance(Const.MINUS_ONE_LONG);
            poList.add(po2);

            TenantDosageConfigPO po3 = new TenantDosageConfigPO();
            long id3 = hostTimeIdg.generateId().longValue();
            po3.setId(id3);
            po3.setTenantCode(param.getTenantCode());
            po3.setConfigType(TenantDosageEnum.TTS.getType());
            po3.setBalance(Const.MINUS_ONE_LONG);
            poList.add(po3);
            tenantDosageConfigManager.saveBatch(poList);

            vo.setDigitalManBalance(Const.MINUS_ONE_LONG);
            vo.setVideoBalance(Const.MINUS_ONE_LONG);
            vo.setTtsBalance(Const.MINUS_ONE_LONG);

        }
        return ResultModel.success(vo);
    }

    @PostMapping("/dosageUpdate")
    public ResultModel<Void> dosageUpdate(@RequestBody DosageConfigUpdateParam param) {
        tenantDosageConfigManager.lambdaUpdate()
                .eq(TenantDosageConfigPO::getTenantCode, param.getTenantCode())
                .eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.DIGITAL_MAN.getType())
                .set(TenantDosageConfigPO::getBalance, param.getDigitalManBalance())
                .update();
        tenantDosageConfigManager.lambdaUpdate()
                .eq(TenantDosageConfigPO::getTenantCode, param.getTenantCode())
                .eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.VIDEO.getType())
                .set(TenantDosageConfigPO::getBalance, param.getVideoBalance())
                .update();
        tenantDosageConfigManager.lambdaUpdate()
                .eq(TenantDosageConfigPO::getTenantCode, param.getTenantCode())
                .eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.TTS.getType())
                .set(TenantDosageConfigPO::getBalance, param.getTtsBalance())
                .update();
        return ResultModel.success(null);
    }
}
