package com.dl.magicvideo.biz.dal.visual;

import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.po.StatisticsJobCountPO;
import com.dl.magicvideo.biz.dal.visual.po.StatisticsJobEfficiencyPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.manager.visual.bo.StatisticsJobCountBO;
import com.dl.magicvideo.biz.manager.visual.bo.StatisticsJobEfficiencyBO;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【visual_produce_job】的数据库操作Mapper
* @createDate 2023-04-26 14:05:39
* @Entity com.dl.magicvideo.biz.dal.visual.po.VisualProduceJob
*/
@BaseDao
public interface VisualProduceJobMapper extends MPJBaseMapper<VisualProduceJobPO> {

    /**
     * 功能描述: T+1数据统计
     * @Param: [bo]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/2/11 13:18
     */
    List<StatisticsJobCountPO> statistics(@Param("param") StatisticsJobCountBO statisticsJobCountBO);

    /**
     * 获取某一时间内总消耗时间和总时长（秒）
     */
    StatisticsJobEfficiencyPO efficiency(@Param("param") StatisticsJobEfficiencyBO statisticsJobEfficiencyBO);


}




