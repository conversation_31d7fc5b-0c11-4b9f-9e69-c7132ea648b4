package com.dl.magicvideo.web.controllers.internal.visual.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-23 16:08
 */
@Data
public class VisualProduceJobInternalPageParam extends AbstractPageParam {

    private String tenantCode;

    private List<Integer> statusList;

    /**
     * 开始时间，时间戳(毫秒)
     */
    private Long startTime;

    /**
     * 结束时间，时间戳（毫秒）
     */
    private Long endTime;

    /**
     * 是否包含删除的作品 0-不包含，1-包含
     */
    private Integer includeDeleted;

}
