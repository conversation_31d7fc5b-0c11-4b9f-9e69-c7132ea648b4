package com.dl.magicvideo.web.controllers.internal.subjectmatter.helper;

import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectAddBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMaterialAddBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.XMindBO;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.param.InternalSubjectAddParam;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.param.MaterialAddParam;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.param.XMindParam;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class InternalSubjectHelper {
    public static SubjectAddBO cnvSubjectAddParam2BO(InternalSubjectAddParam input) {
        SubjectAddBO result = new SubjectAddBO();
        result.setBizId(input.getBizId());
        //名称
        result.setTitle(input.getTitle());
        //XMind根节点名称
        result.setName(input.getName());
        //简介
        result.setIntro(input.getIntro());
        result.setImgUrl(input.getImgUrl());
        result.setXmindUrl(input.getXmindUrl());
        result.setXmindStr(input.getXmindStr());

        //input中的XMindParam转为result中的XMindBO
        XMindParam xmindParam = input.getXmindParam();
        XMindBO xmindBO = new XMindBO();
        xmindBO.setName(xmindParam.getName());
        xmindBO.setLevel(xmindParam.getLevel());
        xmindBO.setChildren(cnvXMindParams2BOs(xmindParam.getChildren()));
        result.setXmindBO(xmindBO);
        //prompt
        result.setPrompt(input.getPrompt());
        result.setOperatorName(input.getOperatorName());
        result.setOperatorId(input.getOperatorId());

        return result;
    }

    public static List<XMindBO> cnvXMindParams2BOs(List<XMindParam> paramList) {
        List<XMindBO> boList = new ArrayList<>();
        for (XMindParam param : paramList) {
            boList.add(cnvXMindParam2BO(param));
        }
        return boList;
    }

    private static XMindBO cnvXMindParam2BO(XMindParam param) {
        XMindBO bo = new XMindBO();
        bo.setName(param.getName());
        bo.setLevel(param.getLevel());
        List<XMindBO> boChildren = new ArrayList<>();
        for (XMindParam childParam : param.getChildren()) {
            boChildren.add(cnvXMindParam2BO(childParam));
        }
        bo.setChildren(boChildren);
        return bo;
    }

    public static List<SubjectMaterialAddBO> cnvSubjectMaterialAddParams2BOs(List<MaterialAddParam> materialList) {
        if (CollectionUtils.isEmpty(materialList)) {
            return Collections.emptyList();
        }
        return materialList.stream().map(param -> {
            SubjectMaterialAddBO bo = new SubjectMaterialAddBO();
            bo.setMatterId(param.getMatterId());
            bo.setUrl(param.getUrl());
            bo.setMaterialType(param.getMaterialType());

            bo.setOperatorName(param.getOperatorName());
            bo.setOperatorId(param.getOperatorId());

            return bo;
        }).collect(Collectors.toList());
    }

/*    public static void main(String[] args) {
        XMindParam root = new XMindParam();
        root.setName("Root");
        root.setLevel(0);

        // 创建第二层，包含两个节点
        XMindParam level2Node1 = createNode("Level 2 Node 1", 1);
        XMindParam level2Node2 = createNode("Level 2 Node 2", 1);
        root.getChildren().add(level2Node1);
        root.getChildren().add(level2Node2);

        // 创建第三层，包含8个节点，并随机分配给第二层的两个节点
        List<XMindParam> level3Nodes = new ArrayList<>();
        for (int i = 1; i <= 8; i++) {
            level3Nodes.add(createNode("Level 3 Node " + i, 2));
        }
        randomlyDistributeNodes(level3Nodes, Arrays.asList(level2Node1, level2Node2));

        // 创建第四层，包含12个节点，并随机分配给第三层的8个节点
        for (XMindParam level3Node : level3Nodes) {
            List<XMindParam> level4NodesForCurrentLevel3 = new ArrayList<>();
            for (int j = 1; j <= (int) (Math.random() * 4) + 1; j++) { // 随机分配1到4个子节点给每个第三层节点
                level4NodesForCurrentLevel3.add(createNode("Level 4 Node " + j + " for " + level3Node.getName(), 3));
            }
            randomlyDistributeNodes(level4NodesForCurrentLevel3, Collections.singletonList(level3Node));
        }

        // 打印结果以验证
        printTree(root, "");
    }

    private static XMindParam createNode(String name, Integer level) {
        XMindParam node = new XMindParam();
        node.setName(name);
        node.setLevel(level);
        return node;
    }

    private static void randomlyDistributeNodes(List<XMindParam> nodesToDistribute, List<XMindParam> targetNodes) {
        Random rand = new Random();
        for (XMindParam node : nodesToDistribute) {
            int randomIndex = rand.nextInt(targetNodes.size());
            targetNodes.get(randomIndex).getChildren().add(node);
        }
    }

    private static void printTree(XMindParam node, String prefix) {
        System.out.println(prefix + node.getName() + " (Level " + node.getLevel() + ")");
        for (XMindParam child : node.getChildren()) {
            printTree(child, prefix + "  ");
        }
    }*/
}
