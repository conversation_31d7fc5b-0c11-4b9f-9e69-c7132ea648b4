package com.dl.magicvideo.biz.manager.visual.helper;

import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobShareInfoPO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobContextBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;

import java.util.Objects;

public class ProduceJobHelper {

    public static ProduceJobContextBO buildProduceJobContext(ProduceJobBO produceJobBO, AdmTenantInfoDTO tenantInfo,
            String extUserId, Long batchId, String apiData) {
        return ProduceJobContextBO.builder().templateId(produceJobBO.getTemplateId())
                .replaceData(produceJobBO.getReplaceData()).batchId(batchId).source(produceJobBO.getSource())
                .templateName(produceJobBO.getProduceJobName()).ttsParam(null)
                .dmProduceMode(tenantInfo.getDmProduceMode()).digitalManParamBO(produceJobBO.getDigitalManParamBO())
                .planId(null).apiData(apiData).lightEditConfigs(produceJobBO.getLightEditConfigs()).extUserId(extUserId)
                .renderData(produceJobBO.getRenderData())
                .dynamicNodes(produceJobBO.getDynamicNodes()).jobCoverUrl(produceJobBO.getJobCoverUrl()).build();
    }

    public static VisualProduceJobDTO cnvVisualProduceJobPO2DTO(VisualProduceJobPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        VisualProduceJobDTO result = new VisualProduceJobDTO();
        result.setJobId(input.getJobId());
        result.setDuration(input.getDuration());
        result.setCompleteDt(input.getCompleteDt());
        result.setCoverUrl(input.getCoverUrl());
        result.setStatus(input.getStatus());
        result.setVideoUrl(input.getVideoUrl());
        result.setName(input.getName());
        result.setSize(input.getSize());
        result.setTemplateId(input.getTemplateId());
        result.setCreatorName(input.getCreatorName());
        result.setCreateDt(input.getCreateDt());
        return result;
    }

    public static VisualProduceJobDTO cnvVisualShareInfoPO2ProduceJobDTO(VisualProduceJobShareInfoPO input){
        if (Objects.isNull(input)){
            return null;
        }
        VisualProduceJobDTO result = new VisualProduceJobDTO();
        result.setResolutionType(input.getResolutionType());
        result.setJobId(input.getJobId());
        result.setName(input.getName());
        result.setVideoUrl(input.getVideoUrl());
        result.setCoverUrl(input.getCoverUrl());
        result.setCompleteDt(input.getCompleteDt());
        result.setDuration(input.getDuration());
        result.setTemplateId(input.getTemplateId());
        return result;
    }

}
