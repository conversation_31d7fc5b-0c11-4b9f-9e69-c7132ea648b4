package com.dl.magicvideo.biz.manager.visual.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ThemesProdEnum {
    *********("15031283.CC", "昨日涨停"),
    *********("15032170.CC", "昨日首板"),
    *********("15032219.CC", "上市首五日"),
    *********("15031398.CC", "昨日连板"),
    *********("15032256.CC", "C股(上市次日至五日)"),
    *********("15032143.CC", "昨日触板"),
    ;

    private String code;

    private String name;

    ThemesProdEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    //返回所有code集合
    public static List<String> getAllCode() {
        return Arrays.stream(values()).map(ThemesProdEnum::getCode).collect(Collectors.toList());
    }
}
