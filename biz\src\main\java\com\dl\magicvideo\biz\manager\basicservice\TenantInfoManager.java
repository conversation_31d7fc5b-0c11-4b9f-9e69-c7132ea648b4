package com.dl.magicvideo.biz.manager.basicservice;

import cn.hutool.json.JSONUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.SysTenantClient;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.client.basicservice.param.TenantInfoParam;
import com.dl.magicvideo.biz.client.basicservice.param.TenantInfoQueryListParamDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-06 14:12
 */
@Component
public class TenantInfoManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantInfoManager.class);

    @Autowired
    private SysTenantClient sysTenantClient;

    @Resource
    private RedisUtil redisUtil;

    public static final String TENANT_INFO_KEY_PREFIX = "dl.tenantInfo.key.";

    public static final Long FIVE_MINUTES_EXPIRE_SECONDS = 5 * 60L;

    public AdmTenantInfoDTO getTenantInfoFromCache(String tenantCode) {
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户号不能为空");

        String key = TENANT_INFO_KEY_PREFIX + tenantCode;
        String value = redisUtil.get(key);
        if (StringUtils.isNotBlank(value)) {
            if (Const.NULL.equals(value)) {
                return null;
            }
            return JSONUtil.toBean(value, AdmTenantInfoDTO.class);
        }

        AdmTenantInfoDTO sysTenantInfoPO = this.getTenantInfo(tenantCode);
        //如果basic-service中不存在该租户，则放置"null"到redis中，防止缓存穿透，缓存5分钟
        if (Objects.isNull(sysTenantInfoPO)) {
            redisUtil.set(key, Const.NULL, FIVE_MINUTES_EXPIRE_SECONDS);
            return null;
        }

        redisUtil.set(key, JSONUtil.toJsonStr(sysTenantInfoPO), FIVE_MINUTES_EXPIRE_SECONDS);
        return sysTenantInfoPO;
    }

    public AdmTenantInfoDTO getTenantInfo(String tenantCode) {
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户号不能为空");
        TenantInfoParam param = new TenantInfoParam();
        param.setTenantCode(tenantCode);
        ResultModel<AdmTenantInfoDTO> resultModel = sysTenantClient.info(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("获取租户信息失败 tenantCode = {}", tenantCode);
            throw BusinessServiceException.getInstance("获取租户信息失败！");
        }
        return resultModel.getDataResult();
    }

    public List<AdmTenantInfoDTO> listTenantInfo(List<String> tenantCodeList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(tenantCodeList), "租户号不能为空");
        TenantInfoQueryListParamDTO param = new TenantInfoQueryListParamDTO();
        param.setTenantCodeList(tenantCodeList);
        ResultModel<List<AdmTenantInfoDTO>> resultModel = sysTenantClient.list(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("批量获取租户信息失败 tenantCodeList = {}", JSONUtil.toJsonStr(tenantCodeList));
            throw BusinessServiceException.getInstance("批量获取租户信息失败！");
        }
        return resultModel.getDataResult();

    }

}
