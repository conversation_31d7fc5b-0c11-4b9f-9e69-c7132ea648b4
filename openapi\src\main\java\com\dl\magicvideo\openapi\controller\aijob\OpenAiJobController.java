package com.dl.magicvideo.openapi.controller.aijob;

import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.openapi.controller.aijob.param.OpenStatisticsAiJobPageParam;
import com.dl.magicvideo.openapi.controller.aijob.param.OpenVisualAiJobPageParam;
import com.dl.magicvideo.openapi.controller.aijob.vo.OpenStatisticsAiJobVO;
import com.dl.magicvideo.openapi.controller.aijob.vo.OpenVisualDmJobInfoVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 16:22
 */
@RestController
@RequestMapping("/openapi/aijob")
public class OpenAiJobController {

    @Resource
    private OpenAiJobProcess openAiJobProcess;

    @ApiOperation("分页查询数字人任务统计")
    @PostMapping("/statistics/dm/page")
    public ResultPageModel<OpenStatisticsAiJobVO> pageDmStatistics(
            @RequestBody @Validated OpenStatisticsAiJobPageParam pageParam) {
        return openAiJobProcess.page(pageParam, AiJobTypeE.DIGITAL_MAN.getCode());
    }

    @PostMapping("/pagedmjob")
    @ApiOperation("分页查询数字人合成任务")
    public ResultPageModel<OpenVisualDmJobInfoVO> pageDmJob(@RequestBody @Validated  OpenVisualAiJobPageParam param) {
        return openAiJobProcess.pageDmJob(param);
    }

}
