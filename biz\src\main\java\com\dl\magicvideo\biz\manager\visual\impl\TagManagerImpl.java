package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.visual.TagMapper;
import com.dl.magicvideo.biz.dal.visual.po.TagPO;
import com.dl.magicvideo.biz.manager.visual.TagManager;
import com.dl.magicvideo.biz.manager.visual.bo.TagPageBO;
import com.dl.magicvideo.biz.manager.visual.dto.TagDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【tenant_material_folder】的数据库操作Service实现
* @createDate 2023-04-25 17:03:16
*/
@Service
public class TagManagerImpl extends ServiceImpl<TagMapper, TagPO> implements TagManager {

    @Override
    public IPage<TagDTO> pageQuery(TagPageBO bo) {
        LambdaQueryWrapper<TagPO> wrapper = Wrappers.lambdaQuery(TagPO.class);
        wrapper.eq(TagPO::getTagType, bo.getTagType());
        wrapper.orderByAsc(TagPO::getId);
        Page<TagPO> resp = this.page(new Page<>(bo.getPageIndex(), bo.getPageSize()), wrapper);
        IPage<TagDTO> result = new Page<>(resp.getCurrent(), resp.getSize(),resp.getTotal());
        result.setPages(resp.getPages());
        List<TagDTO> data = resp.getRecords().stream().map(
                e->{
                    TagDTO dto = new TagDTO();
                    dto.setTagName(e.getName());
                    dto.setTagId(e.getTagId());
                    return dto;
                }).collect(Collectors.toList());
        result.setRecords(data);
        return result;
    }
}




