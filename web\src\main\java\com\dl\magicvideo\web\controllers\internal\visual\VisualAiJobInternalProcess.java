package com.dl.magicvideo.web.controllers.internal.visual;

import cn.hutool.json.JSONUtil;
import com.beust.jcommander.internal.Lists;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceListQueryDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceListQueryPairDTO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.basicservice.dto.UserProfileDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.aiservice.DaVirtualManManager;
import com.dl.magicvideo.biz.manager.aiservice.DaVirtualVoiceManager;
import com.dl.magicvideo.biz.manager.basicservice.AdmUserManager;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.TtsJobBO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.VisualDmJobInternalInfoDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.VisualTtsJobInternalInfoDTO;
import com.dl.magicvideo.web.controllers.internal.visual.convert.VisualAiJobInternalConvert;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualAiJobInternalPageParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 11:40
 */
@Component
public class VisualAiJobInternalProcess extends AbstractController {

    @Resource
    private VisualAiJobManager visualAiJobManager;

    @Resource
    private VisualTemplateManager visualTemplateManager;

    @Resource
    private AdmUserManager admUserManager;

    @Resource
    private DaVirtualManManager daVirtualManManager;

    @Resource
    private DaVirtualVoiceManager daVirtualVoiceManager;

    public ResultPageModel<VisualDmJobInternalInfoDTO> pageDmJob(VisualAiJobInternalPageParam param) {
        VisualAiJobPageBO pageBO = new VisualAiJobPageBO();
        pageBO.setTenantCode(param.getTenantCode());
        pageBO.setMinDt(param.getMinDt());
        pageBO.setMaxDt(param.getMaxDt());
        pageBO.setAiJobType(AiJobTypeE.DIGITAL_MAN.getCode());
        pageBO.setJobStatusList(param.getJobStatusList());
        pageBO.setNeedQueryDeleted(param.getNeedQueryDeleted());
        pageBO.setPageIndex(param.getPageIndex());
        pageBO.setPageSize(param.getPageSize());

        ResponsePageQueryDO<List<VisualAiJobExtPO>> responseDO = visualAiJobManager.pageExt(pageBO);
        if (CollectionUtils.isEmpty(responseDO.getDataResult())) {
            return pageQueryModel(responseDO, Collections.emptyList());
        }

        List<VisualAiJobExtPO> dmJobPOList = responseDO.getDataResult();

        //查询模板
        List<VisualTemplatePO> templateList = visualTemplateManager.lambdaQuery().in(VisualTemplatePO::getTemplateId,
                dmJobPOList.stream().map(VisualAiJobExtPO::getTemplateId).distinct().collect(Collectors.toList()))
                .list();
        Map<Long, VisualTemplatePO> templateMap = templateList.stream()
                .collect(Collectors.toMap(VisualTemplatePO::getTemplateId, Function.identity(), (v1, v2) -> v1));

        //提取请求中的数字人信息
        //key-aiJobId
        Map<Long, DigitalManJobBO> dmJobMap = new HashMap<>();
        Set<String> dmSceneIdSet = new HashSet<>();
        Set<Integer> channelSet = new HashSet<>();

        //创建人id
        Set<Long> creatorUserIdSet = new HashSet<>();

        dmJobPOList.stream().forEach(dmJobPO -> {
            if (Objects.nonNull(dmJobPO.getCreateBy())) {
                creatorUserIdSet.add(dmJobPO.getCreateBy());
            }

            if (StringUtils.isNotBlank(dmJobPO.getRequestInfo())) {
                DigitalManJobBO digitalManJobBO = JSONUtil.toBean(dmJobPO.getRequestInfo(), DigitalManJobBO.class);
                dmJobMap.put(dmJobPO.getJobId(), digitalManJobBO);
                dmSceneIdSet.add(digitalManJobBO.getSceneId());
                channelSet.add(digitalManJobBO.getChannel());
            }
        });

        List<DaVirtualManScenesDTO> daVirtualManScenesDTOList = daVirtualManManager
                .vmSceneListBySceneIds(param.getTenantCode(), Lists.newArrayList(dmSceneIdSet),
                        Lists.newArrayList(channelSet), Const.ZERO);
        //key:channel-sceneId
        Map<String, DaVirtualManScenesDTO> dmSceneMap = daVirtualManScenesDTOList.stream().collect(Collectors
                .toMap((scene -> scene.getChannel() + "-" + scene.getSceneId()), Function.identity(), (s1, s2) -> s1));

        //查询操作人信息
        List<UserProfileDTO> userProfileDTOList = admUserManager
                .listUserProfile(param.getTenantCode(), Lists.newArrayList(creatorUserIdSet));
        Map<Long, String> creatorIdNameMap = userProfileDTOList.stream()
                .collect(Collectors.toMap(UserProfileDTO::getUserId, UserProfileDTO::getName, (u1, u2) -> u1));

        return pageQueryModel(responseDO, dmJobPOList.stream().map(dmJobPO -> VisualAiJobInternalConvert
                .buildVisualDmJobInternalInfoDTO(dmJobPO, templateMap, creatorIdNameMap,
                        dmJobMap.get(dmJobPO.getJobId()), dmSceneMap)).collect(Collectors.toList()));
    }

    public ResultPageModel<VisualTtsJobInternalInfoDTO> pageTtsJob(VisualAiJobInternalPageParam param) {
        VisualAiJobPageBO pageBO = new VisualAiJobPageBO();
        pageBO.setTenantCode(param.getTenantCode());
        pageBO.setMinDt(param.getMinDt());
        pageBO.setMaxDt(param.getMaxDt());
        pageBO.setAiJobTypeList(Lists.newArrayList(AiJobTypeE.TTS.getCode(), AiJobTypeE.DM_TTS.getCode()));
        pageBO.setJobStatusList(param.getJobStatusList());
        pageBO.setNeedQueryDeleted(param.getNeedQueryDeleted());
        pageBO.setPageIndex(param.getPageIndex());
        pageBO.setPageSize(param.getPageSize());

        ResponsePageQueryDO<List<VisualAiJobExtPO>> responseDO = visualAiJobManager.pageExt(pageBO);
        if (CollectionUtils.isEmpty(responseDO.getDataResult())) {
            return pageQueryModel(responseDO, Collections.emptyList());
        }

        List<VisualAiJobExtPO> dmJobPOList = responseDO.getDataResult();

        //查询模板
        List<VisualTemplatePO> templateList = visualTemplateManager.lambdaQuery().in(VisualTemplatePO::getTemplateId,
                dmJobPOList.stream().map(VisualAiJobExtPO::getTemplateId).distinct().collect(Collectors.toList()))
                .list();
        Map<Long, VisualTemplatePO> templateMap = templateList.stream()
                .collect(Collectors.toMap(VisualTemplatePO::getTemplateId, Function.identity(), (v1, v2) -> v1));

        //提取请求中的声音信息
        //key-aiJobId
        Map<Long, TtsJobBO> ttsJobMap = new HashMap<>();
        //channel||voiceKey
        Set<String> channelAndVoiceKeySet = new HashSet<>();

        //创建人id
        Set<Long> creatorUserIdSet = new HashSet<>();

        dmJobPOList.stream().forEach(dmJobPO -> {
            if (Objects.nonNull(dmJobPO.getCreateBy())) {
                creatorUserIdSet.add(dmJobPO.getCreateBy());
            }

            if (StringUtils.isNotBlank(dmJobPO.getRequestInfo())) {
                TtsJobBO ttsJobBO = JSONUtil.toBean(dmJobPO.getRequestInfo(), TtsJobBO.class);
                ttsJobMap.put(dmJobPO.getJobId(), ttsJobBO);

                String key = ttsJobBO.getChannel() + "||" + ttsJobBO.getVoiceName();
                channelAndVoiceKeySet.add(key);
            }
        });

        DaVirtualVoiceListQueryDTO voiceListQueryDTO = VisualAiJobInternalConvert.buildDaVirtualVoiceListQueryDTO(channelAndVoiceKeySet);
        List<DaVirtualVoiceBaseInfoDTO> voiceList = daVirtualVoiceManager
                .listByChannelAndVoiceKeyList(voiceListQueryDTO);
        //key:channel||sceneId
        Map<String, DaVirtualVoiceBaseInfoDTO> voiceMap = voiceList.stream().collect(Collectors
                .toMap((voice -> voice.getChannel() + "||" + voice.getVoiceKey()), Function.identity(), (v1, v2) -> v1));

        //查询操作人信息
        List<UserProfileDTO> userProfileDTOList = admUserManager
                .listUserProfile(param.getTenantCode(), Lists.newArrayList(creatorUserIdSet));
        Map<Long, String> creatorIdNameMap = userProfileDTOList.stream()
                .collect(Collectors.toMap(UserProfileDTO::getUserId, UserProfileDTO::getName, (u1, u2) -> u1));

        return pageQueryModel(responseDO, dmJobPOList.stream().map(dmJobPO -> VisualAiJobInternalConvert
                .buildVisualTtsJobInternalInfoDTO(dmJobPO, templateMap, creatorIdNameMap,
                        ttsJobMap.get(dmJobPO.getJobId()), voiceMap)).collect(Collectors.toList()));
    }

}
