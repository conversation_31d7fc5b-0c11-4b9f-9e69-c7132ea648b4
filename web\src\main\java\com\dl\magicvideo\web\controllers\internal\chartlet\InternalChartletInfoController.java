package com.dl.magicvideo.web.controllers.internal.chartlet;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.manager.chartlet.enums.ChartletCategoryEnum;
import com.dl.magicvideo.web.controllers.internal.chartlet.dto.InternalChartletCategoryDTO;
import com.dl.magicvideo.web.controllers.internal.chartlet.dto.InternalChartletInfoDTO;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChareletDeleteParam;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChartletInfoAddParam;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChartletInfoUptParam;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChartletPageParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-12 16:56
 */
@RestController
@RequestMapping("/visual/internal/chartlet")
public class InternalChartletInfoController {

    private static List<InternalChartletCategoryDTO> categoryDTOList;

    static {
        categoryDTOList = new ArrayList<>();
        for (ChartletCategoryEnum categoryEnum : ChartletCategoryEnum.values()) {
            InternalChartletCategoryDTO categoryDTO = new InternalChartletCategoryDTO();
            categoryDTO.setCode(categoryEnum.getCode());
            categoryDTO.setName(categoryEnum.getName());
            categoryDTO.setSort(categoryEnum.getSort());
            categoryDTOList.add(categoryDTO);
        }
    }

    @Resource
    private InternalChartletInfoProcess internalChartletInfoProcess;

    @ApiOperation("新增贴图")
    @PostMapping("/add")
    public ResultModel<Long> add(@RequestBody @Validated InternalChartletInfoAddParam param) {
        return internalChartletInfoProcess.add(param);
    }

    @ApiOperation("修改贴图")
    @PostMapping("/update")
    public ResultModel<Void> update(@RequestBody @Validated InternalChartletInfoUptParam param) {
        return internalChartletInfoProcess.update(param);
    }

    @ApiOperation("分页查询贴图信息")
    @PostMapping("/page")
    public ResultPageModel<InternalChartletInfoDTO> page(@RequestBody InternalChartletPageParam param) {
        return internalChartletInfoProcess.page(param);
    }

    @ApiOperation("删除贴图")
    @PostMapping("/delete")
    public ResultModel<Void> delete(@RequestBody @Validated InternalChareletDeleteParam param) {
        return internalChartletInfoProcess.delete(param);
    }

    @ApiOperation("获取全部贴图分类")
    @PostMapping("/listallcategory")
    public ResultModel<List<InternalChartletCategoryDTO>> listAllCategory() {
        return ResultModel.success(categoryDTOList);
    }
}