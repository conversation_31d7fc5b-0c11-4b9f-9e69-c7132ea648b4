package com.dl.magicvideo.biz.manager.basicservice;

import cn.hutool.json.JSONUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.SysAdmUserClient;
import com.dl.magicvideo.biz.client.basicservice.dto.UserProfileDTO;
import com.dl.magicvideo.biz.client.basicservice.param.AdmUserProfileListParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 14:46
 */
@Component
public class AdmUserManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdmUserManager.class);

    @Resource
    private SysAdmUserClient sysAdmUserClient;

    public List<UserProfileDTO> listUserProfile(String tenantCode, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }

        AdmUserProfileListParam param = new AdmUserProfileListParam();
        param.setUserIdList(userIds.stream().map(String::valueOf).collect(Collectors.toList()));
        param.setTenantCode(tenantCode);
        ResultModel<List<UserProfileDTO>> resultModel = sysAdmUserClient.userProfileList(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("批量查询用户信息失败，param:{},resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("批量查询用户信息失败");
        }
        return resultModel.getDataResult();
    }
}
