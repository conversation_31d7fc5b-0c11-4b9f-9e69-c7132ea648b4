package com.dl.magicvideo.biz.common.util;

import java.math.BigDecimal;

public class NumberUtils {

    /**
     * 根据数据映射
     * @param value
     * @param inputMin
     * @param inputMax
     * @param outputMin
     * @param outputMax
     * @return
     */
    public static double mapRange(int value, int inputMin, int inputMax, double outputMin, double outputMax) {
        double inputRange = (double) (inputMax - inputMin);
        double outputRange = outputMax - outputMin;

        // 计算映射后的值
        double mappedValue = ((value - inputMin) / inputRange) * outputRange + outputMin;
        return BigDecimal.valueOf(mappedValue).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 根据数据映射
     * @param value
     * @param inputMin
     * @param inputMax
     * @param outputMin
     * @param outputMax
     * @return
     */
    public static String mapRangeDouble(Double value, Double inputMin, Double inputMax, double outputMin, double outputMax) {
        double inputRange = (double) (inputMax - inputMin);
        double outputRange = outputMax - outputMin;

        // 计算映射后的值
        double mappedValue = ((value - inputMin) / inputRange) * outputRange + outputMin;
        return BigDecimal.valueOf(mappedValue).setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }

    public static void main(String[] args) {
        String speedVal = "1";
        String s = String.valueOf(NumberUtils.mapRangeDouble(Double.valueOf(speedVal), 0.5d, 1.5, -500, 500));
        System.out.println(s);
    }
}
