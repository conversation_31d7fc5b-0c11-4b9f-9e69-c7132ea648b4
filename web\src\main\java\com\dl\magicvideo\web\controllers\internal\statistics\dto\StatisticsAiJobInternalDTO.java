package com.dl.magicvideo.web.controllers.internal.statistics.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:29
 */
@Data
public class StatisticsAiJobInternalDTO implements Serializable {
    private static final long serialVersionUID = -536670224217298501L;

    /**
     * 记录id
     */
    private Long recordId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * ai任务类型:1-数字人，2-TTS
     */
    private Integer aiJobType;

    /**
     * ai任务数
     */
    private Integer jobCount;

    /**
     * 总的向上取整分钟数
     */
    private Long totalCeilingMinutes;

    /**
     * 总的毫秒
     */
    private Long totalTimeMillis;

    /**
     * 统计时间
     */
    private Date statisticsTime;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 修改时间
     */
    private Date modifyDt;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 租户名称
     */
    private String tenantName;

}
