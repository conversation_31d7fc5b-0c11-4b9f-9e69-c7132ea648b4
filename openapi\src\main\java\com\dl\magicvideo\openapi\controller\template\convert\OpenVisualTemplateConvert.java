package com.dl.magicvideo.openapi.controller.template.convert;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateLightEditConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualCardDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenDynamicNodeVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenCardVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenVisualTemplateDetailVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenVisualTemplateVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-09 10:27
 */
public class OpenVisualTemplateConvert {

    public static OpenVisualTemplateVO cnvVisualTemplateDTO2VO(VisualTemplateDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        OpenVisualTemplateVO vo = new OpenVisualTemplateDetailVO();
        fillOpenVisualTemplateVO(vo, dto);
        return vo;
    }

    public static void fillOpenVisualTemplateVO(OpenVisualTemplateVO vo, VisualTemplateDTO dto) {
        vo.setTemplateId(String.valueOf(dto.getTemplateId()));
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setPreviewVideoUrl(dto.getPreviewVideoUrl());
        vo.setName(dto.getName());
        vo.setDuration(dto.getDuration());
        vo.setCreateDt(dto.getCreateDt());
        vo.setApiData(dto.getApiData());
    }

    public static OpenVisualTemplateDetailVO cnvVisualTemplateDTO2DetailVO(VisualTemplateDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        OpenVisualTemplateDetailVO vo = new OpenVisualTemplateDetailVO();
        fillOpenVisualTemplateVO(vo, dto);
        if (CollectionUtils.isNotEmpty(dto.getCards())) {
            vo.setCard(dto.getCards().stream().map(visualCardDTO -> {
                OpenCardVO cardVO = new OpenCardVO();
                cardVO.setCardId(String.valueOf(visualCardDTO.getCardId()));
                cardVO.setTemplateId(String.valueOf(visualCardDTO.getTemplateId()));
                cardVO.setName(visualCardDTO.getName());
                cardVO.setCoverUrl(visualCardDTO.getCoverUrl());
                cardVO.setResolution(visualCardDTO.getResolution());
                buildLightEditConfig(visualCardDTO, cardVO);
                if (CollectionUtils.isNotEmpty(visualCardDTO.getDynamicNodes())) {
                    cardVO.setDynamicNodes(visualCardDTO.getDynamicNodes().stream().map(dynamicNodeDTO -> {
                        OpenDynamicNodeVO dynamicNodeVO = new OpenDynamicNodeVO();
                        dynamicNodeVO.setClipId(dynamicNodeDTO.getType());
                        dynamicNodeVO.setCoverUrl(dynamicNodeDTO.getCoverUrl());
                        dynamicNodeVO.setIsEnabled(dynamicNodeDTO.getIsEnabled());
                        return dynamicNodeVO;
                    }).collect(Collectors.toList()));
                }
                return cardVO;
            }).collect(Collectors.toList()));
        }
        return vo;
    }

    public static void buildLightEditConfig(VisualCardDTO visualCardDTO, OpenCardVO cardVO) {
        if (StringUtils.isNotEmpty(visualCardDTO.getLightEditConfigs())) {
            TemplateLightEditConfigDTO configDTO = JSONUtil
                    .toBean(visualCardDTO.getLightEditConfigs(), TemplateLightEditConfigDTO.class);
            cardVO.setLightEditConfigs(configDTO.getLightEditConfigs());
            cardVO.setCrossClips(configDTO.getCrossClips());
        }
    }
}
