package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.visual.ComponentVersionDetailMapper;
import com.dl.magicvideo.biz.dal.visual.po.ComponentVersionDetailPO;
import com.dl.magicvideo.biz.manager.visual.ComponentVersionDetailManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ComponentVersionDetailManagerImpl extends ServiceImpl<ComponentVersionDetailMapper,
        ComponentVersionDetailPO> implements ComponentVersionDetailManager {


    @Override
    public List<ComponentVersionDetailPO> getVersionList() {
        return list();
    }

    @Override
    public ComponentVersionDetailPO getUrlByVersion(String version) {
        return getOne(new LambdaQueryWrapper<ComponentVersionDetailPO>().eq(!StringUtils.isBlank(version), ComponentVersionDetailPO::getVersion, version));
    }
}
