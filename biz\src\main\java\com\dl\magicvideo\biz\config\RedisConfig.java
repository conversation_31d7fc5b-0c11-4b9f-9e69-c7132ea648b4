package com.dl.magicvideo.biz.config;

import com.dl.magicvideo.biz.common.properties.DLRedisProperties;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import java.time.Duration;

@Configuration
@EnableCaching
public class RedisConfig {
    @Autowired
    private DLRedisProperties dlRedisProperties;

    /**
     * <p>SpringBoot配置redis作为默认缓存工具</p>
     * <p>SpringBoot 2.0 以上版本的配置</p>
     */
    @Bean
    public CacheManager cacheManager(RedisTemplate<String, Object> template) {
        // 缓存数据保存1小时
        //.entryTtl(Duration.ofHours(1));
        RedisCacheManager redisCacheManager = RedisCacheManager.RedisCacheManagerBuilder
                // Redis 连接工厂
                .fromConnectionFactory(template.getConnectionFactory())
                // 缓存配置
                .cacheDefaults(getCacheConfigurationWithTtl(template, null))
                // 配置同步修改或删除 put/evict
                .transactionAware()
                //配置企业微信缓存过期时间
                .withCacheConfiguration("qywx", getCacheConfigurationWithTtl(template, 7200L))
                .withCacheConfiguration("mini_program", getCacheConfigurationWithTtl(template, 7200L))
                .withCacheConfiguration("wx_mp", getCacheConfigurationWithTtl(template, 7200L))
                .withCacheConfiguration("uc_ext_apps", getCacheConfigurationWithTtl(template, 72000L))
                .withCacheConfiguration("gold_egg", getCacheConfigurationWithTtl(template, 72000L))
                .withCacheConfiguration("qywx_jsticket", getCacheConfigurationWithTtl(template, 7200L)).build();
        return redisCacheManager;
    }

    private RedisCacheConfiguration getCacheConfigurationWithTtl(RedisTemplate<String, Object> template, Long seconds) {
        RedisCacheConfiguration configuration = RedisCacheConfiguration.defaultCacheConfig()
                // 设置key为String
                .serializeKeysWith(
                        RedisSerializationContext.SerializationPair.fromSerializer(template.getStringSerializer()))
                // 设置value 为自动转Json的Object
                .serializeValuesWith(
                        RedisSerializationContext.SerializationPair.fromSerializer(template.getValueSerializer()))
                // 不缓存null
                .disableCachingNullValues().prefixCacheNameWith("qywx:");
        if (seconds != null) {
            return configuration.entryTtl(Duration.ofSeconds(seconds));
        }
        return configuration;
    }

    /**
     * retemplate<String, Object>
     */
    @Bean(name = "template")
    public RedisTemplate<String, Object> template(RedisConnectionFactory factory) {
        // 创建RedisTemplate<String, Object>对象
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 配置连接工厂
        template.setConnectionFactory(factory);

        // 定义Jackson2JsonRedisSerializer序列化对象
        GenericJackson2JsonRedisSerializer jacksonSeial = new DlGenericJackson2JsonRedisSerializer(new ObjectMapper());
        StringRedisSerializer hashKey = new StringRedisSerializer();
        // redis key 序列化方式使用stringSerial
        template.setKeySerializer(hashKey);
        // redis value 序列化方式使用jackson
        template.setValueSerializer(jacksonSeial);
        // redis hash key 序列化方式使用stringSerial
        template.setHashKeySerializer(hashKey);
        // redis hash value 序列化方式使用jackson
        template.setHashValueSerializer(jacksonSeial);
        template.afterPropertiesSet();
        return template;
    }

    /**
     * redis string
     */
    @Bean
    public ValueOperations<String, Object> valueOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForValue();
    }

    /**
     * redis hash
     */
    @Bean
    public HashOperations<String, String, Object> hashOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForHash();
    }

    /**
     * redis list
     */
    @Bean
    public ListOperations<String, Object> listOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForList();
    }

    /**
     * redis set
     */
    @Bean
    public SetOperations<String, Object> setOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForSet();
    }

    /**
     * redis zset
     */
    @Bean
    public ZSetOperations<String, Object> zSetOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForZSet();
    }

    class DlGenericJackson2JsonRedisSerializer extends GenericJackson2JsonRedisSerializer {

        public DlGenericJackson2JsonRedisSerializer(ObjectMapper objectMapper) {
            super(objectMapper);
            registerNullValueSerializer(objectMapper, null);
            objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
            objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        }
    }
}
