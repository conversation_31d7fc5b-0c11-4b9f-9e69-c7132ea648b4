package com.dl.magicvideo.web.controllers.chartlet;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.chartlet.param.ChartletPageParam;
import com.dl.magicvideo.web.controllers.chartlet.vo.ChartletInfoVO;
import com.dl.magicvideo.web.controllers.chartlet.vo.GroupByCategoryChatletInfoVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-13 11:50
 */
@RestController
@RequestMapping("/visual/chartlet")
public class ChartletInfoController {

    @Resource
    private ChartletInfoProcess chartletInfoProcess;

    @ApiOperation("分页查询贴图信息")
    @PostMapping("/page")
    public ResultPageModel<ChartletInfoVO> page(@RequestBody ChartletPageParam param) {
        return chartletInfoProcess.page(param);
    }

    @ApiOperation("查询全部贴图信息并根据分类分组")
    @PostMapping("/queryallgroupbycategory")
    public ResultModel<List<GroupByCategoryChatletInfoVO>> queryAllGroupByCategory() {
        return chartletInfoProcess.queryAllGroupByCategory();
    }

}
