package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.visual.po.TagPO;
import com.dl.magicvideo.biz.manager.visual.bo.TagPageBO;
import com.dl.magicvideo.biz.manager.visual.dto.TagDTO;

/**
* <AUTHOR>
* @description 针对表【TenantMaterialFolderPO】的数据库操作Service
* @createDate 2023-06-08 16:23:52
*/
public interface TagManager extends IService<TagPO>{
    IPage<TagDTO> pageQuery(TagPageBO bo);
}
