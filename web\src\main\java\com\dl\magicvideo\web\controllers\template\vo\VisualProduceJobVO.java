package com.dl.magicvideo.web.controllers.template.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @describe: VisualProduceJobVO
 * @author: zhousx
 * @date: 2023/2/13 13:54
 */
@Data
public class VisualProduceJobVO {
    @ApiModelProperty("任务id")
    private String jobId;

    @ApiModelProperty("任务状态：0-未开始 1-合成中 2-合成成功 3-合成失败")
    private Integer status;

    @ApiModelProperty("合成视频地址")
    private String videoUrl;

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("成品名称")
    private String name;

    @ApiModelProperty("成品封面")
    private String coverUrl;

    @ApiModelProperty("视频时长，单位毫秒")
    private Long duration;

    @ApiModelProperty("视频大小，单位 字节")
    private Long size;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("完成时间(合成失败和合成中的取修改时间)")
    private Date completeDt;

    @ApiModelProperty("1-竖版 2-横版")
    private String resolutionType;

    @ApiModelProperty("创建人姓名")
    private String creatorName;

    /**
     * @see: ShareConfStateEnum
     */
    @ApiModelProperty("转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置")
    private Integer shareConfState;

    @ApiModelProperty("推荐使用状态 0-未启用 1-已启用")
    private Integer recommendState;
    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String templateName;

    /**
     * 其他格式的视频地址
     */
    @ApiModelProperty("其他格式的视频地址")
    private String otherFormatVideoUrl;

    /**
     * 失败原因
     */
    @ApiModelProperty("失败原因")
    private String failReason;

}
