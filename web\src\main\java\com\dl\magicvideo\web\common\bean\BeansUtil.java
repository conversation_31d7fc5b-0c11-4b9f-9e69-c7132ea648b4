package com.dl.magicvideo.web.common.bean;

import com.dl.magicvideo.biz.common.constant.Const;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.function.BiFunction;

public class BeansUtil {

    public static <T> BiFunction<String, Object, Object> doubleToStringFieldValueEditor(Class<T> clazz) {
        return (t, u) -> {
            Object value;
            Type fieldType;
            try {
                Field field = clazz.getDeclaredField(t);
                field.setAccessible(true);
                fieldType = field.getGenericType();
            } catch (NoSuchFieldException e) {
                throw new RuntimeException(e);
            }
            if (fieldType.equals(Double.class)) {
                if (u == null) {
                    value = "";
                } else {
                    value = new BigDecimal(u.toString(), Const.MATH_CONTEXT).setScale(4, BigDecimal.ROUND_HALF_UP)
                            .toPlainString();
                }
                return value;
            } else {
                return u;
            }
        };
    }

    public static <T> BiFunction<String, Object, Object> transformScale(Class<T> clazz) {
        return (t, u) -> {
            Object value;
            Type fieldType;
            try {
                Field field = clazz.getDeclaredField(t);
                field.setAccessible(true);
                fieldType = field.getGenericType();
            } catch (NoSuchFieldException e) {
                throw new RuntimeException(e);
            }
            if (fieldType.equals(BigDecimal.class)) {
                if (Objects.isNull(u)) {
                    value = null;
                } else {
                    value = ((BigDecimal) u).multiply(new BigDecimal(Const.ONE_HUNDRED))
                            .setScale(Const.TWO, BigDecimal.ROUND_HALF_UP);
                }
                return value;
            } else {
                return u;
            }
        };
    }
}
