package com.dl.magicvideo.biz.dal.music.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("background_music")
public class BackgroundMusicPO extends BasePO {

    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Long id;

    /**
     * 音乐id
     */
    @TableField("biz_id")
    private Long bizId;

    /**
     * 类型 1音乐 2音效
     */
    @TableField("type")
    private Integer type;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 时长
     */
    @TableField("duration")
    private Long duration;

    /**
     * 预览地址
     */
    @TableField("url")
    private String url;

    /**
     * 是否删除 0否 1是
     */
    @TableField("is_deleted")
    private Integer isDeleted;
}
