package com.dl.magicvideo.biz.manager.visual.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MagicVideoJobBO implements Serializable {
    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 作品名称
     */
    private String name;

    /**
     * 封面图地址
     */
    private String coverUrl;

    /**
     * 合成视频地址
     */
    private String videoUrl;

    /**
     * job_type=0:模板id;job_type=1:卡片id
     */
    private Long templateId;

    /**
     * 合成开始时间
     */
    private Date processDt;

    /**
     * 合成结束时间
     */
    private Date completeDt;

    /**
     * 来源
     *
     * @see：com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum
     */
    private Integer source;

    /**
     * 视频时长
     */
    private Long duration;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 作品大小
     */
    private Long size;
}
