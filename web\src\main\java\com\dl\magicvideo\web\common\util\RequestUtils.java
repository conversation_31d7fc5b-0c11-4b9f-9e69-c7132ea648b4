package com.dl.magicvideo.web.common.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;

public class RequestUtils {

    public static final Logger LOGGER = LoggerFactory.getLogger(RequestUtils.class);

    private static final String HTTPS = "https://";
    private static final String HTTP = "http://";

    public static String getReferer(HttpServletRequest httpServletRequest) {
        String requestUrl = HttpUtil.getHeader(httpServletRequest, "referer");
        if (StringUtils.isBlank(requestUrl)) {
            LOGGER.info("Referer为空，从Origin中取");
            requestUrl = HttpUtil.getHeader(httpServletRequest, "origin");
        }
        return requestUrl;
    }

    /**
     * 根据url获取域名
     *
     * @param requestUrl
     * @return
     */
    public static String getHost(String requestUrl) {
        if (org.apache.commons.lang.StringUtils.isEmpty(requestUrl)) {
            return null;
        }
        if (!requestUrl.startsWith(HTTPS) && !requestUrl.startsWith(HTTP)) {
            StringBuffer sbf = new StringBuffer(HTTPS).append(requestUrl);
            requestUrl = sbf.toString();
        }
        try {
            URL url = new URL(requestUrl);
            return url.getHost();
        } catch (MalformedURLException e) {
            LOGGER.warn("URL 转换异常,requestUrl:{},e:", requestUrl, e);
            return null;
        }
    }
}
