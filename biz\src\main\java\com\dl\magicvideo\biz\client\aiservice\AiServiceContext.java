package com.dl.magicvideo.biz.client.aiservice;

import lombok.extern.slf4j.Slf4j;

/**
 * @describe: AiServiceContextHolder
 * @author: zhousx
 * @date: 2023/3/21 9:39
 */
@Slf4j
public class AiServiceContext {
    protected static Class<? extends AiServiceContext> contextClass = AiServiceContext.class;

    private static AiServiceContext currentContext = null;

    private static final ThreadLocal<? extends AiServiceContext> threadLocal = ThreadLocal.withInitial(() -> {
        try {
            return contextClass.newInstance();
        } catch (Throwable e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    });

    public static AiServiceContext getCurrentContext() {
        if (currentContext != null) {
            return currentContext;
        }

        return threadLocal.get();
    }

    public static void init(String tenantCode, Integer channel) {
        AiServiceContext aiServiceContext = AiServiceContext.getCurrentContext();
        aiServiceContext.setTenantCode(tenantCode);
        aiServiceContext.setChannel(channel);
    }

    private String tenantCode;

    private Integer channel;

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }
}
