package com.dl.magicvideo.biz.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 财营助攻手企微端配置
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-07 09:08
 */
@Component
public class WorkBenchConfig {

    @Value("${dl.aiservice.baseUrl}")
    private String aiServiceBaseUrl;

    @Value("${dl.basicservice.baseUrl}")
    private String basicServiceBaseUrl;

    @Value("${dl.saasmagic.baseUrl}")
    private String saasMagicBaseUrl;

    public String getAiServiceBaseUrl() {
        return aiServiceBaseUrl;
    }

    public String getBasicServiceBaseUrl() {
        return basicServiceBaseUrl;
    }

    public String getSaasMagicBaseUrl() {
        return saasMagicBaseUrl;
    }
}
