package com.dl.magicvideo.biz.manager.util;

import com.dl.magicvideo.biz.common.constant.ProduceJobConst;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-22 15:18
 */
public class ProduceFailUtil {

    /**
     * 包装错误原因
     * <p>
     * tts和数字人失败，返回原始原因
     * 内部原因统一包装为 COMMON_FAIL_REASON
     *
     * @return
     */
    public static String wrapperFailReason(VisualProduceJobExtendPO jobExtendPO) {
        if (Objects.isNull(jobExtendPO)) {
            return null;
        }
        return wrapperFailReason(jobExtendPO.getFailReason());
    }

    /**
     * 包装错误原因
     * <p>
     * tts和数字人失败，返回原始原因
     * 内部原因统一包装为 COMMON_FAIL_REASON
     *
     * @return
     */
    public static String wrapperFailReason(String failReason) {
        if (StringUtils.isBlank(failReason)) {
            return null;
        }
        //tts和数字人失败，返回原始原因
        if (failReason.startsWith(ProduceJobConst.TTS_FAIL_REASON_PREFIX) || failReason
                .startsWith(ProduceJobConst.DM_FAIL_REASON_PREFIX)) {
            return failReason;
        }
        return ProduceJobConst.COMMON_FAIL_REASON;
    }
}
