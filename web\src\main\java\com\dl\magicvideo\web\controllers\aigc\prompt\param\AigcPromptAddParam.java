package com.dl.magicvideo.web.controllers.aigc.prompt.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 09:25
 */
@Data
public class AigcPromptAddParam {

    @NotBlank(message = "提示名称不能为空")
    @ApiModelProperty("提示名称")
    private String name;

    @NotNull(message = "提示排序不能为空")
    @ApiModelProperty("提示排序")
    private Integer sort;

    /**
     * 场景，1-热点咨询 2-热点题材事件
     * @see:com.dl.magicvideo.biz.manager.aigc.prompt.enums.AigcPromptScene
     */
    @NotNull(message = "场景不能为空")
    @ApiModelProperty(name = "场景，1-热点咨询 2-热点题材事件")
    private Integer scene;

    @ApiModelProperty("关联文件")
    private AigcPromptFileParam relFile;

    @ApiModelProperty("内容")
    private AigcPromptContentParam content;

}
