package com.dl.magicvideo.biz.dal.visual.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-13 17:14
 */
@Data
public class VisualAiJobExtPO {

    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 视频合成任务id
     */
    private Long produceJobId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 1-数字人，2-TTS
     */
    private Integer jobType;

    /**
     * 0-未执行，1-执行中，2-执行成功，3-执行失败
     */
    private Integer jobStatus;

    private String requestInfo;

    private Date createDt;

    private Long createBy;

    private Long duration;

    private String tenantCode;

    /**
     * visual_produce_job.is_deleted
     */
    private Integer isDeleted;

    private Integer textLength;

}
