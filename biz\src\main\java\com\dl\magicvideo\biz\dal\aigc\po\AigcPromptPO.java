package com.dl.magicvideo.biz.dal.aigc.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 11:36
 */
@Data
@TableName("aigc_prompt")
public class AigcPromptPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id ;

    private Long bizId;

    private String name;

    /**
     * 关联文件对象 json
     */
    private String relFile;

    private Integer sort;

    private Integer isDeleted;

    /**
     * 场景，1-热点咨询 2-热点题材事件
     * @see:com.dl.magicvideo.biz.manager.aigc.prompt.enums.AigcPromptScene
     */
    private Integer scene;

    /**
     * 文案内容对象
     */
    private String content;
}
