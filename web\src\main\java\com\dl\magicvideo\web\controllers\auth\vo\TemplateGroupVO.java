package com.dl.magicvideo.web.controllers.auth.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TemplateGroupVO {
    @ApiModelProperty("模板组id")
    private String templateGroupId;

    @ApiModelProperty("模板组名称")
    private String name;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("修改时间")
    private Date modifyDt;

    @ApiModelProperty("修改人姓名")
    private String modifyName;

    @ApiModelProperty("状态 0-停用 1-启用")
    private Integer status;

}
