package com.dl.magicvideo.biz.manager.visual.dto.preview;

import lombok.Data;

import java.util.List;

/**
 * @describe: PreviewCardDTO
 * @author: zhousx
 * @date: 2023/4/26 11:12
 */
@Data
public class PreviewCardDTO {
    private String cardId;

    private String templateId;

    private String name;

    private String coverUrl;

    private String resolution;

    private String renderData;

    private String lightEditConfigs;

    private List<TtsDTO> ttsData;

    private List<DigitalManDTO> dmData;

    private List<VideoDTO> videoData;

    private List<AudioDTO> audioData;

    private List<DataSheetDTO> dataSheetData;

    private List<DynamicNodeDTO> nodes;

    /**
     * 不展示的隐藏集合
     */
    private List<String> hideNodeTypeList;

    @Data
    public static class DynamicNodeDTO {
        private Long nodeId;

        private Long startTime;
    }

    @Data
    public static class TtsDTO {
        private String id;

        private Integer type;

        private Long start;

        private Long realStart;

        private Long duration;

        private String text;

        private String url;

        private Integer channel;

        private List<SubtitleDTO> subtitles;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;
    }

    @Data
    public static class DigitalManDTO {
        private String id;

        private Integer type;

        private Long start;

        private Long realStart;

        private Long duration;

        private String text;

        private String url;

        private List<SubtitleDTO> subtitles;

        //在整个数字人视频中的开始时间
        private Long startInDmVideo;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;
    }

    @Data
    public static class SubtitleDTO {
        private String text;

        private Long beginTime;

        private Long endTime;
    }

    @Data
    public static class VideoDTO {
        private String id;

        private Integer type;

        private Long start;

        private Long realStart;

        private Long duration;

        private String url;

        private String volume;

        /**
         * 裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频裁剪开始的时间
         */
        private Long startDelay;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失
         */
        private String activeRotationMode;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;
    }

    @Data
    public static class AudioDTO {
        private String id;

        private Integer type;

        private Long start;

        private Long realStart;

        private Long duration;

        private String url;

        private String volume;

        /**
         * 裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频裁剪开始的时间
         */
        private Long startDelay;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失
         */
        private String activeRotationMode;

        /**
         * 淡入时间
         */
        private String fadeInTime;

        /**
         * 淡出时间
         */
        private String fadeOutTime;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;
    }

    @Data
    public static class DataSheetDTO {
        private String dataSheetId;

        /**
         * 内容 {"replaceKey":"替换变量key"}
         */
        private String content;

        /**
         * 时长，单位毫秒
         */
        private Long duration;

        /**
         * 入场延迟
         */
        private Long start;

        /**
         * 出场延迟
         */
        private Long endDelay;

        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;

        /**
         * 实际开始时间
         */
        private Long realStart;
    }
}
