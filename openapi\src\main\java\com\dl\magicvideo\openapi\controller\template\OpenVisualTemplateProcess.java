package com.dl.magicvideo.openapi.controller.template;

import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSimpleInfoUpdateBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.openapi.controller.template.convert.OpenVisualTemplateConvert;
import com.dl.magicvideo.openapi.controller.template.param.OpenTemplatePageParam;
import com.dl.magicvideo.openapi.controller.template.param.OpenTemplateSimpleInfoUpdateParam;
import com.dl.magicvideo.openapi.controller.template.vo.OpenDigitalManSceneVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenDigitalManVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenVisualTemplateDetailVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenVisualTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OpenVisualTemplateProcess {

    @Resource
    private VisualTemplateManager visualTemplateManager;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private AiServiceClient aiServiceClient;

    public ResultModel<OpenVisualTemplateDetailVO> detail(Long templateId) {
        VisualTemplateDTO templateDTO = visualTemplateManager.detail(templateId, operatorUtil.getTenantCode());
        Assert.notNull(templateDTO, "模板不存在。");
        return ResultModel.success(OpenVisualTemplateConvert.cnvVisualTemplateDTO2DetailVO(templateDTO));
    }

    public ResultPageModel<OpenVisualTemplateVO> page(OpenTemplatePageParam param) {
        TemplateSearchBO bo = new TemplateSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setIsSys(Const.ZERO);

        ResponsePageQueryDO<List<VisualTemplateDTO>> pageResponseDO = visualTemplateManager.pageQuery(bo);
        ResultPageModel<OpenVisualTemplateVO> model = new ResultPageModel<>();
        model.setPageIndex(pageResponseDO.getPageIndex());
        model.setPageSize(pageResponseDO.getPageSize());
        model.setTotalPage(pageResponseDO.getTotalPage());
        model.setTotal(pageResponseDO.getTotal());
        if (CollectionUtils.isEmpty(pageResponseDO.getDataResult())) {
            return model;
        }
        List<OpenVisualTemplateVO> voList = pageResponseDO.getDataResult().stream()
                .map(OpenVisualTemplateConvert::cnvVisualTemplateDTO2VO).collect(Collectors.toList());
        model.setDataResult(voList);
        return model;
    }

    public ResultModel<Void> updateSimpleInfo(OpenTemplateSimpleInfoUpdateParam param) {
        TemplateSimpleInfoUpdateBO updateBO = new TemplateSimpleInfoUpdateBO();
        updateBO.setTemplateId(param.getTemplateId());
        updateBO.setName(param.getName());
        updateBO.setCoverUrl(param.getCoverUrl());
        visualTemplateManager.updateTemplateSimpleInfo(updateBO);
        return ResultModel.success(null);
    }

    public List<OpenDigitalManVO> dmList() {
        ResultModel<List<DaVirtualManDTO>> resultModel = aiServiceClient.getDigitalmanList(operatorUtil.getTenantCode(), new DaVirtualManRequestDTO());
        List<DaVirtualManDTO> list = resultModel.getDataResult();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        DaVirtualManRequestDTO daVirtualManRequestDTO = new DaVirtualManRequestDTO();
        daVirtualManRequestDTO.setBizIds(list.stream().map(DaVirtualManDTO::getBizId).collect(Collectors.toList()));
        ResultModel<List<DaVirtualManScenesDTO>> sceneList = aiServiceClient.getDigitalmanSceneListByBizIds(operatorUtil.getTenantCode(), daVirtualManRequestDTO);
        Map<Long, List<DaVirtualManScenesDTO>> dmSceneMap = sceneList.getDataResult().stream().collect(Collectors.groupingBy(DaVirtualManScenesDTO::getBizId));

        return list.stream().map(dm -> {
            OpenDigitalManVO openDigitalManVO = new OpenDigitalManVO();
            BeanUtils.copyProperties(dm, openDigitalManVO);
            List<DaVirtualManScenesDTO> scenesList = dmSceneMap.get(dm.getBizId());
            if (CollectionUtils.isNotEmpty(scenesList)) {
                openDigitalManVO.setSceneList(scenesList.stream().map(scene -> {
                    OpenDigitalManSceneVO sceneVO = new OpenDigitalManSceneVO();
                    BeanUtils.copyProperties(scene, sceneVO);
                    return sceneVO;
                }).collect(Collectors.toList()));
            }
            return openDigitalManVO;
        }).collect(Collectors.toList());
    }
}