package com.dl.magicvideo.web.controllers.internal.chartlet.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-12 19:18
 */
@Data
public class InternalChartletInfoUptParam {

    @NotNull(message = "bizId不能为空")
    @ApiModelProperty("bizId")
    private Long bizId;

    @NotBlank(message = "贴图名称不能为空")
    @ApiModelProperty("名称")
    private String name;

    @NotBlank(message = "内容url不能为空")
    @ApiModelProperty("内容url")
    private String contentUrl;

    @NotBlank(message = "分辨率不能为空")
    @ApiModelProperty("分辨率")
    private String resolutionRatio;

    @ApiModelProperty("封面图")
    private String coverImg;

    @ApiModelProperty("时长，毫秒")
    private Long duration;

    @NotBlank(message = "修改人名称不能为空")
    @ApiModelProperty("修改人名称")
    private String modifyName;

    @ApiModelProperty("修改人")
    private Long modifyBy;

    @NotNull(message = "分类不能为空")
    @ApiModelProperty("分类")
    private Integer category;
}
