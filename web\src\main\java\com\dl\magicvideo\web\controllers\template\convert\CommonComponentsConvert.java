package com.dl.magicvideo.web.controllers.template.convert;

import com.dl.magicvideo.biz.dal.visual.po.CommonComponentsPO;
import com.dl.magicvideo.web.controllers.template.vo.CommonComponentsListVO;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2024/11/19 14:38
 */
public class CommonComponentsConvert {
     public static CommonComponentsListVO cnvCommonComponentsListVO(CommonComponentsPO param) {
         CommonComponentsListVO vo = new CommonComponentsListVO();
         vo.setBizId(param.getBizId().toString());
         vo.setUrl(param.getUrl());
         vo.setName(param.getName());
         return vo;
     }
}
