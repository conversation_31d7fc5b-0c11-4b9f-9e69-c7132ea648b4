package com.dl.magicvideo.web.controllers;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.controller.BaseController;
import com.dl.framework.core.controller.param.AbstractPageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.function.Function;

import static com.dl.magicvideo.biz.common.constant.Const.TOKEN_HEADER_NAME;

@Slf4j
@Validated
public class AbstractController extends BaseController {
    @Resource
    private HttpServletRequest request;

    protected Long getUserId() {
        return null;
    }

    protected <T> ResponsePageQueryDO<T> pageQueryDO(IPage<T> page) {
        ResponsePageQueryDO qd = new ResponsePageQueryDO();
        qd.setPageIndex(page.getCurrent());
        qd.setPageSize(page.getSize());
        qd.setTotal(page.getTotal());
        qd.setDataResult(page.getRecords());
        return qd;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage<T> page) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.getRecords());
        return model;
    }

    protected <T, R> ResultPageModel<R> pageQueryModel(IPage<T> page, Function<T, R> function) {
        ResultPageModel<R> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.convert(t -> function.apply(t)).getRecords());
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(ResponsePageQueryDO page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getPageIndex());
        model.setPageSize(page.getPageSize());
        model.setTotalPage(page.getTotalPage());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

    public static <T> Page<T> convert(AbstractPageParam param) {
        return new Page<>(param.getPageIndex(), param.getPageSize());
    }

    @Override
    public String getToken() {
        return request.getHeader(TOKEN_HEADER_NAME);
    }
}
