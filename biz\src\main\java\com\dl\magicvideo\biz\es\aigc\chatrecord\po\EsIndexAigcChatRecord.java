package com.dl.magicvideo.biz.es.aigc.chatrecord.po;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.IndexId;
import cn.easyes.annotation.IndexName;
import cn.easyes.annotation.rely.FieldType;
import cn.easyes.annotation.rely.IdType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 索引——aigc用户聊天记录
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 14:24
 */
@Data
@IndexName(value = "es_index_aigc_chat_record")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EsIndexAigcChatRecord {

    @IndexId(type = IdType.NONE)
    private String id;

    /**
     * 记录id
     */
    @IndexField(value = "record_id", fieldType = FieldType.LONG)
    @JsonProperty("record_id")
    private Long recordId;

    /**
     * 用户id
     */
    @IndexField(value = "user_id", fieldType = FieldType.LONG)
    @JsonProperty("user_id")
    private Long userId;

    /**
     * 租户编码
     */
    @IndexField("tenant_code")
    @JsonProperty("tenant_code")
    private String tenantCode;

    /**
     * aigc聊天记录类型枚举
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum
     */
    @IndexField("type")
    @JsonProperty("type")
    private Integer type;

    /**
     * 聊天内容
     */
    @IndexField("content")
    @JsonProperty("content")
    private String content;

    /**
     * 聊天内容类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum
     */
    @IndexField("content_type")
    @JsonProperty("content_type")
    private Integer contentType;

    /**
     * 是否删除
     */
    @IndexField("is_deleted")
    @JsonProperty("is_deleted")
    private Integer isDeleted;

    /**
     * 发送时间
     */
    @IndexField("send_dt")
    @JsonProperty("send_dt")
    private Date sendDt;

    /**
     * 创建时间
     */
    @IndexField("create_dt")
    @JsonProperty("create_dt")
    private Date createDt;

    /**
     * 修改时间
     */
    @IndexField("modify_dt")
    @JsonProperty("modify_dt")
    private Date modifyDt;

    /**
     * 是否能生产视频 0-否，1-是
     */
    @IndexField("can_produce")
    @JsonProperty("can_produce")
    private Integer canProduce;

    /**
     * 聊天记录的来源是哪个工具
     * 1-通用工具（AI创作、文本转视频）
     * 2-热点事件题材工具
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum
     */
    @IndexField("from_tool")
    @JsonProperty("from_tool")
    private Integer fromTool;

}
