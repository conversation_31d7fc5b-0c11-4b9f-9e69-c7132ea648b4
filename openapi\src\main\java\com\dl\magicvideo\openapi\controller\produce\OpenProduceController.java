package com.dl.magicvideo.openapi.controller.produce;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.openapi.aspect.annotation.ExtUserIdConvert;
import com.dl.magicvideo.openapi.controller.produce.param.OpenProduceJobInfoParam;
import com.dl.magicvideo.openapi.controller.produce.param.OpenProduceJobPageParam;
import com.dl.magicvideo.openapi.controller.produce.param.OpenProduceParam;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceCreateResultVO;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceJobDetailVO;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceJobInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/openapi/produce")
@Api("开放合成数影作品控制器")
public class OpenProduceController {

    @Resource
    private OpenProduceProcess openProduceProcess;

    @ExtUserIdConvert
    @PostMapping("/submit")
    @ApiOperation("合成")
    public ResultModel<OpenVisualProduceCreateResultVO> produce(@RequestBody @Validated OpenProduceParam param) {
        return openProduceProcess.produce(param);
    }

    @PostMapping("/jobinfo")
    @ApiOperation("查询作品信息")
    public ResultModel<OpenVisualProduceJobDetailVO> jobInfo(@RequestBody @Validated OpenProduceJobInfoParam param) {
        return openProduceProcess.jobInfo(param);
    }

    @PostMapping("/pagejob")
    @ApiOperation("分页查询作品信息")
    public ResultPageModel<OpenVisualProduceJobInfoVO> pageJob(@RequestBody @Validated OpenProduceJobPageParam param) {
        return openProduceProcess.pageJob(param);
    }

}
