package com.dl.magicvideo.web.controllers.template.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: VisualProduceJobPageQueryParam
 * @author: zhousx
 * @date: 2023/2/13 16:23
 */
@Data
public class VisualProduceJobPageQueryParam extends AbstractPageParam {
    @ApiModelProperty("成品名称")
    private String name;

    @ApiModelProperty("0-未开始 1-合成中 2-合成成功 3-合成失败")
    private List<Integer> statusList;

    @ApiModelProperty("排序类型 0-默认排序，时间倒序 1.生产时间正序 2.生成时间倒序")
    private Integer sortType;

    /**
     * 开始时间，时间戳(毫秒)
     */
    private Long startTime;

    /**
     * 结束时间，时间戳（毫秒）
     */
    private Long endTime;

    @ApiModelProperty("1-竖版 2-横版")
    private Integer resolutionType;

    @ApiModelProperty("推荐使用状态 0-未启用 1-已启用")
    private Integer recommendState;

    /**
     * 作品类型，1-常规作品，2-数据图表作品 3-aigc作品
     * @see: com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum
     */
    @ApiModelProperty("作品类型，1-常规作品，2-数据图表作品")
    private Integer type;

    @ApiModelProperty("是否只查自己的作品，0-否，1-是，默认为0")
    private Integer onlyMine;

}
