package com.dl.magicvideo.web.controllers.music;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.aiservice.share.aichat.consts.AiChatKimiConst;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.PlaceHolderUtils;
import com.dl.magicvideo.biz.dal.music.po.BackgroundMusicPO;
import com.dl.magicvideo.biz.manager.aigc.chat.AigcChatManager;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatResponseBO;
import com.dl.magicvideo.biz.manager.aigc.properties.AigcPropertites;
import com.dl.magicvideo.biz.manager.music.BackgroundMusicManager;
import com.dl.magicvideo.biz.manager.music.dto.BackgroundMusicDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.music.convert.BackgroundMusicConvert;
import com.dl.magicvideo.web.controllers.music.param.BackgroundMusicAiChooseStyleParam;
import com.dl.magicvideo.web.controllers.music.param.BackgroundMusicPageParam;
import com.dl.magicvideo.web.controllers.music.vo.BackgroundMusicAiChooseStyleVO;
import com.dl.magicvideo.web.controllers.music.vo.BackgroundMusicVO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

@Component
public class BackgroundMusicProcess extends AbstractController {

    @Autowired
    private BackgroundMusicManager backGroundMusicManager;
    @Resource
    private AigcChatManager aigcChatManager;
    @Resource
    private AigcPropertites aigcPropertites;
    @Resource
    private OperatorUtil operatorUtil;

    private static List<String> bgmStyleList = Lists.newArrayList("'轻松'", "'伤感'");

    public ResultModel<List<BackgroundMusicVO>> list(BackgroundMusicPageParam param) {
        List<BackgroundMusicDTO> backgroundMusicDTOList = backGroundMusicManager.backgroundMusicList(param.getType());
        return ResultModel.success(backgroundMusicDTOList.stream().map(BackgroundMusicConvert::cnvBackgroundMusicDTO2VO)
                .collect(Collectors.toList()));
    }

    public ResultPageModel<BackgroundMusicVO> page(BackgroundMusicPageParam param) {
        LambdaQueryWrapper<BackgroundMusicPO> wrapper = Wrappers.lambdaQuery(BackgroundMusicPO.class);
        wrapper.eq(Objects.nonNull(param.getType()), BackgroundMusicPO::getType, param.getType())
                .like(StringUtils.isNotBlank(param.getName()), BackgroundMusicPO::getName, param.getName())
                .eq(BackgroundMusicPO::getIsDeleted, Const.ZERO);
        Page<BackgroundMusicPO> resp = backGroundMusicManager
                .page(new Page<>(param.getPageIndex(), param.getPageSize()), wrapper);

        return pageQueryModel(resp, resp.getRecords().stream().map(BackgroundMusicConvert::cnvBackgroundMusicPO2VO)
                .collect(Collectors.toList()));
    }

    public ResultModel<BackgroundMusicAiChooseStyleVO> aiChooseStyle(BackgroundMusicAiChooseStyleParam param) {
        Assert.isTrue(StringUtils.isNotBlank(param.getText()), "输入文本不能为空");

        //1.构建请求参数
        Properties properties = new Properties();
        properties.put(AigcPropertites.TEXT, param.getText());
        properties.put(AigcPropertites.BGM_STYLE_NUMBER, bgmStyleList.size() + "");
        properties
                .put(AigcPropertites.BGM_STYLE_STR, String.join(SymbolE.PUNCTUATION_MARK_CN.getValue(), bgmStyleList));
        //完整预设文案
        String wholePresupposeText = PlaceHolderUtils
                .resolveValue(aigcPropertites.getAigcChooseBgmStylePresupposeText(), properties);

        //2.调用ai对话服务传入消息，接口要返回ai的响应结果
        AigcSingleChatRequestBO singleChatRequestBO = new AigcSingleChatRequestBO();
        singleChatRequestBO.setUserId(operatorUtil.getOperator());
        singleChatRequestBO.setUserMessage(wholePresupposeText);
        singleChatRequestBO.setModel(AiChatKimiConst.MOONSHOT_8K);
        singleChatRequestBO.setRespMaxToken(Const.ONE_ZERO_TWO_FOUR);
        AigcSingleChatResponseBO singleChatResponseBO = aigcChatManager
                .singleChat(operatorUtil.getTenantCode(), singleChatRequestBO);

        String respStyle = singleChatResponseBO.getContent();
        if (StringUtils.isBlank(respStyle)) {
            return ResultModel.success(null);
        }
        BackgroundMusicAiChooseStyleVO vo = new BackgroundMusicAiChooseStyleVO();
        vo.setStyleDesc(respStyle);
        return ResultModel.success(vo);
    }

    public static void main(String[] args) {
        Properties properties = new Properties();
        properties.put(AigcPropertites.TEXT,
                "2024年3月22日晚，俄罗斯首都莫斯科近郊克拉斯诺戈尔斯克市一家音乐厅发生严重恐怖袭击事件。此次恐怖袭击造成至少143人死亡，是俄罗斯多年来最严重的恐怖袭击之一。当地缘政治和市场不确定性增加时，黄金作为避险资产，其价格往往会上涨，因为投资者倾向于寻求安全的资产来保护其财富。");
        properties.put(AigcPropertites.BGM_STYLE_NUMBER, bgmStyleList.size() + "");
        properties
                .put(AigcPropertites.BGM_STYLE_STR, String.join(SymbolE.PUNCTUATION_MARK_CN.getValue(), bgmStyleList));
        //完整预设文案
        String wholePresupposeText = PlaceHolderUtils
                .resolveValue(AigcPropertites.AIGC_CHOOSE_BGM_STYLE_PRESUPPOSE_TEXT, properties);
        System.out.println(wholePresupposeText);
    }

}
