package com.dl.magicvideo.web.controllers.internal.tenant.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class TenantTemplateAuthPageQueryParam extends AbstractPageParam {

    @ApiModelProperty("租户编号")
    @NotBlank
    private String tenantCode;

    @ApiModelProperty("模板id")
    private Long templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("状态 0-授权 1-未授权")
    private Integer status;
}
