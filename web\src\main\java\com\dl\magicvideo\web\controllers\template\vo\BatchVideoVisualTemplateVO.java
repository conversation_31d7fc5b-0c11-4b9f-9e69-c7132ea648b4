package com.dl.magicvideo.web.controllers.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe: TemplateVO
 * @author: zhousx
 * @date: 2023/2/1 14:55
 */
@Data
public class BatchVideoVisualTemplateVO {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty("短视频")
    private String shortVideoUrl;

    @ApiModelProperty("tts配置")
    private String ttsParam;

    @ApiModelProperty("模板时长，毫秒")
    private Long duration;
}
