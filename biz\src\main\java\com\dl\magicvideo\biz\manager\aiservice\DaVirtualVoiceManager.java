package com.dl.magicvideo.biz.manager.aiservice;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceListQueryDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-22 14:19
 */
@Component
public class DaVirtualVoiceManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(DaVirtualVoiceManager.class);

    @Resource
    private AiServiceClient aiServiceClient;

    public List<DaVirtualVoiceBaseInfoDTO> listByChannelAndVoiceKeyList(DaVirtualVoiceListQueryDTO queryDTO) {
        ResultModel<List<DaVirtualVoiceBaseInfoDTO>> resultModel = aiServiceClient
                .listByChannelAndVoiceKeyList(queryDTO);
        if (!resultModel.isSuccess()) {
            LOGGER.error("根据根据厂商+声音编码列表查询声音列表失败，queryDTO:{},resultModel:{}", JSONUtil.toJsonStr(queryDTO),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询声音列表失败");
        }
        return resultModel.getDataResult();

    }

}
