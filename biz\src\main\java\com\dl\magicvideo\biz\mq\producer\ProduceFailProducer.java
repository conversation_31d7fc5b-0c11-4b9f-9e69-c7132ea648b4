package com.dl.magicvideo.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.mq.DlChannels;
import com.dl.magicvideo.biz.mq.dto.ProduceFailDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Component
public class ProduceFailProducer {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProduceFailProducer.class);

    @Autowired
    private DlChannels dlChannels;

    public void sendProduceFailMsg(Long jobId, String tenantCode, String failReason) {
        LOGGER.info("准备发送视频合成失败消息 jobId = {}", jobId);
        ProduceFailDTO dto = new ProduceFailDTO();
        dto.setJobId(jobId);
        dto.setTenantCode(tenantCode);
        dto.setFailReason(failReason);
        Message<ProduceFailDTO> message = MessageBuilder.withPayload(dto).build();
        try {
            boolean sendResult = dlChannels.producefailproducer().send(message, 2000L);
            LOGGER.info("发送视频合成失败的消息,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
        } catch (Exception e) {
            LOGGER.error("发送视频合成失败的消息发生异常,message:{},e:{}", JSONUtil.toJsonStr(message), e);
        }
    }
}
