package com.dl.magicvideo.biz.manager.visual.enums;

/**
 * 租户在数影的合作方式枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 14:33
 */
public enum TenantProduceTypeEnum {

    //0-纯页面模式，1-纯API提供模式，2-页面+API模式
    PURE_PAGE(0, "纯页面模式"),
    PURE_API(1, "纯API提供模式"),
    PAGE_AND_API(2, "页面+API模式");

    private Integer type;

    private String desc;

    TenantProduceTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
