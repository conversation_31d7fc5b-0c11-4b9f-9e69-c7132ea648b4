package com.dl.magicvideo.biz.manager.visual.enums;

/**
 * 模板类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-16 16:08
 */
public enum TemplateTypeEnum {

    NORMAL(1,"常规模板"),
    DATA_CHART(2,"数据图表模板")
    ;

    private Integer type;

    private String desc;

    TemplateTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
