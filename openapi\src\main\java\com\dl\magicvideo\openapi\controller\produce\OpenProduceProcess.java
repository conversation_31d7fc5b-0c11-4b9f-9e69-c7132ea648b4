package com.dl.magicvideo.openapi.controller.produce;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.account.trial.emuns.TenantDosageEnum;
import com.dl.magicvideo.biz.dal.account.trial.po.TenantDosageConfigPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.account.trial.TenantDosageConfigManager;
import com.dl.magicvideo.biz.manager.aiservice.DaVirtualManManager;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManParamBO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.InterfaceDTO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateLightEditConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.openapi.controller.produce.convert.OpenVisualProduceJobConvert;
import com.dl.magicvideo.openapi.controller.produce.param.OpenDigitalManParam;
import com.dl.magicvideo.openapi.controller.produce.param.OpenProduceJobInfoParam;
import com.dl.magicvideo.openapi.controller.produce.param.OpenProduceJobPageParam;
import com.dl.magicvideo.openapi.controller.produce.param.OpenProduceParam;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceCreateResultVO;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceJobDetailVO;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceJobInfoVO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OpenProduceProcess {

    @Resource
    private AccountTenantTrialManager accountTenantTrialManager;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private VisualProduceJobManager visualProduceJobManager;
    @Resource
    private VisualTemplateManager visualTemplateManager;
    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;
    @Resource
    private VisualAiJobManager visualAiJobManager;
    @Resource
    private DaVirtualManManager daVirtualManManager;

    @Resource
    private TenantDosageConfigManager tenantDosageConfigManager;

    private void checkDosage(){
        TenantDosageConfigPO configPO = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, operatorUtil.getTenantCode()).eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.DIGITAL_MAN.getType()).one();
        if (Objects.nonNull(configPO) && !Objects.equals(configPO.getBalance(), Const.MINUS_ONE_LONG) && configPO.getBalance() * 60 * 1000 <= configPO.getUsed()) {
            throw BusinessServiceException.getInstance("数字人合成分钟数不足，请联系商务处理");
        }
        TenantDosageConfigPO configPO2 = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, operatorUtil.getTenantCode()).eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.VIDEO.getType()).one();
        if (Objects.nonNull(configPO2) && !Objects.equals(configPO2.getBalance(), Const.MINUS_ONE_LONG) && configPO2.getBalance() * 60 * 1000 <= configPO2.getUsed()) {
            throw BusinessServiceException.getInstance("视频合成分钟数不足，请联系商务处理");
        }
        TenantDosageConfigPO configPO3 = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, operatorUtil.getTenantCode()).eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.TTS.getType()).one();
        if (Objects.nonNull(configPO3) && !Objects.equals(configPO3.getBalance(), Const.MINUS_ONE_LONG) && configPO3.getBalance() * 10000 <= configPO3.getUsed()) {
            throw BusinessServiceException.getInstance("TTS合成字数不足，请联系商务处理");
        }
    }

    public ResultModel<OpenVisualProduceCreateResultVO> produce(OpenProduceParam param) {
        checkDosage();
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getLightEditConfigs()), "轻编辑配置不能为空");
        accountTenantTrialManager.checkTenantBalance(operatorUtil.getTenantCode(), Const.ONE);
        TemplateLightEditConfigDTO lightEditConfigs = new TemplateLightEditConfigDTO();
        lightEditConfigs.setLightEditConfigs(param.getLightEditConfigs());
        lightEditConfigs.setCrossClips(param.getCrossClips());


        VisualTemplatePO visualTemplatePO = visualTemplateManager.lambdaQuery()
                .select(VisualTemplatePO::getTemplateId, VisualTemplatePO::getApiData)
                .eq(VisualTemplatePO::getTemplateId, param.getTemplateId()).eq(VisualTemplatePO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(visualTemplatePO, "不存在的模板");


        ProduceJobBO produceJobBO = new ProduceJobBO();
        produceJobBO.setTemplateId(Long.valueOf(param.getTemplateId()));
        produceJobBO.setReplaceData(null);
        produceJobBO.setSource(VisualProduceJobSourceEnum.OPEN_PRODUCE.getCode());
        produceJobBO.setLightEditConfigs(lightEditConfigs);
        produceJobBO.setExtUserId(param.getExtUserId());
        if (Objects.nonNull(param.getDigitalMan())) {
            DigitalManParamBO digitalManParamBO = new DigitalManParamBO();
            produceJobBO.setDigitalManParamBO(digitalManParamBO);

            OpenDigitalManParam digitalMan = param.getDigitalMan();
            digitalManParamBO.setChannel(digitalMan.getChannel());
            digitalManParamBO.setSceneId(digitalMan.getSceneId());
            digitalManParamBO.setVoiceCode(digitalMan.getVmVoiceKey());
            digitalManParamBO.setSpeed(digitalMan.getSpeed());
        }
        produceJobBO.setRenderData("");
        produceJobBO.setApiData(visualTemplatePO.getApiData());

        if (CollectionUtils.isNotEmpty(param.getInterfaceParam())) {
            List<List<InterfaceDTO>> interfaceList = buildInterface(param.getInterfaceParam());
            for (List<InterfaceDTO> interfaceDTO : interfaceList) {
                produceJobBO.setApiData(JSONUtil.toJsonStr(interfaceDTO));
                Long jobId = visualProduceJobManager.produce(produceJobBO);
                log.info("第三方批量合成结果，jobId:{}", jobId);
            }
            return ResultModel.success(null);
        }
        Long jobId = visualProduceJobManager.produce(produceJobBO);
        OpenVisualProduceCreateResultVO openVisualProduceCreateResultVO = new OpenVisualProduceCreateResultVO();
        openVisualProduceCreateResultVO.setJobId(String.valueOf(jobId));
        return ResultModel.success(openVisualProduceCreateResultVO);
    }

    public ResultModel<OpenVisualProduceJobDetailVO> jobInfo(OpenProduceJobInfoParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getJobId()), "视频id参数错误");
        VisualProduceJobPO job = visualProduceJobManager.lambdaQuery()
                .eq(VisualProduceJobPO::getJobId, Long.valueOf(param.getJobId()))
                .eq(VisualProduceJobPO::getTenantCode, operatorUtil.getTenantCode())
                .eq(VisualProduceJobPO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(job, "作业不存在");

        VisualTemplatePO templatePO = visualTemplateManager.getOne(Wrappers.lambdaQuery(VisualTemplatePO.class)
                .eq(VisualTemplatePO::getTemplateId, job.getTemplateId()));

        VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                        .eq(VisualProduceJobExtendPO::getProduceJobId, job.getJobId()));

        List<VisualAiJobPO> dmJobList = visualAiJobManager
                .list(Wrappers.lambdaQuery(VisualAiJobPO.class).eq(VisualAiJobPO::getProduceJobId, job.getJobId())
                        .eq(VisualAiJobPO::getJobType, AiJobTypeE.DIGITAL_MAN.getCode()));


        Map<String, DaVirtualManScenesDTO> dmSceneMap = Maps.newHashMap();
        DigitalManJobBO digitalManJobBO = new DigitalManJobBO();

        if (CollectionUtils.isNotEmpty(dmJobList)) {
            //数字人信息都一样取第一个
            String requestInfo = dmJobList.get(0).getRequestInfo();
            digitalManJobBO = JSONUtil.toBean(requestInfo, DigitalManJobBO.class);
            List<DaVirtualManScenesDTO> daVirtualManScenesDTOList = daVirtualManManager
                    .vmSceneListBySceneIds(operatorUtil.getTenantCode(), Collections.singletonList(digitalManJobBO.getSceneId()),
                            Collections.singletonList(digitalManJobBO.getChannel()), Const.ZERO);
            //key:channel-sceneId
            dmSceneMap = daVirtualManScenesDTOList.stream().collect(Collectors
                    .toMap((scene -> scene.getChannel() + "-" + scene.getSceneId()), Function.identity(), (s1, s2) -> s1));
        }

        return ResultModel.success(
                OpenVisualProduceJobConvert.buildOpenVisualProduceJobDetailVO(job, templatePO, jobExtendPO, dmJobList
                        , digitalManJobBO, dmSceneMap));
    }

    public ResultPageModel<OpenVisualProduceJobInfoVO> pageJob(OpenProduceJobPageParam param) {
        ProduceJobSearchBO bo = new ProduceJobSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setType(JobTypeEnum.NORMAL.getType());
        ResponsePageQueryDO<List<VisualProduceJobDTO>> pageResultDTO = visualProduceJobManager.pageQuery(bo);

        ResultPageModel<OpenVisualProduceJobInfoVO> resultPageModel = new ResultPageModel<>();
        resultPageModel.setPageIndex(pageResultDTO.getPageIndex());
        resultPageModel.setPageSize(pageResultDTO.getPageSize());
        resultPageModel.setTotalPage(pageResultDTO.getTotalPage());
        resultPageModel.setTotal(pageResultDTO.getTotal());
        if (CollectionUtils.isEmpty(pageResultDTO.getDataResult())) {
            return resultPageModel;
        }
        List<VisualProduceJobDTO> jobDTOList = pageResultDTO.getDataResult();

        //提取作品id,查询扩展表
        Set<Long> jobIds = jobDTOList.stream().map(VisualProduceJobDTO::getJobId).collect(Collectors.toSet());
        List<VisualProduceJobExtendPO> jobExtendPOList = visualProduceJobExtendManager
                .list(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                        .in(VisualProduceJobExtendPO::getProduceJobId, jobIds));
        Map<Long, VisualProduceJobExtendPO> jobExtendPOMap = jobExtendPOList.stream().collect(
                Collectors.toMap(VisualProduceJobExtendPO::getProduceJobId, Function.identity(), (e1, e2) -> e1));

        resultPageModel.setDataResult(jobDTOList.stream().map(jobDTO -> OpenVisualProduceJobConvert
                        .buildOpenVisualProduceJobInfoVO(jobDTO, jobExtendPOMap.get(jobDTO.getJobId())))
                .collect(Collectors.toList()));
        return resultPageModel;
    }


    /**
     * 针对/txy/dayReview拆分成多条数据，进行批量合成
     *
     * [
     *     {
     *         "name": "/txy/dayReview/900630",
     *         "keyMap": {
     *             "time": [
     *                 170110080001,
     *                 170110080002
     *             ]
     *         }
     *     },
     *     {
     *         "name": "/common/manager/detail/d8edd2",
     *         "keyMap": {
     *             "code": [
     *                 "600195.SH",
     *                 "600280.SH",
     *                 "000002.SH",
     *                 "000999.SZ"
     *             ]
     *         }
     *     }
     * ]
     *
     * @param source
     * @return
     */
    public List<List<InterfaceDTO>> buildInterface(List<InterfaceDTO> source) {

        List<List<InterfaceDTO>> interfaceTarget = Lists.newArrayList();
        source.forEach(interfaceDTO -> {
            String interfaceName = strValue(interfaceDTO.getName(), true);
            if ("/txy/dayReview".equals(interfaceName)) {
                Map<String, Object> keyMap = interfaceDTO.getKeyMap();
                List<Long> timeList = (List<Long>) keyMap.get("time");
                for (Long date : timeList) {
                    Map<String, Object> targetMap = Maps.newHashMap();
                    InterfaceDTO target = new InterfaceDTO();
                    target.setName(interfaceDTO.getName());
                    targetMap.put("time", date);
                    interfaceDTO.setKeyMap(targetMap);
                    interfaceTarget.add(new ArrayList<>(Arrays.asList(interfaceDTO)));
                }
            }
        });

        List<InterfaceDTO> collect = source.stream().filter(i -> !"/txy/dayReview".equals(strValue(i.getName(), true))).collect(Collectors.toList());
        for (List<InterfaceDTO> interfaceDTOS : interfaceTarget) {
            interfaceDTOS.addAll(collect);
        }
        return interfaceTarget;
    }

    /**
     * @param str  字符串
     * @param head true为去除/前的结果，false为/之后的结果尾部
     * @return
     */
    public String strValue(String str, boolean head) {
        if (head) {
            int lastIndex = str.lastIndexOf("/");
            return str.substring(0, lastIndex);
        } else {
            String[] parts = str.split("/");
            return parts[parts.length - 1];
        }
    }

}
