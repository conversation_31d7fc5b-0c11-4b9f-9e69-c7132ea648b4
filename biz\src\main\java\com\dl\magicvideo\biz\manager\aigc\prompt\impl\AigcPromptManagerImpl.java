package com.dl.magicvideo.biz.manager.aigc.prompt.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.aigc.AigcPromptMapper;
import com.dl.magicvideo.biz.dal.aigc.po.AigcPromptPO;
import com.dl.magicvideo.biz.manager.aigc.prompt.AigcPromptManager;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptAddBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptQueryBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptUptBO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 11:39
 */
@Component
public class AigcPromptManagerImpl extends ServiceImpl<AigcPromptMapper, AigcPromptPO> implements AigcPromptManager {

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    public IPage<AigcPromptPO> pageQuey(AigcPromptQueryBO queryBO) {
        return this.page(new Page<>(queryBO.getPageIndex(), queryBO.getPageSize()),
                Wrappers.lambdaQuery(AigcPromptPO.class).eq(AigcPromptPO::getIsDeleted, Const.ZERO)
                        .eq(Objects.nonNull(queryBO.getScene()), AigcPromptPO::getScene, queryBO.getScene())
                        .orderByAsc(AigcPromptPO::getSort, AigcPromptPO::getId));
    }

    @Override
    public Long add(AigcPromptAddBO addBO) {
        AigcPromptPO po = new AigcPromptPO();
        po.setBizId(hostTimeIdg.generateId().longValue());
        po.setName(addBO.getName());
        if (Objects.nonNull(addBO.getRelFile())) {
            po.setRelFile(JSONUtil.toJsonStr(addBO.getRelFile()));
        }
        if(Objects.nonNull(addBO.getContent())){
            po.setContent(JSONUtil.toJsonStr(addBO.getContent()));
        }
        po.setSort(addBO.getSort());
        po.setIsDeleted(Const.ZERO);
        po.setCreateDt(addBO.getCreateDt());
        po.setCreateBy(addBO.getCreateBy());
        po.setModifyDt(addBO.getModifyDt());
        po.setModifyBy(addBO.getModifyBy());
        po.setScene(addBO.getScene());

        this.save(po);

        return po.getBizId();
    }

    @Override
    public void update(AigcPromptUptBO uptBO) {
        AigcPromptPO exist = this
                .getOne(Wrappers.lambdaQuery(AigcPromptPO.class).eq(AigcPromptPO::getBizId, uptBO.getBizId())
                        .eq(AigcPromptPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(exist)) {
            throw BusinessServiceException.getInstance("该提示不存在");
        }

        exist.setName(uptBO.getName());
        exist.setSort(uptBO.getSort());
        exist.setRelFile(Objects.nonNull(uptBO.getRelFile()) ? JSONUtil.toJsonStr(uptBO.getRelFile()) : null);
        exist.setContent(Objects.nonNull(uptBO.getContent()) ? JSONUtil.toJsonStr(uptBO.getContent()) : null);
        exist.setModifyBy(uptBO.getModifyBy());
        exist.setModifyDt(uptBO.getModifyDt());

        this.updateById(exist);
    }
}
