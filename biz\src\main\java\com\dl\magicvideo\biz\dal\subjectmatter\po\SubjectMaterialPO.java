package com.dl.magicvideo.biz.dal.subjectmatter.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("subject_material")
public class SubjectMaterialPO implements Serializable {
    private static final long serialVersionUID = -8937579827349872394L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 题材id(关联表id)
     */
    private Long matterId;

    /**
     * 素材地址
     */
    private String url;

    /**
     * 素材类型：3-视频，6-图片
     */
    private Integer materialType;

    /**
     * 分辨率
     */
    private String resolution;

    /**
     * 视频时长，毫秒
     */
    private Long duration;

    private Date createDt;
    private Long createBy;
    private String creatorName;
    private Date modifyDt;
    private Long modifyBy;
    private String modifyName;
}
