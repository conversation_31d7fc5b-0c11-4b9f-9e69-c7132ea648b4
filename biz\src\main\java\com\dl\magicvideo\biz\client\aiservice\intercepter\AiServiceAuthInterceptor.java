package com.dl.magicvideo.biz.client.aiservice.intercepter;

import com.dl.aiservice.share.common.auth.AuthHeader;
import com.dl.aiservice.share.common.auth.AuthTokenDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.properties.AuthProperties;
import com.dl.magicvideo.biz.common.util.ApplicationUtil;
import com.dl.magicvideo.biz.common.util.RsaUtil;
import com.dl.magicvideo.biz.config.WorkBenchConfig;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * @describe: AiServiceAuthInterceptor
 * @author: zhousx
 * @date: 2023/3/20 17:04
 */
@Slf4j
public class AiServiceAuthInterceptor implements Interceptor {
    
    @Override
    public boolean beforeExecute(ForestRequest request) {
        request = setUrl(request);
        Object[] args = request.getArguments();
        Integer channel = null;
        String tenantCode = null;
        if(args.length > 1) {
            if(StringUtils.isNumeric(args[0].toString())) {
                channel = Integer.valueOf(args[0].toString());
            } else {
                tenantCode = args[0].toString();
            }
        }

        AuthTokenDTO authTokenDTO = new AuthTokenDTO();
        authTokenDTO.setChannel(channel == null ? ServiceChannelEnum.ALIYUN.getCode():channel);
        authTokenDTO.setTokenCreateDt(new Date());
        authTokenDTO.setTenantCode(StringUtils.isNotBlank(tenantCode)?tenantCode:Const.DEFAULT_TENANT_CODE);
        request.addHeader(AuthHeader.TOKEN_HEADER_NAME, genAuthToken(authTokenDTO, getTenantAuth(Const.DEFAULT_TENANT_CODE)));
        request.addHeader(AuthHeader.TENANT_CODE_HEADER_NAME, Const.DEFAULT_TENANT_CODE);
        return Boolean.TRUE;
    }

    private ForestRequest setUrl(ForestRequest request) {
        WorkBenchConfig bean = ApplicationUtil.getBean(WorkBenchConfig.class);
        return request.setUrl(bean.getAiServiceBaseUrl() + request.getMethod().getMetaRequest().getUrl());
    }

    private String genAuthToken(AuthTokenDTO authTokenDTO, AuthProperties.Tenant tenant) {
        String str = JsonUtils.toJSON(authTokenDTO);
        try {
            return RsaUtil.encryptByPrivateKey(tenant.getPrivateKey(), str);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw BusinessServiceException.getInstance("aiservice token 生成失败");
        }
    }

    private AuthProperties.Tenant getTenantAuth(String tenantCode) {
        AuthProperties authProperties = ApplicationUtil.getBean(AuthProperties.class);
        List<AuthProperties.Tenant> tenants = authProperties.getTenants();
        if(CollectionUtils.isNotEmpty(tenants)) {
            return tenants.stream().filter(tenant -> tenant.getTenantCode().equals(tenantCode)).findFirst().get();
        }
        return null;
    }
}
