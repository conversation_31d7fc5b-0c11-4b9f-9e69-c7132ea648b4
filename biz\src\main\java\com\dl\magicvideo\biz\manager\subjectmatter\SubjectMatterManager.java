package com.dl.magicvideo.biz.manager.subjectmatter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectAddBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterJoinPageBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterPageBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterSaveBO;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.SubjectTreeDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:28
 */
public interface SubjectMatterManager extends IService<SubjectMatterPO> {

    Long save(SubjectMatterSaveBO saveBO);

    IPage<SubjectMatterPO> page(SubjectMatterPageBO pageBO);

    ResponsePageQueryDO<List<SubjectMatterPO>> joinPage(SubjectMatterJoinPageBO pageBO);

    Long addAll(SubjectAddBO subjectAddBO);

    /**
     * 根据事件驱动文案获取数结构
     * @return
     */
    SubjectTreeDTO getTreeByText(Long matterId, String text);
}
