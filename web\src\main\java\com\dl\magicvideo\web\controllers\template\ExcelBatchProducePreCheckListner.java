package com.dl.magicvideo.web.controllers.template;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.web.controllers.template.vo.ExcelBatchProducePreCheckResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-04 18:07
 */
@Slf4j
public class ExcelBatchProducePreCheckListner extends AnalysisEventListener<Map<Integer, String>> {

    /**
     * 最大行数：20
     */
    private static final int MAX_ROW_COUNT = 20;

    /**
     * 校验结果
     */
    private ExcelBatchProducePreCheckResultVO resultVO;

    /**
     * 行的数量
     */
    private Integer rows = 0;

    public ExcelBatchProducePreCheckListner(ExcelBatchProducePreCheckResultVO resultVO) {
        this.resultVO = resultVO;
    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(dataMap));
        // 获取当前行号
        int currentRowNum = context.readRowHolder().getRowIndex();
        dataMap.values().forEach(data -> {
            if (StringUtils.isBlank(data)) {
                resultVO.getBlankDataRows().add(currentRowNum + 1);
            }
        });

        rows += 1;
        if (rows > MAX_ROW_COUNT) {
            throw BusinessServiceException.getInstance("单次批量合成不超过20条，如需增加请联系相关销售人员");
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        resultVO.setTotalRows(rows);
    }

}