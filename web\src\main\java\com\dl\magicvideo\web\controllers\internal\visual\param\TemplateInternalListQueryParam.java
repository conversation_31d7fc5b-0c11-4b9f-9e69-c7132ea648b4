package com.dl.magicvideo.web.controllers.internal.visual.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @describe: TemplateInternalPageQueryParam
 * @author: zhousx
 * @date: 2023/9/7 14:17
 */
@Data
public class TemplateInternalListQueryParam {

    @NotBlank(message = "租户编号不能为空")
    private String tenantCode;

    private Integer status;

    @NotEmpty(message = "模板id列表不能为空")
    private List<Long> templateIdList;
}
