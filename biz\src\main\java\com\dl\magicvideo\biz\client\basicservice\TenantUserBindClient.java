package com.dl.magicvideo.biz.client.basicservice;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.client.basicservice.dto.TenantUserSimpleDTO;
import com.dl.magicvideo.biz.client.basicservice.intercepter.BasicServiceInterceptor;
import com.dl.magicvideo.biz.client.basicservice.param.InternalTenantUserBindQueryParam;
import com.dl.magicvideo.biz.client.basicservice.param.TenantUserBindQueryDTO;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 10:37
 */
@BaseRequest(interceptor = BasicServiceInterceptor.class)
public interface TenantUserBindClient {

    /**
     * 根据用户id查询外部用户id
     *
     * @param param
     * @return
     */
    @Post(url = "/internal/tenantuserbind/querybyuserid")
    ResultModel<String> queryExtUserIdByUserId(@JSONBody InternalTenantUserBindQueryParam param);

    /**
     * 根据外部用户id查询用户信息
     *
     * @param param
     * @return
     */
    @Post(url = "/internal/tenantuserbind/querybyextuserid")
    ResultModel<TenantUserSimpleDTO> queryByExtUserId(@JSONBody TenantUserBindQueryDTO param);

}
