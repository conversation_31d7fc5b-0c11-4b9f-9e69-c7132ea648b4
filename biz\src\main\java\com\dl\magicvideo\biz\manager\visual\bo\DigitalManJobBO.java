package com.dl.magicvideo.biz.manager.visual.bo;

import lombok.Data;

/**
 * @describe: DigitalManJobBO
 * @author: zhousx
 * @date: 2023/6/8 17:52
 */
@Data
public class DigitalManJobBO {
    private Integer channel;

    private String sceneId;

    private Long templateId;

    private Long produceJobId;


    /**
     * 驱动类型，0音频驱动 1文本驱动
     */
    private Integer driveType;

    private String text;

    /**
     * 音频驱动的链接
     */
    private String audioUrl;

    private String voiceCode;

    /**
     * 数字人合成方式是0-合并合成时，不需要此字段。
     * 数字人合成方式是1-分段合成时，需要此字段。
     */
    private String configId;

    private Integer enableSubtitle;

    private Integer maxLength;

    private Double speed;

    private Integer customStoreUrl;

    /**
     * 数字人视频合成方式。 0-模板每个卡片合成1次请求数字人合成视频，并通过ASR识别时间戳。1-模板每个卡片都请求数字人合成视频方式。
     */
    private Integer dmProduceMode;

    private Long userId;
}
