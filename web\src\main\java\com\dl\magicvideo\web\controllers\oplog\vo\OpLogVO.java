package com.dl.magicvideo.web.controllers.oplog.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 17:37
 */
@Data
@ApiModel("操作日志VO")
public class OpLogVO {

    @ApiModelProperty("日志id")
    private String logId;

    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

    @ApiModelProperty(value = "接入业务的编码 数影固定是magic")
    private String bizCode;

    @ApiModelProperty(value = "操作对象 模板：template 用户:user")
    private String opObject;

    @ApiModelProperty(value = "操作对象主键")
    private String opKey;

    @ApiModelProperty(value = "操作对象名称")
    private String opObjectName;

    @ApiModelProperty(value = "操作类型 自定义（add、update、delete、login）")
    private String opType;

    @ApiModelProperty(value = "数影操作类型")
    private String magicOpType;

    @ApiModelProperty(value = "数影操作类型描述")
    private String magicOpTypeDesc;

    @ApiModelProperty("操作前数据 格式自定义")
    private String opBefore;

    @ApiModelProperty("操作后数据 格式自定义")
    private String opAfter;

    @ApiModelProperty("操作说明")
    private String remark;

    @ApiModelProperty(value = "操作人")
    private String opUserId;

    @ApiModelProperty(value = "操作人姓名")
    private String opUserName;

    @ApiModelProperty(value = "操作时间")
    private Date opDt;

    @ApiModelProperty("ip地址")
    private String ip;
}
