package com.dl.magicvideo.web.controllers.internal.auth;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.internal.auth.param.AddTemplateAndAuthParam;
import com.dl.magicvideo.web.controllers.internal.auth.param.AuthParam;
import com.dl.magicvideo.web.controllers.internal.auth.param.ChangeSourceTemplateParam;
import com.dl.magicvideo.web.controllers.internal.auth.param.SwitchStatusParam;
import com.dl.magicvideo.web.controllers.internal.auth.param.TemplateAuthPageQueryParam;
import com.dl.magicvideo.web.controllers.internal.auth.dto.TemplateAuthDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @describe: TemplateAuthController
 * @author: zhousx
 * @date: 2023/6/18 10:47
 */
@RestController
@RequestMapping("/visual/internal/templateauth")
@Api("模板授权模块")
public class TemplateAuthInternalController {
    @Autowired
    private TemplateAuthInternalProcess templateAuthInternalProcess;

    @PostMapping("/list")
    @ApiOperation("系统模板列表")
    public ResultPageModel<TemplateAuthDTO> list(@RequestBody @Validated TemplateAuthPageQueryParam param) {
        return templateAuthInternalProcess.list(param);
    }

    @PostMapping("/addandauth")
    @ApiOperation("创建系统模板并授权")
    public ResultModel<TemplateAuthDTO> addAndAuth(@RequestBody @Validated AddTemplateAndAuthParam param) {
        return templateAuthInternalProcess.addAndAuth(param);
    }

    @PostMapping("/auth")
    @ApiOperation("修改授权")
    public ResultModel<Void> auth(@RequestBody @Validated AuthParam param) {
        return templateAuthInternalProcess.auth(param);
    }

    @PostMapping("/switchstatus")
    @ApiOperation("系统模板启停")
    public ResultModel<Void> switchStatus(@RequestBody @Validated SwitchStatusParam param) {
        return templateAuthInternalProcess.switchStatus(param);
    }

    @PostMapping("/changesourcetemplate")
    @ApiOperation("修改系统模板的来源模板")
    public ResultModel<Void> changeSourceTemplate(@RequestBody @Validated ChangeSourceTemplateParam param) {
        return templateAuthInternalProcess.changeSourceTemplate(param);
    }
}
