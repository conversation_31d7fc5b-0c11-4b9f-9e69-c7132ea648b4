package com.dl.magicvideo.biz.dal.visual.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName visual_produce_job
 */
//@TableName(value ="visual_produce_job")
@Data
public class VisualProduceJoBJoinTemplateDTO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
//    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 任务状态：-1-未开始 0-排队中 1-合成中 2-合成成功 3-合成失败
     */
    private Integer status;

    /**
     * 
     */
    private String name;

    /**
     * 
     */
    private String coverUrl;

    /**
     * 合成视频地址
     */
    private String videoUrl;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * job_type=0:模板id;job_type=1:卡片id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 动态参数
     */
    private String replaceData;

    /**
     * 预览视频结构化数据
     */
    private String previewData;

    /**
     * 模板快照数据
     */
    private String templateData;

    /**
     * 合成开始时间
     */
    private Date processDt;

    /**
     * 合成结束时间
     */
    private Date completeDt;

    /**
     * 来源 0-平台触发 1-接口触发
     */
    private Integer source;

    /**
     * 视频时长
     */
    private Long duration;

    /**
     * 视频大小
     */
    private Long size;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 横版/竖版
     */
    private String resolutionType;

    /**
     * 尺寸
     */
    private String resolution;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     * @see: ShareConfStateEnum
     */
    private Integer shareConfState;

    /**
     * 推荐使用状态 0-未启用 1-已启用
     */
    private Integer recommendState;

    /**
     * 推送状态 0-未推送 1-推送成功 2-推送失败
     */
    private Integer pushStatus;

    /**
     * 其他格式的视频地址
     */
    private String otherFormatVideoUrl;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 作品类型
     */
    private Integer type;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}