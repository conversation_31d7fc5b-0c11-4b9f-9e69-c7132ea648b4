package com.dl.magicvideo.web.controllers.subjectmatter.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 15:40
 */
@Data
public class SubjectMatterQueryParam {

    /**
     * 题材名称
     */
    private String name;

    /**
     * 股票编码
     */
    private String stockCode;

    @Max(value = 20, message = "查询数量不得超过20")
    @Min(value = 1, message = "查询数量需大于1")
    @ApiModelProperty("查询数量，请输入【1-20】")
    private Integer number;

    /**
     * 题材类型，1-个股题材库，2-题材库
     */
    private Integer type = 1;

}
