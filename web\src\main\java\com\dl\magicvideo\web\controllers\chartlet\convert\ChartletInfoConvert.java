package com.dl.magicvideo.web.controllers.chartlet.convert;

import com.dl.magicvideo.biz.dal.chartlet.po.ChartletInfoPO;
import com.dl.magicvideo.web.controllers.chartlet.vo.ChartletInfoVO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-16 09:21
 */
public class ChartletInfoConvert {

    public static ChartletInfoVO cnvChartletInfoPO2VO(ChartletInfoPO input) {
        ChartletInfoVO result = new ChartletInfoVO();
        result.setName(input.getName());
        result.setBizId(input.getBizId());
        result.setContentUrl(input.getContentUrl());
        result.setResolutionRatio(input.getResolutionRatio());
        result.setCoverImg(input.getCoverImg());
        result.setDuration(input.getDuration());
        result.setCategory(input.getCategory());
        return result;
    }

}
