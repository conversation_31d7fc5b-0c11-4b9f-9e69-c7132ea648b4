package com.dl.magicvideo.web.controllers.voice.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @describe: GenericVoiceVO
 * @author: zhousx
 * @date: 2023/5/9 15:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GenericVoiceVO {
    @ApiModelProperty("外部厂商声音唯一标识")
    private String voiceKey;

    @ApiModelProperty("声音名称")
    private String voiceName;

    @ApiModelProperty("性别：1 男 ；2 女")
    private Integer voiceGender;

    @ApiModelProperty("默认：通用")
    private String voiceCategory;

    @ApiModelProperty("渠道 2腾讯云克隆音 4-阿里云 5-腾讯云TTS 6-火山")
    private Integer channel;

    @ApiModelProperty("样例")
    private String sampleLink;
    @ApiModelProperty("音量")
    private String volume;
    @ApiModelProperty("语速")
    private String speed;

    @ApiModelProperty("语音描述")
    private String voiceDesc;

    /**
     * 音频时长(单位：毫秒)
     */
    @ApiModelProperty(value = "音频时长(单位：毫秒)")
    private Long duration;

    @ApiModelProperty(value = "声音类型：1 克隆音；2 合成音")
    private Integer voiceType;

    @ApiModelProperty(value = "头像")
    private String headImg;

    /**
     * 多试听链接
     */
    @ApiModelProperty(value = "多试听链接")
    private List<VirtualVoiceLinkVO> voiceLinks;

}
