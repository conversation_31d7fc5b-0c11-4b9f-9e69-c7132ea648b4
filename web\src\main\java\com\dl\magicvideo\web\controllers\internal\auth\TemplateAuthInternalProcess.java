package com.dl.magicvideo.web.controllers.internal.auth;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.*;
import com.dl.magicvideo.biz.manager.visual.*;
import com.dl.magicvideo.biz.manager.visual.bo.*;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateCopyDTO;
import com.dl.magicvideo.biz.manager.visual.enums.TemplateSyncStatusEnum;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.auth.dto.TemplateAuthDTO;
import com.dl.magicvideo.web.controllers.internal.auth.param.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @describe: TemplateAuthProcess
 * @author: zhousx
 * @date: 2023/6/18 11:22
 */
@Slf4j
@Component
public class TemplateAuthInternalProcess extends AbstractController {
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private VisualTemplateManager visualTemplateManager;
    @Autowired
    private VisualTemplateAuthManager visualTemplateAuthManager;
    @Resource
    private TemplateProductManager templateProductManager;
    @Resource
    private VisualShareConfManager visualShareConfManager;
    @Autowired
    private VisualCardManager visualCardManager;
    @Autowired
    private VisualDynamicNodeManager visualDynamicNodeManager;
    @Autowired
    private VisualAiConfigManager visualAiConfigManager;

    public ResultModel<TemplateAuthDTO> addAndAuth(AddTemplateAndAuthParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getSourceTemplateId()), "模板Id格式错误");
        TemplateCopyDTO sysTemplateId = visualTemplateManager
                .copy(buildTemplateCopyBO(param));
        List<VisualTemplateAuthPO> visualTemplateAuthPOList = new ArrayList<>();
        for (TenantInfoParam tenant : param.getTenants()) {
            VisualTemplateAuthPO visualTemplateAuthPO = new VisualTemplateAuthPO();
            visualTemplateAuthPO.setTemplateId(sysTemplateId.getTemplateId());
            visualTemplateAuthPO.setTenantCode(tenant.getTenantCode());
            visualTemplateAuthPO.setTenantName(tenant.getTenantName());
            String userName = operatorUtil.getUserName();
            visualTemplateAuthPO.setCreatorName(userName);
            visualTemplateAuthPO.setModifyName(userName);
            visualTemplateAuthPO.setSourceTemplateId(Long.valueOf(param.getSourceTemplateId()));
            visualTemplateAuthPO.setSyncStatus(TemplateSyncStatusEnum.SUCCESS.getStatus());
            visualTemplateAuthPOList.add(visualTemplateAuthPO);
        }
        visualTemplateAuthManager.saveBatch(visualTemplateAuthPOList);
        TemplateAuthDTO vo = new TemplateAuthDTO();
        vo.setTemplateId(sysTemplateId + "");
        return ResultModel.success(vo);
    }

    private TemplateCopyBO buildTemplateCopyBO(AddTemplateAndAuthParam param){
        TemplateCopyBO bo = new TemplateCopyBO();
        bo.setTemplateId(Long.valueOf(param.getSourceTemplateId()));
        bo.setName(param.getName());
        bo.setCoverUrl(param.getCoverUrl());
        bo.setPreviewVideoUrl(param.getPreviewVideoUrl());
        bo.setSys(true);
        bo.setTenantCode(Const.DEFAULT_TENANT_CODE);
        bo.setIsManager(param.getIsManager());
        bo.setFirstCategory(param.getFirstCategory());
        bo.setSecondCategory(param.getSecondCategory());
        bo.setCopySource(0);
        bo.setTagIds(param.getTagIds());
        bo.setTagNames(param.getTagNames());
        return bo;
    }

    public ResultModel<Void> auth(AuthParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板Id格式错误");
        TemplateAuthBO bo = new TemplateAuthBO();
        bo.setTemplateId(Long.valueOf(param.getTemplateId()));
        bo.setCoverUrl(param.getCoverUrl());
        bo.setName(param.getName());
        bo.setPreviewVideoUrl(param.getPreviewVideoUrl());
        if(CollectionUtils.isNotEmpty(param.getTenants())) {
            bo.setTenants(param.getTenants().stream().map(tenantInfoParam -> {
                TemplateAuthBO.Tenant tenant = new TemplateAuthBO.Tenant();
                tenant.setTenantCode(tenantInfoParam.getTenantCode());
                tenant.setTenantName(tenantInfoParam.getTenantName());
                return tenant;
            }).collect(Collectors.toList()));
        }
        bo.setIsManager(param.getIsManager());
        bo.setFirstCategory(param.getFirstCategory());
        bo.setSecondCategory(param.getSecondCategory());
        bo.setTagIds(param.getTagIds());
        bo.setTagNames(param.getTagNames());
        visualTemplateAuthManager.auth(bo);
        return ResultModel.success(null);
    }

    public ResultModel<Void> switchStatus(SwitchStatusParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板Id格式错误");
        visualTemplateManager.lambdaUpdate().eq(VisualTemplatePO::getTemplateId, Long.valueOf(param.getTemplateId())).set(VisualTemplatePO::getStatus, param.getStatus()).
                set(VisualTemplatePO::getModifyName,operatorUtil.getUserName()).update();
        return ResultModel.success(null);
    }

    public ResultPageModel<TemplateAuthDTO> list(TemplateAuthPageQueryParam param) {
        ResponsePageQueryDO<List<com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO>> result = null;
        if (StringUtils.isBlank(param.getAuthTenantCode())) {
            TemplateAuthSearchBO bo = new TemplateAuthSearchBO();
            bo.setPageIndex(param.getPageIndex());
            bo.setPageSize(param.getPageSize());
            bo.setName(param.getName());
            bo.setResolutionType(param.getResolutionType());
            bo.setTemplateId(
                    StringUtils.isNotBlank(param.getTemplateId()) ? Long.valueOf(param.getTemplateId()) : null);
            bo.setSourceTemplateId(StringUtils.isNotBlank(param.getSourceTemplateId()) ?
                    Long.valueOf(param.getSourceTemplateId()) : null);
            result = visualTemplateAuthManager.pageQuery(bo);
        } else {
            TemplateAuthTenantCodeSearchBO bo = new TemplateAuthTenantCodeSearchBO();
            bo.setPageIndex(param.getPageIndex());
            bo.setPageSize(param.getPageSize());
            bo.setName(param.getName());
            bo.setResolutionType(param.getResolutionType());
            bo.setAuthTenantCode(param.getAuthTenantCode());
            bo.setTemplateId(
                    StringUtils.isNotBlank(param.getTemplateId()) ? Long.valueOf(param.getTemplateId()) : null);
            result = visualTemplateAuthManager.pageQueryByAuthTenantCode(bo);
        }
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<TemplateAuthDTO> vos = result.getDataResult().stream().map(this::cnvTemplateAuthDTO2VO)
                .collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultModel<Void> changeSourceTemplate(ChangeSourceTemplateParam param) {
        //新的来源模板
        VisualTemplatePO sourceTemplatePO = visualTemplateManager.lambdaQuery()
                .eq(VisualTemplatePO::getTemplateId, Long.valueOf(param.getNewSourceTemplateId()))
                .eq(VisualTemplatePO::getTenantCode, Const.DEFAULT_TENANT_CODE)
                .eq(VisualTemplatePO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(sourceTemplatePO, "新的来源模板不存在");

        //系统模板
        VisualTemplatePO sysTemplatePO = visualTemplateManager.lambdaQuery()
                .eq(VisualTemplatePO::getTemplateId, Long.valueOf(param.getTemplateId())).one();
        Assert.notNull(sysTemplatePO, "系统模板不存在");
        log.info("修改系统模板的来源模板,系统模板:{},,,新的来源模板:{}", sysTemplatePO.getTemplateId(), sourceTemplatePO.getTemplateId());

        //删除现有系统模板的数据
        visualCardManager.remove(Wrappers.lambdaQuery(VisualCardPO.class)
                .eq(VisualCardPO::getTemplateId, sysTemplatePO.getTemplateId()));
        visualDynamicNodeManager.remove(Wrappers.lambdaQuery(VisualDynamicNodePO.class)
                .eq(VisualDynamicNodePO::getTemplateId, sysTemplatePO.getTemplateId()));
        visualAiConfigManager.remove(Wrappers.lambdaQuery(VisualAiConfigPO.class)
                .eq(VisualAiConfigPO::getTemplateId, sysTemplatePO.getTemplateId()));
        templateProductManager.remove(Wrappers.lambdaQuery(TemplateProductPO.class)
                .eq(TemplateProductPO::getTemplateId, sysTemplatePO.getTemplateId()));
        visualShareConfManager.remove(Wrappers.lambdaQuery(VisualShareConfPO.class)
                .eq(VisualShareConfPO::getBizId, sysTemplatePO.getTemplateId())
                .eq(VisualShareConfPO::getBizType, Const.ONE));

        //更新系统模板主表
        updateTemplateParam(sysTemplatePO, sourceTemplatePO);
        visualTemplateManager.updateById(sysTemplatePO);

        //更新模板授权表的来源模板id为新的模板id
        visualTemplateAuthManager.update(Wrappers.lambdaUpdate(VisualTemplateAuthPO.class)
                .eq(VisualTemplateAuthPO::getTemplateId, sysTemplatePO.getTemplateId())
                .set(VisualTemplateAuthPO::getSourceTemplateId, sourceTemplatePO.getTemplateId()));

        //查询来源模板的卡片数据
        List<VisualCardPO> sourceCards = visualCardManager.lambdaQuery()
                .eq(VisualCardPO::getTemplateId, sourceTemplatePO.getTemplateId())
                .eq(VisualCardPO::getIsDeleted, Const.ZERO).list();
        if (CollectionUtils.isEmpty(sourceCards)) {
            return ResultModel.success(null);
        }
        Map<Long, List<VisualDynamicNodePO>> sourceDynamicNodeMap = new HashMap<>();
        Map<Long, List<VisualAiConfigPO>> sourceTtsConfigMap = new HashMap<>();
        //查询来源模板的片段数据
        List<VisualDynamicNodePO> sourceDynamicNodes = visualDynamicNodeManager.lambdaQuery()
                .eq(VisualDynamicNodePO::getTemplateId, sourceTemplatePO.getTemplateId()).list();
        if (CollectionUtils.isNotEmpty(sourceDynamicNodes)) {
            sourceDynamicNodeMap
                    .putAll(sourceDynamicNodes.stream().collect(Collectors.groupingBy(VisualDynamicNodePO::getCardId)));
        }
        //查询来源模板的tts和数字人配置数据
        List<VisualAiConfigPO> sourceTtsConfigs = visualAiConfigManager.lambdaQuery()
                .eq(VisualAiConfigPO::getTemplateId, sourceTemplatePO.getTemplateId()).list();
        if (CollectionUtils.isNotEmpty(sourceTtsConfigs)) {
            sourceTtsConfigMap
                    .putAll(sourceTtsConfigs.stream().collect(Collectors.groupingBy(VisualAiConfigPO::getNodeId)));
        }

        List<VisualCardPO> targetCards = new ArrayList<>();
        List<VisualDynamicNodePO> targetDynamicNodes = new ArrayList<>();
        List<VisualAiConfigPO> tagetTtsConfigs = new ArrayList<>();
        this.fillNewSysTemplateCards(sysTemplatePO, sourceCards, sourceDynamicNodeMap, sourceTtsConfigMap, targetCards,
                targetDynamicNodes, tagetTtsConfigs);
        if (CollectionUtils.isNotEmpty(targetCards)) {
            visualCardManager.saveBatch(targetCards);
        }
        if (CollectionUtils.isNotEmpty(targetDynamicNodes)) {
            visualDynamicNodeManager.saveBatch(targetDynamicNodes);
        }
        if (CollectionUtils.isNotEmpty(tagetTtsConfigs)) {
            visualAiConfigManager.saveBatch(tagetTtsConfigs);
        }

        //处理关联产品
        this.handleTemplateData(sourceTemplatePO, sysTemplatePO);

        //处理转发设置
        this.handleShareConf(sourceTemplatePO, sysTemplatePO);

        return ResultModel.success(null);
    }

    private void handleTemplateData(VisualTemplatePO sourceTemplatePO, VisualTemplatePO sysTemplatePO) {
        //判断是否关联产品
        List<TemplateProductPO> list = templateProductManager.lambdaQuery()
                .eq(TemplateProductPO::getTemplateId, sourceTemplatePO.getTemplateId()).list();
        if (CollectionUtils.isNotEmpty(list)) {
            List<TemplateProductPO> copyList = new ArrayList<>();
            for (TemplateProductPO templateProductPO : list) {
                TemplateProductPO copyTemplateProductPO = new TemplateProductPO();
                Long id = hostTimeIdg.generateId().longValue();
                copyTemplateProductPO.setId(id);
                copyTemplateProductPO.setTemplateId(sysTemplatePO.getTemplateId());
                copyTemplateProductPO.setProdCode(templateProductPO.getProdCode());
                copyList.add(copyTemplateProductPO);
            }
            templateProductManager.saveBatch(copyList);
        }
    }

    private void handleShareConf(VisualTemplatePO sourceTemplatePO, VisualTemplatePO sysTemplatePO) {
        if (Objects.nonNull(sourceTemplatePO.getShareConfState()) && !Objects
                .equals(sourceTemplatePO.getShareConfState(), Const.ZERO)) {
            //拷贝 基础转发配置 和 交互式配置
            ShareConfCopyBO shareConfCopyBO = new ShareConfCopyBO();
            shareConfCopyBO.setSourceBizId(sourceTemplatePO.getTemplateId());
            shareConfCopyBO.setSourceBizType(Const.ONE);
            shareConfCopyBO.setTargetBizId(sysTemplatePO.getTemplateId());
            shareConfCopyBO.setTargetBizType(Const.ONE);
            shareConfCopyBO.setSourceTenantCode(sourceTemplatePO.getTenantCode());
            shareConfCopyBO.setTargetTenantCode(sysTemplatePO.getTenantCode());
            visualShareConfManager.copyShareConf(shareConfCopyBO);
        }
    }

    private TemplateAuthDTO cnvTemplateAuthDTO2VO(com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        TemplateAuthDTO vo = new TemplateAuthDTO();
        vo.setTemplateId(dto.getTemplateId() + "");
        vo.setStatus(dto.getStatus());
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setName(dto.getName());
        vo.setResolution(dto.getResolution());
        vo.setResolutionType(dto.getResolutionType());
        vo.setBgMusic(dto.getBgMusic());
        vo.setBgMusicParam(dto.getBgMusicParam());
        vo.setTtsParam(dto.getTtsParam());
        vo.setReplaceData(dto.getReplaceData());
        vo.setCreatorName(dto.getCreatorName());
        vo.setCreateDt(dto.getCreateDt());
        vo.setDuration(dto.getDuration());
        vo.setPreviewVideoUrl(dto.getPreviewVideoUrl());
        vo.setShortVideoUrl(dto.getShortVideoUrl());
        vo.setModifyName(dto.getModifyName());
        vo.setModifyDt(dto.getModifyDt());
        vo.setSourceTemplateId(
                Objects.nonNull(dto.getSourceTemplateId()) ? String.valueOf(dto.getSourceTemplateId()) : "");
        if (CollectionUtils.isNotEmpty(dto.getAuthTenantList())) {
            vo.setAuthTenantList(dto.getAuthTenantList().stream().map(t -> {
                TemplateAuthDTO.Tenant tenant = new TemplateAuthDTO.Tenant();
                tenant.setTenantCode(t.getTenantCode());
                tenant.setTenantName(t.getTenantName());
                return tenant;
            }).collect(Collectors.toList()));
        }
        return vo;
    }

    private void updateTemplateParam(VisualTemplatePO sysTemplatePO, VisualTemplatePO sourceTemplatePO) {
        sysTemplatePO.setBgMusic(sourceTemplatePO.getBgMusic());
        sysTemplatePO.setApiData(sourceTemplatePO.getApiData());
        sysTemplatePO.setBgMusicParam(sourceTemplatePO.getBgMusicParam());
        sysTemplatePO.setReplaceData(sourceTemplatePO.getReplaceData());
        sysTemplatePO.setResolution(sourceTemplatePO.getResolution());
        sysTemplatePO.setResolutionType(sourceTemplatePO.getResolutionType());
        sysTemplatePO.setShareConfState(sourceTemplatePO.getShareConfState());
        sysTemplatePO.setTtsParam(sourceTemplatePO.getTtsParam());
        sysTemplatePO.setDuration(sourceTemplatePO.getDuration());
        sysTemplatePO.setModifyName(operatorUtil.getUserName());
    }

    private void fillNewSysTemplateCards(VisualTemplatePO sysTemplatePO, List<VisualCardPO> sourceCards,
            Map<Long, List<VisualDynamicNodePO>> sourceDynamicNodeMap,
            Map<Long, List<VisualAiConfigPO>> sourceTtsConfigMap, List<VisualCardPO> targetCards,
            List<VisualDynamicNodePO> targetDynamicNodes, List<VisualAiConfigPO> tagetTtsConfigs) {
        for (VisualCardPO sourceCard : sourceCards) {
            VisualCardPO targetCard = new VisualCardPO();
            Long newCardId = hostTimeIdg.generateId().longValue();
            BeanUtils.copyProperties(sourceCard, targetCard, "id");
            targetCard.setCardId(newCardId);
            targetCard.setTemplateId(sysTemplatePO.getTemplateId());
            targetCards.add(targetCard);
            List<VisualDynamicNodePO> sourceDynamicNodesByCardId = sourceDynamicNodeMap.get(sourceCard.getCardId());
            if (CollectionUtils.isEmpty(sourceDynamicNodesByCardId)) {
                continue;
            }
            for (VisualDynamicNodePO sourceDynamicNode : sourceDynamicNodesByCardId) {
                VisualDynamicNodePO targetDynamicNode = new VisualDynamicNodePO();
                Long newNodeId = hostTimeIdg.generateId().longValue();
                BeanUtils.copyProperties(sourceDynamicNode, targetDynamicNode, "id");
                targetDynamicNode.setNodeId(newNodeId);
                targetDynamicNode.setCardId(newCardId);
                targetDynamicNode.setTemplateId(sysTemplatePO.getTemplateId());
                targetDynamicNodes.add(targetDynamicNode);
                List<VisualAiConfigPO> sourceTtsConfigsByNodeId = sourceTtsConfigMap.get(sourceDynamicNode.getNodeId());
                if (CollectionUtils.isEmpty(sourceTtsConfigsByNodeId)) {
                    continue;
                }
                for (VisualAiConfigPO sourceTtsConfig : sourceTtsConfigsByNodeId) {
                    VisualAiConfigPO targetTtsConfig = new VisualAiConfigPO();
                    BeanUtils.copyProperties(sourceTtsConfig, targetTtsConfig, "id");
                    targetTtsConfig.setNodeId(newNodeId);
                    targetTtsConfig.setCardId(newCardId);
                    targetTtsConfig.setTemplateId(sysTemplatePO.getTemplateId());
                    tagetTtsConfigs.add(targetTtsConfig);
                }
            }
        }
    }
}
