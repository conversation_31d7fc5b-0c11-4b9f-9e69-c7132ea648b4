package com.dl.magicvideo.web.controllers.voice;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationRequestDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.config.TtsThreadPoolConfig;
import com.dl.magicvideo.biz.dal.account.trial.emuns.TenantDosageEnum;
import com.dl.magicvideo.biz.dal.account.trial.po.TenantDosageConfigPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.manager.account.trial.TenantDosageConfigManager;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.TtsJobBO;
import com.dl.magicvideo.biz.manager.visual.consts.VoiceConst;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.voice.config.SubtitleMarkReplaceWordsConfig;
import com.dl.magicvideo.web.controllers.voice.helper.VisualVoiceHelper;
import com.dl.magicvideo.web.controllers.voice.param.QueryVmVoiceParam;
import com.dl.magicvideo.web.controllers.voice.param.TtsParam;
import com.dl.magicvideo.web.controllers.voice.param.TtsSubtitleMarkParam;
import com.dl.magicvideo.web.controllers.voice.param.TtsSubtitleParam;
import com.dl.magicvideo.web.controllers.voice.vo.DmVoiceVO;
import com.dl.magicvideo.web.controllers.voice.vo.GenericVoiceVO;
import com.dl.magicvideo.web.controllers.voice.vo.TtsSubtitleMarkResultVO;
import com.dl.magicvideo.web.controllers.voice.vo.TtsSubtitleMarkWordsVO;
import com.dl.magicvideo.web.controllers.voice.vo.TtsSubtitleVO;
import com.dl.magicvideo.web.controllers.voice.vo.TtsVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.CallableWrapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @describe: VisualVoiceProcess
 * @author: zhousx
 * @date: 2023/5/6 13:38
 */
@Slf4j
@Component
public class VisualVoiceProcess extends AbstractController {
    @Resource
    private AiServiceClient aiServiceClient;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private VisualAiJobManager visualAiJobManager;
    @Resource
    private TtsThreadPoolConfig ttsThreadPoolConfig;
    @Resource
    private SubtitleMarkReplaceWordsConfig subtitleMarkReplaceWordsConfig;
    @Resource
    private TenantDosageConfigManager tenantDosageConfigManager;

    public ResultModel<TtsVO> produceTts(TtsParam param) {

        TenantDosageConfigPO configPO = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, operatorUtil.getTenantCode()).eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.TTS.getType()).one();
        if (Objects.nonNull(configPO) && !Objects.equals(configPO.getBalance(), Const.MINUS_ONE_LONG) && configPO.getBalance() <= configPO.getUsed()) {
            throw BusinessServiceException.getInstance("TTS合成数用量不足，请联系商务处理");
        }

        TtsJobBO ttsJobBO = new TtsJobBO();
        ttsJobBO.setVoiceName(param.getVoiceKey());
        ttsJobBO.setText(param.getText());
        ttsJobBO.setTemplateId(Long.valueOf(param.getTemplateId()));
        ttsJobBO.setSpeed(param.getSpeed());
        ttsJobBO.setVolume(param.getVolume());
        ttsJobBO.setMaxLength(param.getMaxLength());
        ttsJobBO.setCustomStoreUrl(Const.ONE);
        ttsJobBO.setUserId(operatorUtil.getOperator());
        ttsJobBO.setChannel(param.getChannel());
        ttsJobBO.setAiJobType(AiJobTypeE.TTS.getCode());
        ttsJobBO.setEnableSubtitle(Const.ONE);
        ttsJobBO.setSubtitleKeyWordsHighlight(param.getSubtitleKeyWordsHighlight());
        this.specialReplaceVoice(ttsJobBO);

        //获取tts的线程池
        ExecutorService ttsThreadPool = ttsThreadPoolConfig.getTtsThreadPool(param.getChannel());
        //异步提交任务
        String tenantCode = operatorUtil.getTenantCode();
        Future<VisualAiJobPO> future = ttsThreadPool.submit(CallableWrapper.of(() -> {
            operatorUtil.init(ttsJobBO.getUserId(), "", tenantCode, "");
            return visualAiJobManager.submitTtsJob(ttsJobBO);
        }));
        //阻塞等待
        VisualAiJobPO ttsJob = null;
        try {
            ttsJob = future.get();
        } catch (ExecutionException e) {
            Throwable throwable = e.getCause();
            log.error("tts语音合成失败!ttsJobBO:{},,,e:", JSONUtil.toJsonStr(ttsJobBO), e);
            if (throwable instanceof BusinessServiceException) {
                throw (BusinessServiceException) throwable;
            }
            throw BusinessServiceException.getInstance("语音合成失败");
        } catch (Exception e) {
            log.error("tts语音合成失败!ttsJobBO:{},,,e:", JSONUtil.toJsonStr(ttsJobBO), e);
            throw BusinessServiceException.getInstance("语音合成失败");
        }
        ResultModel<TTSResponseDTO> ttsResult = JSONObject
                .parseObject(ttsJob.getResponseInfo(), new TypeReference<ResultModel<TTSResponseDTO>>() {
                });
        TTSResponseDTO ttsResponseDTO = ttsResult.getDataResult();

        TtsVO ttsVO = new TtsVO();
        ttsVO.setDuration(ttsResponseDTO.getDuration());
        ttsVO.setAudioUrl(ttsJob.getMediaInfo());
        if (CollectionUtils.isNotEmpty(ttsResponseDTO.getSubtitles())) {
            ttsVO.setSubtitles(ttsResponseDTO.getSubtitles().stream().map(ttsSubtitleDTO -> {
                TtsSubtitleVO ttsSubtitleVO = new TtsSubtitleVO();
                ttsSubtitleVO.setBeginTime(ttsSubtitleDTO.getBeginTime());
                ttsSubtitleVO.setEndTime(ttsSubtitleDTO.getEndTime());
                ttsSubtitleVO.setText(ttsSubtitleDTO.getText());
                return ttsSubtitleVO;
            }).collect(Collectors.toList()));
        }
        return ResultModel.success(ttsVO);
    }

    public ResultModel<List<GenericVoiceVO>> genericVoiceList() {
        List<GenericVoiceVO> resultList = new ArrayList<>();
        //排序规则：克隆音在前，合成音在后
        //克隆音
        DaVirtualVoiceRequestDTO cloneVoiceparam = new DaVirtualVoiceRequestDTO();
        cloneVoiceparam.setVoiceType(1);
        cloneVoiceparam.setChannels(Lists.newArrayList(2, 6));
        ResultModel<List<DaVirtualVoiceDTO>> cloneVoiceResultModel = aiServiceClient
                .getGenericVoiceList(operatorUtil.getTenantCode(), cloneVoiceparam);
        if (Objects.nonNull(cloneVoiceResultModel) && CollectionUtils
                .isNotEmpty(cloneVoiceResultModel.getDataResult())) {
            List<GenericVoiceVO> cloneVoiceCollect = cloneVoiceResultModel.getDataResult().stream()
                    .map(VisualVoiceHelper::cnvVoiceDTO2VO).collect(Collectors.toList());
            resultList.addAll(cloneVoiceCollect);
        }

        //合成音
        DaVirtualVoiceRequestDTO param = new DaVirtualVoiceRequestDTO();
        param.setVoiceType(2);
        ResultModel<List<DaVirtualVoiceDTO>> resultModel = aiServiceClient
                .getGenericVoiceList(operatorUtil.getTenantCode(), param);
        if (Objects.nonNull(resultModel) && CollectionUtils.isNotEmpty(resultModel.getDataResult())) {
            List<GenericVoiceVO> collect = resultModel.getDataResult().stream().map(VisualVoiceHelper::cnvVoiceDTO2VO)
                    .collect(Collectors.toList());
            resultList.addAll(collect);
        }
        return ResultModel.success(resultList);
    }

    public ResultModel<List<DmVoiceVO>> digitalManVoiceList(QueryVmVoiceParam param) {
        //1.先根据数字人bizId查询绑定的声音
        DigitalManAggregationRequestDTO requestDTO = new DigitalManAggregationRequestDTO();
        requestDTO.setVmBizId(param.getVmBizId());
        ResultModel<DigitalManAggregationInfoDTO> resultModel = aiServiceClient
                .aggregationinfo(operatorUtil.getTenantCode(), requestDTO);
        if (!resultModel.isSuccess()) {
            throw BusinessServiceException.getInstance("查询数字人的声音信息失败");
        }
        DigitalManAggregationInfoDTO dmAggregationInfoDTO = resultModel.getDataResult();
        DaVirtualVoiceBaseInfoDTO voiceBaseInfoDTO = dmAggregationInfoDTO.getVoiceInfo();

        //数字人绑定的声音
        DmVoiceVO boundedVoiceVO = VisualVoiceHelper.cnvVoiceBaseInfoVO2DmVoiceVO(voiceBaseInfoDTO);
        //2.1如果数字人绑定的声音key和入参中的key相等，则直接返回对象返回
        if (voiceBaseInfoDTO.getVoiceKey().equals(param.getDefaultVoiceKey())) {
            return ResultModel.success(Lists.newArrayList(boundedVoiceVO));
        }
        //2.2数字人绑定的声音key和入参中的key不等，则用入参中的voiceKey查询声音信息
        DaVirtualVoiceRequestDTO voiceRequestDTO = new DaVirtualVoiceRequestDTO();
        voiceRequestDTO.setVoiceKey(param.getDefaultVoiceKey());
        //兼容前端renderData中部分历史模板没有声音厂商
        if (Objects.nonNull(param.getVoiceChannel())) {
            voiceRequestDTO.setChannels(Lists.newArrayList(param.getVoiceChannel()));
        }
        ResultModel<List<DaVirtualVoiceDTO>> voiceResult = aiServiceClient.voiceListQuery(voiceRequestDTO);
        if (CollectionUtils.isEmpty(voiceResult.getDataResult())) {
            return ResultModel.success(Lists.newArrayList(boundedVoiceVO));
        }
        List<DmVoiceVO> voiceVOList = new ArrayList<>();
        voiceVOList.add(boundedVoiceVO);
        voiceVOList.addAll(voiceResult.getDataResult().stream().map(VisualVoiceHelper::cnvDaVirtualVoiceDTO2DmVoiceVO)
                .collect(Collectors.toList()));
        return ResultModel.success(voiceVOList);
    }

    public ResultModel<TtsSubtitleMarkResultVO> subtitleMark(TtsSubtitleMarkParam param) {
        TtsSubtitleMarkResultVO resultVO = new TtsSubtitleMarkResultVO();
        List<TtsSubtitleMarkWordsVO> markWordList = new ArrayList<>();
        resultVO.setMarkWordList(markWordList);

        //去除空串，去重
        List<String> needToMarkWordsList = param.getNeedToMarkWords().stream().filter(StringUtils::isNotBlank).distinct()
                .collect(Collectors.toList());

        for (TtsSubtitleParam subtitleParam : param.getSubtitles()) {
            //替换词
            String subtitle = replaceWords(subtitleParam.getText(),
                    subtitleMarkReplaceWordsConfig.getReplaceWordsMap());
            log.info("替换词后的字幕:{}", subtitle);

            needToMarkWordsList.forEach(needToMarkWords -> {
                if (subtitle.contains(needToMarkWords)) {
                    TtsSubtitleMarkWordsVO markWordsVO = new TtsSubtitleMarkWordsVO();
                    markWordsVO.setMarkWords(needToMarkWords);
                    markWordsVO.setBeginTime(subtitleParam.getBeginTime());
                    markWordsVO.setEndTime(subtitleParam.getEndTime());
                    markWordList.add(markWordsVO);
                }
            });
        }

        return ResultModel.success(resultVO);
    }

    /**
     * 特殊替换声音编码
     *
     * @param ttsJobBO
     */
    private void specialReplaceVoice(TtsJobBO ttsJobBO) {
        //如果声音编码是腾讯云的张忆南，则替换声音编码和厂商。
        //腾讯云的张忆南声音一年2500，而用火山云克隆音一次只要138+12=150块，因此腾讯云张忆南声音到期后就不再续费，转而使用火山云克隆音。
        //这是因为用到张忆南的模板实在太多了，不仅我们内部，客户也在用，无法让他们一一修改模板，那么只能替换处理。
        if (ServiceChannelEnum.IVH.getCode().equals(ttsJobBO.getChannel()) && VoiceConst.IVH_ZHANGYINAN_VOICE_KEY
                .equals(ttsJobBO.getVoiceName())) {
            ttsJobBO.setChannel(ServiceChannelEnum.VOLC_ENGINE.getCode());
            ttsJobBO.setVoiceName(VoiceConst.VOLC_ENGINE_ZHANGYINAN_VOICE_KEY);
            return;
        }
    }

    private static String replaceWords(String originalText, Map<String, String> replaceWordsMap) {
        Iterator<Map.Entry<String, String>> iterator = replaceWordsMap.entrySet().iterator();
        String result = originalText;
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String sensitiveWords = entry.getKey();
            String replaceWords = entry.getValue();

            result = result.replaceAll(sensitiveWords, replaceWords);
        }

        return result;
    }

    public static void main(String[] args) {
        /*int inputMin = 0;
        int inputMax = 200;
        double outputMin = 0.5;
        double outputMax = 1.5;
        int inputValue = 50;

        double outputValue = NumberUtils.mapRange(inputValue, inputMin, inputMax, outputMin, outputMax);
        System.out.println("Mapped value: " + outputValue);*/

        //String json = "{\"subtitles\":[{\"text\":\"七月公告业绩预增公司中\",\"beginTime\":0,\"endTime\":2245},{\"text\":\"微电生理 韦尔股份等净利润\",\"beginTime\":2245,\"endTime\":4872},{\"text\":\"业绩均超预期7倍以上\",\"beginTime\":4872,\"endTime\":7277},{\"text\":\"澜起科技 中恒电气等公司中报\",\"beginTime\":7277,\"endTime\":10045},{\"text\":\"业绩预增超5倍\",\"beginTime\":10045,\"endTime\":11786},{\"text\":\"另外\",\"beginTime\":11786,\"endTime\":12440},{\"text\":\"格力博 金固股份等公司中报\",\"beginTime\":12440,\"endTime\":15059},{\"text\":\"业绩预期也同样表现亮眼\",\"beginTime\":15059,\"endTime\":17502}],\"needToMarkWords\":[\"微电生理\",\"韦尔gu份\",\"中捷资源\",\"澜起科技\",\"中恒电气\",\"鲁西化工\",\"ge力博\",\"金固股份\"]}";
        String json = "{\"subtitles\":[{\"text\":\"七月公告业绩预增公司中\",\"beginTime\":0,\"endTime\":2245},{\"text\":\"微电生理 韦尔gu份等净利润\",\"beginTime\":2245,\"endTime\":4872},{\"text\":\"业绩均超预期7倍以上\",\"beginTime\":4872,\"endTime\":7277},{\"text\":\"澜起科技 中恒电气等公司中报\",\"beginTime\":7277,\"endTime\":10045},{\"text\":\"业绩预增超5倍\",\"beginTime\":10045,\"endTime\":11786},{\"text\":\"另外\",\"beginTime\":11786,\"endTime\":12440},{\"text\":\"ge力博 金固股份等公司中报\",\"beginTime\":12440,\"endTime\":15059},{\"text\":\"业绩预期也同样表现亮眼\",\"beginTime\":15059,\"endTime\":17502}],\"needToMarkWords\":[\"微电生理\",\"韦尔gu份\",\"中捷资源\",\"澜起科技\",\"中恒电气\",\"鲁西化工\",\"ge力博\",\"金固股份\"]}";

        //String json = "{\"subtitles\":[{\"text\":\"七月公告业绩预增公司中\",\"beginTime\":0,\"endTime\":2245},{\"text\":\"彤程HM 韦尔gu份等净利润\",\"beginTime\":2245,\"endTime\":4872},{\"text\":\"业绩均超预期7倍以上\",\"beginTime\":4872,\"endTime\":7277},{\"text\":\"澜起科技 中恒电气等公司中报\",\"beginTime\":7277,\"endTime\":10045},{\"text\":\"业绩预增超5倍\",\"beginTime\":10045,\"endTime\":11786},{\"text\":\"另外\",\"beginTime\":11786,\"endTime\":12440},{\"text\":\"ge力博 金固股份等公司中报\",\"beginTime\":12440,\"endTime\":15059},{\"text\":\"业绩预期也同样表现亮眼\",\"beginTime\":15059,\"endTime\":17502}],\"needToMarkWords\":[\"微电生理\",\"韦尔gu份\",\"中捷资源\",\"澜起科技\",\"中恒电气\",\"鲁西化工\",\"ge力博\",\"金固股份\",\"彤程HM\"]}";
        //String json = "{\"needToMarkWords\":[\"彤程HM\",\"晶瑞jun工\",\"Z国光电\",\"Na米感光\",\"ge力股份\",\"威li科技\",\"止xue新阳\",\"新能源装备特气\",\"dong航气体\",\"奥莱气体\",\"芯源微\",\"冠石科技\",\"华润微\",\"路维光电\",\"大族激光\",\"杰普特\",\"华工科技\",\"波长光电\",\"福晶科技\",\"茂莱光学\",\"赛微电子\",\"华卓精科\",\"蓝英装备\",\"美埃科技\",\"金力泰\",\"苏大维格\",\"炬光科技\",\"腾景科技\",\"京华激光\",\"富创精密\",\"新莱应材\",\"联合精密\",\"同飞股份\",\"海立股份\",\"上海电气\",\"奥普光电\",\"东方嘉盛\",\"中芯国际\"],\"subtitles\":[{\"text\":\"彤程HM从事新材料的研发\",\"beginTime\":0,\"endTime\":2460},{\"text\":\"生产光刻胶专利\",\"beginTime\":2460,\"endTime\":3945},{\"text\":\"保证光刻胶回刻的平坦化程度\",\"beginTime\":3945,\"endTime\":6228}]}";

        //String json = "{\"needToMarkWords\":[\"彤程HM\",\"晶瑞jun工\",\"Z国光电\",\"Na米感光\",\"ge力gu份\",\"威li科技\",\"止xue新阳\",\"新能源装备特气\",\"dong航气体\",\"奥莱气体\",\"芯源gu份\",\"冠石科技\",\"华润微\",\"路维光电\",\"大族激光\",\"杰普特\",\"华工科技\",\"波长光电\",\"福晶科技\",\"茂莱光学\",\"赛微电子\",\"华卓精科\",\"蓝英装备\",\"美埃科技\",\"金力泰\",\"苏大维格\",\"炬光科技\",\"腾景科技\",\"京华激光\",\"富创精密\",\"新莱应材\",\"联合精密\",\"同飞股份\",\"海立股份\",\"上海电气\",\"奥普光电\",\"东方嘉盛\",\"中芯国际\"],\"subtitles\":[{\"text\":\"Z国光电\",\"beginTime\":0,\"endTime\":950},{\"text\":\"产品涵盖应用于中高端市场的芯片\",\"beginTime\":1090,\"endTime\":3860},{\"text\":\"及半导体行业i/g/h线\",\"beginTime\":3940,\"endTime\":5820},{\"text\":\"光刻胶等\",\"beginTime\":5820,\"endTime\":6588}]}";

        TtsSubtitleMarkParam param = JSONUtil.toBean(json, TtsSubtitleMarkParam.class);
        /*VisualVoiceProcess visualVoiceProcess = new VisualVoiceProcess();
        ResultModel<TtsSubtitleMarkResultVO> resultModel = visualVoiceProcess.subtitleMark(param);
        System.out.println("resultModel:   " + JSONUtil.toJsonStr(resultModel));*/

        TtsSubtitleMarkResultVO resultVO = new TtsSubtitleMarkResultVO();
        List<TtsSubtitleMarkWordsVO> markWordList = new ArrayList<>();
        resultVO.setMarkWordList(markWordList);

        HashSet<String> needToMarkWordsSet = new HashSet<>();
        param.getNeedToMarkWords().forEach(words -> {
            //添加到set中
            needToMarkWordsSet.add(words);
        });

        for (TtsSubtitleParam subtitleParam : param.getSubtitles()) {
            //替换词
            String subtitle = subtitleParam.getText();
            log.info("替换词后的字幕:{}", subtitle);

            needToMarkWordsSet.forEach(needToMarkWords -> {
                if (subtitle.contains(needToMarkWords)) {
                    TtsSubtitleMarkWordsVO markWordsVO = new TtsSubtitleMarkWordsVO();
                    markWordsVO.setMarkWords(needToMarkWords);
                    markWordsVO.setBeginTime(subtitleParam.getBeginTime());
                    markWordsVO.setEndTime(subtitleParam.getEndTime());
                    markWordList.add(markWordsVO);
                }
            });
        }

        log.info("markWordList:{}", JSONUtil.toJsonStr(markWordList));
    }
}
