package com.dl.magicvideo.biz.manager.shorturl.util;

import com.dl.framework.common.idg.HostTimeIdg;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.util.Random;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-17 13:29
 */
@Component
public class ShortUrlGenerator {

    @Resource
    private HostTimeIdg hostTimeIdg;

    private static final String KEY = "dinglitec";

    /**
     * 要使用生成 URL 的字符
     */
    private static final String[] CHARS = new String[] {
            "f", "g", "i", "l", "p", "q", "r", "s", "t", "u", "F", "v", "w", "x", "y", "z", "1", "2", "6", "7", "8",
            "A", "G", "H", "I", "J", "K", "m", "h", "n", "o", "L", "N", "O", "P", "Q", "U", "V", "W", "X", "Y", "Z",
            "a", "b", "c", "d", "e", "B", "C", "D", "E", "0", "R", "S", "T", "9", "5", "j", "k", "3", "4", "M"
    };

    public String createShortUrl(String url) {
        String[] aResult = shortUrl(url + hostTimeIdg.generateId());
        // 打印出结果S
        for (int i = 0; i < aResult.length; i++) {
            System.out.println("[" + i + "]:::" + aResult[i]);
        }
        Random random = new Random();
        int j = random.nextInt(4);//产成4以内随机数
        System.out.println("短链接:" + aResult[j]);//随机取一个作为短链
        return aResult[j];
    }

    private static String[] shortUrl(String url) {
        // 对传入网址进行 MD5 加密
        String sMD5EncryptResult = md5(KEY + url);

        String hex = sMD5EncryptResult;

        String[] resUrl = new String[4];
        for (int i = 0; i < 4; i++) {

            // 把加密字符按照 8 位一组 16 进制与 0x3FFFFFFF 进行位与运算
            String sTempSubString = hex.substring(i * 8, i * 8 + 8);

            // 这里需要使用 long 型来转换，因为 Inteper .parseInt() 只能处理 31 位 , 首位为符号位 , 如果不用 long ，则会越界
            long lHexLong = 0x3FFFFFFF & Long.parseLong(sTempSubString, 16);
            StringBuilder outChars = new StringBuilder();
            for (int j = 0; j < 6; j++) {
                // 把得到的值与 0x0000003D 进行位与运算，取得字符数组 chars 索引
                long index = 0x0000003D & lHexLong;
                // 把取得的字符相加
                outChars.append(CHARS[(int) index]);
                // 每次循环按位右移 5 位
                lHexLong = lHexLong >> 5;
            }
            // 把字符串存入对应索引的输出数组
            resUrl[i] = outChars.toString();
        }
        return resUrl;
    }

    // 十六进制下数字到字符的映射数组
    private final static String[] hexDigits = {
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F"
    };

    /**
     * 把inputString加密
     */
    public static String md5(String inputStr) {
        return encodeByMD5(inputStr);
    }

    /**
     * 对字符串进行MD5编码
     */
    private static String encodeByMD5(String originString) {
        if (originString != null) {
            try {
                // 创建具有指定算法名称的信息摘要
                MessageDigest md5 = MessageDigest.getInstance("MD5");
                // 使用指定的字节数组对摘要进行最后更新，然后完成摘要计算
                byte[] results = md5.digest(originString.getBytes());
                // 将得到的字节数组变成字符串返回
                String result = byteArrayToHexString(results);
                return result;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 轮换字节数组为十六进制字符串
     *
     * @param b 字节数组
     * @return 十六进制字符串
     */
    private static String byteArrayToHexString(byte[] b) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }

    /**
     * 将一个字节转化成十六进制形式的字符串
     */
    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    public static boolean isLetterDigit(String str) {
        String regex = "^[a-z0-9A-Z]+$";
        return str.matches(regex);
    }

    /**
     * @param args
     */
    public static void main(String[] args) {
        String sLongUrl = "https://test.dinglitec.com/qr?qrUrl=https%3A%2F%2Fwework.qpic.cn%2Fwwpic%2F287906_B3bS5zqxQP6PAth_1655378656%2F0&name=%E5%BC%80%E5%8F%91%E8%87%AA%E6%B5%8B%E6%B4%BB%E7%A0%815";
        String[] aResult = shortUrl(sLongUrl);
        // 打印出结果
        for (int i = 0; i < aResult.length; i++) {
            System.out.println("[" + i + "]:::" + aResult[i] + "    isLetterDigit：" + isLetterDigit(aResult[i]));
        }
        Random random = new Random();
        //产成4以内随机数
        int j = random.nextInt(4);
        //随机取一个作为短链
        System.out.println("短链接:" + aResult[j]);
    }

}

