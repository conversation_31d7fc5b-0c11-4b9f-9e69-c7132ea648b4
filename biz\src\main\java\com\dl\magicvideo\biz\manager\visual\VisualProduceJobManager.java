package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.dal.visual.po.StatisticsJobCountPO;
import com.dl.magicvideo.biz.dal.visual.po.StatisticsJobEfficiencyPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.visual.bo.*;
import com.dl.magicvideo.biz.manager.visual.dto.InterfaceDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【visual_produce_job】的数据库操作Service
 * @createDate 2023-04-26 14:05:39
 */
public interface VisualProduceJobManager extends IService<VisualProduceJobPO> {
    /**
     * 功能描述: <br>
     *
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List < com.dl.magicvideo.biz.manager.job.dto.VideoProduceJobDTO>>
     * @Author: zhousx
     * @Date: 2023/4/26 14:10
     */
    ResponsePageQueryDO<List<VisualProduceJobDTO>> pageQuery(ProduceJobSearchBO bo);

    ResponsePageQueryDO<List<VisualProduceJobDTO>> pageQuery(String tenantCode);

    /**
     * 合成视频
     */
    Long produce(ProduceJobBO produceJobBO);

    /**
     * 功能描述: <br>合成视频
     * <p>
     *
     * @Param: [bo]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/2/11 13:18
     */
    Long doProduce(ProduceJobContextBO bo);

    /**
     * 功能描述: T+1数据统计
     *
     * @Param: [bo]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/2/11 13:18
     */
    List<StatisticsJobCountPO> statistics(StatisticsJobCountBO statisticsJobCountBO);

    /**
     * 获取某一时间内总消耗时间和总时长（秒）
     */
    StatisticsJobEfficiencyPO efficiency(StatisticsJobEfficiencyBO statisticsJobEfficiencyBO);

    String suppleApiData(List<InterfaceDTO> interfaceList);

}
