package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @describe: ProduceParam
 * @author: zhousx
 * @date: 2023/6/25 10:07
 */
@Data
public class BatchProduceParam {
    @ApiModelProperty(value = "模板id", required = true)
    @NotBlank
    private String templateId;

    @ApiModelProperty("产品code列表")
    private List<String> prodCodeList;

    @ApiModelProperty("标题")
    @NotBlank
    private String title;
}
