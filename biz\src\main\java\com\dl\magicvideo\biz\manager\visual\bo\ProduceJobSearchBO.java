package com.dl.magicvideo.biz.manager.visual.bo;

import com.dl.magicvideo.biz.common.SearchBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: JobSearchBO
 * @author: zhousx
 * @date: 2023/2/13 10:34
 */
@Data
public class ProduceJobSearchBO extends SearchBO {
    private String tenantCode;

    private Long batchId;

    private String name;

    private List<Integer> statusList;

    @ApiModelProperty("排序类型 0-默认排序，时间倒序 1.生产时间正序 2.生成时间倒序")
    private Integer sortType;

    private Boolean encludeDeleted = false;

    /**
     * 开始时间，时间戳(毫秒)
     */
    private Long startTime;

    /**
     * 结束时间，时间戳（毫秒）
     */
    private Long endTime;

    /**
     * 横版/竖版
     */
    private String resolutionType;

    /**
     * 推荐使用状态 0-未启用 1-已启用
     */
    private Integer recommendState;

    /**
     * 作品类型，1-常规作品，2-数据图表作品
     * @see: com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum
     */
    private Integer type;

    /**
     * 创建人userId
     */
    private Long creatorId;

}
