package com.dl.magicvideo.web.controllers.auth.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 系统模板组列表参数
 */
@Data
public class TemplateGroupPageQueryParam extends AbstractPageParam {
    @ApiModelProperty("模板组名称")
    @NotBlank
    private String name;

    @ApiModelProperty(value = "模板组id")
    @NotBlank
    private String templateGroupId;

    @ApiModelProperty(value = "模板启用状态 0-停用，1-启用")
    @NotBlank
    private Integer status;
}
