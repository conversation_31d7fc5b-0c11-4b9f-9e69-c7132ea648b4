package com.dl.magicvideo.biz.dal.visual;

import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobShareInfoPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobSharePageQueryPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualShareConfPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.dl.magicvideo.biz.dal.visual.po.VisualShareConfPO
 */
@BaseDao
public interface VisualShareConfMapper extends BaseMapper<VisualShareConfPO> {
    List<VisualProduceJobShareInfoPO> selectVisualProduceJobExtendInfoPOList(@Param("param")
    VisualProduceJobSharePageQueryPO param);

    Long selectVisualProduceJobExtendInfoPOTotal(@Param("param") VisualProduceJobSharePageQueryPO param);

    List<VisualTemplatePO> selectVisualShareTemplateList(@Param("param") VisualProduceJobSharePageQueryPO param);

    Long selectVisualShareTemplateTotal(@Param("param") VisualProduceJobSharePageQueryPO param);
}




