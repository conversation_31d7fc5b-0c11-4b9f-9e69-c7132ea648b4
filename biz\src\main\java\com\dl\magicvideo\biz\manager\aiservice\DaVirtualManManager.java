package com.dl.magicvideo.biz.manager.aiservice;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesQueryDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-16 09:34
 */
@Component
public class DaVirtualManManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(DaVirtualManManager.class);

    @Resource
    private AiServiceClient aiServiceClient;

    /**
     * 根据数字人场景id列表和渠道列表查询数字人场景列表
     *
     * @param tenantCode
     * @param sceneIds
     * @param channels
     * @param enableFilter
     * @return
     */
    public List<DaVirtualManScenesDTO> vmSceneListBySceneIds(String tenantCode, List<String> sceneIds,
            List<Integer> channels, Integer enableFilter) {
        DaVirtualManScenesQueryDTO queryDTO = new DaVirtualManScenesQueryDTO();
        queryDTO.setSceneIds(sceneIds);
        queryDTO.setChannels(channels);
        queryDTO.setEnableFilter(enableFilter);
        ResultModel<List<DaVirtualManScenesDTO>> resultModel = aiServiceClient
                .vmSceneListBySceneIds(tenantCode, queryDTO);
        if (!resultModel.isSuccess()) {
            LOGGER.error("根据数字人场景id列表和渠道列表查询数字人场景列表失败，queryDTO:{},resultModel:{}", JSONUtil.toJsonStr(queryDTO),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("查询数字人场景列表失败");
        }
        return resultModel.getDataResult();
    }
}
