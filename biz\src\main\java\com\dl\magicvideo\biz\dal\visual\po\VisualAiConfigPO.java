package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * @TableName visual_tts_config
 */
@TableName(value ="visual_ai_config")
@Data
public class VisualAiConfigPO implements Serializable {
    /**
     * 自增主键
     */
    @TableId
    private Long id;

    /**
     * tts_id
     */
    private String ttsId;

    /**
     * 所属节点id
     */
    private Long nodeId;

    /**
     * 所属卡片id
     */
    private Long cardId;

    /**
     * 所属模板id
     */
    private Long templateId;

    /**
     * 0 TTS 1 数字人 2 视频 3 音效
     */
    private Integer type;

    /**
     * 入场延迟
     */
    private Long start;

    /**
     * 
     */
    private String content;

    /**
     * 是否开启字幕，0-否 1-是
     */
    private Integer enableSubtitle;

    /**
     * 字幕分段最大长度
     */
    private Integer maxLength;

    /**
     * 出场延迟
     */
    private Long endDelay;

    /**
     * 隐藏
     */
    private Integer isHide;

    /**
     * 音量
     */
    private String volume;

    /**
     * 视频URL-type为2时存在
     */
    private String url;

    /**
     * 裁剪时长
     */
    private Long croppedDuration;

    /**
     * 视频裁剪开始的时间
     */
    private Long startDelay;

    /**
     * 轮播方式:static 静帧，loop 循环，vanish 消失
     */
    private String activeRotationMode;

    /**
     * 视频时长（去掉裁剪时长后的时间）
     */
    private Long duration;

    /**
     * 淡入时间
     */
    private String fadeInTime;

    /**
     * 淡出时间
     */
    private String fadeOutTime;

    /**
     * 0 非关键时长组件 1 关键时长组件
     */
    private Integer keyTime;

    /**
     * 是否需要字幕中关键词高亮，0-否，1-是
     */
    @TableField("subtitle_key_words_highlight")
    private Integer subtitleKeyWordsHighlight;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}