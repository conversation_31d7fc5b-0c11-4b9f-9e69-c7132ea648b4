package com.dl.magicvideo.biz.manager.statistics.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 */
@Data
public class StatisticsCountDTO implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 统计ID
     */
    private Long countId;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 统计value
     */
    private String statisticsValue;

    /**
     * 统计时间
     */
    private String statisticsTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}