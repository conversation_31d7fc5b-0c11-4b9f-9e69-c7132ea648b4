<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.subjectmatter.SubjectMaterialMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizId" column="biz_id"/>
        <result property="matterId" column="matter_id"/>
        <result property="url" column="url"/>
        <result property="materialType" column="material_type"/>
        <result property="resolution" column="resolution"/>
        <result property="duration" column="duration"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_id,matter_id,url,material_type,resolution_ratio,duration
    </sql>

    <select id="randomMaterial" resultType="com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO">
        SELECT id,biz_id,matter_id,url,material_type,resolution,duration
        FROM subject_material AS sm
        WHERE
        sm.matter_id = #{param.matterId}
        <if test="param.materialType != null">
            AND sm.material_type = #{param.materialType}
        </if>
        order by RAND()
        <if test="param.limit != null">
            limit #{param.limit}
        </if>

    </select>

</mapper>
