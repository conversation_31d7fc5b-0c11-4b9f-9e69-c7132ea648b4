package com.dl.magicvideo.biz.client.basicservice.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 10:38
 */
@Data
public class InternalTenantUserBindQueryParam {

    @NotBlank(message = "租户号不能为空")
    @ApiModelProperty("租户号")
    private String tenantCode;

    @ApiModelProperty("外部用户id")
    private String extUserId;

    @ApiModelProperty("用户id")
    private Long userId;
}
