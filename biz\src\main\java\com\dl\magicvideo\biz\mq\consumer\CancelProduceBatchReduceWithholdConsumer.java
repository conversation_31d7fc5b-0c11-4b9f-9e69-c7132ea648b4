package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.SysTenantClient;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.client.basicservice.param.TenantInfoParam;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceBatchCancelMsgBO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class CancelProduceBatchReduceWithholdConsumer {

    @Autowired
    private VisualProduceJobManager visualProduceJobManager;

    @Autowired
    private TenantInfoManager tenantInfoManager;

    @Autowired
    private AccountTenantTrialManager accountTenantTrialManager;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    private static final String CANCEL_BATCH_REDUCE_WITHHOLD_KEY = "cancel_batch_reduce_withhold_key:";

    /**
     * 1天
     */
    private static final Long LOCK_TIME = 24 * 60 * 60L;

    private String genKey(Long batchId) {
        return CANCEL_BATCH_REDUCE_WITHHOLD_KEY + batchId;
    }

    @StreamListener("cancelbatchreducewithholdconsumer")
    private void consume(@Payload ProduceBatchCancelMsgBO input) {
        try {
            log.info("收到取消批次的消息，准备进行预扣额度返还 input = {}", JSONUtil.toJsonStr(input));
            String lockKey = genKey(input.getBatchId());
            if (!redisUtil.tryLockAndSetTimeout(lockKey, LOCK_TIME)) {
                log.info("已处理过该批次的预扣额度返回！batchId:{}", input.getBatchId());
                return;
            }

            //1.判断是否试用租户
            AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfoFromCache(input.getTenantCode());
            if (!Const.ONE.equals(tenantInfo.getIsTrial())) {
                return;
            }
            //2.查询试用租户账户
            AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager
                    .getOne(Wrappers.lambdaQuery(AccountTenantTrialPO.class)
                            .eq(AccountTenantTrialPO::getTenantCode, input.getTenantCode())
                            .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
            if (Objects.isNull(tenantTrialAccount)) {
                log.error("未找到试用账户，tenantCode:{},input:{}", tenantInfo.getTenantCode(), JSONUtil.toJsonStr(input));
                return;
            }

            //3.找出当前批次job
            List<VisualProduceJobPO> visualProduceJobList = visualProduceJobManager
                    .list(Wrappers.lambdaQuery(VisualProduceJobPO.class)
                            .eq(VisualProduceJobPO::getBatchId, input.getBatchId())
                            .eq(VisualProduceJobPO::getIsDeleted, Const.ZERO));
            if (CollectionUtils.isEmpty(visualProduceJobList)) {
                log.error("该批次对应的任务不存在! batchId:{},,,input:{}", input.getBatchId(), JSONUtil.toJsonStr(input));
                return;
            }

            //取一条作品 判断作品类型
            VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                    .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                            .eq(VisualProduceJobExtendPO::getProduceJobId, visualProduceJobList.get(0).getJobId()));
            //若是数据图表作品，则不涉及试用账户余额
            if (Objects.nonNull(jobExtendPO) && JobTypeEnum.DATA_CHART.getType().equals(jobExtendPO.getType())) {
                return;
            }

            //4.返还预扣值
            tenantTrialAccount.setWithhold(tenantTrialAccount.getWithhold() - visualProduceJobList.size());
            accountTenantTrialManager.updateById(tenantTrialAccount);
            log.info("取消批次返还预扣次数成功");
        }catch (Exception e){
            log.error("返还预扣额度失败");
        }
    }

}
