package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName visual_produce_job
 */
@TableName(value ="visual_produce_job")
@Data
public class VisualProduceJobPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 任务状态：-1-未开始 0-排队中 1-合成中 2-合成成功 3-合成失败
     */
    private Integer status;

    /**
     *
     */
    private String name;

    /**
     *
     */
    private String coverUrl;

    /**
     * 合成视频地址
     */
    private String videoUrl;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * job_type=0:模板id;job_type=1:卡片id
     */
    private Long templateId;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 动态参数
     */
    private String replaceData;

    /**
     * 预览视频结构化数据
     * @see: com.dl.magicvideo.biz.manager.visual.dto.preview.PreviewDTO
     */
    private String previewData;

    /**
     * 模板快照数据
     */
    private String templateData;

    /**
     * 合成开始时间
     */
    private Date processDt;

    /**
     * 合成结束时间
     */
    private Date completeDt;

    /**
     * 来源 0-平台触发(即页面点击合成按钮) 1-批量合成接口触发 2-excel批量合成接口触发 3-开放平台合成接口触发 4-内部合成接口触发 5-交付计划触发
     * <p>
     * 原本来源 0-平台触发 1-接口触发 4-内部合成接口触发。 现于【2024-03-04】对该字段各个值做细分定义。
     * 原本代码中0和1乱用了，故已有作品数据中0和1也是不准确的。由于无法区分，故无法对历史数据做数据订正。
     *
     * @see：com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum
     */
    private Integer source;

    /**
     * 视频时长
     */
    private Long duration;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 智能任务完成时间
     */
    private Date aiCompleteDt;

    /**
     * 作品大小
     */
    private Long size;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 分片状态 0-未分片 1-已分片 2-分片完成
     */
    private Integer segmentStatus;

    /**
     * 数字人版本 0 照片版本 1 数字人版本
     */
    private Integer digitalManType;

    /**
     * 推送状态 0-未推送 1-推送成功 2-推送失败
     */
    private Integer pushStatus;

    /**
     * 接口动态参数
     */
    private String apiData;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}