<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.VisualProduceJobExtendMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="produceJobId" column="produce_job_id" jdbcType="BIGINT"/>
            <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
            <result property="shareConfState" column="share_conf_state" jdbcType="TINYINT"/>
            <result property="recommendState" column="recommend_state" jdbcType="TINYINT"/>
            <result property="recommendEnableDt" column="recommend_enable_dt" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
            <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="modifyDt" column="modify_dt" jdbcType="TIMESTAMP"/>
            <result property="modifyBy" column="modify_by" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="produceJobResultMap" type="com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="job_id" property="jobId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="cover_url" property="coverUrl" jdbcType="VARCHAR"/>
        <result column="video_url" property="videoUrl" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
        <result column="template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="batch_id" property="batchId" jdbcType="BIGINT"/>
        <result column="replace_data" property="replaceData" jdbcType="VARCHAR"/>
        <result column="preview_data" property="previewData" jdbcType="VARCHAR"/>
        <result column="template_data" property="templateData" jdbcType="VARCHAR"/>
        <result column="process_dt" property="processDt" jdbcType="TIMESTAMP"/>
        <result column="complete_dt" property="completeDt" jdbcType="TIMESTAMP"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="ai_complete_dt" property="aiCompleteDt" jdbcType="TIMESTAMP"/>
        <result column="size" property="size" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="segment_status" property="segmentStatus" jdbcType="INTEGER"/>
    </resultMap>



    <sql id="Base_Column_List">
        id,produce_job_id,tenant_code,
        share_conf_state,recommend_state,recommend_enable_dt,
        is_deleted,create_dt,create_by,
        modify_dt,modify_by
    </sql>

    <sql id="produceJobColumns">
        vpj.id, vpj.job_id, vpj.status, vpj.name, vpj.cover_url, vpj.video_url, vpj.is_deleted,
    vpj.template_id, vpj.batch_id, vpj.replace_data, vpj.preview_data, vpj.template_data,
    vpj.process_dt, vpj.complete_dt, vpj.source, vpj.duration, vpj.tenant_code,
    vpj.tenant_name, vpj.ai_complete_dt, vpj.size, vpj.creator_name, vpj.segment_status
    </sql>
    <select id="recommendJobPage" resultMap="produceJobResultMap">
        SELECT <include refid="produceJobColumns"/>
        FROM visual_produce_job AS vpj
               INNER JOIN visual_produce_job_extend AS vpje on vpj.job_id = vpje.produce_job_id
        WHERE vpj.tenant_code = #{param.tenantCode}
          AND vpj.is_deleted = 0
          AND vpje.recommend_state = 1
          AND vpje.is_deleted = 0
        ORDER BY vpje.recommend_enable_dt DESC
        LIMIT #{param.offset},#{param.pageSize}
    </select>
    <select id="recommendJobTotal" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM visual_produce_job AS vpj
        INNER JOIN visual_produce_job_extend AS vpje on vpj.job_id = vpje.produce_job_id
        WHERE vpj.tenant_code = #{param.tenantCode}
        AND vpje.recommend_state = 1
        AND vpj.is_deleted = 0
        AND vpje.is_deleted = 0
    </select>
</mapper>
