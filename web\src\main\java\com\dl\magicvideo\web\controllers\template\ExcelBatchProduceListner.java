package com.dl.magicvideo.web.controllers.template;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.alibaba.fastjson.JSON;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.account.trial.impl.AccountTenantTrialManagerImpl;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.visual.VisualAiConfigManager;
import com.dl.magicvideo.biz.manager.visual.VisualDynamicNodeManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobContextBO;
import com.dl.magicvideo.biz.manager.visual.dto.InterfaceDTO;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.biz.manager.visual.impl.VisualAiConfigManagerImpl;
import com.dl.magicvideo.biz.manager.visual.impl.VisualDynamicNodeManagerImpl;
import com.dl.magicvideo.biz.manager.visual.impl.VisualProduceJobManagerImpl;
import com.dl.magicvideo.web.controllers.template.param.BatchParamDTO;
import com.dl.magicvideo.web.controllers.template.param.ExcelBatchProduceParam;
import com.dl.magicvideo.web.controllers.template.vo.VisualProduceCreateBatchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-04 18:07
 */
@Slf4j
public class ExcelBatchProduceListner extends AnalysisEventListener<Map<Integer, String>> {

    private static final String CODE = "code";

    private OperatorUtil operatorUtil;

    private ExcelBatchProduceParam excelBatchProduceParam;

    private AccountTenantTrialManager accountTenantTrialManager;

    private VisualProduceJobManager visualProduceJobManager;

    private HostTimeIdg hostTimeIdg;

    private TenantInfoManager tenantInfoManager;

    private VisualProduceCreateBatchVO resultVO;

    /**
     * 行头，key-index  value-列名称，即参数名-接口名
     */
    private Map<Integer, String> header;

    private VisualDynamicNodeManager visualDynamicNodeManager;

    private VisualAiConfigManager visualAiConfigManager;

    /**
     * 批量合成参数列
     */
    private List<BatchParamDTO> batchParams = new ArrayList<>();

    public ExcelBatchProduceListner(OperatorUtil operatorUtil, ExcelBatchProduceParam excelBatchProduceParam,
            AccountTenantTrialManager accountTenantTrialManager, VisualProduceJobManager visualProduceJobManager,
            HostTimeIdg hostTimeIdg, TenantInfoManager tenantInfoManager, VisualProduceCreateBatchVO resultVO,
            VisualDynamicNodeManager visualDynamicNodeManager, VisualAiConfigManager visualAiConfigManager) {
        this.operatorUtil = operatorUtil;
        this.excelBatchProduceParam = excelBatchProduceParam;
        this.accountTenantTrialManager = accountTenantTrialManager;
        this.visualProduceJobManager = visualProduceJobManager;
        this.hostTimeIdg = hostTimeIdg;
        this.tenantInfoManager = tenantInfoManager;
        this.resultVO = resultVO;
        this.visualDynamicNodeManager = visualDynamicNodeManager;
        this.visualAiConfigManager = visualAiConfigManager;
    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        header = ConverterUtils.convertToStringMap(headMap, context);
    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(dataMap));
        this.transformData(dataMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        this.batchProduce();
    }

    /**
     * 数据转换
     *
     * @param dataMap
     */
    private void transformData(Map<Integer, String> dataMap) {
        BatchParamDTO batchParamDTO = new BatchParamDTO();
        batchParamDTO.setList(new ArrayList<>());
        //key-interfaceUrl
        Map<String, InterfaceDTO> interfaceDTOMap = new HashMap<>();

        Iterator<Map.Entry<Integer, String>> iterator = dataMap.entrySet().iterator();
        List<Object> paramValueList = new ArrayList();
        while (iterator.hasNext()) {
            Map.Entry<Integer, String> entry = iterator.next();
            Integer index = entry.getKey();
            String paramValue = entry.getValue();
            String headerName = header.get(index);

            String[] splitHeader = headerName.split("【");
            String paramName = splitHeader[0];
            String interfaceName = splitHeader[1].replace("】","");

            String interfaceUrl = excelBatchProduceParam.getInterfaceNameUrlMap().get(interfaceName);
            if(StringUtils.isBlank(interfaceUrl)){
                throw BusinessServiceException.getInstance("数据接口匹配有误");
            }

            InterfaceDTO interfaceDTO;
            if (interfaceDTOMap.containsKey(interfaceUrl)) {
                interfaceDTO = interfaceDTOMap.get(interfaceUrl);
            } else {
                interfaceDTO = new InterfaceDTO();
                interfaceDTO.setName(interfaceUrl);
                interfaceDTOMap.put(interfaceUrl, interfaceDTO);
                batchParamDTO.getList().add(interfaceDTO);
            }
            Map<String, Object> keyMap;
            if (Objects.isNull(interfaceDTO.getKeyMap())) {
                keyMap = new HashMap<>();
                interfaceDTO.setKeyMap(keyMap);
            } else {
                keyMap = interfaceDTO.getKeyMap();
            }
            Object paramValueObj = paramValue;

            //如果参数名为code
            if (CODE.equals(paramName)) {
                batchParamDTO.setSubTitle(paramValue);
                //参数名非code 且值为纯数字，则转换成Long
            } else if (StringUtils.isNumeric(paramValue)) {
                //纯数字，转换成Long
                paramValueObj = Long.valueOf(paramValue);
            }
            paramValueList.add(paramValue);
            keyMap.put(paramName, paramValueObj);
        }
        batchParamDTO.setSubTitle(StringUtils.join(paramValueList, "_"));

        //如果没有搜到，用第一个接口的第一个参数
        if (StringUtils.isBlank(batchParamDTO.getSubTitle())) {
            batchParamDTO.setSubTitle(dataMap.get(0));
        }

        batchParams.add(batchParamDTO);
    }

    /**
     * 批量合成
     */
    private void batchProduce() {
        log.info("templateId:{},,,共{}条数据，开始批量合成！batchParams:{}", excelBatchProduceParam.getTemplateId(),
                batchParams.size(), JSONUtil.toJsonStr(batchParams));
        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(operatorUtil.getTenantCode());
        Integer balance = accountTenantTrialManager
                .checkTenantBalance(operatorUtil.getTenantCode(), batchParams.size());
        batchParams.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e.getList())) {
                List<InterfaceDTO> interfaceList = e.getList();
                String subTitle = StringUtils.isNotBlank(e.getSubTitle()) ? e.getSubTitle() : StringUtils.EMPTY;
                String templateName = excelBatchProduceParam.getTitle() + subTitle;
                Long batchId = hostTimeIdg.generateId().longValue();
                String apiData = visualProduceJobManager.suppleApiData(interfaceList);
                log.info("查询到的apiData:{}", apiData);
                if (StringUtils.isBlank(apiData) || "{}".equals(apiData)) {
                    throw BusinessServiceException.getInstance("未查到数据");
                }

                visualProduceJobManager.doProduce(
                        ProduceJobContextBO.builder().templateId(Long.parseLong(excelBatchProduceParam.getTemplateId()))
                                .batchId(batchId).source(VisualProduceJobSourceEnum.EXCEL_BATCH_PRODUCE.getCode()).templateName(templateName)
                                .dmProduceMode(tenantInfo.getDmProduceMode()).apiData(apiData)
                                .dynamicNodes(VisualTemplateProcess.convert(visualDynamicNodeManager,
                                        visualAiConfigManager, excelBatchProduceParam.getTemplateId())).build());
            }
        });

        resultVO.setBalance(String.valueOf(balance));
        log.info("批量合成成功！templateId:{}", excelBatchProduceParam.getTemplateId());
    }

    public static void main(String[] args) {
        String filePath = "/Users/<USER>/Downloads/自测数据接口批量合成1.xlsx";

        // 定义一个 Map 保存读取的数据，key 为第一行的内容，value 为其余行的内容列表
        //Map<String, List<String>> dataMap = new HashMap<>();

        ExcelBatchProduceParam param = new ExcelBatchProduceParam();
        Map<String, String> interfaceNameUrlMap = new HashMap<>();
        interfaceNameUrlMap.put("接口1", "url1");
        interfaceNameUrlMap.put("接口2", "url2");
        interfaceNameUrlMap.put("接口3", "url3");
        param.setInterfaceNameUrlMap(interfaceNameUrlMap);
        VisualProduceCreateBatchVO resultVO = new VisualProduceCreateBatchVO();
        // 执行读取操作
        EasyExcel.read(filePath,
                new ExcelBatchProduceListner(new OperatorUtil(), param, new AccountTenantTrialManagerImpl(),
                        new VisualProduceJobManagerImpl(), new HostTimeIdg(), new TenantInfoManager(), resultVO,
                        new VisualDynamicNodeManagerImpl(), new VisualAiConfigManagerImpl()))
                .sheet().doRead();
        System.out.println("resultVO:" + JSONUtil.toJsonStr(resultVO));
    }

}