package com.dl.magicvideo.web.controllers.font;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.font.param.PatternFontDetailParam;
import com.dl.magicvideo.web.controllers.font.param.PatternFontPageParam;
import com.dl.magicvideo.web.controllers.font.vo.PatternFontPageVO;
import com.dl.magicvideo.web.controllers.font.vo.PatternFontVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/pattern/font")
public class PatternFontController extends AbstractController {

    @Autowired
    private PatternProcess patternProcess;

    @PostMapping("/list")
    public ResultModel<List<PatternFontPageVO>> list(){
        return patternProcess.list();
    }

    @PostMapping("/detail")
    @ApiOperation("花字详情接口")
    public ResultModel<PatternFontVO> detail(@RequestBody PatternFontDetailParam param){
        return patternProcess.detail(param);
    }

    @PostMapping("/page")
    @ApiOperation("花字分页接口")
    public ResultPageModel<PatternFontPageVO> page(@RequestBody PatternFontPageParam param){
        return patternProcess.page(param);
    }
}
