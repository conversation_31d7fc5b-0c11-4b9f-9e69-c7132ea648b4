package com.dl.magicvideo.web.controllers.internal.visual.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 11:32
 */
@Data
public class VisualAiJobInternalPageParam extends AbstractPageParam {

    @NotBlank(message = "租户编号不能为空")
    @ApiModelProperty("租户编号")
    private String tenantCode;

    @ApiModelProperty("最小时间")
    private Date minDt;

    @ApiModelProperty("最大时间")
    private Date maxDt;

    @ApiModelProperty("任务状态列表")
    private List<Integer> jobStatusList;

    /**
     * 是否需要查询已删除的数据
     * 0-否  即只查询未删除的数据
     * 1-是  即查询所有数据
     * 默认为0
     */
    @ApiModelProperty("是否需要查询已删除的数据,0-否  即只查询未删除的数据,1-是  即查询所有数据,默认为0")
    private Integer needQueryDeleted = 0;

}
