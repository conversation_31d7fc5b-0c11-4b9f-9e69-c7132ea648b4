package com.dl.magicvideo.web.controllers.material;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.material.param.*;
import com.dl.magicvideo.web.controllers.material.process.MaterialProcess;
import com.dl.magicvideo.web.controllers.material.vo.FolderListVO;
import com.dl.magicvideo.web.controllers.material.vo.FolderListWithLatestMaterialVO;
import com.dl.magicvideo.web.controllers.material.vo.MaterialDetailVO;
import com.dl.magicvideo.web.controllers.material.vo.MaterialPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @describe: 素材库相关controller
 * @author: hongcj
 * @date: 2023/7/6 14:43
 */
@Slf4j
@RestController
@RequestMapping("/visual/material")
@Api("素材库")
public class MaterialController {
    @Resource
    private MaterialProcess materialProcess;

    /**
     * 素材列表
     *
     * @param param
     * @return
     */
    @PostMapping("/page")
    @ApiOperation("素材列表")
    public ResultPageModel<MaterialPageVO> page(@RequestBody @Validated MaterialPageParam param) {
        return materialProcess.page(param);
    }

    /**
     * 素材详情
     *
     * @param param
     * @return
     */
    @PostMapping("/detail")
    @ApiOperation("素材详情")
    public ResultModel<MaterialDetailVO> detail(@RequestBody @Validated MaterialDetailParam param) {
        return materialProcess.detail(param);
    }

    /**
     * 素材重命名
     *
     * @param param
     * @return
     */
    @PostMapping("/rename")
    @ApiOperation("素材重命名")
    public ResultModel<Boolean> rename(@RequestBody @Validated MaterialRenameParam param) {
        return materialProcess.rename(param);
    }

    /**
     * 素材删除
     *
     * @param param
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation("素材删除")
    public ResultModel<Boolean> delete(@RequestBody @Validated MaterialDeleteParam param) {
        return materialProcess.delete(param);
    }

    /**
     * 素材批量删除
     *
     * @param param
     * @return
     */
    @PostMapping("/batchdelete")
    @ApiOperation("素材批量删除")
    public ResultModel<Boolean> batchDelete(@RequestBody @Validated MaterialBatchDeleteParam param) {
        return materialProcess.batchDelete(param);
    }

    /**
     * 素材移动文件夹
     *
     * @param param
     * @return
     */
    @PostMapping("/movefolder")
    @ApiOperation("素材移动文件夹")
    public ResultModel<Boolean> moveFolder(@RequestBody @Validated MaterialFolerdMoveParam param) {
        return materialProcess.moveFolder(param);
    }

    /**
     * 素材批量移动文件夹
     *
     * @param param
     * @return
     */
    @PostMapping("/batchmovefolder")
    @ApiOperation("素材移动文件夹")
    public ResultModel<Boolean> batchMoveFolder(@RequestBody @Validated MaterialFolerdBatchMoveParam param) {
        return materialProcess.batchMoveFolder(param);
    }

    /**
     * 批量上传素材
     *
     * @param materialType
     * @return
     */
    @PostMapping("/batchupload")
    @ApiOperation("批量上传素材")
    public ResultModel<List<String>> batchUpload(@RequestParam Integer materialType,
                                                 @RequestParam(required = false) Long folderId,
                                                 @RequestPart(value = "files") MultipartFile[] multipartFiles, HttpServletRequest request) {
        return materialProcess.batchUpload(materialType, folderId, multipartFiles);
    }

    /**
     * 文件夹列表
     *
     * @return
     */
    @PostMapping("/folder/list")
    @ApiOperation("文件夹列表")
    public ResultModel<List<FolderListVO>> folderList(@RequestBody @Validated FolderListParam param) {
        return materialProcess.folderList(param);
    }

    @PostMapping("/folder/listwithlatestmaterial")
    @ApiOperation("获取文件夹列表并查询各个文件夹下最新的素材")
    public ResultModel<List<FolderListWithLatestMaterialVO>> folderListWithLatestMaterial(
            @RequestBody @Validated FolderListWithLatestMaterialParam param) {
        return materialProcess.folderListWithLatestMaterial(param);
    }

    /**
     * 文件夹删除
     *
     * @param param
     * @return
     */
    @PostMapping("/folder/delete")
    @ApiOperation("文件夹删除")
    public ResultModel<Boolean> folderDelete(@RequestBody @Validated FolderDeleteParam param) {
        return materialProcess.folderDelete(param);
    }

    /**
     * 文件夹重命名
     *
     * @return
     */
    @PostMapping("/folder/rename")
    @ApiOperation("文件夹重命名")
    public ResultModel<Boolean> folderRename(@RequestBody @Validated FolderRenameParam param) {
        return materialProcess.folderRename(param);
    }

    /**
     * 文件夹添加
     *
     * @return
     */
    @PostMapping("/folder/add")
    @ApiOperation("文件夹添加")
    public ResultModel<Long> folderAdd(@RequestBody @Validated FolderAddParam param) {
        return materialProcess.folderAdd(param);
    }

}
