package com.dl.magicvideo.openapi.controller.auth;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.constant.OpenApiConstant;
import com.dl.magicvideo.biz.common.util.JwtUtil;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.openapi.controller.auth.vo.OpenApiTokenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static com.dl.magicvideo.biz.common.constant.OpenApiConstant.OPENAPI_TOKEN_CACHE_KEY_PREFIX;

@Api(tags = "openapi-权限相关")
@RestController
@RequestMapping("/openapi/auth")
@Slf4j
public class OpenApiAuthController {

    private final String subject = "dl-open-api";

    @Autowired
    private RedisUtil redisUtil;

    @ApiOperation("获取token")
    @RequestMapping("/token")
    @NotLogin
    public ResultModel<OpenApiTokenVO> getToken(HttpServletRequest request) {
        String tenantCode = request.getHeader(OpenApiConstant.ACCESS_KEY);

        Map<String, Object> claims = new HashMap<String, Object>();
        claims.put("tenantCode", tenantCode);
        claims.put("timestamp", System.currentTimeMillis());
        String token = JwtUtil.createJWT(claims, subject);

        //设置缓存，有效时间1天
        redisUtil.set(OPENAPI_TOKEN_CACHE_KEY_PREFIX + tenantCode, token, 86400);
        log.info("租户:{},来获取openApi的token:{}", tenantCode, token);
        OpenApiTokenVO openApiTokenVO = new OpenApiTokenVO();
        openApiTokenVO.setToken(token);
        return ResultModel.success(openApiTokenVO);
    }
}
