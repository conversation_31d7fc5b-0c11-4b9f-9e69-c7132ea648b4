package com.dl.magicvideo.web.controllers.internal.visual.vo;

import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-23 15:26
 */
@Data
public class VisualProduceJobInternalPageVO extends VisualProduceJobInternalVO {
    private static final long serialVersionUID = -7197977757233710651L;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    /**
     * @see JobTypeEnum
     */
    @ApiModelProperty(value = "视频类型,1-常规作品,2-数据图表作品,3-AIGC作品")
    private Integer type;

    @ApiModelProperty(value = "视频类型描述")
    private String typeDesc;

}
