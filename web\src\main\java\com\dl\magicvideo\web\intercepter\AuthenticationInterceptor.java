package com.dl.magicvideo.web.intercepter;

import cn.hutool.json.JSONUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import com.dl.magicvideo.biz.client.basicservice.SysAdmUserClient;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmUserExtDTO;
import com.dl.magicvideo.biz.client.basicservice.param.TokenParamDTO;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.CommonCode;
import com.dl.magicvideo.biz.common.properties.WebIgnoreProperties;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;

import static com.dl.magicvideo.biz.common.constant.Const.TOKEN_HEADER_NAME;

@Component
@Slf4j
public class AuthenticationInterceptor implements HandlerInterceptor, Ordered {

    private final PathMatcher pathMatcher = new AntPathMatcher();
    @Autowired
    private WebIgnoreProperties properties;
    @Autowired
    private OperatorUtil operatorUtil;
    @Resource
    private SysAdmUserClient sysAdmUserClient;

    private boolean isIgnoreUrl(HttpServletRequest request) {
        String url = request.getRequestURI();
        List<String> urlPatterns = properties.getUrls();
        if (Collections.isEmpty(urlPatterns)) {
            return false;
        }
        for (String urlPattern : urlPatterns) {
            if (pathMatcher.match(urlPattern, url)) {
                return true;
            }
        }
        return false;
    }

    private boolean isApp(HttpServletRequest request) {
        String url = request.getRequestURI();
        return pathMatcher.match("/app/**", url);
    }

    private boolean isInternal(HttpServletRequest request) {
        String url = request.getRequestURI();
        return pathMatcher.match("/visual/internal/**", url);
    }

    private boolean isOpenApi(HttpServletRequest request) {
        String url = request.getRequestURI();
        return pathMatcher.match("/openapi/**", url);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        //判断是否为app端，app端请求不走此判断
        if (isInternal(request)) {
            operatorUtil.init(0L, "管理员","DL", "定力科技");
            return true;
        }
        if (isApp(request)) {
            return true;
        }
        if (isOpenApi(request)) {
            return true;
        }
        //判断是否忽略url
        if (isIgnoreUrl(request)) {
            return true;
        } else if (handler instanceof ResourceHttpRequestHandler) {
            //静态资源
            return false;
        }

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //2、判断 NotLogin，有则跳过认证
        if (method.isAnnotationPresent(NotLogin.class)) {
            NotLogin loginToken = method.getAnnotation(NotLogin.class);
            if (loginToken.required()) {
                return true;
            }
        }

        // 从 http 请求头中取出 token
        String token = request.getHeader(TOKEN_HEADER_NAME);
        //1、如果token为空的情况下，返回非法token，禁止访问
        if (StringUtils.isEmpty(token)) {
            //非法token
            throw new CertificateException();
        } else {
            AdmUserExtDTO admUserDTO = checkToken(token);
            //biz端的用户注入
            operatorUtil.init(admUserDTO.getUserId(), admUserDTO.getUserName(), admUserDTO.getTenantCode(),
                    admUserDTO.getTenantName());
        }
        return true;
    }

    public AdmUserExtDTO checkToken(String token) {
        TokenParamDTO tokenParamDTO = new TokenParamDTO();
        tokenParamDTO.setToken(token);
        ResultModel<AdmUserExtDTO> resultModel = sysAdmUserClient.checkToken(tokenParamDTO);
        if (!Const.ZERO_STR.equals(resultModel.getCode()) || !Const.ZERO_STR.equals(resultModel.getFlag())) {
            log.error("校验token失败!,token:{},resultModel:{}", token, JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance(CommonCode.TOKEN_CHECK_EXPIRED.getCode(), "校验token失败");
        }
        return resultModel.getDataResult();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        if (!isApp(request)) {
            //去除biz端的用户注入
            operatorUtil.remove();
        }

    }

    private boolean checkNotNull(String headValue){
        if(StringUtils.isBlank(headValue) || "null".equals(headValue)){
            return false;
        }
        return true;
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
