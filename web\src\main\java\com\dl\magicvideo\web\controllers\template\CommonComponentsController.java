package com.dl.magicvideo.web.controllers.template;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsAddParam;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsDetailParam;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsGroupParam;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsPageParam;
import com.dl.magicvideo.web.controllers.template.vo.CommonComponentsDetailVO;
import com.dl.magicvideo.web.controllers.template.vo.CommonComponentsGroupVO;
import com.dl.magicvideo.web.controllers.template.vo.CommonComponentsListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2024/11/19 14:01
 */
@Slf4j
@RestController
@RequestMapping("/visual/components")
@Api("常用组合管理")
public class CommonComponentsController {
    @Resource
    private CommonComponentsProcess commonComponentsProcess;

    @PostMapping("/commonList")
    @ApiOperation("常用组合列表")
    public ResultPageModel<CommonComponentsListVO> list(@RequestBody @Validated CommonComponentsPageParam param) {
        return commonComponentsProcess.list(param);
    }

    @PostMapping("/commonGroup")
    @ApiOperation("常用组合聚合结果")
    public ResultModel<List<CommonComponentsGroupVO>> groupList(@RequestBody @Validated CommonComponentsGroupParam param) {
        return commonComponentsProcess.groupList(param);
    }

    @PostMapping("/detail")
    @ApiOperation("常用组合详情")
    public ResultModel<CommonComponentsDetailVO> detail(@RequestBody @Validated CommonComponentsDetailParam param) {
        return commonComponentsProcess.detail(param);
    }

    @PostMapping("/add")
    @ApiOperation("添加常用组合")
    public ResultModel<Void> add(@RequestBody @Validated CommonComponentsAddParam param) {
        return commonComponentsProcess.add(param);
    }
}
