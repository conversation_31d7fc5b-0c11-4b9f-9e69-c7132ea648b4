<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.VisualProduceJobMapper">


    <select id="statistics" resultType="com.dl.magicvideo.biz.dal.visual.po.StatisticsJobCountPO">
        SELECT
        tenant_code AS tenantCode,tenant_name AS tenantName,
		sum(case when status = 2 then 1 else 0 end) as completeNum,
		sum(case when status = 3 then 1 else 0 end) as failNum,
		sum(case when status = 2 then duration else 0 end) as totalDuration
        FROM
        visual_produce_job
        WHERE
            `status` in (2,3) AND
        <![CDATA[
            create_dt >= #{param.startTime}
            AND create_dt <= #{param.endTime}
        ]]>
        GROUP BY
        tenant_code,
        tenant_name
        ORDER BY
        completeNum DESC
    </select>

    <select id="efficiency" resultType="com.dl.magicvideo.biz.dal.visual.po.StatisticsJobEfficiencyPO">
        SELECT
               SUM(TIMESTAMPDIFF(SECOND,create_dt, `ai_complete_dt`)) AS aiCompleteSecond,
               SUM(TIMESTAMPDIFF(SECOND,process_dt, `complete_dt`)) AS completeSecond,
               SUM(TIMESTAMPDIFF(SECOND,create_dt, `complete_dt`)) AS allCompleteSecond,
               SUM(duration)/1000 AS durationSecond
        FROM `visual_produce_job`
        WHERE
            `status` = 2 AND
        <![CDATA[
            complete_dt >= #{param.startTime}
          AND complete_dt <= #{param.endTime}
        ]]>
        <if test="param.type != null">
            AND digital_man_type = #{param.type}
        </if>
    </select>
</mapper>
