package com.dl.magicvideo.biz.dal.account.trial.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

@Data
@TableName(value = "account_tenant_trial")
public class AccountTenantTrialPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;


    @TableField("tenant_code")
    private String tenantCode;

    /**
     *
     * 剩余使用次数
     */
    @TableField("balance")
    private Long balance;

    /**
     *
     * 预扣次数
     */
    @TableField("withhold")
    private Long  withhold;

    /**
     *
     * 是否删除 0否 1是
     */
    @TableField("is_deleted")
    private Integer isDeleted;

}
