package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: DynamicNodeParam
 * @author: zhousx
 * @date: 2023/4/25 17:16
 */
@Data
public class DynamicNodeParam {
    @ApiModelProperty("节点类型")
    private String id;

    @ApiModelProperty("节点id")
    private String nodeId;

    @ApiModelProperty("节点时长")
    private Long duration;

    @ApiModelProperty("tts配置")
    private List<TtsConfigParam> ttsList;

    @ApiModelProperty("数字人配置")
    private List<DigitalManConfigParam> dmList;

    @ApiModelProperty("视频配置")
    private List<VideoParam> videoList;

    @ApiModelProperty("视频配置")
    private List<AudioParam> audioList;

    @ApiModelProperty("数据图表配置")
    private List<DataSheetParam> dataSheetList;

    @ApiModelProperty("是否启用，0-否 1-是")
    private Integer isEnabled = 1;
    /**
     * 节点程序选择表达式
     */
    private String expression;

    /**
     * 是否启用程序选择
     */
    private Integer expressionFlag;

    @Data
    public static class TtsConfigParam {
        @ApiModelProperty("id")
        private String ttsId;

        @ApiModelProperty("入场延迟")
        private Long start;

        @ApiModelProperty("{\"text\":\"文本内容\",\"replaceKey\":\"替换变量key\",\"speed\":语速}")
        private String content;

        @ApiModelProperty("是否开启字幕 0-否 1-是")
        private Integer enableSubtitle;

        @ApiModelProperty("分段最大字数")
        private Integer maxLength;

        @ApiModelProperty("出场延迟")
        private Long endDelay;

        @ApiModelProperty("是否隐藏")
        private boolean hide;

        /**
         * 是否需要字幕中关键词高亮，0-否，1-是
         */
        @ApiModelProperty("是否需要字幕中关键词高亮，0-否，1-是")
        private Integer subtitleKeyWordsHighlight;

        @ApiModelProperty("是否为关键时长组件，0-否，1-是")
        private Integer keyTime;
    }

    @Data
    public static class DigitalManConfigParam {
        @ApiModelProperty("id")
        private String dmId;

        @ApiModelProperty("入场延迟")
        private Long start;

        @ApiModelProperty("{\"text\":\"文本内容\",\"replaceKey\":\"替换变量key\",\"sceneId\":\"场景id\",\"voiceCode\":\"声音代码\",,\"channel\":\"渠道\"}")
        private String content;

        @ApiModelProperty("是否开启字幕 0-否 1-是")
        private Integer enableSubtitle;

        @ApiModelProperty("分段最大字数")
        private Integer maxLength;
        @ApiModelProperty("是否隐藏")
        private boolean hide;

        @ApiModelProperty("出场延迟")
        private Long endDelay;
        @ApiModelProperty("是否为关键时长组件，0-否，1-是")
        private Integer keyTime;
    }

    @Data
    public static class VideoParam {
        /**
         * id
         */
        private String videoId;

        /**
         * 入场延迟
         */
        private Long start;

        /**
         * 视频链接
         */
        private String url;

        /**
         * 出场延迟
         */
        private Long endDelay;
        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 音量
         */
        private String volume;

        /**
         * 裁剪开始的时间
         */
        private Long startDelay;

        /**
         * 裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频时长
         */
        private Long duration;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失
         */
        private String activeRotationMode;
        @ApiModelProperty("是否为关键时长组件，0-否，1-是")
        private Integer keyTime;
    }

    @Data
    public static class AudioParam {
        /**
         * id
         */
        private String audioId;

        /**
         * 入场延迟
         */
        private Long start;

        /**
         * 视频链接
         */
        private String url;

        /**
         * 出场延迟
         */
        private Long endDelay;
        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 音量
         */
        private String volume;

        /**
         * 裁剪开始的时间
         */
        private Long startDelay;

        /**
         * 裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频时长
         */
        private Long duration;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失
         */
        private String activeRotationMode;

        /**
         * 淡入时间
         */
        private String fadeInTime;

        /**
         * 淡出时间
         */
        private String fadeOutTime;
        @ApiModelProperty("是否为关键时长组件，0-否，1-是")
        private Integer keyTime;
    }

    @Data
    public static class DataSheetParam {
        private String id;
        /**
         * 动画时长
         */
        private Long animationTime;
        /**
         *动态时长
         */
        private boolean dynamicDuration;
        /**
         *开始等待
         */
        private Long scrollStartDelay;
        /**
         *结束等待
         */
        private Long scrollEndDelay;
        /**
         *分页数量，用于计算动态时长
         */
        private Integer pageSize;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;

        /**
         * 内容 {"replaceKey":"替换变量key"}
         */
        private String bindKey;

        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 每页时长，默认3000
         */
        private Integer pageScrollTime = 3000;
    }
}
