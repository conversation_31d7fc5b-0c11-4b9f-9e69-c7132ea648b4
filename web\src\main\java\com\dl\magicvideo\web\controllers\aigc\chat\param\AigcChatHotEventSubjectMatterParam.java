package com.dl.magicvideo.web.controllers.aigc.chat.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-13 15:40
 */
@Data
@ApiModel("aigc-聊天-热点事件题材参数")
public class AigcChatHotEventSubjectMatterParam {

    @NotNull(message = "发送消息参数不能为空")
    @ApiModelProperty(value = "发送消息参数", required = true)
    private AigcChatSendMessageParam messageParam;

}
