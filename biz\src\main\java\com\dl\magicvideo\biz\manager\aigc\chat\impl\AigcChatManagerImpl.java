package com.dl.magicvideo.biz.manager.aigc.chat.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.dl.aiservice.share.aichat.AiFileContentAndTitleRespDTO;
import com.dl.aiservice.share.aichat.AiMultiChatMessageDTO;
import com.dl.aiservice.share.aichat.AiMultiChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatResponseDTO;
import com.dl.aiservice.share.aichat.consts.AiChatKimiConst;
import com.dl.aiservice.share.aichat.errorcode.AiChatErrorCodeEnum;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.manager.aigc.chat.AigcChatManager;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AiFileContentAndTitleRespBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcMultiChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatResponseBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 17:47
 */
@Component
public class AigcChatManagerImpl implements AigcChatManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(AigcChatManagerImpl.class);

    @Resource
    private AiServiceClient aiServiceClient;

    @Override
    public AigcSingleChatResponseBO singleChat(String tenantCode, AigcSingleChatRequestBO requestBO) {
        AiSingleChatRequestDTO requestDTO = new AiSingleChatRequestDTO();
        requestDTO.setUserId(requestBO.getUserId());
        requestDTO.setUserMessage(requestBO.getUserMessage());
        requestDTO.setFileUrl(requestBO.getFileUrl());
        requestDTO.setModel(requestBO.getModel());
        requestDTO.setRespMaxToken(requestBO.getRespMaxToken());

        ResultModel<AiSingleChatResponseDTO> resultModel = aiServiceClient.singleChat(tenantCode, requestDTO);
        if (Objects.isNull(resultModel)) {
            LOGGER.error("调用ai对话服务，单条对话失败!resultModel为null,,, requestDTO:{}", JSONUtil.toJsonStr(requestDTO));
            throw BusinessServiceException.getInstance("服务器开小差了，请稍后再试");
        }
        if (!resultModel.isSuccess()) {
            LOGGER.error("调用ai对话服务，单条对话失败! requestDTO:{},,,,resultModel:{}", JSONUtil.toJsonStr(requestDTO),
                    JSONUtil.toJsonStr(resultModel));
            AiChatErrorCodeEnum aiChatErrorCodeEnum = AiChatErrorCodeEnum.parse(resultModel.getCode());
            if (Objects.nonNull(aiChatErrorCodeEnum)) {
                throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
            }
            throw BusinessServiceException.getInstance("服务器开小差了，请稍后再试");
        }
        AigcSingleChatResponseBO aigcSingleChatResponseBO = new AigcSingleChatResponseBO();
        aigcSingleChatResponseBO.setContent(resultModel.getDataResult().getContent());
        return aigcSingleChatResponseBO;
    }

    @Override
    public InputStream singleChatStream(String tenantCode, AigcSingleChatRequestBO requestBO) {
        AiSingleChatRequestDTO requestDTO = new AiSingleChatRequestDTO();
        requestDTO.setUserId(requestBO.getUserId());
        requestDTO.setUserMessage(requestBO.getUserMessage());
        requestDTO.setFileUrl(requestBO.getFileUrl());
        requestDTO.setModel(requestBO.getModel());
        requestDTO.setRespMaxToken(requestBO.getRespMaxToken());

        InputStream inputStream = aiServiceClient.singlechatstream(tenantCode, requestDTO);
        return inputStream;
    }

    @Override
    public AiFileContentAndTitleRespBO extractFileContentAndTitle(String tenantCode, Long userId, String presupposeText,
            File file) {
        Assert.notNull(file, "文件不能为空");

        ResultModel<AiFileContentAndTitleRespDTO> resultModel = aiServiceClient
                .extractFileContentAndTitle(tenantCode, file, presupposeText, userId, AiChatKimiConst.MOONSHOT_32K,
                        Const.FIFTEEN_THOUSAND);
        if (!resultModel.isSuccess()) {
            LOGGER.error("调用ai对话服务，提取文件内容和标题失败! tenantCode:{},,, userId:{},,,,fileName:{},,,resultModel:{}", tenantCode,
                    userId, file.getName(), JSONUtil.toJsonStr(resultModel));
            AiChatErrorCodeEnum aiChatErrorCodeEnum = AiChatErrorCodeEnum.parse(resultModel.getCode());
            if (Objects.nonNull(aiChatErrorCodeEnum)) {
                throw BusinessServiceException.getInstance(resultModel.getCode(), resultModel.getMessage());
            }
            throw BusinessServiceException.getInstance("服务器开小差了，请稍后再试");
        }
        AiFileContentAndTitleRespBO respBO = new AiFileContentAndTitleRespBO();
        respBO.setTitle(resultModel.getDataResult().getTitle());
        respBO.setContent(resultModel.getDataResult().getContent());
        return respBO;
    }

    @Override
    public InputStream multiChatFinalStream(String tenantCode, AigcMultiChatRequestBO requestBO) {
        Assert.notNull(requestBO.getUserId(), "用户id不能为空");
        Assert.notEmpty(requestBO.getMessages(), "消息列表不能为空");

        AiMultiChatRequestDTO requestDTO = new AiMultiChatRequestDTO();
        requestDTO.setUserId(requestBO.getUserId());
        requestDTO.setModel(requestBO.getModel());
        requestDTO.setRespMaxToken(requestBO.getRespMaxToken());
        requestDTO.setMessages(requestBO.getMessages().stream().map(bo -> {
            AiMultiChatMessageDTO dto = new AiMultiChatMessageDTO();
            dto.setText(bo.getText());
            return dto;
        }).collect(Collectors.toList()));

        return aiServiceClient.multiChatFinalStream(tenantCode, requestDTO);
    }
}
