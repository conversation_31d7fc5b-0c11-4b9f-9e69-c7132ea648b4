package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobCallbackManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobMsgBO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 视频创建的消息消费者
 */
@Component
@Slf4j
public class ProduceJobCreatedConsumer {

    @Autowired
    private TenantInfoManager tenantInfoManager;

    @Autowired
    private AccountTenantTrialManager accountTenantTrialManager;

    @Resource
    private VisualProduceJobCallbackManager visualProduceJobCallbackManager;

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    @Resource
    private RedisUtil redisUtil;

    private static final String ADD_WITHHOLD_KEY = "add_withhold_key:";

    /**
     * 1天
     */
    private static final Long LOCK_TIME = 24 * 60 * 60L;

    private String genKey(Long jobId) {
        return ADD_WITHHOLD_KEY + jobId;
    }

    @StreamListener("addwithholdconsumer")
    public void consume(@Payload ProduceJobMsgBO input) {
        log.info("收到视频创建消息，input:{}", JSONUtil.toJsonStr(input));

        //预扣
        this.addWithHold(input);

        //回调第三方
        this.callbackThird(input);
    }

    private void addWithHold(ProduceJobMsgBO input) {
        try {
            String lockKey = genKey(input.getJobId());
            if (!redisUtil.tryLockAndSetTimeout(lockKey, LOCK_TIME)) {
                log.warn("已处理过该作品的预扣！jobId:{}", input.getJobId());
                return;
            }

            //判断作品类型
            VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                    .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                            .eq(VisualProduceJobExtendPO::getProduceJobId, input.getJobId()));
            //若是数据图表作品，则不涉及试用账户余额
            if (Objects.nonNull(jobExtendPO) && JobTypeEnum.DATA_CHART.getType().equals(jobExtendPO.getType())) {
                return;
            }

            log.info("准备进行预扣处理，input:{}", JSONUtil.toJsonStr(input));
            AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfoFromCache(input.getTenantCode());
            if (Objects.isNull(tenantInfo)) {
                log.error("该视频对应的租户不存在:job:{}", JSONUtil.toJsonStr(input));
                return;
            }
            //非试用租户，不需要对预试用额度进行扣除
            if (!Const.ONE.equals(tenantInfo.getIsTrial())) {
                return;
            }
            AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager
                    .getOne(Wrappers.lambdaQuery(AccountTenantTrialPO.class)
                            .eq(AccountTenantTrialPO::getTenantCode, input.getTenantCode())
                            .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
            if (Objects.isNull(tenantTrialAccount)) {
                log.warn("该视频的租户未找到试用账户,jobId:{},tenantCode:{}", input.getJobId(), input.getTenantCode());
                return;
            }

            tenantTrialAccount.setWithhold(tenantTrialAccount.getWithhold() + 1);
            accountTenantTrialManager.updateById(tenantTrialAccount);
            log.info("试用账户预扣成功，jobId:{},tenantCode:{}", input.getJobId(), input.getTenantCode());
        } catch (Exception e) {
            log.error("处理增加账户预扣失败，jobId:{},tenantCode:{}", input.getJobId(), input.getTenantCode());
        }
    }

    private void callbackThird(ProduceJobMsgBO input) {
        try {
            //回调第三方
            visualProduceJobCallbackManager.callbackThird(input.getJobId(), input.getTenantCode(), null);
        } catch (Exception e) {
            log.error("视频创建的消费者内，回调第三方 发生异常! input:{},e:{}", JSONUtil.toJsonStr(input), e);
        }
    }
}
