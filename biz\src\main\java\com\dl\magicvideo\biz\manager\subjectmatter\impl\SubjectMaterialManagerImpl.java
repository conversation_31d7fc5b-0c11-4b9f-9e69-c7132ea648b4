package com.dl.magicvideo.biz.manager.subjectmatter.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.subjectmatter.SubjectMaterialMapper;
import com.dl.magicvideo.biz.dal.subjectmatter.param.RandSubjectMaterialParam;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMaterialManager;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMaterialAddBO;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SubjectMaterialManagerImpl extends ServiceImpl<SubjectMaterialMapper, SubjectMaterialPO> implements SubjectMaterialManager {
    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    public void batchAdd(Long rootId, List<SubjectMaterialAddBO> materialAddBOS) {
        remove(Wrappers.lambdaQuery(SubjectMaterialPO.class).eq(SubjectMaterialPO::getMatterId, rootId));
        if (CollectionUtils.isEmpty(materialAddBOS)) {
            return;
        }
        //根据素材类型获取数据
        List<SubjectMaterialPO> subjectMaterialPOList = materialAddBOS.stream().map(bo -> {
            SubjectMaterialPO po = new SubjectMaterialPO();
            //设置基本属性
            po.setBizId(hostTimeIdg.generateId().longValue());
            po.setMatterId(rootId);
            po.setUrl(bo.getUrl());
            po.setMaterialType(bo.getMaterialType());

            po.setCreateBy(bo.getOperatorId());
            po.setCreateDt(new Date());
            po.setCreatorName(bo.getOperatorName());
            po.setModifyBy(bo.getOperatorId());
            po.setModifyDt(new Date());
            po.setModifyName(bo.getOperatorName());
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(bo.getUrl());
            try {
                grabber.start();
                //获取分辨率
                int imageHeight = grabber.getImageHeight();
                int imageWidth = grabber.getImageWidth();
                po.setResolution(imageWidth + "*" + imageHeight);
                //根据素材类型设置特有属性
                if (Objects.equals(bo.getMaterialType(), Const.THREE)) {
                    //获取时长(毫秒)
                    po.setDuration(grabber.getLengthInTime() / 1000);
                }
                grabber.stop();
            } catch (FFmpegFrameGrabber.Exception e) {
                log.error("获取素材属性失败：bizId={},url={}", po.getBizId(), po.getUrl(), e);
            }
            return po;
        }).collect(Collectors.toList());

        saveBatch(subjectMaterialPOList);
    }

    @Override
    public List<SubjectMaterialPO> randomMaterial(RandSubjectMaterialParam param) {
        return this.getBaseMapper().randomMaterial(param);
    }
}
