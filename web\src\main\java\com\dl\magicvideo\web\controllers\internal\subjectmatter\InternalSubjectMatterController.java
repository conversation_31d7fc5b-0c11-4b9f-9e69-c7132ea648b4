package com.dl.magicvideo.web.controllers.internal.subjectmatter;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.param.*;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.vo.InternalSubjectMatterDetailVO;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.vo.InternalSubjectMatterEditVO;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.vo.InternalSubjectMatterVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 内部-题材控制器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 14:24
 */
@RestController
@RequestMapping("/visual/internal/subjectmatter")
public class InternalSubjectMatterController {

    @Resource
    private InternalSubjectMatterProcess internalSubjectMatterProcess;

    @PostMapping("/save")
    public ResultModel<Long> save(@RequestBody @Validated InternalSubjectMatterSaveParam param) {
        return internalSubjectMatterProcess.save(param);
    }

    @GetMapping("/detail")
    public ResultModel<InternalSubjectMatterDetailVO> detail(@RequestParam Long bizId) {
        return internalSubjectMatterProcess.detail(bizId);
    }

    @PostMapping("/page")
    public ResultPageModel<InternalSubjectMatterVO> page(@RequestBody InternalSubjectMatterPageParam pageParam) {
        return internalSubjectMatterProcess.page(pageParam);
    }

    @PostMapping("/delete")
    public ResultModel<Void> delete(@RequestBody @Validated InternalSubjectMatterDeleteParam param) {
        return internalSubjectMatterProcess.delete(param);
    }

    @GetMapping("/listsons")
    public ResultModel<List<InternalSubjectMatterVO>> listSons(@RequestParam Long parentId) {
        return internalSubjectMatterProcess.listSons(parentId);
    }

    @PostMapping("/add")
    public ResultModel<Long> add(@RequestBody @Validated InternalSubjectAddParam param) {
        return internalSubjectMatterProcess.add(param);
    }

    @GetMapping("/edit")
    public ResultModel<InternalSubjectMatterEditVO> edit(@RequestParam Long bizId) {
        return internalSubjectMatterProcess.edit(bizId);
    }

    @PostMapping("/deletesubject")
    public ResultModel<Void> deleteSubject(@RequestBody @Validated InternalSubjectDeleteParam param) {
        return internalSubjectMatterProcess.deleteSubject(param);
    }
}
