package com.dl.magicvideo.web.controllers.account.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

@Data
public class AccountTenantTrialVO {

    @TableField("tenant_code")
    private String tenantCode;

    /**
     *
     * 剩余使用次数
     */
    @TableField("balance")
    private Integer balance;

    /**
     *
     * 预扣次数
     */
    @TableField("withhold")
    private Integer withhold;

    /**
     *
     * 是否删除 0否 1是
     */
    @TableField("is_deleted")
    private Integer isDeleted;
}
