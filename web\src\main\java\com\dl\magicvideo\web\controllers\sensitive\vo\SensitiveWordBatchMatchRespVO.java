package com.dl.magicvideo.web.controllers.sensitive.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-12 19:16
 */
@Data
@ApiModel("敏感词批量匹配响应")
public class SensitiveWordBatchMatchRespVO {

    @ApiModelProperty("敏感词键值对，key-入参中的自定义key，value-敏感词列表，若文字中没有敏感词则列表为空")
    private Map<String, List<String>> sensitiveWordMap;
}
