package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @describe: CopyTemplateParam
 * @author: zhousx
 * @date: 2023/6/21 11:46
 */
@Data
public class CopyTemplateParam {
    @ApiModelProperty(value = "0-从系统模板复制 1-直接复制", required = true)
    @NotNull
    private Integer type;

    /**
     * 来源 0，普通复制， 1 PPT复制
     */
    private Integer source;

    @ApiModelProperty(value = "模板id", required = true)
    @NotBlank
    private String sourceTemplateId;
}
