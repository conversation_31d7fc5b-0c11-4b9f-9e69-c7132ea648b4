package com.dl.magicvideo.biz.manager.visual.dto;

import com.dl.magicvideo.biz.manager.visual.vo.ValueFormVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WeeklyReviewDTO {
    @JsonProperty("v1-text")
    private String v1Text;
    @JsonProperty("v1-text2")
    private String v1Text2;
    @JsonProperty("v1-text3")
    private String v1Text3;
    @JsonProperty("v2-form")
    private List<List<String>> v2Form;
    @JsonProperty("v2-tts")
    private String v2Tts;
    @JsonProperty("v3-form")
    private List<List<String>> v3Form;
    @JsonProperty("v3-tts")
    private String v3Tts;
    @JsonProperty("v4-form")
    private List<List<String>> v4Form;
    @JsonProperty("v4-tts")
    private String v4Tts;
    @JsonProperty("v5-form")
    private List<List<String>> v5Form;
    @JsonProperty("v5-tts")
    private String v5Tts;
    @JsonProperty("v5-flipper")
    private String v5Flipper;
    @JsonProperty("v5-flipper2")
    private String v5Flipper2;
    @JsonProperty("v6-text")
    private String v6Text;
    @JsonProperty("v6-dtTts")
    private String v6DtTts;
    @JsonProperty("v7-form")
    private List<List<String>> v7Form;
    @JsonProperty("v7-dtTts")
    private String v7DtTts;
    @JsonProperty("v7-flipper")
    private String v7Flipper;
    @JsonProperty("v7-2-form")
    private List<List<String>> v7Form2;

    @JsonProperty("v7-2-dtTts")
    private String v7DtTts2;
    @JsonProperty("v7-2-flipper")
    private String v7Flipper2;

    @JsonProperty("v8-text")
    private String v8Text;

    @JsonProperty("v8-dtTts")
    private String v8DtTts;

    @JsonProperty("v9-form")
    private List<List<String>> v9Form;

    @JsonProperty("v9-tts")
    private String v9Tts;

    @JsonProperty("v10-text")
    private String v10Text;

    @JsonProperty("v10-text2")
    private String v10Text2;

    @JsonProperty("v10-tts")
    private String v10Tts;

    @JsonProperty("v11-form")
    private List<List<String>> v11Form;

    @JsonProperty("v11-dtTts")
    private String v11DtTts;

    @JsonProperty("v12-form")
    private List<List<String>> v12Form;

    @JsonProperty("v12-dtTts")
    private String v12DtTts;

    @JsonProperty("v13-form")
    private ValueFormVO v13Form;

    @JsonProperty("v13-form2")
    private ValueFormVO v13Form2;

    @JsonProperty("v13-form3")
    private ValueFormVO v13Form3;

    @JsonProperty("v13-form4")
    private ValueFormVO v13Form4;

    @JsonProperty("v13-form5")
    private ValueFormVO v13Form5;

    @JsonProperty("v13-form6")
    private ValueFormVO v13Form6;

    @JsonProperty("v13-tts")
    private String v13Tts;

    @JsonProperty("v14-tts")
    private String v14Tts;

    @JsonProperty("v14-form")
    private ValueFormVO v14Form;

    @JsonProperty("v14-form2")
    private ValueFormVO v14Form2;

    @JsonProperty("v15-dtTts")
    private String v15DtTts;

    @JsonProperty("v15-form")
    private ValueFormVO v15Form;

    @JsonProperty("v15-form2")
    private ValueFormVO v15Form2;

    @JsonProperty("v15-form3")
    private ValueFormVO v15Form3;

    @JsonProperty("v16-form")
    private List<List<String>> v16Form;

    @JsonProperty("v16-dtTts")
    private String v16DtTts;

    @JsonProperty("v17-dtTts")
    private String v17DtTts;

    @JsonProperty("v17-flipper")
    private String v17Flipper;

    @JsonProperty("v17-flipper2")
    private String v17Flipper2;

    @JsonProperty("v19-text")
    private String v19Text;

    @JsonProperty("v19-dtTts")
    private String v19DtTts;
}
