package com.dl.magicvideo.web.controllers.template.convert;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.manager.visual.bo.CardBO;
import com.dl.magicvideo.biz.manager.visual.bo.DynamicNodeBO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualTemplateBO;
import com.dl.magicvideo.biz.manager.visual.dto.CrossClipsDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO;
import com.dl.magicvideo.biz.manager.visual.dto.LightEditConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateLightEditConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualCardDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.web.controllers.template.param.CardUpdateParam;
import com.dl.magicvideo.web.controllers.template.param.VisualTemplateAddIntegrityParam;
import com.dl.magicvideo.web.controllers.template.vo.CardVO;
import com.dl.magicvideo.web.controllers.template.vo.DynamicNodeVO;
import com.dl.magicvideo.web.controllers.template.vo.VisualTemplateVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 17:54
 */
public class VisualTemplateConvert {

    public static VisualTemplateBO cnvVisualTemplateAddIntegrityParam2BO(VisualTemplateAddIntegrityParam param) {
        VisualTemplateBO bo = new VisualTemplateBO();
        bo.setName(param.getName());
        bo.setCoverUrl(param.getCoverUrl());
        bo.setResolution(param.getResolution());
        bo.setResolutionType(param.getResolutionType());
        bo.setBgMusic(param.getBgMusic());
        bo.setBgMusicParam(param.getBgMusicParam());
        bo.setTtsParam(param.getTtsParam());
        bo.setReplaceData(param.getReplaceData());
        bo.setApiData(param.getApiDataList());
        bo.setDuration(param.getDuration());

        if (CollectionUtils.isEmpty(param.getCards())) {
            return bo;
        }

        bo.setCards(param.getCards().stream().map(cardParam -> {
            CardBO cardBO = new CardBO();
            cardBO.setName(cardParam.getName());

            cardBO.setCoverUrl(cardParam.getCoverUrl());
            cardBO.setResolution(cardParam.getResolution());
            cardBO.setRenderData(cardParam.getRenderData());
            cardBO.setDuration(cardBO.getDuration());
            buildLightEditConfig(cardParam, cardBO);
            if (CollectionUtils.isNotEmpty(cardParam.getDynamicNodes())) {
                cardBO.setDynamicNodes(cardParam.getDynamicNodes().stream().map(dynamicNodeParam -> {
                    DynamicNodeBO dynamicNodeBO = new DynamicNodeBO();
                    dynamicNodeBO.setType(dynamicNodeParam.getId());
                    dynamicNodeBO.setIsEnabled(dynamicNodeParam.getIsEnabled());
                    dynamicNodeBO.setDuration(dynamicNodeParam.getDuration());
                    dynamicNodeBO.setExpression(dynamicNodeParam.getExpression());
                    dynamicNodeBO.setExpressionFlag(dynamicNodeParam.getExpressionFlag());
                    if (CollectionUtils.isNotEmpty(dynamicNodeParam.getTtsList())) {
                        dynamicNodeBO.setTtsList(dynamicNodeParam.getTtsList().stream().map(ttsConfigParam -> {
                            DynamicNodeBO.TtsConfigBO ttsConfigBO = new DynamicNodeBO.TtsConfigBO();
                            ttsConfigBO.setTtsId(ttsConfigParam.getTtsId());
                            ttsConfigBO.setContent(ttsConfigParam.getContent());
                            ttsConfigBO.setStart(ttsConfigParam.getStart());
                            ttsConfigBO.setEnableSubtitle(ttsConfigParam.getEnableSubtitle());
                            ttsConfigBO.setMaxLength(ttsConfigParam.getMaxLength());
                            ttsConfigBO.setHide(ttsConfigParam.isHide());
                            ttsConfigBO.setEndDelay(ttsConfigParam.getEndDelay());
                            return ttsConfigBO;
                        }).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(dynamicNodeParam.getDmList())) {
                        dynamicNodeBO.setDmList(dynamicNodeParam.getDmList().stream().map(dmConfigParam -> {
                            DynamicNodeBO.DigitalManConfigBO dmConfigBO = new DynamicNodeBO.DigitalManConfigBO();
                            dmConfigBO.setDmId(dmConfigParam.getDmId());
                            dmConfigBO.setContent(dmConfigParam.getContent());
                            dmConfigBO.setStart(dmConfigParam.getStart());
                            dmConfigBO.setEnableSubtitle(dmConfigParam.getEnableSubtitle());
                            dmConfigBO.setMaxLength(dmConfigParam.getMaxLength());
                            dmConfigBO.setHide(dmConfigParam.isHide());
                            dmConfigBO.setEndDelay(dmConfigParam.getEndDelay());
                            return dmConfigBO;
                        }).collect(Collectors.toList()));
                    }
                    return dynamicNodeBO;
                }).collect(Collectors.toList()));
            }
            return cardBO;
        }).collect(Collectors.toList()));

        return bo;
    }

    public static void buildLightEditConfig(CardUpdateParam cardUpdateParam, CardBO cardBO) {
        TemplateLightEditConfigDTO templatelightDTO = new TemplateLightEditConfigDTO();

        if (StringUtils.isNotEmpty(cardUpdateParam.getLightEditConfigs())) {
            templatelightDTO.setLightEditConfigs(
                    JSONUtil.toList(cardUpdateParam.getLightEditConfigs(), LightEditConfigDTO.class));
            cardBO.setLightEditConfigs(JSONUtil.toJsonStr(templatelightDTO));
        }

        if (StringUtils.isNotEmpty(cardUpdateParam.getCrossClips())) {
            templatelightDTO.setCrossClips(JSONUtil.toList(cardUpdateParam.getCrossClips(), CrossClipsDTO.class));
            cardBO.setLightEditConfigs(JSONUtil.toJsonStr(templatelightDTO));
        }
    }


    public static VisualTemplateVO cnvVisualTemplateDTO2VO(VisualTemplateDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        VisualTemplateVO vo = new VisualTemplateVO();
        vo.setTemplateId(dto.getTemplateId() + "");
        vo.setStatus(dto.getStatus());
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setPreviewVideoUrl(dto.getPreviewVideoUrl());
        vo.setShortVideoUrl(dto.getShortVideoUrl());
        vo.setName(dto.getName());
        vo.setResolution(dto.getResolution());
        vo.setResolutionType(dto.getResolutionType());
        vo.setBgMusic(dto.getBgMusic());
        vo.setBgMusicParam(dto.getBgMusicParam());
        vo.setTtsParam(dto.getTtsParam());
        vo.setReplaceData(dto.getReplaceData());
        vo.setDuration(dto.getDuration());
        vo.setCreateDt(dto.getCreateDt());
        vo.setModifyDt(dto.getModifyDt());
        vo.setShareConfState(dto.getShareConfState());
        vo.setIsManager(dto.getIsManager());
        vo.setFirstCategory(dto.getFirstCategory());
        vo.setSecondCategory(dto.getSecondCategory());
        vo.setComponentVersion(dto.getComponentVersion());
        if (CollectionUtils.isNotEmpty(dto.getCards())) {
            vo.setCards(dto.getCards().stream().map(visualCardDTO -> {
                CardVO cardVO = new CardVO();
                cardVO.setCardId(visualCardDTO.getCardId() + "");
                cardVO.setTemplateId(visualCardDTO.getTemplateId() + "");
                cardVO.setName(visualCardDTO.getName());
                cardVO.setCoverUrl(visualCardDTO.getCoverUrl());
                cardVO.setResolution(visualCardDTO.getResolution());
                cardVO.setRenderData(visualCardDTO.getRenderData());
                buildLightEditConfig(visualCardDTO, cardVO);
                if (CollectionUtils.isNotEmpty(visualCardDTO.getDynamicNodes())) {
                    cardVO.setDynamicNodes(visualCardDTO.getDynamicNodes().stream().map(dynamicNodeDTO -> {
                        DynamicNodeVO dynamicNodeVO = new DynamicNodeVO();
                        dynamicNodeVO.setNodeId(dynamicNodeDTO.getNodeId() + "");
                        dynamicNodeVO.setType(dynamicNodeDTO.getType());
                        dynamicNodeVO.setDuration(dynamicNodeDTO.getDuration());
                        dynamicNodeVO.setCoverUrl(dynamicNodeDTO.getCoverUrl());
                        dynamicNodeVO.setIsEnabled(dynamicNodeDTO.getIsEnabled());
                        if (CollectionUtils.isNotEmpty(dynamicNodeDTO.getTtsList())) {
                            dynamicNodeVO.setTtsList(dynamicNodeDTO.getTtsList().stream().map(ttsConfigDTO -> {
                                DynamicNodeVO.TtsConfigVO ttsConfigVO = new DynamicNodeVO.TtsConfigVO();
                                ttsConfigVO.setTtsId(ttsConfigDTO.getTtsId());
                                ttsConfigVO.setContent(ttsConfigDTO.getContent());
                                ttsConfigVO.setStart(ttsConfigDTO.getStart());
                                ttsConfigVO.setEnableSubtitle(ttsConfigDTO.getEnableSubtitle());
                                ttsConfigVO.setMaxLength(ttsConfigDTO.getMaxLength());
                                ttsConfigVO.setSubtitleKeyWordsHighlight(ttsConfigDTO.getSubtitleKeyWordsHighlight());
                                return ttsConfigVO;
                            }).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(dynamicNodeDTO.getDmList())) {
                            dynamicNodeVO.setDmList(dynamicNodeDTO.getDmList().stream().map(ttsConfigDTO -> {
                                DynamicNodeVO.DigitalManConfigVO dmConfigVO = new DynamicNodeVO.DigitalManConfigVO();
                                dmConfigVO.setDmId(ttsConfigDTO.getDmId());
                                dmConfigVO.setContent(ttsConfigDTO.getContent());
                                dmConfigVO.setStart(ttsConfigDTO.getStart());
                                dmConfigVO.setEnableSubtitle(ttsConfigDTO.getEnableSubtitle());
                                dmConfigVO.setMaxLength(ttsConfigDTO.getMaxLength());
                                return dmConfigVO;
                            }).collect(Collectors.toList()));
                        }
                        return dynamicNodeVO;
                    }).collect(Collectors.toList()));
                }
                return cardVO;
            }).collect(Collectors.toList()));
        }
        return vo;
    }

    public static void buildLightEditConfig(VisualCardDTO visualCardDTO, CardVO cardVO) {
        if (StringUtils.isNotEmpty(visualCardDTO.getLightEditConfigs())) {
            TemplateLightEditConfigDTO configDTO = JSONUtil.toBean(visualCardDTO.getLightEditConfigs(), TemplateLightEditConfigDTO.class);
            cardVO.setLightEditConfigs(JSONUtil.toJsonStr(configDTO.getLightEditConfigs()));
            cardVO.setCrossClips(JSONUtil.toJsonStr(configDTO.getCrossClips()));
        }
    }

    public static VisualTemplateVO cnvSimpleVisualTemplateDTO2VO(VisualTemplateDTO dto, Map<Long, Long> templateCollectMap) {
        if (Objects.isNull(dto)) {
            return null;
        }

        VisualTemplateVO vo = new VisualTemplateVO();
        vo.setTemplateId(dto.getTemplateId() + "");
        vo.setStatus(dto.getStatus());
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setPreviewVideoUrl(dto.getPreviewVideoUrl());
        vo.setShortVideoUrl(dto.getShortVideoUrl());
        vo.setName(dto.getName());
        vo.setResolution(dto.getResolution());
        vo.setResolutionType(dto.getResolutionType());
        vo.setBgMusic(dto.getBgMusic());
        vo.setBgMusicParam(dto.getBgMusicParam());
        vo.setTtsParam(dto.getTtsParam());
        vo.setDuration(dto.getDuration());
        vo.setCreateDt(dto.getCreateDt());
        vo.setModifyDt(dto.getModifyDt());
        vo.setShareConfState(dto.getShareConfState());
        vo.setIsManager(dto.getIsManager());
        vo.setFirstCategory(dto.getFirstCategory());
        vo.setSecondCategory(dto.getSecondCategory());
        vo.setIsPPTType(dto.getIsPPT());
        vo.setComponentVersion(dto.getComponentVersion());
        if (templateCollectMap != null && templateCollectMap.containsKey(dto.getTemplateId())) {
            vo.setCollect(true);
        } else {
            vo.setCollect(false);
        }
        return vo;
    }
}
