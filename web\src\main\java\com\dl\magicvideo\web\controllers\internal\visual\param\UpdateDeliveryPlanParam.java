package com.dl.magicvideo.web.controllers.internal.visual.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @describe: UpdateDeliveryPlanParam
 * @author: zhousx
 * @date: 2023/9/7 9:56
 */
@Data
public class UpdateDeliveryPlanParam {
    /**
     * 交付计划ID
     */
    @NotNull
    private Long planId;

    /**
     * 名称
     */
    @NotBlank
    private String name;

    /**
     * 简介
     */
    private String desc;

    /**
     * 负责人
     */
    private String director;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 触发次数，默认-1 代表无限制
     */
    private Integer limit;

    /**
     * 周期，-1-无，0-天，1-周，2-月
     */
    private Integer period;

    private Integer status;

    private Integer isNotify;

    private String notifyUrl;

    private String callbackUrl;

    private Integer produceWay;

    private Integer hasPeriod;
}
