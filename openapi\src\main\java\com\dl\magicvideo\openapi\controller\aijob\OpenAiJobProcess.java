package com.dl.magicvideo.openapi.controller.aijob;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.basicservice.dto.UserProfileDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.aiservice.DaVirtualManManager;
import com.dl.magicvideo.biz.manager.basicservice.AdmUserManager;
import com.dl.magicvideo.biz.manager.statistics.StatisticsAiJobManager;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobPageBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobDTO;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.openapi.controller.OpenAbstractController;
import com.dl.magicvideo.openapi.controller.aijob.convert.OpenVisualAiJobConvert;
import com.dl.magicvideo.openapi.controller.aijob.param.OpenStatisticsAiJobPageParam;
import com.dl.magicvideo.openapi.controller.aijob.param.OpenVisualAiJobPageParam;
import com.dl.magicvideo.openapi.controller.aijob.vo.OpenStatisticsAiJobVO;
import com.dl.magicvideo.openapi.controller.aijob.vo.OpenVisualDmJobInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 16:27
 */
@Component
public class OpenAiJobProcess extends OpenAbstractController {

    @Resource
    private VisualAiJobManager visualAiJobManager;

    @Resource
    private VisualTemplateManager visualTemplateManager;

    @Resource
    private AdmUserManager admUserManager;

    @Resource
    private StatisticsAiJobManager statisticsAiJobManager;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private DaVirtualManManager daVirtualManManager;

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    public ResultPageModel<OpenStatisticsAiJobVO> page(OpenStatisticsAiJobPageParam pageParam, Integer aiJobType) {
        StatisticsAiJobPageBO pageBO = new StatisticsAiJobPageBO();
        pageBO.setTenantCode(operatorUtil.getTenantCode());
        pageBO.setMinDt(pageParam.getMinDt());
        pageBO.setMaxDt(pageParam.getMaxDt());
        pageBO.setAiJobType(aiJobType);
        pageBO.setPageIndex(pageParam.getPageIndex());
        pageBO.setPageSize(pageParam.getPageSize());

        ResponsePageQueryDO<List<StatisticsAiJobDTO>> responseDO = statisticsAiJobManager.pageQuery(pageBO);
        if (CollectionUtils.isEmpty(responseDO.getDataResult())) {
            return pageQueryModel(responseDO, Collections.emptyList());
        }

        return pageQueryModel(responseDO, responseDO.getDataResult().stream().map(input -> {
            OpenStatisticsAiJobVO result = new OpenStatisticsAiJobVO();
            result.setTotalCeilingMinutes(input.getTotalCeilingMinutes());
            result.setStatisticsTime(input.getStatisticsTime());
            return result;
        }).collect(Collectors.toList()));
    }

    public ResultPageModel<OpenVisualDmJobInfoVO> pageDmJob(OpenVisualAiJobPageParam param) {
        VisualAiJobPageBO pageBO = new VisualAiJobPageBO();
        pageBO.setTenantCode(operatorUtil.getTenantCode());
        pageBO.setMinDt(DateUtil.getMinDate(param.getDate()));
        pageBO.setMaxDt(DateUtil.getMaxDate(param.getDate()));
        pageBO.setAiJobType(AiJobTypeE.DIGITAL_MAN.getCode());
        pageBO.setJobStatusList(Collections.singletonList(JobStatusE.SUCCESS.getCode()));
        pageBO.setNeedQueryDeleted(Const.ONE);
        pageBO.setPageIndex(param.getPageIndex());
        pageBO.setPageSize(param.getPageSize());

        ResponsePageQueryDO<List<VisualAiJobExtPO>> responseDO = visualAiJobManager.pageExt(pageBO);
        if (CollectionUtils.isEmpty(responseDO.getDataResult())) {
            return pageQueryModel(responseDO, Collections.emptyList());
        }

        List<VisualAiJobExtPO> dmJobPOList = responseDO.getDataResult();

        //查询模板
        List<VisualTemplatePO> templateList = visualTemplateManager.lambdaQuery().in(VisualTemplatePO::getTemplateId,
                dmJobPOList.stream().map(VisualAiJobExtPO::getTemplateId).distinct().collect(Collectors.toList()))
                .list();
        Map<Long, VisualTemplatePO> templateMap = templateList.stream()
                .collect(Collectors.toMap(VisualTemplatePO::getTemplateId, Function.identity(), (v1, v2) -> v1));

        //提取请求中的数字人信息
        //key-aiJobId
        Map<Long, DigitalManJobBO> dmJobMap = new HashMap<>();
        Set<String> dmSceneIdSet = new HashSet<>();
        Set<Integer> channelSet = new HashSet<>();

        //创建人id
        Set<Long> creatorUserIdSet = new HashSet<>();

        dmJobPOList.stream().forEach(dmJobPO -> {
            if (Objects.isNull(dmJobPO.getCreateBy())) {
                creatorUserIdSet.add(dmJobPO.getCreateBy());
            }

            if (StringUtils.isNotBlank(dmJobPO.getRequestInfo())) {
                DigitalManJobBO digitalManJobBO = JSONUtil.toBean(dmJobPO.getRequestInfo(), DigitalManJobBO.class);
                dmJobMap.put(dmJobPO.getJobId(), digitalManJobBO);
                dmSceneIdSet.add(digitalManJobBO.getSceneId());
                channelSet.add(digitalManJobBO.getChannel());
            }
        });

        List<DaVirtualManScenesDTO> daVirtualManScenesDTOList = daVirtualManManager
                .vmSceneListBySceneIds(operatorUtil.getTenantCode(), Lists.newArrayList(dmSceneIdSet),
                        Lists.newArrayList(channelSet), Const.ZERO);
        //key:channel-sceneId
        Map<String, DaVirtualManScenesDTO> dmSceneMap = daVirtualManScenesDTOList.stream().collect(Collectors
                .toMap((scene -> scene.getChannel() + "-" + scene.getSceneId()), Function.identity(), (s1, s2) -> s1));

        //查询操作人信息
        List<UserProfileDTO> userProfileDTOList = admUserManager
                .listUserProfile(operatorUtil.getTenantCode(), Lists.newArrayList(creatorUserIdSet));
        Map<Long, String> creatorIdNameMap = userProfileDTOList.stream()
                .collect(Collectors.toMap(UserProfileDTO::getUserId, UserProfileDTO::getName, (u1, u2) -> u1));


        //查询外部联系人信息
        List<VisualProduceJobExtendPO> produceJobExtendList = visualProduceJobExtendManager
                .list(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                        .in(VisualProduceJobExtendPO::getProduceJobId,
                                responseDO.getDataResult().stream().filter(Objects::nonNull).map(VisualAiJobExtPO::getProduceJobId)
                                        .collect(Collectors.toSet())));
        Map<Long, VisualProduceJobExtendPO> produceJobExtendMap = produceJobExtendList.stream()
                .collect(Collectors.toMap(VisualProduceJobExtendPO::getProduceJobId, Function.identity()));


        return pageQueryModel(responseDO, dmJobPOList.stream().map(dmJobPO -> OpenVisualAiJobConvert
                .buildVisualDmJobInternalInfoDTO(dmJobPO, templateMap, creatorIdNameMap,
                        dmJobMap.get(dmJobPO.getJobId()), dmSceneMap, produceJobExtendMap))
                .collect(Collectors.toList()));
    }

}
