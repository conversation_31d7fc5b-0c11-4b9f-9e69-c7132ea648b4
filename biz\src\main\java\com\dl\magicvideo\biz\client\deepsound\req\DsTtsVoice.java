package com.dl.magicvideo.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName DsAudioTrainSource
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 18:17
 * @Version 1.0
 **/
@Data
@NoArgsConstructor
public class DsTtsVoice implements Serializable {

    private static final long serialVersionUID = -4213730735414879102L;

    /**
     * 语言类型 支持中英混合，设置为 zh-CN
     */
    @JsonProperty(value = "language", defaultValue = "zh-CN")
    private String language = "zh-CN";

    /**
     * 发言人 训练成功的音色名
     */
    @JsonProperty("voice-name")
    private String voiceName;

}
