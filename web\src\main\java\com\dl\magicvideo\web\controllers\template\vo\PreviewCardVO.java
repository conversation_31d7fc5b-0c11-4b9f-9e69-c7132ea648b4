package com.dl.magicvideo.web.controllers.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: PreviewCardVO
 * @author: zhousx
 * @date: 2023/4/26 11:12
 */
@Data
public class PreviewCardVO {
    @ApiModelProperty("卡片id")
    private String cardId;

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("卡片名称")
    private String name;

    @ApiModelProperty("卡片封面")
    private String coverUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("渲染数据")
    private String renderData;

    @ApiModelProperty("tts数据")
    private List<TTSVO> ttsData;

    @ApiModelProperty("数字人数据")
    private List<DigitalManVO> dmData;

    @ApiModelProperty("数据表格")
    private List<DataSheetVO> dataSheetData;

    @ApiModelProperty("轻编辑配置")
    private String lightEditConfigs;

    @ApiModelProperty("跨片段配置")
    private String crossClips;

    @ApiModelProperty("需要隐藏的片段type集合")
    private List<String> hideNodeTypeList;

    @Data
    public static class TTSVO {
        @ApiModelProperty("id")
        private String id;

        @ApiModelProperty("类型")
        private Integer type;

        @ApiModelProperty("片段延迟进场时间")
        private Long start;

        @ApiModelProperty("语音开始时间，以整个模板时间轴为准")
        private Long realStart;

        @ApiModelProperty("语音时长")
        private Long duration;

        @ApiModelProperty("文本")
        private String text;

        @ApiModelProperty("音频地址")
        private String url;

        @ApiModelProperty("字幕")
        private List<SubtitleVO> subtitles;
    }

    @Data
    public static class DigitalManVO {
        @ApiModelProperty("id")
        private String id;

        @ApiModelProperty("类型")
        private Integer type;

        @ApiModelProperty("片段延迟进场时间")
        private Long start;

        @ApiModelProperty("视频开始时间，以整个模板时间轴为准")
        private Long realStart;

        @ApiModelProperty("视频时长")
        private Long duration;

        @ApiModelProperty("文本")
        private String text;

        @ApiModelProperty("视频地址")
        private String url;

        @ApiModelProperty("字幕")
        private List<SubtitleVO> subtitles;

        /**
         * 前端根据该字段是否有值判断数字人播放逻辑
         * 若为Long则在经过框架时null会转为0，故用string
         */
        @ApiModelProperty("在整个数字人视频中的开始时间")
        private String startInDmVideo;
    }

    @Data
    public static class SubtitleVO {
        @ApiModelProperty("字幕")
        private String text;

        @ApiModelProperty("起始时间，毫秒")
        private Long beginTime;

        @ApiModelProperty("结束时间，毫秒")
        private Long endTime;
    }

    @Data
    public static class DataSheetVO {
        @ApiModelProperty("入场延迟")
        private Long start;

        @ApiModelProperty("id")
        private String dataSheetId;

        @ApiModelProperty("时长")
        private Long duration;

        @ApiModelProperty("是否隐藏")
        private boolean hide;

        @ApiModelProperty("开始时间")
        private Long realStart;

        @ApiModelProperty("入场延迟")
        private Long endDelay;
    }
}
