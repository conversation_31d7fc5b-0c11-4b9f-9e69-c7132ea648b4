package com.dl.magicvideo.biz.common.util;

import com.aspose.slides.*;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * aspose的转换方式
 */
@Slf4j
public class PPT2Util {


    private static InputStream license;
    private static InputStream slides;

    /**
     * 获取license
     *
     * @return
     */
    public static boolean getLicense() {
        boolean result = false;
        try {
            license = PPT2Util.class.getClassLoader().getResourceAsStream("license.xml");// license路径
            if (Objects.nonNull(license)) {
                License aposeLic = new License();
                aposeLic.setLicense(license);
                result = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("license:" + result);
        return result;
    }

    /**
     * 缩略图方式
     */
    public static List<File> pptx2Png(File pptxFile){
        List<File> pngFileList = new ArrayList<>();
        // 验证License
        if (!getLicense()) {
            return pngFileList;
        }

        // 加载 PPT 文件
        Presentation presentation = new Presentation(pptxFile.getPath());

        // 遍历每个幻灯片
        for (int i = 0; i < presentation.getSlides().size(); i++) {
            // 创建缩略图选项
            int slideIndex = i;
            float scale = 1f;

            // 获取当前幻灯片
            ISlide slide = presentation.getSlides().get_Item(slideIndex);

            // 创建缩略图
            BufferedImage image = slide.getThumbnail(scale, scale);

            // 将缩略图保存为文件
            try {
                String outputFileName = pptxFile.getPath().replace(".pptx", String.format("-%04d.jpg", slideIndex + 1));
                File pngFile = new File(outputFileName);
                ImageIO.write(image, "png", pngFile);
                pngFileList.add(pngFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        // 关闭 PPT 文件
        presentation.dispose();
        return pngFileList;
    }

    /**
     *
     * @param args
     */
    public static void main(String[] args) {
        List<File> files = PPT2Util.pptx2Png(new File("C:\\Users\\<USER>\\Downloads\\区块链.pptx"));
        for (File file : files){
            System.out.println(file.getPath());
        }
    }
}
