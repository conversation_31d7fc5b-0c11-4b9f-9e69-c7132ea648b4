package com.dl.magicvideo.biz.dal.subjectmatter.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:19
 */
@Data
@TableName("subject_matter")
public class SubjectMatterPO extends BasePO {
    private static final long serialVersionUID = 7500147115645563262L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 题材名称
     */
    private String name;

    /**
     * 标题名称
     */
    private String title;

    /**
     * 图片链接地址
     */
    private String imgUrl;

    /**
     * XMind文件地址
     */
    private String xmindUrl;

    /**
     * XMind文件json
     */
    private String xmindJson;

    /**
     * 题材路径
     */
    private String subjectPath;

    /**
     * 题材简介
     */
    private String intro;

    /**
     * prompt
     */
    private String prompt;

    /**
     * 题材类型，1-图表题材库，2-题材库
     */
    private Integer type;

    /**
     * 题材根id
     */
    private Long rootId;

    /**
     * excel链接地址
     */
    private String excelUrl;

    /**
     * 题材级别，1-一级题材，2-二级题材，3-三级题材，4-四级题材
     */
    private Integer level;

    /**
     * 父级题材bizId
     */
    private Long parentId;

    /**
     * 是否有子级，0-否，1-是
     */
    @TableField("is_have_child")
    private Integer isHaveChild;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer isDeleted;

    /**
     * json文件地址
     */
    @TableField("json_url")
    private String jsonUrl;
}
