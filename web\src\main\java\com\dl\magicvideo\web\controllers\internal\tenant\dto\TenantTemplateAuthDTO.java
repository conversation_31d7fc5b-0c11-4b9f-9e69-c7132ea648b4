package com.dl.magicvideo.web.controllers.internal.tenant.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * TenantTemplateAuthVO
 */
@Data
public class TenantTemplateAuthDTO {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("状态 0-启用 1-禁用")
    private Integer status;

}
