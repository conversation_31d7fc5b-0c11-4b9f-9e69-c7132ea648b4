package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @describe: ClipAddParam
 * @author: zhousx
 * @date: 2023/2/7 17:43
 */
@Data
public class CardAddParam {
    @ApiModelProperty("卡片名称")
    @NotBlank
    private String name;

    @ApiModelProperty("模板id")
    @NotBlank
    private String templateId;

    @ApiModelProperty("卡片封面")
    private String coverUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("渲染数据")
    private String renderData;
}
