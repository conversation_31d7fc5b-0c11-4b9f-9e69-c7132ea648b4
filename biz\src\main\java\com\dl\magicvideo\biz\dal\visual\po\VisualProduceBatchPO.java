package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

/**
 * 作业批次表
 * @TableName visual_produce_batch
 */
@TableName(value ="visual_produce_batch")
@Data
public class VisualProduceBatchPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 批次名称
     */
    private String batchName;

    /**
     * 任务状态：-1-未开始 0-排队中 1-生产中 2-生产完成 3-生产异常 4-已取消
     */
    private Integer status;

    /**
     * 批次下中作业数
     */
    private Integer jobNumTotal;

    /**
     * 批次下已完成作业数
     */
    private Integer jobNumSuccess;

    /**
     * 租户id
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 生产人名称
     */
    private String creatorName;

    private Long planId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}