package com.dl.magicvideo.openapi.config;
import com.dl.magicvideo.openapi.filter.OpenApiFilter;
import com.dl.magicvideo.openapi.interceptor.OpenApiAuthenticationInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Autowired
    OpenApiAuthenticationInterceptor openApiAuthenticationInterceptor;

    @Bean
    public FilterRegistrationBean opanApiFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new OpenApiFilter());
        registration.addUrlPatterns("/openapi/*");
        registration.setName("openapifilter");
        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
        return registration;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(openApiAuthenticationInterceptor);
    }
}
