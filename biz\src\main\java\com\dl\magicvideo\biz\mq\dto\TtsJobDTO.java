package com.dl.magicvideo.biz.mq.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-03 14:54
 */
@Data
public class TtsJobDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 视频合成任务id
     */
    private Long produceJobId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * aiservice任务id
     */
    private Long aiJobId;

    /**
     * 片段配置id
     */
    private String configId;

    /**
     * 1-数字人，2-TTS,  3-数字人的tts
     * @see: com.dl.magicvideo.biz.common.enums.AiJobTypeE
     */
    private Integer jobType;

    /**
     * 0-未执行，1-执行中，2-执行成功，3-执行失败
     */
    private Integer jobStatus;

    /**
     * 任务执行结果
     */
    private String mediaInfo;

    /**
     * 字幕
     */
    private String subtitleInfo;

    private String requestInfo;

    private String responseInfo;

    private Date createDt;

    private Long createBy;

    private Date modifyDt;

    private Long duration;

}
