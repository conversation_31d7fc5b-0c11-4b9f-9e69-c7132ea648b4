package com.dl.magicvideo.web.controllers.template.param;

import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.manager.visual.dto.InterfaceDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * @describe: ProduceParam
 * @author: zhousx
 * @date: 2023/6/25 10:07
 */
@Data
public class BatchProduceNewParam {
    @ApiModelProperty(value = "模板id", required = true)
    @NotBlank
    private String templateId;

    @ApiModelProperty("标题")
    @NotBlank
    private String title;

    /**
     * 1.为数据接口  batchParams不能为空
     * 2.数字人替换  digitalManParam不能为空
     */
    @NotNull
    private Integer type;

    private List<DigitalManParam> digitalManParams;

    /**
     * list为接口名称 +  参数值（按顺序给就好了）
     */
    private List<BatchParamDTO> batchParams;

    public static void main(String[] args) {
        BatchProduceNewParam param = new BatchProduceNewParam();
        param.setTitle("111");
        param.setTemplateId("222");
        List<BatchParamDTO> batchParams = new ArrayList<>();
        BatchParamDTO batchParamDTO = new BatchParamDTO();
        List<InterfaceDTO> list = new ArrayList<>();
        InterfaceDTO dto = new InterfaceDTO();
        dto.setName("/common/manager/detail/03c838");
        Map<String, Object> keyMap = new HashMap<>();
        keyMap.put("code","03c838");
        dto.setKeyMap(keyMap);
        list.add(dto);
        batchParamDTO.setList(list);
        batchParams.add(batchParamDTO);
        param.setBatchParams(batchParams);
        System.out.println(JsonUtils.toJSON(param));
    }
}
