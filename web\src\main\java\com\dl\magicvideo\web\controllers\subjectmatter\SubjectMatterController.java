package com.dl.magicvideo.web.controllers.subjectmatter;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectMaterialParam;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectMatterAiMatchParam;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectMatterQueryParam;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectNameParam;
import com.dl.magicvideo.web.controllers.subjectmatter.vo.SubjectMaterialVO;
import com.dl.magicvideo.web.controllers.subjectmatter.vo.SubjectMatterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-01 13:55
 */
@RestController
@Api("题材控制器")
@RequestMapping("/visual/subjectmatter")
public class SubjectMatterController {

    @Resource
    private SubjectMatterProcess subjectMatterProcess;

    @PostMapping("/latest")
    @ApiOperation("查询最新的N条题材")
    public ResultModel<List<SubjectMatterVO>> latest(@RequestBody @Validated SubjectMatterQueryParam pageParam) {
        return subjectMatterProcess.latest(pageParam);
    }

    @PostMapping("/findMaterialBySubjectId")
    @ApiOperation("根据一级题材ID随机查询素材")
    public ResultModel<List<SubjectMaterialVO>> findMaterialBySubjectId(@RequestBody @Validated SubjectMaterialParam param) {
        return subjectMatterProcess.findMaterialBySubjectId(param);
    }

    @PostMapping("/findSubjectMatterByName")
    @ApiOperation("根据一级题材名称查询一级题材")
    public ResultModel<List<SubjectMatterVO>> findSubjectMatterByName(@RequestBody @Validated SubjectNameParam param) {
        return subjectMatterProcess.findSubjectMatterByName(param);
    }

    @PostMapping("/aimatch")
    @ApiOperation("根据文案由ai智能匹配一级题材")
    public ResultModel<SubjectMatterVO> aiMatch(@RequestBody @Validated SubjectMatterAiMatchParam param){
        return subjectMatterProcess.aiMatch(param);
    }
}
