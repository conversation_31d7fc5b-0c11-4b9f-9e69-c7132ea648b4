package com.dl.magicvideo.web.controllers.internal.music.convert;

import com.dl.magicvideo.biz.dal.music.po.BackgroundMusicPO;
import com.dl.magicvideo.biz.manager.music.dto.BackgroundMusicDTO;
import com.dl.magicvideo.web.controllers.internal.music.vo.BackgroundMusicInternalVO;

import java.util.Objects;

public class BackgroundMusicInternalConvert {
    public static BackgroundMusicInternalVO cnvBackgroundMusicDTO2InternalVO(BackgroundMusicDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        BackgroundMusicInternalVO result = new BackgroundMusicInternalVO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setType(input.getType());
        result.setDuration(input.getDuration());
        result.setUrl(input.getUrl());
        return result;
    }

    public static BackgroundMusicInternalVO cnvBackgroundMusicPO2InternalVO(BackgroundMusicPO input){
        if (Objects.isNull(input)){
            return null;
        }
        BackgroundMusicInternalVO result = new BackgroundMusicInternalVO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setType(input.getType());
        result.setDuration(input.getDuration());
        result.setUrl(input.getUrl());
        result.setUserId(input.getCreateBy());
        result.setCreateDt(input.getCreateDt());
        return result;
    }
}
