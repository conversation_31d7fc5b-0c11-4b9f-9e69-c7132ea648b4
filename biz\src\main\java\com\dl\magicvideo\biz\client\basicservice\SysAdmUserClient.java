package com.dl.magicvideo.biz.client.basicservice;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmUserExtDTO;
import com.dl.magicvideo.biz.client.basicservice.dto.UserProfileDTO;
import com.dl.magicvideo.biz.client.basicservice.intercepter.BasicServiceInterceptor;
import com.dl.magicvideo.biz.client.basicservice.param.AdmUserProfileListParam;
import com.dl.magicvideo.biz.client.basicservice.param.TokenParamDTO;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 10:25
 */
@BaseRequest(interceptor = BasicServiceInterceptor.class)
public interface SysAdmUserClient {

    @Post("/internal/auth/checktoken")
    ResultModel<AdmUserExtDTO> checkToken(@JSONBody TokenParamDTO paramDTO);

    @Post("/internal/admusers/userprofile/list")
    ResultModel<List<UserProfileDTO>> userProfileList(@JSONBody AdmUserProfileListParam param);

}
