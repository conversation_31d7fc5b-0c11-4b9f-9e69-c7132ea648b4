package com.dl.magicvideo.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobMsgDTO;
import com.dl.magicvideo.biz.mq.DlChannels;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * 视频创建的消息生产者
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-09 09:41
 */
@Component
public class ProduceJobCreatedProducer {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProduceJobCreatedProducer.class);

    @Autowired
    private DlChannels dlChannels;

    public void sendProduceJobCreatedMsg(VisualProduceJobPO jobPO) {
        LOGGER.info("准备发送视频创建的消息 jobId = {}", jobPO.getJobId());
        VisualProduceJobMsgDTO target = new VisualProduceJobMsgDTO();
        BeanUtils.copyProperties(jobPO, target);
        // Kafka不支持延时消息，直接发送
        Message message = MessageBuilder.withPayload(target).build();
        try {
            boolean resp = dlChannels.visualproduceJobready().send(message, 1000L);
            LOGGER.info("发送视频创建的消息,message:{},sendResult:{}", JSONUtil.toJsonStr(message), resp);
        } catch (Exception e) {
            LOGGER.error("发送视频创建的消息发生异常,message:{},e:{}", JSONUtil.toJsonStr(message), e);
        }
    }
}