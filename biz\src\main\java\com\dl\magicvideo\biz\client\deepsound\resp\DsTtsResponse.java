package com.dl.magicvideo.biz.client.deepsound.resp;

import com.dl.magicvideo.biz.client.deepsound.enums.DsEvnCheckErrCodeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DsTtsResponse implements Serializable {

    private static final long serialVersionUID = -6442816271939461816L;

    @JsonProperty("error_code")
    private Integer errCode;

    @JsonProperty("error_msg")
    private String errMsg;

    /**
     * tts 合成请求唯一id
     */
    @JsonProperty("sid")
    private String sid;

    /**
     * 合成后的音频下载/播放地址
     */
    @JsonProperty("audio_url")
    private String audioUrl;

    public boolean isSuccess() {
        if (DsEvnCheckErrCodeEnum.ERROR_CODE_0.getErrorCode().equals(getErrCode())) {
            return true;
        }
        return false;
    }
}
