package com.dl.magicvideo.web.controllers.chartlet;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.chartlet.po.ChartletInfoPO;
import com.dl.magicvideo.biz.manager.chartlet.ChartletInfoManager;
import com.dl.magicvideo.biz.manager.chartlet.enums.ChartletCategoryEnum;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.chartlet.convert.ChartletInfoConvert;
import com.dl.magicvideo.web.controllers.chartlet.param.ChartletPageParam;
import com.dl.magicvideo.web.controllers.chartlet.vo.ChartletInfoVO;
import com.dl.magicvideo.web.controllers.chartlet.vo.GroupByCategoryChatletInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-13 11:50
 */
@Component
public class ChartletInfoProcess extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ChartletInfoProcess.class);

    @Resource
    private ChartletInfoManager chartletInfoManager;

    public ResultPageModel<ChartletInfoVO> page(ChartletPageParam param) {
        LambdaQueryWrapper<ChartletInfoPO> wrapper = Wrappers.lambdaQuery(ChartletInfoPO.class)
                .eq(ChartletInfoPO::getIsDeleted, Const.ZERO).orderByDesc(ChartletInfoPO::getModifyDt);

        Page<ChartletInfoPO> resp = chartletInfoManager
                .page(new Page<>(param.getPageIndex(), param.getPageSize()), wrapper);
        return pageQueryModel(resp,
                resp.getRecords().stream().map(ChartletInfoConvert::cnvChartletInfoPO2VO).collect(Collectors.toList()));
    }

    public ResultModel<List<GroupByCategoryChatletInfoVO>> queryAllGroupByCategory() {
        List<ChartletInfoPO> chartletInfoPOList = chartletInfoManager
                .list(Wrappers.lambdaQuery(ChartletInfoPO.class).eq(ChartletInfoPO::getIsDeleted, Const.ZERO)
                        .orderByDesc(ChartletInfoPO::getModifyDt));

        //key-分类编码
        Map<Integer, List<ChartletInfoPO>> categoryMap = chartletInfoPOList.stream()
                .collect(Collectors.groupingBy(ChartletInfoPO::getCategory));

        List<GroupByCategoryChatletInfoVO> resultList = new ArrayList<>();
        Iterator<Map.Entry<Integer, List<ChartletInfoPO>>> iterator = categoryMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<ChartletInfoPO>> entry = iterator.next();
            Integer category = entry.getKey();
            List<ChartletInfoPO> chartletInfoPOS = entry.getValue();

            ChartletCategoryEnum categoryEnum = ChartletCategoryEnum.parse(category);
            if (Objects.isNull(categoryEnum)) {
                LOGGER.warn("不存在该分类编码!,categoryCode:{}", category);
                continue;
            }
            GroupByCategoryChatletInfoVO result = new GroupByCategoryChatletInfoVO();
            result.setCategory(categoryEnum.getCode());
            result.setCategoryName(categoryEnum.getName());
            result.setSort(categoryEnum.getSort());
            result.setChartletList(chartletInfoPOS.stream().map(ChartletInfoConvert::cnvChartletInfoPO2VO)
                    .collect(Collectors.toList()));

            resultList.add(result);
        }

        return ResultModel.success(resultList);
    }

}
