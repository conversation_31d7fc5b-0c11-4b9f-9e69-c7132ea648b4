package com.dl.magicvideo.web.controllers.aigc.chat;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum;
import com.dl.magicvideo.web.controllers.aigc.chat.param.*;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AiFileContentAndTitleRespVO;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AigcChatRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 14:32
 */
@Api("aigc-聊天控制器")
@RestController
@RequestMapping("/visual/aigc/chat")
public class AigcChatController {

    @Resource
    private AigcChatProcess aigcChatProcess;

    @ApiOperation("分页查询聊天记录")
    @PostMapping("/pagerecord")
    public ResultPageModel<AigcChatRecordVO> pageChatRecord(@RequestBody AigcChatRecordPageParam param) {
        return aigcChatProcess.pageChatRecord(param);
    }

    @ApiOperation("提问并返回ai回答")
    @PostMapping({ "/askandanswer", "/sendmessages" })
    public ResultModel<AigcChatRecordVO> askAndSyncAnswer(
            @RequestBody @Validated List<AigcChatSendMessageParam> params) {
        return aigcChatProcess.askAndSyncAnswer(params);
    }

    @ApiOperation("提问并流式返回ai回答")
    @PostMapping(value = { "/askandstreamanswer", "/stream/askandstreamanswer" })
    public void askAndStreamAnswer(HttpServletResponse resp,
            @RequestBody @Validated List<AigcChatSendMessageParam> params,
            @RequestParam(required = false, defaultValue = "1") Integer fromTool,
            @RequestParam(required = false, defaultValue = "0") Integer style) throws IOException {
        try (OutputStream out = resp.getOutputStream()) {
            //若是热点事件题材工具 则
            if (AigcChatToolEnum.HOT_EVENT_SUBJECT_MATTER_TOOL.getCode().equals(fromTool)) {
                aigcChatProcess.hotEventSubjectMatter(resp, params.get(0), style);
                return;
            }
            aigcChatProcess.askAndStreamAnswer(resp, params);
        }
    }

    @ApiOperation("修改聊天内容")
    @PostMapping("/updatecontent")
    public ResultModel<Void> updateContent(@RequestBody @Validated AigcChatUpdateContentParam param) {
        return aigcChatProcess.updateContent(param);
    }

    //迁移至AigcToolController
    @Deprecated
    @ApiOperation("提取文件内容和标题")
    @PostMapping("/extractfilecontentandtitle")
    public ResultModel<AiFileContentAndTitleRespVO> extractFileContentAndTitle(MultipartFile file) {
        return aigcChatProcess.extractFileContentAndTitle(file);
    }

    @ApiOperation("新增一条聊天记录")
    @PostMapping("/addRecord")
    public ResultModel<AigcChatRecordVO> addRecord(@RequestBody @Validated AigcAddChatRecordParam param) {
        return aigcChatProcess.addRecord(param);
    }

    @ApiOperation("查询聊天记录信息")
    @PostMapping("/recordinfo")
    public ResultModel<AigcChatRecordVO> recordInfo(@RequestBody @Validated AigcChatRecordQueryInfoParam param) {
        return aigcChatProcess.recordInfo(param);
    }

    @ApiOperation("热点事件题材文本风格改写")
    @PostMapping("/stream/hoteventstylerewrite")
    public void hotEventStyleRewrite(HttpServletResponse resp,
            @RequestBody @Validated AigcChatHotEventStyleRewriteParam param) throws IOException {
        try (OutputStream out = resp.getOutputStream()) {
            aigcChatProcess.hotEventStyleRewrite(resp, param);
        }
    }

    @ApiOperation("删除聊天记录信息")
    @PostMapping("/delrecord")
    public ResultModel<Void> delRecord(@RequestBody @Validated AigcChatRecordDelParam param) {
        return aigcChatProcess.delRecord(param);
    }

}
