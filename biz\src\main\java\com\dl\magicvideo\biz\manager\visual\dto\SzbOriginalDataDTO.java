package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class SzbOriginalDataDTO {
    /**
     * 代码
     */
    private String secucode;
    /**
     * 简称
     */
    private String secuabbr;
    /**
     * 公司全称
     */
    private String chiname;
    /**
     * 报告标题
     */
    private String title;
    /**
     * 是否是金融公司
     */
    private Integer isFinance;
    /**
     * 报告期
     */
    private String endDate;
    /**
     * 申万行业
     */
    private String swIndustry;
    /**
     * 公司简介
     */
    private String companyDetail;
    /**
     * 公司业绩相关数据
     */
    private SzbPerformanceDTO performance;
    /**
     * 盈利能力相关数据
     */
    private SzbProfitabilityDTO profitability;
    /**
     * 偿债能力相关数据
     */
    private SzbSolvencyDTO solvency;
    /**
     * 营运能力相关数据
     */
    private SzbOperatingDTO operating;
    /**
     * 主营收入相关数据
     */
    private SzbMainBusinessDTO mainBusiness;
    /**
     * 股东情况相关数据
     */
    private SzbShareholderDTO shareholder;
    /**
     * 银行相关数据
     */
    private SzbBankDataDTO bankData;
}
