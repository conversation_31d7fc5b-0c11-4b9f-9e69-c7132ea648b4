<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.statistics.StatisticsAiJobMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.statistics.po.StatisticsAiJobPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="recordId" column="record_id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="aiJobType" column="ai_job_type"/>
        <result property="jobCount" column="job_count"/>
        <result property="statisticsTime" column="statistics_time"/>
        <result property="totalCeilingMinutes" column="total_ceiling_minutes"/>
        <result property="totalTimeMillis" column="total_time_millis"/>
        <result property="createDt" column="create_dt"/>
        <result property="modifyDt" column="modify_dt"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantName" column="tenant_name"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_id,tenant_code,
        ai_job_type,job_count,statistics_time,total_ceiling_minutes,total_time_millis,create_dt,
        modify_dt,is_deleted,tenant_name
    </sql>

    <select id="topMaxTenantCode" resultType="com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO">
        SELECT
        SUM(total_time_millis) AS sumValue,tenant_code AS tenant_code
        FROM
        statistics_ai_job
        WHERE
        <![CDATA[
            statistics_time >= #{param.startTime}
            AND statistics_time <= #{param.endTime}
        ]]>
        GROUP BY
        tenant_code
        ORDER BY
        sumValue DESC
        LIMIT #{param.count}
    </select>

    <select id="totalCount" resultType="com.dl.magicvideo.biz.dal.statistics.po.AiStatisticsTotalCountPO">
        SELECT
            SUM(total_time_millis) AS sumValue,
            statistics_time AS statisticsTime
        FROM
        statistics_ai_job
        WHERE
        <![CDATA[
            statistics_time >= #{param.startTime}
            AND statistics_time <= #{param.endTime}
        ]]>
        GROUP BY statistics_time
        ORDER BY statistics_time ASC
    </select>

    <select id="topMaxMsg" resultType="com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO">
        SELECT
        tenant_code AS tenantCode,tenant_name AS tenantName,
        statistics_time AS statisticsTime,total_time_millis AS statisticsValue
        FROM
        statistics_ai_job
        WHERE
        <![CDATA[
            statistics_time >= #{param.startTime}
            AND statistics_time <= #{param.endTime}
        ]]>
        <if test="param.tenantCodeList != null and param.tenantCodeList.size() > 0">
            AND tenant_code in
            <foreach collection="param.tenantCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY tenant_code,tenant_name,statistics_time,total_time_millis
        ORDER BY statistics_time ASC
    </select>

    <select id="specificTenantSummary"
            resultType="com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobTenantSummaryDTO">
        SELECT sum(total_time_millis) as summaryTimeMillis,max(modify_dt) as latestModifyDt,
        SUM(total_text_length) AS totalTextLength
        from statistics_ai_job
        where
        tenant_code = #{param.tenantCode}
        <if test="param.minDt != null">
            AND statistics_time &gt;= #{param.minDt}
        </if>
        <if test="param.maxDt != null">
            AND statistics_time &lt;= #{param.maxDt}
        </if>
        <if test="param.aiJobTypeList != null and param.aiJobTypeList.size() > 0">
            and ai_job_type in
            <foreach collection="param.aiJobTypeList" item="item" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
