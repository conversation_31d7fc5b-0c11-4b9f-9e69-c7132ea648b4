package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @describe: ClipUpdateParam
 * @author: zhousx
 * @date: 2023/2/8 18:05
 */
@Data
public class CardUpdateParam {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("卡片id")
    private String cardId;

    @ApiModelProperty("卡片名称")
    private String name;

    @ApiModelProperty("卡片封面")
    private String coverUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("渲染数据")
    @NotBlank
    private String renderData;

    @ApiModelProperty("轻编辑配置")
    private String lightEditConfigs;

    @ApiModelProperty("跨片段配置")
    private String crossClips;

    @ApiModelProperty("动态节点")
    private List<DynamicNodeParam> dynamicNodes;
}
