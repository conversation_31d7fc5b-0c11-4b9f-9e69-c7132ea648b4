package com.dl.magicvideo.biz.manager.visual.vo;

import com.dl.magicvideo.biz.manager.visual.vo.ValueFormVO;
import lombok.Data;

import java.util.List;

@Data
public class WeeklyReviewVO {
    private String v1Text;
    private String v1Text2;
    private String v1Text3;
    private List<List<String>> v2Form;
    private String v2Tts;
    private List<List<String>> v3Form;
    private String v3Tts;
    private List<List<String>> v4Form;
    private String v4Tts;
    private List<List<String>> v5Form;
    private String v5Tts;
    private String v5Flipper;
    private String v5Flipper2;
    private String v6Text;
    private String v6DtTts;
    private List<List<String>> v7Form;
    private String v7DtTts;
    private String v7Flipper;
    private List<List<String>> v7Form2;

    private String v7DtTts2;
    private String v7Flipper2;

    private String v8Text;

    private String v8DtTts;

    private List<List<String>> v9Form;

    private String v9Tts;

    private String v10Text;

    private String v10Text2;

    private String v10Tts;

    private List<List<String>> v11Form;

    private String v11DtTts;

    private List<List<String>> v12Form;

    private String v12DtTts;

    private ValueFormVO v13Form;

    private ValueFormVO v13Form2;

    private ValueFormVO v13Form3;

    private ValueFormVO v13Form4;

    private ValueFormVO v13Form5;

    private ValueFormVO v13Form6;

    private String v13Tts;

    private String v14Tts;

    private ValueFormVO v14Form;

    private ValueFormVO v14Form2;

    private String v15DtTts;

    private ValueFormVO v15Form;

    private ValueFormVO v15Form2;

    private ValueFormVO v15Form3;

    private List<List<String>> v16Form;

    private String v16DtTts;

    private String v17DtTts;

    private String v17Flipper;

    private String v17Flipper2;

    private String v19Text;

    private String v19DtTts;
}
