package com.dl.magicvideo.web.controllers.internal.visual.convert;

import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualShareConfPO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualShareConfDTO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalDetailVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalPageVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalShareConfVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class VisualProduceJobInternalConvert {

    public static VisualProduceJobInternalDetailVO cnvVisualShareConfDTO2InternalDetailVO(VisualShareConfDTO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        VisualProduceJobInternalDetailVO result = new VisualProduceJobInternalDetailVO();
        result.setShareRemark(input.getShareRemark());
        result.setShareTitle(input.getShareTitle());
        result.setUrl(input.getVideoUrl());
        result.setJobId(input.getBizId());
        result.setCoverImg(input.getCoverImg());
        result.setVideoName(input.getVideoName());
        result.setMpShareCoverImg(input.getMpShareCoverImg());
        result.setRecommendShareCnt(input.getRecommendShareCnt());
        result.setUseCase(input.getUseCase());
        result.setH5ShareCoverImg(input.getH5ShareCoverImg());
        result.setNavigationBarTitle(input.getNavigationBarTitle());
        result.setTenantCode(input.getTenantCode());
        result.setDuration(input.getDuration());
        result.setResolutionType(input.getResolutionType());
        result.setIsDeleted(input.getIsDeleted());
        return result;
    }

    public static List<VisualProduceJobInternalVO> cnvVisualProduceJobPOList2InternalVOList(
            List<VisualProduceJobPO> inputList) {
        if (CollectionUtils.isEmpty(inputList)) {
            return Collections.emptyList();
        }
        return inputList.stream().map(input -> {
            VisualProduceJobInternalVO result = new VisualProduceJobInternalVO();
            result.setJobId(input.getJobId());
            result.setUrl(input.getVideoUrl());
            result.setTemplateId(input.getTemplateId());
            result.setCoverImg(input.getCoverUrl());
            result.setVideoName(input.getName());
            result.setTenantCode(input.getTenantCode());
            result.setDuration(input.getDuration());
            result.setIsDeleted(input.getIsDeleted());
            result.setCreatorName(input.getCreatorName());
            result.setModifyDt(input.getModifyDt());
            result.setCreateDt(input.getCreateDt());
            result.setCompleteDt(input.getCompleteDt());
            result.setStatus(input.getStatus());
            return result;
        }).collect(Collectors.toList());
    }

    public static VisualProduceJobInternalVO cnvVisualProduceJobPOList2InternalVO(VisualProduceJobPO input){
        if (Objects.isNull(input)) {
            return null;
        }
        VisualProduceJobInternalVO result = new VisualProduceJobInternalVO();
        result.setJobId(input.getJobId());
        result.setTemplateId(input.getTemplateId());
        result.setUrl(input.getVideoUrl());
        result.setCoverImg(input.getCoverUrl());
        result.setVideoName(input.getName());
        result.setTenantCode(input.getTenantCode());
        result.setDuration(input.getDuration());
        result.setIsDeleted(input.getIsDeleted());
        result.setCreatorName(input.getCreatorName());
        result.setCreateDt(input.getCreateDt());
        result.setModifyDt(input.getModifyDt());
        result.setCompleteDt(input.getCompleteDt());
        result.setStatus(input.getStatus());
        return result;
    }

    public static VisualProduceJobInternalShareConfVO cnvSharePO2VO(VisualShareConfPO input) {
        VisualProduceJobInternalShareConfVO result = new VisualProduceJobInternalShareConfVO();
        result.setShareRemark(input.getShareRemark());
        result.setShareTitle(input.getShareTitle());
        result.setBizId(input.getBizId());
        result.setBizType(input.getBizType());
        result.setCoverImg(input.getCoverImg());
        result.setMpShareCoverImg(input.getMpShareCoverImg());
        result.setRecommendShareCnt(input.getRecommendShareCnt());
        result.setUseCase(input.getUseCase());
        result.setH5ShareCoverImg(input.getH5ShareCoverImg());
        result.setNavigationBarTitle(input.getNavigationBarTitle());
        return result;
    }

    public static VisualProduceJobInternalPageVO cnvVisualProduceJobDTO2InternalPageVO(VisualProduceJobDTO input){
        VisualProduceJobInternalPageVO result = new VisualProduceJobInternalPageVO();
        result.setTemplateName(input.getTemplateName());
        result.setType(input.getType());
        result.setTypeDesc(JobTypeEnum.getDescByType(input.getType()));
        result.setJobId(input.getJobId());
        result.setTemplateId(input.getTemplateId());
        result.setVideoName(input.getName());
        result.setUrl(input.getVideoUrl());
        result.setCoverImg(input.getCoverUrl());
        result.setDuration(input.getDuration());
        result.setCreatorName(input.getCreatorName());
        result.setCreateDt(input.getCreateDt());
        result.setIsDeleted(input.getIsDeleted());
        result.setModifyDt(input.getModifyDt());
        result.setCompleteDt(input.getCompleteDt());
        result.setStatus(input.getStatus());
        return result;
    }
}
