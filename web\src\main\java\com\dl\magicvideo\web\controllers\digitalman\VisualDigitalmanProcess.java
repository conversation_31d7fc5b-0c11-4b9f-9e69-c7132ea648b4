package com.dl.magicvideo.web.controllers.digitalman;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManBaseInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManCallbackDTO;
import com.dl.aiservice.share.digitalman.DigitalManVideoGenResultDTO;
import com.dl.aiservice.share.digitalman.DmSubtitleRequestDTO;
import com.dl.aiservice.share.digitalman.DmSubtitleResponseDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.mq.producer.GeneratePreviewDataProducer;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.digitalman.convert.VisualDigitalmanConvert;
import com.dl.magicvideo.web.controllers.digitalman.enums.DigitalmanForBatchEnums;
import com.dl.magicvideo.web.controllers.digitalman.param.DmProduceParam;
import com.dl.magicvideo.web.controllers.digitalman.param.GetJobDetailParam;
import com.dl.magicvideo.web.controllers.digitalman.param.GetSceneListParam;
import com.dl.magicvideo.web.controllers.digitalman.vo.DigitalManSceneVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DigitalmanForBatchVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DmAggregationVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DmSubtitleResultVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DmSubtitleVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.GenericDigitalmanVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.JobVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.ProduceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @describe: VisualDigitalmanProcess
 * @author: zhousx
 * @date: 2023/6/5 14:31
 */
@Slf4j
@Component
public class VisualDigitalmanProcess extends AbstractController {
    @Resource
    private VisualAiJobManager visualAiJobManager;
    @Resource
    private AiServiceClient aiServiceClient;
    @Autowired
    private OperatorUtil operatorUtil;
    @Resource
    private GeneratePreviewDataProducer generatePreviewDataProducer;
    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    public ResultModel<List<GenericDigitalmanVO>> genericDigitalmanList() {
        //16为定力数影数字人
        List<Integer> channels = Arrays
                .asList(ServiceChannelEnum.IVH.getCode(), ServiceChannelEnum.ALIYUN_DIGIITAL.getCode(),
                        ServiceChannelEnum.IFLY_TEK.getCode(), ServiceChannelEnum.GUI_JI.getCode(),
                        ServiceChannelEnum.HEYGEN.getCode(), ServiceChannelEnum.VOLC_ENGINE.getCode(),
                        ServiceChannelEnum.FUJIA_IVH.getCode(),ServiceChannelEnum.CJHX_GUIJI.getCode(), 16);

        //获取数字人
        DaVirtualManRequestDTO requestDTO = new DaVirtualManRequestDTO();
        requestDTO.setChannels(channels);
        ResultModel<List<DaVirtualManDTO>> resultModel = aiServiceClient
                .getDigitalmanList(operatorUtil.getTenantCode(), requestDTO);

        List<GenericDigitalmanVO> list = resultModel.getDataResult().stream()
                .map(dto -> GenericDigitalmanVO.builder().gender(dto.getGender()).headImg(dto.getHeadImg())
                        .vmCode(dto.getVmCode()).vmName(dto.getVmName()).channel(dto.getChannel())
                        .digitalManId(dto.getBizId() + "").vmType(dto.getVmType()).vmVoiceKey(dto.getVmVoiceKey())
                        .build()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> defaultVoiceKeyList = list.stream().map(GenericDigitalmanVO::getVmVoiceKey)
                    .filter(Objects::nonNull).collect(Collectors.toList());

            //获取数字人对应的声音
            DaVirtualVoiceRequestDTO daVirtualVoiceRequestDTO = new DaVirtualVoiceRequestDTO();
            daVirtualVoiceRequestDTO.setVoiceType(1);
            daVirtualVoiceRequestDTO.setChannels(channels);
            ResultModel<List<DaVirtualVoiceDTO>> voiceModel = aiServiceClient.getGenericVoiceList(
                    operatorUtil.getTenantCode(), daVirtualVoiceRequestDTO);
            if (Objects.nonNull(voiceModel) && CollectionUtils.isNotEmpty(voiceModel.getDataResult())) {
                List<DaVirtualVoiceDTO> voiceList = voiceModel.getDataResult();
                if (CollectionUtils.isNotEmpty(defaultVoiceKeyList)) {
                    //voiceKey, map
                    Map<String, String> voiceKeyMap = voiceList.stream()
                            .filter(dto -> defaultVoiceKeyList.contains(dto.getVoiceKey()))
                            .collect(Collectors.toMap(DaVirtualVoiceDTO::getVoiceKey, DaVirtualVoiceDTO::getSpeed));
                    for (GenericDigitalmanVO genericDigitalmanVO : list) {
                        if (StringUtils.isNotBlank(genericDigitalmanVO.getVmVoiceKey())) {
                            String speed = voiceKeyMap.get(genericDigitalmanVO.getVmVoiceKey());
                            genericDigitalmanVO.setSpeed(StringUtils.isNotBlank(speed) ? speed : "1.0");
                        }
                    }
                }
            }
        }
        return ResultModel.success(list);
    }

    public ResultModel<GenericDigitalmanVO> info(Long vmBizId) {
        ResultModel<DigitalManBaseInfoDTO> resultModel = aiServiceClient
                .digitalManBaseInfo(operatorUtil.getTenantCode(), vmBizId);
        if (!resultModel.isSuccess()) {
            throw BusinessServiceException.getInstance("查询数字人信息失败");
        }
        return ResultModel.success(VisualDigitalmanConvert.cnvDigitalManBaseInfoDTO2VO(resultModel.getDataResult()));
    }

    public ResultModel<DmAggregationVO> aggregationinfo(Long vmBizId, String sceneId) {
        DigitalManAggregationRequestDTO requestDTO = new DigitalManAggregationRequestDTO();
        requestDTO.setVmBizId(vmBizId);
        ResultModel<DigitalManAggregationInfoDTO> resultModel = aiServiceClient
                .aggregationinfo(operatorUtil.getTenantCode(), requestDTO);
        if (!resultModel.isSuccess()) {
            throw BusinessServiceException.getInstance("查询数字人组合信息失败");
        }
        return ResultModel
                .success(VisualDigitalmanConvert.cnvDmAggregationDTO2VO(resultModel.getDataResult(), sceneId));
    }

    public ResultModel<ProduceVO> produce(DmProduceParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板id格式错误");
        DigitalManJobBO digitalManJobBO = new DigitalManJobBO();
        digitalManJobBO.setTemplateId(Long.valueOf(param.getTemplateId()));
        digitalManJobBO.setChannel(param.getChannel());
        digitalManJobBO.setText(param.getText());
        digitalManJobBO.setDriveType(Const.ONE);
        digitalManJobBO.setSceneId(param.getSceneId());
        digitalManJobBO.setVoiceCode(param.getVoiceCode());
        digitalManJobBO.setSpeed(param.getSpeed());
        digitalManJobBO.setCustomStoreUrl(Const.ONE);
        digitalManJobBO.setUserId(operatorUtil.getOperator());
        Long jobId = visualAiJobManager.submitDigitalManJob(digitalManJobBO);
        ProduceVO produceVO = new ProduceVO();
        produceVO.setJobId(jobId + "");
        return ResultModel.success(produceVO);
    }

    @Deprecated
    public ResultModel<DmSubtitleResultVO> generateSubtitle(DmProduceParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板id格式错误");
        DmSubtitleRequestDTO request = new DmSubtitleRequestDTO();
        request.setSceneId(param.getSceneId());
        request.setText(param.getText());
        request.setSpeakerId(param.getVoiceCode());
        request.setWorksBizId(Long.valueOf(param.getTemplateId()));
        request.setMaxLength(param.getMaxLength());
        request.setSpeed(param.getSpeed());
        ResultModel<DmSubtitleResponseDTO> resultModel = aiServiceClient.generateDmSubtitle(param.getChannel(), request);
        DmSubtitleResultVO vo = new DmSubtitleResultVO();
        if (Objects.nonNull(resultModel.getDataResult()) && CollectionUtils.isNotEmpty(
                resultModel.getDataResult().getSubtitles())) {
            vo.setSubtitles(resultModel.getDataResult().getSubtitles().stream().map(dto -> {
                DmSubtitleVO subtitleVO = new DmSubtitleVO();
                subtitleVO.setEndTime(dto.getEndTime());
                subtitleVO.setBeginTime(dto.getBeginTime());
                subtitleVO.setText(dto.getText());
                return subtitleVO;
            }).collect(Collectors.toList()));
        }
        return ResultModel.success(vo);
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<Boolean> callback(DigitalManCallbackDTO param) {
        log.info("数字人合成回调：dgCallback>>param>>>{}", JSONUtil.toJsonStr(param));
        List<DigitalManVideoGenResultDTO> videoList = param.getVideoList();
        if (CollectionUtils.isEmpty(videoList)) {
            log.error("dgCallback>>mediaProduceJob>>is empty!param>>>{}", JSONUtil.toJsonStr(param));
            return ResultModel.success(Boolean.TRUE);
        }

        //视频作品id集合
        Set<Long> produceJobIds = new HashSet<>();
        for (DigitalManVideoGenResultDTO video : videoList) {
            //成功
            if (Objects.equals(video.getStatus(), Const.ZERO)) {
                VisualAiJobPO dmJob = visualAiJobManager.getOne(Wrappers.lambdaQuery(VisualAiJobPO.class)
                        .eq(VisualAiJobPO::getJobId, param.getWorksBizId())
                        .eq(VisualAiJobPO::getAiJobId, video.getMediaJobId())
                        .eq(VisualAiJobPO::getJobStatus, JobStatusE.PROCESSING.getCode())
                        .eq(VisualAiJobPO::getJobType, AiJobTypeE.DIGITAL_MAN.getCode()));

                produceJobIds.add(dmJob.getProduceJobId());

                dmJob.setModifyDt(new Date());
                dmJob.setMediaInfo(video.getMediaUrl());
                dmJob.setJobStatus(JobStatusE.SUCCESS.getCode());
                dmJob.setDuration(new BigDecimal(video.getDuration()).multiply(new BigDecimal(1000L)).longValue());
                visualAiJobManager.updateById(dmJob);

                //失败
            } else if (Objects.equals(video.getStatus(), -Const.ONE)) {
                VisualAiJobPO dmJob = visualAiJobManager.getOne(Wrappers.lambdaQuery(VisualAiJobPO.class)
                        .eq(VisualAiJobPO::getJobId, param.getWorksBizId())
                        .eq(VisualAiJobPO::getAiJobId, video.getMediaJobId())
                        .eq(VisualAiJobPO::getJobType, AiJobTypeE.DIGITAL_MAN.getCode()));

                produceJobIds.add(dmJob.getProduceJobId());
                //增加错误信息
                dmJob.setResponseInfo(JSONUtil.toJsonStr(param));
                dmJob.setModifyDt(new Date());
                dmJob.setJobStatus(JobStatusE.FAILED.getCode());
                dmJob.setFailReason(video.getFailReason());
                visualAiJobManager.updateById(dmJob);
            }
        }

        //判断作品的所有ai任务是否有进行中的。
        for (Long produceJobId : produceJobIds) {
            List<VisualAiJobPO> aiJobList = visualAiJobManager
                    .list(Wrappers.lambdaQuery(VisualAiJobPO.class).eq(VisualAiJobPO::getProduceJobId, produceJobId));
            Optional<VisualAiJobPO> processingAiJobOpt = aiJobList.stream()
                    .filter(aiJob -> JobStatusE.PROCESSING.getCode().equals(aiJob.getJobStatus())).findAny();
            //若不存在进行中的ai任务，则发送生成预览数据的消息。ai任务的成功和失败处理统一在这个消息的消费者中处理。
            if (!processingAiJobOpt.isPresent()) {
                generatePreviewDataProducer.sendGeneratePreviewDataMsg(produceJobId);
            }
        }

        return ResultModel.success(Boolean.TRUE);
    }

    public ResultModel<JobVO> jobDetail(GetJobDetailParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getJobId()), "任务id格式错误");
        VisualAiJobPO visualAiJobPO = visualAiJobManager.lambdaQuery().eq(VisualAiJobPO::getJobId, Long.parseLong(param.getJobId())).one();
        Assert.notNull(visualAiJobPO, "任务不存在");
        JobVO vo = new JobVO();
        vo.setJobId(visualAiJobPO.getJobId() + "");
        vo.setJobStatus(visualAiJobPO.getJobStatus());
        vo.setMediaInfo(visualAiJobPO.getMediaInfo());
        if(Objects.nonNull(visualAiJobPO.getDuration())) {
            vo.setDuration(visualAiJobPO.getDuration().intValue());
        }
        if(StringUtils.isNotBlank(visualAiJobPO.getSubtitleInfo())) {
            vo.setSubtitles(JSONUtil.toBean(visualAiJobPO.getSubtitleInfo(), new TypeReference<List<DmSubtitleVO>>() {
            }, false));
        }
        return ResultModel.success(vo);
    }

    public ResultModel<DmSubtitleResultVO> subtitleDetail(GetJobDetailParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getJobId()), "任务id格式错误");
        VisualAiJobPO visualAiJobPO = visualAiJobManager.lambdaQuery().eq(VisualAiJobPO::getJobId, Long.parseLong(param.getJobId())).one();
        Assert.notNull(visualAiJobPO, "任务不存在");
        DmSubtitleResultVO vo = new DmSubtitleResultVO();
        if(StringUtils.isNotBlank(visualAiJobPO.getSubtitleInfo())) {
            vo.setSubtitles(JSONUtil.toBean(visualAiJobPO.getSubtitleInfo(), new TypeReference<List<DmSubtitleVO>>() {
            }, false));
        }
        return ResultModel.success(vo);
    }

    public ResultModel<List<DigitalManSceneVO>> sceneList(GetSceneListParam param) {
        DaVirtualManRequestDTO requestDTO = new DaVirtualManRequestDTO();
        requestDTO.setBizId(Long.valueOf(param.getDigitalManId()));
        ResultModel<List<DaVirtualManScenesDTO>> resultModel = aiServiceClient.getDigitalmanSceneList(operatorUtil.getTenantCode(), requestDTO);
        if(Objects.isNull(resultModel) || CollectionUtils.isEmpty(resultModel.getDataResult())) {
            return ResultModel.success(Collections.EMPTY_LIST);
        }
        return ResultModel.success(resultModel.getDataResult().stream().map(dto -> DigitalManSceneVO.builder().sceneId(dto.getSceneId()).sceneName(dto.getSceneName()).coverUrl(dto.getCoverUrl()).exampleUrl(dto.getExampleUrl()).exampleText(dto.getExampleText()).exampleDuration(dto.getExampleDuration()).cloth(dto.getCloth()).pose(dto.getPose()).resolution(dto.getResolution()).build()).collect(Collectors.toList()));
    }

    public ResultModel<List<DigitalmanForBatchVO>> listForBatch() {
        List<Integer> channels = new ArrayList<>();
        channels.addAll(Arrays.asList(ServiceChannelEnum.IVH.getCode(), ServiceChannelEnum.ALIYUN_DIGIITAL.getCode(), ServiceChannelEnum.IFLY_TEK.getCode(), ServiceChannelEnum.GUI_JI.getCode(), ServiceChannelEnum.HEYGEN.getCode()));
        DaVirtualManRequestDTO requestDTO = new DaVirtualManRequestDTO();
        requestDTO.setChannels(channels);
        ResultModel<List<DaVirtualManDTO>> resultModel = aiServiceClient.getDigitalmanList(operatorUtil.getTenantCode(), requestDTO);
        if(Objects.isNull(resultModel) || CollectionUtils.isEmpty(resultModel.getDataResult())) {
            return ResultModel.success(Collections.EMPTY_LIST);
        }
        List<DigitalmanForBatchVO> list = new ArrayList<>();
        resultModel.getDataResult().forEach(e->{
            DigitalmanForBatchEnums byVmCode = DigitalmanForBatchEnums.getByVmCode(e.getVmCode());
            if(Objects.nonNull(byVmCode)){
                DigitalmanForBatchVO vo = new DigitalmanForBatchVO();
                vo.setName(byVmCode.getName());
                vo.setChannel(byVmCode.getChannel());
                vo.setHeadImg(byVmCode.getHeadImg());
                vo.setSpeed(byVmCode.getSpeed());
                vo.setSceneId(byVmCode.getSceneId());
                vo.setVoiceCode(byVmCode.getVoiceCode());
                vo.setTtsUrl(byVmCode.getTtsUrl());
                vo.setTtsDuration(byVmCode.getTtsDuration());
                vo.setWebmUrl(byVmCode.getWebmUrl());
                vo.setWebmDuration(byVmCode.getWebmDuration());
                vo.setDefualtText("我是数字人主播，请输入文案体验");
                vo.setDigitalManId(byVmCode.getBizId());
                vo.setVolume(1);
                list.add(vo);
            }
        });
        return ResultModel.success(list);
    }
}
