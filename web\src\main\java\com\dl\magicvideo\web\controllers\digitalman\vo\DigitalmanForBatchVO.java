package com.dl.magicvideo.web.controllers.digitalman.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @describe: GenericDigitalmanVO
 * @author: zhousx
 * @date: 2023/6/5 14:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DigitalmanForBatchVO {
    /**
     * 渠道
     */
    @ApiModelProperty("渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    /**
     * 场景ID
     */
    private String sceneId;

    /**
     * 声音code
     */
    private String voiceCode;

    /**
     * 语速
     */
    private Double speed;

    @ApiModelProperty("数字人头像地址url")
    private String headImg;

    /**
     * 数字人名称
     */
    private String name;

    private String webmUrl;

    private Long webmDuration;

    private String ttsUrl;

    private Long ttsDuration;

    /**
     * 默认文案
     */
    private String defualtText;

    /**
     * 数字人Id
     */
    private String digitalManId;

    /**
     * 声音
     */
    private Integer volume;

}
