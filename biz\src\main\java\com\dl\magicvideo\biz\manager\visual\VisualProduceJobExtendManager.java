package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualProduceJobExtendBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;

/**
 * 作品扩展信息表
 */
public interface VisualProduceJobExtendManager extends IService<VisualProduceJobExtendPO> {

    /**
     * 新增或修改扩展信息
     *
     * @param bo
     */
    void saveOrUpdateExtend(VisualProduceJobExtendBO bo);

    IPage<VisualProduceJobDTO> pageRecommend(ProduceJobSearchBO bo);
}
