package com.dl.magicvideo.web.controllers.internal.subjectmatter.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MaterialAddParam {
    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 素材id
     */
    private Long matterId;

    /**
     * 素材地址
     */
    @NotBlank
    private String url;

    /**
     * 素材类型：3-视频，6-图片
     */
    @NotNull
    private Integer materialType;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 操作人名称
     */
    @NotBlank
    private String operatorName;

    /**
     * 操作人id
     */
    @NotBlank
    private Long operatorId;
}
