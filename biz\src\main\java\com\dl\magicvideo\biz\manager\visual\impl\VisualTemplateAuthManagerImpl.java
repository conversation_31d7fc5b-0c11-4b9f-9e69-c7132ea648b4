package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.visual.VisualTemplateAuthMapper;
import com.dl.magicvideo.biz.dal.visual.VisualTemplateMapper;
import com.dl.magicvideo.biz.dal.visual.param.AuthedTemplatePageQueryParam;
import com.dl.magicvideo.biz.dal.visual.po.TagPO;
import com.dl.magicvideo.biz.dal.visual.po.TemplateTagLinkPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplateAuthPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.TagManager;
import com.dl.magicvideo.biz.manager.visual.TemplateTagLinkManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateAuthManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthTenantCodeSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO;
import com.dl.magicvideo.biz.manager.visual.enums.TemplateSyncStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【visual_template_auth】的数据库操作Service实现
* @createDate 2023-06-18 10:34:13
*/
@Slf4j
@Service
public class VisualTemplateAuthManagerImpl extends ServiceImpl<VisualTemplateAuthMapper, VisualTemplateAuthPO>
        implements VisualTemplateAuthManager {
    private static final String AUTH_OPT_KEY_PREFIX = "dl-visualtemplateauth-";
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private VisualTemplateManager visualTemplateManager;
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private VisualTemplateMapper visualTemplateMapper;
    @Autowired
    private TagManager tagManager;
    @Autowired
    private TemplateTagLinkManager templateTagLinkManager;
    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auth(TemplateAuthBO bo) {
        Assert.notNull(bo.getTemplateId(), "模板id不可为空");
        String lockKey = AUTH_OPT_KEY_PREFIX + bo.getTemplateId();
        if (!redisUtil.tryLockAndSetTimeout(lockKey, 5)) {
            throw BusinessServiceException.getInstance("该模板正在由其他账号进行授权操作，请稍后重试。");
        }
        List<VisualTemplateAuthPO> existAuthList = this.list(Wrappers.lambdaQuery(VisualTemplateAuthPO.class)
                .eq(VisualTemplateAuthPO::getTemplateId, bo.getTemplateId()));
        Long sourceTemplateId = existAuthList.stream().filter(po -> Objects.nonNull(po.getSourceTemplateId()))
                .map(VisualTemplateAuthPO::getSourceTemplateId).findFirst().orElse(null);
        this.remove(Wrappers.lambdaQuery(VisualTemplateAuthPO.class)
                .eq(VisualTemplateAuthPO::getTemplateId, bo.getTemplateId()));
        if (CollectionUtils.isEmpty(bo.getTenants())) {
            return;
        }
        saveBatch(bo.getTenants().stream().map(tenant -> {
            VisualTemplateAuthPO po = new VisualTemplateAuthPO();
            po.setTemplateId(bo.getTemplateId());
            po.setTenantCode(tenant.getTenantCode());
            po.setTenantName(tenant.getTenantName());
            po.setCreatorName(operatorUtil.getUserName());
            po.setModifyName(operatorUtil.getUserName());
            po.setSourceTemplateId(sourceTemplateId);
            po.setSyncStatus(TemplateSyncStatusEnum.SUCCESS.getStatus());
            return po;
        }).collect(Collectors.toList()));

        if(StringUtils.isBlank(bo.getName()) && StringUtils.isBlank(bo.getCoverUrl()) && StringUtils.isBlank(bo.getPreviewVideoUrl())) {
            return;
        }
        visualTemplateManager.lambdaUpdate().eq(VisualTemplatePO::getTemplateId, bo.getTemplateId())
                .set(StringUtils.isNotBlank(bo.getName()), VisualTemplatePO::getName, bo.getName())
                .set(StringUtils.isNotBlank(bo.getCoverUrl()), VisualTemplatePO::getCoverUrl, bo.getCoverUrl())
                .set(StringUtils.isNotBlank(bo.getPreviewVideoUrl()), VisualTemplatePO::getPreviewVideoUrl, bo.getPreviewVideoUrl())
                .set(VisualTemplatePO::getModifyName, operatorUtil.getUserName())
                .set(VisualTemplatePO::getModifyDt, new Date())
                .set(Objects.nonNull(bo.getIsManager()), VisualTemplatePO::getIsManager, bo.getIsManager())
                .set(Objects.nonNull(bo.getFirstCategory()), VisualTemplatePO::getFirstCategory, bo.getFirstCategory())
                .set(Objects.nonNull(bo.getSecondCategory()), VisualTemplatePO::getSecondCategory, bo.getSecondCategory())
                .update();
        handleTag(bo.getTemplateId(), bo.getTagIds(), bo.getTagNames());
        if (StringUtils.isNotBlank(bo.getPreviewVideoUrl())) {
            Long userId = operatorUtil.getOperator();
            CompletableFuture.runAsync(() -> {
                operatorUtil.init(userId, "", "", "");
                visualTemplateManager.generateShortVideo(bo.getPreviewVideoUrl(), bo.getTemplateId());
            });
        }
        redisUtil.del(lockKey);
    }

    /**
     * 模板标签关联
     * @param templateId
     * @param tagIds
     * @param tagNames
     */
    private void handleTag(Long templateId, List<Long> tagIds, List<String> tagNames){
        List<TagPO> tagPOList = new ArrayList<>();
        //新增标签
        List<Long> newTagIds = new ArrayList<>();
        for (String tagName : tagNames) {
            long newTagId = hostTimeIdg.generateId().longValue();
            TagPO tagPO = new TagPO();
            tagPO.setTagType(1);
            tagPO.setTagId(newTagId);
            tagPO.setName(tagName);
            tagPOList.add(tagPO);
            newTagIds.add(newTagId);
        }
        tagManager.saveBatch(tagPOList);

        tagIds.addAll(newTagIds);
        //移除原来的标签
        templateTagLinkManager.lambdaUpdate().eq(TemplateTagLinkPO::getTemplateId, templateId).remove();

        List<TemplateTagLinkPO> templateTagLinkPOList = new ArrayList<>();
        for (Long tagId : tagIds) {
            TemplateTagLinkPO templateTagLinkPO = new TemplateTagLinkPO();
            templateTagLinkPO.setTemplateId(templateId);
            templateTagLinkPO.setTagId(tagId);
            templateTagLinkPOList.add(templateTagLinkPO);
        }
        //添加新的标签
        templateTagLinkManager.saveBatch(templateTagLinkPOList);
    }

    @Override
    public ResponsePageQueryDO<List<TemplateAuthDTO>> pageQuery(TemplateAuthSearchBO bo) {
        ResponsePageQueryDO<List<TemplateAuthDTO>> response = new ResponsePageQueryDO<>();
        LambdaQueryWrapper<VisualTemplatePO> queryWrapper = Wrappers.lambdaQuery(VisualTemplatePO.class);
        queryWrapper.like(StringUtils.isNotBlank(bo.getName()), VisualTemplatePO::getName, bo.getName())
                .eq(Objects.nonNull(bo.getTemplateId()), VisualTemplatePO::getTemplateId, bo.getTemplateId())
                .eq(VisualTemplatePO::getIsDeleted, Const.ZERO).eq(VisualTemplatePO::getIsSys, Const.ONE)
                .eq(Objects.nonNull(bo.getResolutionType()), VisualTemplatePO::getResolutionType,
                        bo.getResolutionType());

        if (Objects.nonNull(bo.getSourceTemplateId())) {
            List<VisualTemplateAuthPO> authPOList = baseMapper.selectList(
                    Wrappers.lambdaQuery(VisualTemplateAuthPO.class)
                            .eq(VisualTemplateAuthPO::getSourceTemplateId, bo.getSourceTemplateId()));
            Set<Long> templateIdSet = authPOList.stream().map(VisualTemplateAuthPO::getTemplateId)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(templateIdSet)) {
                response.setPageIndex(bo.getPageIndex());
                response.setPageSize(bo.getPageSize());
                response.setTotal(0);
                return response;
            }

            if (CollectionUtils.isNotEmpty(templateIdSet)) {
                queryWrapper.in(VisualTemplatePO::getTemplateId, templateIdSet);
            }
        }
        queryWrapper.orderByDesc(VisualTemplatePO::getCreateDt);

        IPage<VisualTemplatePO> pageResult = visualTemplateManager.getBaseMapper()
                .selectPage(convert(bo), queryWrapper);
        List<VisualTemplatePO> data = pageResult.getRecords();
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        log.info("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }
        List<Long> templateIds = data.stream().map(VisualTemplatePO::getTemplateId).collect(Collectors.toList());
        List<VisualTemplateAuthPO> allAuthList = this.lambdaQuery().in(VisualTemplateAuthPO::getTemplateId, templateIds)
                .list();
        Map<Long, List<VisualTemplateAuthPO>> authMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allAuthList)) {
            authMap.putAll(allAuthList.stream().collect(Collectors.groupingBy(VisualTemplateAuthPO::getTemplateId)));
        }

        List<TemplateAuthDTO> dtos = new ArrayList<>();
        dtos.addAll(data.stream().map(t -> {
            TemplateAuthDTO authDTO = cnvVisualTemplatePO2DTO(t);
            List<VisualTemplateAuthPO> authList = authMap.get(t.getTemplateId());
            if (Objects.nonNull(authDTO) && CollectionUtils.isNotEmpty(authList)) {
                Long sourceTemplateId = authList.get(0).getSourceTemplateId();
                authDTO.setSourceTemplateId(sourceTemplateId);
                authDTO.setAuthTenantList(authList.stream().map(auth -> {
                    TemplateAuthDTO.Tenant tenant = new TemplateAuthDTO.Tenant();
                    tenant.setTenantCode(auth.getTenantCode());
                    tenant.setTenantName(auth.getTenantName());
                    return tenant;
                }).collect(Collectors.toList()));
            }
            return authDTO;
        }).collect(Collectors.toList()));
        response.setDataResult(dtos);
        return response;
    }

    @Override
    public ResponsePageQueryDO<List<TemplateAuthDTO>> pageQueryByAuthTenantCode(TemplateAuthTenantCodeSearchBO bo) {
        ResponsePageQueryDO<List<TemplateAuthDTO>> response = new ResponsePageQueryDO<>();
        AuthedTemplatePageQueryParam param = new AuthedTemplatePageQueryParam();
        param.setTenantCode(bo.getAuthTenantCode());
        param.setName(bo.getName());
        if (Objects.nonNull(bo.getResolutionType())) {
            param.setResolutionType(bo.getResolutionType() + "");
        }
        param.setPageIndex(bo.getPageIndex());
        param.setPageSize(bo.getPageSize());
        param.setIsSys(Const.ONE);
        param.setTemplateId(bo.getTemplateId());
        Integer count = visualTemplateMapper.countAuthedTemplates(param);

        response.setPageIndex(bo.getPageIndex());
        response.setPageSize(bo.getPageSize());
        if (Objects.equals(count, 0)) {
            return response;
        }
        List<VisualTemplatePO> list = visualTemplateMapper.listAuthedTemplates(param);
        if (CollectionUtils.isEmpty(list)) {
            return response;
        }
        List<Long> templateIds = list.stream().map(VisualTemplatePO::getTemplateId).collect(Collectors.toList());
        List<VisualTemplateAuthPO> allAuthList = this.lambdaQuery().in(VisualTemplateAuthPO::getTemplateId, templateIds)
                .list();
        Map<Long, List<VisualTemplateAuthPO>> authMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(allAuthList)) {
            authMap.putAll(allAuthList.stream().collect(Collectors.groupingBy(VisualTemplateAuthPO::getTemplateId)));
        }
        List<TemplateAuthDTO> dtos = new ArrayList<>();
        dtos.addAll(list.stream().map(t -> {
            TemplateAuthDTO authDTO = cnvVisualTemplatePO2DTO(t);
            List<VisualTemplateAuthPO> authList = authMap.get(t.getTemplateId());
            if (Objects.nonNull(authDTO) && CollectionUtils.isNotEmpty(authList)) {
                Long sourceTemplateId = authList.get(0).getSourceTemplateId();
                authDTO.setSourceTemplateId(sourceTemplateId);
                authDTO.setAuthTenantList(authList.stream().map(auth -> {
                    TemplateAuthDTO.Tenant tenant = new TemplateAuthDTO.Tenant();
                    tenant.setTenantCode(auth.getTenantCode());
                    tenant.setTenantName(auth.getTenantName());
                    return tenant;
                }).collect(Collectors.toList()));
            }
            return authDTO;
        }).collect(Collectors.toList()));
        response.setTotal(count);
        response.setDataResult(dtos);
        return response;
    }

    private TemplateAuthDTO cnvVisualTemplatePO2DTO(VisualTemplatePO templatePO) {
        if (Objects.isNull(templatePO)) {
            return null;
        }
        TemplateAuthDTO templateDTO = new TemplateAuthDTO();
        templateDTO.setTemplateId(templatePO.getTemplateId());
        templateDTO.setStatus(templatePO.getStatus());
        templateDTO.setName(templatePO.getName());
        templateDTO.setCoverUrl(templatePO.getCoverUrl());
        templateDTO.setResolution(templatePO.getResolution());
        templateDTO.setBgMusic(templatePO.getBgMusic());
        templateDTO.setBgMusicParam(templatePO.getBgMusicParam());
        templateDTO.setTtsParam(templatePO.getTtsParam());
        templateDTO.setReplaceData(templatePO.getReplaceData());
        templateDTO.setResolutionType(templatePO.getResolutionType());
        templateDTO.setTenantCode(templatePO.getTenantCode());
        templateDTO.setCreateDt(templatePO.getCreateDt());
        templateDTO.setModifyDt(templatePO.getModifyDt());
        templateDTO.setCreatorName(templatePO.getCreatorName());
        templateDTO.setDuration(templatePO.getDuration());
        templateDTO.setPreviewVideoUrl(templatePO.getPreviewVideoUrl());
        templateDTO.setShortVideoUrl(templatePO.getShortVideoUrl());
        templateDTO.setModifyName(templatePO.getModifyName());
        return templateDTO;
    }
}




