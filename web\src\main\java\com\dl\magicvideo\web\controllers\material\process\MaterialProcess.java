package com.dl.magicvideo.web.controllers.material.process;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.DownloadUtil;
import com.dl.magicvideo.biz.common.util.FfmpegUtil;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.LatestTenantMaterialPO;
import com.dl.magicvideo.biz.dal.visual.po.TenantMaterialFolderPO;
import com.dl.magicvideo.biz.dal.visual.po.TenantMaterialPO;
import com.dl.magicvideo.biz.manager.cos.CosFileUploadManager;
import com.dl.magicvideo.biz.manager.visual.TenantMaterialFolderManager;
import com.dl.magicvideo.biz.manager.visual.TenantMaterialManager;
import com.dl.magicvideo.biz.manager.visual.dto.AudioInfoDTO;
import com.dl.magicvideo.biz.manager.visual.dto.FileInfoDTO;
import com.dl.magicvideo.biz.manager.visual.dto.ImageInfoDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VideoInfoDTO;
import com.dl.magicvideo.biz.manager.visual.enums.MaterialTypeEnum;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.material.param.*;
import com.dl.magicvideo.web.controllers.material.vo.FolderListVO;
import com.dl.magicvideo.web.controllers.material.vo.FolderListWithLatestMaterialVO;
import com.dl.magicvideo.web.controllers.material.vo.MaterialDetailVO;
import com.dl.magicvideo.web.controllers.material.vo.MaterialPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MaterialProcess extends AbstractController {
    @Resource
    private TenantMaterialManager tenantMaterialManager;
    @Resource
    private TenantMaterialFolderManager tenantMaterialFolderManager;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private CosFileUploadManager cosFileUploadManager;

    private static final String WEBM = ".webm";

    private static final String JPG = ".jpg";

    public static final String COS_PATH_CONTENT = "visual/matranwebm";

    public static final String COS_PATH_CONTENT_VIDEO = "visual/mavideo";

    public static final String COS_PATH_CONTENT_IMAGE = "visual/maimage";

    public static final String COS_PATH_CONTENT_AUDIO = "visual/maaudio";

    @Value("${visual.fileTempPath}")
    public String LOCAL_PATH_PREFIX;

    @Value("${dl.material.commonTenantCode}")
    public String commonTenantCode;

    private final ExecutorService tranWebmPool = new ThreadPoolExecutor(2, 4, 5 * 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(200));

    public ResultPageModel<MaterialPageVO> page(MaterialPageParam param) {
        if (BooleanUtils.isFalse(param.isCommon())) {
            Assert.notNull(param.getFolderId(), "文件夹id不能为空");
        }

        LambdaQueryWrapper<TenantMaterialPO> queryWrapper = Wrappers.lambdaQuery(TenantMaterialPO.class);
        String tenantCode = BooleanUtils.isTrue(param.isCommon()) ? commonTenantCode : operatorUtil.getTenantCode();
        queryWrapper.like(StringUtils.isNotBlank(param.getTitle()), TenantMaterialPO::getTitle, param.getTitle())
                .eq(Objects.nonNull(param.getMaterialType()), TenantMaterialPO::getMaterialType, param.getMaterialType())
                .eq(TenantMaterialPO::getTenantCode, tenantCode)
                .eq(Objects.nonNull(param.getFolderId()), TenantMaterialPO::getFolderId, param.getFolderId())
                .eq(TenantMaterialPO::getIsDeleted, Const.ZERO);
        queryWrapper.orderByDesc(TenantMaterialPO::getCreateDt);

        IPage<TenantMaterialPO> pageResult = tenantMaterialManager.getBaseMapper()
                .selectPage(convert(param), queryWrapper);

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new ResultPageModel<>();
        }
        List<MaterialPageVO> vos = pageResult.getRecords().stream().map(dto -> {
            MaterialPageVO vo = new MaterialPageVO();
            vo.setTitle(dto.getTitle());
            vo.setMaterialType(dto.getMaterialType());
            vo.setBizId(dto.getBizId().toString());
            vo.setLogoImg(dto.getLogoImg());
            vo.setDuration(dto.getDuration());
            vo.setUrl(dto.getUrl());
            vo.setWebmUrl(dto.getWebmUrl());
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(pageResult, vos);
    }

    public ResultModel<MaterialDetailVO> detail(MaterialDetailParam param) {
        TenantMaterialPO tenantMaterialPO = tenantMaterialManager.lambdaQuery()
                .eq(TenantMaterialPO::getBizId, param.getBizId()).one();
        Assert.notNull(tenantMaterialPO, "未查询到媒体信息");
        MaterialDetailVO vo = new MaterialDetailVO();
        vo.setTitle(tenantMaterialPO.getTitle());
        vo.setMaterialType(tenantMaterialPO.getMaterialType());
        vo.setBizId(tenantMaterialPO.getBizId().toString());
        vo.setSize(tenantMaterialPO.getSize());
        vo.setResolutionRatio(tenantMaterialPO.getResolutionRatio());
        vo.setCreateBy(tenantMaterialPO.getCreateBy());
        vo.setCreatorName(tenantMaterialPO.getCreatorName());
        vo.setCreateDt(tenantMaterialPO.getCreateDt().getTime());
        vo.setLogoImg(tenantMaterialPO.getLogoImg());
        vo.setDuration(tenantMaterialPO.getDuration());
        vo.setUrl(tenantMaterialPO.getUrl());
        vo.setWebmUrl(tenantMaterialPO.getWebmUrl());
        return ResultModel.success(vo);
    }

    private void tranWebm(long bizId, File file, String url, FileInfoDTO fileInfoDTO) {
        tranWebmPool.submit(RunnableWrapper.of(() -> {
            log.info("收到视频转化为webm的消息，bizId:{},url={}", bizId, url);
            Assert.isTrue(StringUtils.isNotBlank(url), "资源信息不能为空");
            try {
                long startTime = System.currentTimeMillis();
                //获取后缀名
                String suffix = FileUtil.extName(url);
                if ("webm".equals(suffix)) {
                    //4.更新对应resourceId下的 webm地址
                    tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getBizId, bizId)
                            .set(TenantMaterialPO::getWebmUrl, url).update();
                    return;
                }
                String fileName = FileUtil.mainName(url);
                String webmPath = LOCAL_PATH_PREFIX + fileName + WEBM; // 替换为保存文件的路径和文件名
                //String webmPath = "C:\\Users\\<USER>\\Videos\\webm\\" + fileName + WEBM; // 替换为保存文件的路径和文件名

                //如果是MP4
                if ("MP4".equalsIgnoreCase(suffix)) {
                    //判断
                    long maxBitrate = 8000;
                    long maxWidth = 1920;
                    long maxHeight = 1920;
                    //获取分辨率和码率
                    Integer imageHeight = fileInfoDTO.getImageHeight();
                    Integer imageWidth = fileInfoDTO.getImageWidth();
                    Integer bitrate = fileInfoDTO.getBitrate();
                    //如果有长且宽小于等于1920或码率小于等于8M就不压缩
                    if (imageWidth <= maxWidth && imageHeight <= maxHeight || bitrate <= maxBitrate) {
                        log.info("收到MP4视频文件不满足压缩的消息，bizId:{},url={}", bizId, url);
                        tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getBizId, bizId)
                                .set(TenantMaterialPO::getWebmUrl, url).update();
                        return;
                    }
                }

                //其他格式直接压缩
                FfmpegUtil.ffmpegToWebm(file.getAbsolutePath(), webmPath);

                long ffmpegToWebmEndTime = System.currentTimeMillis();
                log.info("转化为webm结束，共耗时={}", ffmpegToWebmEndTime - startTime);
                //2.将webm上传到腾讯云
                File webmFile = new File(webmPath);
                String webmUrl = cosFileUploadManager.uploadFile(webmFile, null, COS_PATH_CONTENT, true, true);
                long uploadToTxyEndTime = System.currentTimeMillis();
                log.info("上传到腾讯云结束，共耗时={},webm腾讯云url={}", uploadToTxyEndTime - ffmpegToWebmEndTime, webmUrl);

                //3.删除临时文件
                if (webmFile.exists()) {
                    FileUtils.deleteQuietly(webmFile);
                }
                if (file.exists()) {
                    FileUtils.deleteQuietly(file);
                }
                //4.更新对应resourceId下的 webm地址
                tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getBizId, bizId)
                        .set(TenantMaterialPO::getWebmUrl, webmUrl).update();
            } catch (Exception e) {
                log.error("文件转化异常", e);
            }
        }));
    }

    public ResultModel<Boolean> rename(MaterialRenameParam param) {
        tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getBizId, param.getBizId())
                .set(TenantMaterialPO::getTitle, param.getNewName())
                .update();
        return ResultModel.success(true);
    }

    public ResultModel<Boolean> delete(MaterialDeleteParam param) {
        tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getBizId, param.getBizId())
                .set(TenantMaterialPO::getIsDeleted, Const.ONE)
                .update();
        return ResultModel.success(true);
    }

    public ResultModel<Boolean> batchDelete(MaterialBatchDeleteParam param) {
        tenantMaterialManager.lambdaUpdate().in(TenantMaterialPO::getBizId, param.getBizIdList())
                .set(TenantMaterialPO::getIsDeleted, Const.ONE)
                .update();
        return ResultModel.success(true);
    }

    public ResultModel<Boolean> folderDelete(FolderDeleteParam param) {
        tenantMaterialFolderManager.lambdaUpdate().eq(TenantMaterialFolderPO::getFolderId, param.getFolderId())
                .set(TenantMaterialFolderPO::getIsDeleted, Const.ONE).update();
        if (Objects.equals(Const.TWO, param.getDeleteType())) {
            tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getFolderId, param.getFolderId())
                    .set(TenantMaterialPO::getFolderId, Const.ZERO).update();
        } else {
            tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getFolderId, param.getFolderId())
                    .set(TenantMaterialPO::getIsDeleted, Const.ONE).update();
        }
        return ResultModel.success(true);
    }

    public ResultModel<List<FolderListVO>> folderList(FolderListParam param) {
        List<FolderListVO> voList = new ArrayList<>();
        String tenantCode = BooleanUtils.isTrue(param.isCommon()) ? commonTenantCode : operatorUtil.getTenantCode();
        List<TenantMaterialFolderPO> list = tenantMaterialFolderManager.lambdaQuery()
                .eq(Objects.nonNull(param.getMaterialType()), TenantMaterialFolderPO::getMaterialType,
                        param.getMaterialType()).eq(TenantMaterialFolderPO::getIsDeleted, Const.ZERO)
                .eq(TenantMaterialFolderPO::getTenantCode, tenantCode).orderByDesc(TenantMaterialFolderPO::getId)
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                FolderListVO vo = new FolderListVO();
                vo.setFolderId(e.getFolderId().toString());
                vo.setFolderName(e.getName());
                voList.add(vo);
            });
        }
        return ResultModel.success(voList);
    }

    public ResultModel<List<FolderListWithLatestMaterialVO>> folderListWithLatestMaterial(
            FolderListWithLatestMaterialParam param) {
        List<FolderListWithLatestMaterialVO> voList = new ArrayList<>();
        String tenantCode = BooleanUtils.isTrue(param.isCommon()) ? commonTenantCode : operatorUtil.getTenantCode();
        List<TenantMaterialFolderPO> folderList = tenantMaterialFolderManager.lambdaQuery()
                .eq(Objects.nonNull(param.getMaterialType()), TenantMaterialFolderPO::getMaterialType,
                        param.getMaterialType()).eq(TenantMaterialFolderPO::getIsDeleted, Const.ZERO)
                .eq(TenantMaterialFolderPO::getTenantCode, tenantCode).orderByDesc(TenantMaterialFolderPO::getId)
                .list();
        if (CollectionUtils.isEmpty(folderList)) {
            return ResultModel.success(voList);
        }

        Set<Long> folderIds = folderList.stream().map(TenantMaterialFolderPO::getFolderId).collect(Collectors.toSet());
        List<LatestTenantMaterialPO> latestMaterialPOList = tenantMaterialManager
                .latestMaterialByFolderIds(tenantCode, folderIds, param.getLatestMaterialNumber(),
                        param.getMaterialType());
        Map<Long, List<LatestTenantMaterialPO>> folderMaterialListMap = latestMaterialPOList.stream()
                .collect(Collectors.groupingBy(LatestTenantMaterialPO::getFolderId));

        folderList.forEach(folderPO -> {
            FolderListWithLatestMaterialVO folderVO = new FolderListWithLatestMaterialVO();
            folderVO.setFolderId(folderPO.getFolderId().toString());
            folderVO.setFolderName(folderPO.getName());
            voList.add(folderVO);

            List<LatestTenantMaterialPO> materialPOList = folderMaterialListMap.get(folderPO.getFolderId());
            if (CollectionUtils.isEmpty(materialPOList)) {
                folderVO.setMaterialList(Collections.emptyList());
            } else {
                List<MaterialPageVO> materialList = materialPOList.stream().map(materialPO -> {
                    MaterialPageVO materialVO = new MaterialPageVO();
                    materialVO.setTitle(materialPO.getTitle());
                    materialVO.setMaterialType(materialPO.getMaterialType());
                    materialVO.setBizId(materialPO.getBizId().toString());
                    materialVO.setLogoImg(materialPO.getLogoImg());
                    materialVO.setDuration(materialPO.getDuration());
                    materialVO.setUrl(materialPO.getUrl());
                    materialVO.setWebmUrl(materialPO.getWebmUrl());
                    return materialVO;
                }).collect(Collectors.toList());
                folderVO.setMaterialList(materialList);
            }
        });

        return ResultModel.success(voList);
    }

    public ResultModel<Boolean> folderRename(FolderRenameParam param) {
        tenantMaterialFolderManager.lambdaUpdate().eq(TenantMaterialFolderPO::getFolderId, param.getFolderId())
                .set(TenantMaterialFolderPO::getName, param.getNewName()).update();
        return ResultModel.success(true);
    }

    public ResultModel<Long> folderAdd(FolderAddParam param) {
        TenantMaterialFolderPO po = new TenantMaterialFolderPO();
        long folderId = hostTimeIdg.generateId().longValue();
        po.setFolderId(folderId);
        po.setName(param.getName());
        po.setMaterialType(param.getMaterialType());
        po.setTenantCode(operatorUtil.getTenantCode());
        tenantMaterialFolderManager.save(po);
        return ResultModel.success(folderId);
    }

    /**
     * 填充文件信息
     *
     * @param tenantMaterialPO
     * @param file             传入的文件信息
     * @param materialType     如果为视频文件，则返回VideoInfoDTO
     */
    private FileInfoDTO fileFileMsg(TenantMaterialPO tenantMaterialPO, File file, Integer materialType) {
        FileInfoDTO result = new FileInfoDTO();
        tenantMaterialPO.setSize(file.length());
        if (Objects.equals(MaterialTypeEnum.AUDIT.getCode(), materialType)) {
            VideoInfoDTO videoInfoDTO = FfmpegUtil.extractVideoInfo(file.getAbsolutePath());
            Assert.isTrue((Objects.nonNull(videoInfoDTO.getImageWidth()) && Objects.nonNull(videoInfoDTO.getImageHeight()) && Objects.nonNull(videoInfoDTO.getDuration())),
                    "资源信息获取失败：获取到的信息为：" + JsonUtils.toJSON(videoInfoDTO));
            tenantMaterialPO.setDuration(videoInfoDTO.getDuration());
            tenantMaterialPO.setResolutionRatio(videoInfoDTO.getImageWidth() + "*" + videoInfoDTO.getImageHeight());
            //设置返回信息
            result.setBitrate(videoInfoDTO.getBitrate());
            result.setImageWidth(videoInfoDTO.getImageWidth());
            result.setImageHeight(videoInfoDTO.getImageHeight());
            result.setDuration(videoInfoDTO.getDuration());
        } else if (Objects.equals(MaterialTypeEnum.FOREVER.getCode(), materialType)) {
            ImageInfoDTO imageInfoDTO = FfmpegUtil.extractImageInfo(file.getAbsolutePath());
            Assert.isTrue((Objects.nonNull(imageInfoDTO.getImageWidth()) && Objects.nonNull(imageInfoDTO.getImageHeight())),
                    "资源信息获取失败：获取到的信息为：" + JsonUtils.toJSON(imageInfoDTO));
            tenantMaterialPO.setResolutionRatio(imageInfoDTO.getImageWidth() + "*" + imageInfoDTO.getImageHeight());
            //设置返回信息
            result.setImageWidth(imageInfoDTO.getImageWidth());
            result.setImageHeight(imageInfoDTO.getImageHeight());
        } else {
            AudioInfoDTO audioInfoDTO = FfmpegUtil.extractAudioInfo(file.getAbsolutePath());
            Assert.isTrue((Objects.nonNull(audioInfoDTO.getBitrate()) && Objects.nonNull(audioInfoDTO.getDuration())),
                    "资源信息获取失败：获取到的信息为：" + JsonUtils.toJSON(audioInfoDTO));
            tenantMaterialPO.setDuration(audioInfoDTO.getDuration());
            //设置返回信息
            result.setBitrate(audioInfoDTO.getBitrate());
            result.setDuration(audioInfoDTO.getDuration());
        }
        return result;
    }

    /**
     * 从URL字符串表示中提取路径部分
     *
     * @param urlStr URL字符串
     * @return 如果是合法URL，则返回路径部分，否则返回null
     */
    public static String getCosPathFromUrlString(String urlStr) {
        URL url = null;
        try {
            url = new URL(urlStr);
            String path = url.getPath();
            if (StringUtils.isNotBlank(path) && path.length() > 1) {
                return path.substring(1);
            }
        } catch (MalformedURLException e) {
            log.error("获取url失败,url={}", url, e);
        }
        return null;
    }


    public static void main(String[] args) {
//        String url = "https://dl-prod-1314522657.cos.ap-shanghai.myqcloud.com/visual/mavideo/4新闻资讯切入.webm";
//        String suffix = getSuffix(url);
//        System.out.println(suffix);

        String filename = "新闻资讯切入";
        String extName = FileUtil.extName(filename);
        System.out.println("Extension Name: " + extName);
        String mainName = FileUtil.mainName(filename);
        System.out.println("mainName Name: " + mainName);
    }

    public ResultModel<Boolean> moveFolder(MaterialFolerdMoveParam param) {
        if (!Objects.equals(param.getFolderId(), Const.ZERO_LONG)) {
            //非默认文件夹需要
            TenantMaterialFolderPO folderPO = tenantMaterialFolderManager.lambdaQuery()
                    .eq(TenantMaterialFolderPO::getFolderId, param.getFolderId())
                    .eq(TenantMaterialFolderPO::getTenantCode, operatorUtil.getTenantCode())
                    .eq(TenantMaterialFolderPO::getIsDeleted, Const.ZERO).one();
            Assert.notNull(folderPO, "该文件夹不存在");
        }
        tenantMaterialManager.lambdaUpdate().eq(TenantMaterialPO::getBizId, param.getBizId())
                .set(TenantMaterialPO::getFolderId, param.getFolderId()).update();
        return ResultModel.success(true);
    }

    public ResultModel<Boolean> batchMoveFolder(MaterialFolerdBatchMoveParam param) {
        if (!Objects.equals(param.getFolderId(), Const.ZERO_LONG)) {
            //非默认文件夹需要
            TenantMaterialFolderPO folderPO = tenantMaterialFolderManager.lambdaQuery()
                    .eq(TenantMaterialFolderPO::getFolderId, param.getFolderId())
                    .eq(TenantMaterialFolderPO::getTenantCode, operatorUtil.getTenantCode())
                    .eq(TenantMaterialFolderPO::getIsDeleted, Const.ZERO).one();
            Assert.notNull(folderPO, "该文件夹不存在");
        }
        tenantMaterialManager.lambdaUpdate().in(TenantMaterialPO::getBizId, param.getBizIdList())
                .set(TenantMaterialPO::getFolderId, param.getFolderId()).update();
        return ResultModel.success(true);
    }

    public ResultModel<List<String>> batchUpload(Integer materialType, Long folderId, MultipartFile[] multipartFiles) {
        Assert.isTrue(Objects.nonNull(multipartFiles) && multipartFiles.length > 0, "文件不能为空");
        List<String> urlList = new ArrayList();
        for (MultipartFile multipartFile : multipartFiles) {
            try {
                TenantMaterialPO tenantMaterialPO = new TenantMaterialPO();
                //文件名称
                String originalFilename = multipartFile.getOriginalFilename();
                //LOCAL_PATH_PREFIX = "C:/Users/<USER>/Desktop/音频/";
                File file = DownloadUtil.convertInputStreamToFile(LOCAL_PATH_PREFIX + originalFilename,
                        multipartFile.getInputStream());
                String url = cosFileUploadManager.uploadFile(file, null, getCosPath(materialType), true, false);
                //将文件上传到腾讯云上
                FileInfoDTO fileInfoDTO = fileFileMsg(tenantMaterialPO, file, materialType);
                long bizId = hostTimeIdg.generateId().longValue();
                tenantMaterialPO.setBizId(bizId);
                tenantMaterialPO.setUrl(url);
                if (Objects.equals(materialType, MaterialTypeEnum.AUDIT.getCode())) {
                    tenantMaterialPO.setLogoImg(url + JPG);
                }
                tenantMaterialPO.setCreatorName(operatorUtil.getUserName());
                tenantMaterialPO.setModifyName(operatorUtil.getUserName());
                tenantMaterialPO.setMaterialType(materialType);
                tenantMaterialPO.setTenantCode(operatorUtil.getTenantCode());
                tenantMaterialPO.setTitle(originalFilename);
                tenantMaterialPO.setFolderId(folderId);
                boolean save = tenantMaterialManager.save(tenantMaterialPO);
                Assert.isTrue(save, "素材库信息保存失败, tenantMaterialPO = " + JsonUtils.toJSON(tenantMaterialPO));
                //处理文件为中文的情况
                String decodeUrl = URLDecoder.decode(url, "UTF-8");
                //异步处理视频转换流程
                if (Objects.equals(MaterialTypeEnum.AUDIT.getCode(), materialType)) {
                    tranWebm(bizId, file, decodeUrl, fileInfoDTO);
                }
                urlList.add(url);
            } catch (Exception e) {
                log.error("文件批量上传异常", e);
            }
        }
        return ResultModel.success(urlList);
    }

    private String getCosPath(Integer materialType) {
        if (MaterialTypeEnum.AUDIT.getCode().equals(materialType)) {
            return COS_PATH_CONTENT_VIDEO;
        } else if (MaterialTypeEnum.FOREVER.getCode().equals(materialType)) {
            return COS_PATH_CONTENT_IMAGE;
        } else
            return COS_PATH_CONTENT_AUDIO;
    }
}
