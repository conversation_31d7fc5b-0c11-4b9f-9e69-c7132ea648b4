package com.dl.magicvideo.web.controllers.template;

import cn.hutool.json.JSONUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AigcChatRecordVO;
import com.dl.magicvideo.web.controllers.internal.visual.param.TagPageQueryParam;
import com.dl.magicvideo.web.controllers.internal.visual.vo.TagVO;
import com.dl.magicvideo.web.controllers.template.param.*;
import com.dl.magicvideo.web.controllers.template.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @describe: 模板相关接口
 * @author: zhousx
 * @date: 2023/2/1 14:46
 */
@Slf4j
@RestController
@RequestMapping("/visual/template")
@Api("模板管理")
public class VisualTemplateController {
    @Autowired
    private VisualTemplateProcess templateProcess;

    @PostMapping("/list")
    @ApiOperation("模板列表")
    public ResultPageModel<VisualTemplateVO> list(@RequestBody @Validated TemplatePageQueryParam param) {
        return templateProcess.list(param);
    }

    @PostMapping("/authlist")
    @ApiOperation("授权的模板列表")
    public ResultPageModel<VisualTemplateVO> authList(@RequestBody @Validated AuthedTemplatePageQueryParam param) {
        return templateProcess.authList(param);
    }

    @PostMapping("/batchTemplateList")
    @ApiOperation("批量模板列表")
    public ResultModel<List<BatchVideoVisualTemplateVO>> batchTemplateList() {
        return templateProcess.batchTemplateList();
    }

    @PostMapping("/add")
    @ApiOperation("添加模板基本信息")
    public ResultModel<VisualTemplateVO> add(@RequestBody VisualTemplateAddParam param) {
        return templateProcess.add(param);
    }

    @PostMapping("/update")
    @ApiOperation("更新模板")
    public ResultModel<VisualTemplateVO> update(@RequestBody VisualTemplateUpdateParam param) {
        return templateProcess.update(param);
    }

    @PostMapping("/addintegrity")
    @ApiOperation("添加模板完整信息")
    public ResultModel<VisualTemplateVO> addIntegrity(@RequestBody @Validated VisualTemplateAddIntegrityParam param) {
        return templateProcess.addIntegrity(param);
    }

    @GetMapping("/detail/{templateId}")
    @ApiOperation("模板详情")
    public ResultModel<VisualTemplateVO> detail(@PathVariable Long templateId) {
        return templateProcess.detail(templateId);
    }

    @GetMapping("/delete/{templateId}")
    @ApiOperation("删除模板")
    public ResultModel<Void> delete(@PathVariable Long templateId) {
        return templateProcess.delete(templateId);
    }

    @PostMapping("/addcard")
    @ApiOperation("添加卡片")
    public ResultModel<CardVO> addCard(@RequestBody CardAddParam param) {
        return templateProcess.addCard(param);
    }

    @GetMapping("/deletecard/{cardId}")
    @ApiOperation("删除卡片")
    public ResultModel<Void> deleteCard(@PathVariable Long cardId) {
        return templateProcess.deleteCard(cardId);
    }

    @PostMapping("/preview")
    @ApiOperation("预览(调试)")
    @NotLogin
    public ResultModel<PreviewVO> preview(@RequestBody PreviewParam param) {
        return templateProcess.preview(param);
    }

    @GetMapping("/produce/{templateId}")
    @ApiOperation("合成")
    public ResultModel<VisualProduceCreateVO> produce(@PathVariable Long templateId) {
        ProduceParam produceParam = new ProduceParam();
        produceParam.setTemplateId(String.valueOf(templateId));
        produceParam.setSource(VisualProduceJobSourceEnum.PLATFORM.getCode());
        return templateProcess.produce(produceParam);
    }

    @PostMapping("/produce")
    @ApiOperation("合成")
    public ResultModel<VisualProduceCreateVO> produce(@RequestBody @Validated ProduceParam param) {
        param.setSource(VisualProduceJobSourceEnum.PLATFORM.getCode());
        return templateProcess.produce(param);
    }

    @PostMapping("/aigcproduce")
    @ApiOperation("aigc合成视频")
    public ResultModel<AigcChatRecordVO> aigcProduce(@RequestBody @Validated AigcProduceParam param) {
        if (AigcChatToolEnum.HOT_EVENT_SUBJECT_MATTER_TOOL.getCode().equals(param.getFromTool())) {
            param.setSource(VisualProduceJobSourceEnum.AIGC_PRODUCE_HOT_EVENT_SUBJECT_MATTER_TOOL.getCode());
        } else {
            param.setSource(VisualProduceJobSourceEnum.AIGC_PRODUCE_COMMON_TOOL.getCode());
        }
        return templateProcess.aigcProduce(param);
    }

    @PostMapping("/producewithplan")
    @ApiOperation("通过交付计划合成")
    public ResultModel<VisualProduceCreateVO> produceWithPlan(@RequestBody @Validated PlanParam param) {
        return templateProcess.produceWithPlan(Long.valueOf(param.getPlanId()), param.getReplaceData(),
                VisualProduceJobSourceEnum.PRODUCE_WITH_PLAN.getCode());
    }

    @PostMapping("/produce/joblist")
    @ApiOperation("作品列表")
    public ResultPageModel<VisualProduceJobVO> jobList(@RequestBody VisualProduceJobPageQueryParam param) {
        return templateProcess.jobList(param);
    }

    @PostMapping("/produce/job/notice")
    @ApiOperation("作品消息列表")
    public ResultPageModel<VisualProduceJobVO> jobNotice() {
        return templateProcess.jogMessage();
    }

    @PostMapping("/copy")
    @ApiOperation("复制模板")
    public ResultModel<TemplateCopyVO> copy(@RequestBody @Validated CopyTemplateParam param) {
        return templateProcess.copy(param);
    }

    @PostMapping("/prodlist/{templateId}")
    @ApiOperation("产品列表")
    public ResultModel<List<TemplateProductVO>> prodlist(@PathVariable Long templateId) {
        return templateProcess.prodlist(templateId);
    }

    @PostMapping("/batchproduce")
    @ApiOperation("批量合成")
    public ResultModel<VisualProduceCreateBatchVO> batchProduce(@RequestBody @Validated BatchProduceParam param) {
        return templateProcess
                .batchProduce(param.getTitle(), Long.valueOf(param.getTemplateId()), param.getProdCodeList());
    }

    @PostMapping("/batchproducenew")
    @ApiOperation("批量合成-新-数据接口方式")
    public ResultModel<VisualProduceCreateBatchVO> batchProduceNew(@RequestBody @Validated BatchProduceNewParam param) {
        return templateProcess.batchProduceNew(param);
    }

    @PostMapping("/excelbatchproduceprecheck")
    @ApiOperation("根据excel表格批量合成-数据接口方式-前置校验")
    public ResultModel<ExcelBatchProducePreCheckResultVO> excelBatchProducePreChek(@RequestPart(value = "file") MultipartFile file)
            throws IOException {
        return templateProcess.excelBatchProducePreChek(file);
    }

    @PostMapping("/excelbatchproduce")
    @ApiOperation("根据excel表格批量合成-数据接口方式")
    public ResultModel<VisualProduceCreateBatchVO> excelBatchProduce(@RequestPart(value = "file") MultipartFile file,
            @RequestParam String param) {
        ExcelBatchProduceParam paramObject = JSONUtil.toBean(param, ExcelBatchProduceParam.class);
        return templateProcess.excelBatchProduce(file, paramObject);
    }

    @PostMapping("/produce/updaterecommendstate")
    @ApiOperation("推荐使用状态变更")
    public ResultModel<Boolean> updaterecommendstate(@RequestBody @Validated ProduceUpdateParam param) {
        return templateProcess.updaterecommendstate(param);
    }

    @PostMapping("/updateshareconfstate")
    @ApiOperation("交互式状态变更")
    @NotLogin
    public ResultModel<Boolean> updateShareConfState(@RequestBody @Validated ShareConfUpdateStateParam param) {
        return templateProcess.updateShareConfState(param);
    }

    /**
     * @param
     * @return
     */
    @PostMapping("/ppt")
    @ApiOperation("ppt转视频")
    public ResultModel<List<PPTVO>> ppt(@RequestPart(value = "file") MultipartFile multipartFiles, HttpServletRequest request) {
        return templateProcess.ppt(multipartFiles, request);
    }

    /**
     * @param
     * @return
     */
    @PostMapping("/ppttemplate")
    @ApiOperation("ppt模板")
    public ResultModel<List<PPTTemplateVO>> pptTemplate() {
        return templateProcess.pptTemplate();
    }

    /**
     * @return 版本号
     */
    @ApiOperation("组件版本号")
    @NotLogin
    @PostMapping("/version")
    public ResultModel<List<ComponentVersionDetailVO>> version() {
        return templateProcess.version();
    }

    @ApiOperation("根据版本号获取组件url")
    @NotLogin
    @PostMapping("/getUrlByVersion")
    public ResultModel<ComponentVersionDetailVO> getUrlByVersion(@RequestBody @Validated UrlParam param) {
        return templateProcess.getUrlByVersion(param);
    }

    @PostMapping("/tagList")
    @ApiOperation("标签列表")
    public ResultPageModel<TagVO> tagList(@RequestBody @Validated TagPageQueryParam param) {
        if (Objects.isNull(param.getTagType())){
            param.setTagType(1);
        }
        return templateProcess.tagList(param);
    }

    @PostMapping("/collect")
    @ApiOperation("收藏")
    public ResultModel<Void> collect(@RequestBody @Validated CollectParam param) {
        return templateProcess.collect(param);
    }

    @PostMapping("/cancelCollect")
    @ApiOperation("取消收藏")
    public ResultModel<Void> cancelCollect(@RequestBody @Validated CollectParam param) {
        return templateProcess.cancelCollect(param);
    }
}
