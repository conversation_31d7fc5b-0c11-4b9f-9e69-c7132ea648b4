package com.dl.magicvideo.biz.es.aigc.chatrecord.enums;

/**
 * aigc聊天记录类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 15:09
 */
public enum AigcChatRecordTypeEnum {

    //类型，1-发送，2-接收，3-系统消息
    SEND(1,"发送"),
    RECEIVE(2,"接收"),
    SYSTEM(3,"系统消息");

    private Integer type;

    private String desc;

    AigcChatRecordTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
