package com.dl.magicvideo.biz.manager.statistics;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxTenantCodeParam;
import com.dl.magicvideo.biz.dal.statistics.param.TotalCountParam;
import com.dl.magicvideo.biz.dal.statistics.po.AiStatisticsTotalCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsAiJobPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobPageBO;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobTenantSummaryQueryBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobDTO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobTenantSummaryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-13 16:35
 */
public interface StatisticsAiJobManager extends IService<StatisticsAiJobPO> {

    ResponsePageQueryDO<List<StatisticsAiJobDTO>> pageQuery(StatisticsAiJobPageBO bo);

    /**
     * 一段时间内topX 租户
     *
     * @param param
     * @return
     */
    List<StatisticsCountTopMaxPO> topMaxTenantCode(TopMaxTenantCodeParam param);

    /**
     * 一段时间内的总数
     *
     * @param param
     * @return
     */
    List<AiStatisticsTotalCountPO> totalCount(TotalCountParam param);

    /**
     * 一端时间内租户的数量信息
     *
     * @param param
     * @return
     */
    List<StatisticsMsgPO> topMaxMsg(TopMaxMsgParam param);

    /**
     * 指定租户的ai任务数据统计汇总
     *
     * @return
     */
    StatisticsAiJobTenantSummaryDTO specificTenantSummary(StatisticsAiJobTenantSummaryQueryBO bo);
}
