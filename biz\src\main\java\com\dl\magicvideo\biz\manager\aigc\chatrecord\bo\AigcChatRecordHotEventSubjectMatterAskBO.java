package com.dl.magicvideo.biz.manager.aigc.chatrecord.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-15 11:44
 */
@Data
@ApiModel("aigc-聊天记录-热点事件题材业务提问对象")
public class AigcChatRecordHotEventSubjectMatterAskBO {

    @ApiModelProperty(value = "题材bizId")
    private Long subjectMatterBizId;

    @ApiModelProperty(value = "提问内容")
    private AigcChatRecordHotEventSubjectMatterAskContentBO askContent;

}
