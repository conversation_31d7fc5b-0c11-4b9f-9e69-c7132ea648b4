package com.dl.magicvideo.biz.common.util;


import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class PDFUtil{
    public static void main(String[] args) {
        // 指定PDF文件的路径
        String pdfFilePath = "C:\\Users\\<USER>\\Desktop\\pdf\\1.pdf";
        // 指定输出图片的目录
        String outputDir = "C:\\Users\\<USER>\\Desktop\\pdf\\out\\";

       File pdfFile = new File(pdfFilePath);
        PDDocument document = null;

        try {
            // 加载PDF文件
            document = PDDocument.load(pdfFile);
            PDFRenderer pdfRenderer = new PDFRenderer(document);

            // 遍历PDF的每一页
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                // 渲染页面为图片
                BufferedImage image = pdfRenderer.renderImageWithDPI(pageIndex, 300);

                // 创建输出文件
                File outputFile = new File(outputDir + "page_" + (pageIndex + 1) + ".png");

                // 保存图片到文件
                ImageIO.write(image, "png", outputFile);
            }
        } catch (IOException e) {
            // 处理IO异常
            e.printStackTrace();
        } finally {
            // 确保PDDocument被关闭
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    // 处理关闭PDDocument时的异常
                    e.printStackTrace();
                }
            }
        }
    }
}