package com.dl.magicvideo.biz.client.basicservice;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.basicservice.dto.OpLogDTO;
import com.dl.magicvideo.biz.client.basicservice.intercepter.BasicServiceInterceptor;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogAddReq;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogPageReq;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:48
 */
@BaseRequest(interceptor = BasicServiceInterceptor.class)
public interface OpLogClient {

    /**
     * 新增操作日志
     *
     * @param req
     * @return
     */
    @Post(url = "/internal/oplog/newlog")
    ResultModel<Void> newOpLog(@JSONBody OpLogAddReq req);

    /**
     * 分页查询操作日志
     *
     * @param req
     * @return
     */
    @Post(url = "/internal/oplog/page")
    ResultPageModel<OpLogDTO> page(@JSONBody OpLogPageReq req);

}
