package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 视频转发配置表
 * @TableName visual_share_conf
 */
@TableName(value ="visual_share_conf")
@Data
public class VisualShareConfPO extends BasePO implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务来源id
     */
    private Long bizId;

    /**
     * 业务类型 1模板 2作品
     */
    private Integer bizType;

    /**
     * 导航栏标题
     */
    private String navigationBarTitle;

    /**
     * 视频封面图
     */
    private String coverImg;

    /**
     * 推荐转发文案
     */
    private String recommendShareCnt;

    /**
     * 小程序转发封面图
     */
    private String mpShareCoverImg;

    /**
     * h5转发封面图
     */
    private String h5ShareCoverImg;

    /**
     * 转发标题
     */
    private String shareTitle;

    /**
     * 转发摘要
     */
    private String shareRemark;

    /**
     * 使用场景 0-朋友圈/群发/视频号 1-私聊
     */
    private Integer useCase;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer isDeleted;

}