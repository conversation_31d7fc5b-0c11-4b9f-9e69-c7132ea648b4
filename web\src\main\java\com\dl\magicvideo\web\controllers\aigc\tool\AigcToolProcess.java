package com.dl.magicvideo.web.controllers.aigc.tool;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.share.aichat.consts.AiChatKimiConst;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.PlaceHolderUtils;
import com.dl.magicvideo.biz.manager.aigc.chat.AigcChatManager;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AiFileContentAndTitleRespBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatResponseBO;
import com.dl.magicvideo.biz.manager.aigc.properties.AigcPropertites;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.enums.AigcScriptStyleEnum;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AiFileContentAndTitleRespVO;
import com.dl.magicvideo.web.controllers.aigc.tool.param.AigcExtractTitleFromTextParam;
import com.dl.magicvideo.web.controllers.aigc.tool.vo.AigcExtractTitleFromTextResultVO;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-06 11:32
 */
@Component
public class AigcToolProcess {
    private static final Logger LOGGER = LoggerFactory.getLogger(AigcToolProcess.class);

    @Resource
    private AigcChatManager aigcChatManager;

    @Resource
    private OperatorUtil operatorUtil;

    @Value("${visual.fileTempPath}")
    public String localPathPrefix;

    @Resource
    private AigcPropertites aigcPropertites;

    public ResultModel<AiFileContentAndTitleRespVO> extractFileContentAndTitle(MultipartFile multipartFile) {
        // 创建一个 File 对象，指定文件的保存路径
        File file = null;
        String filePath = localPathPrefix + System.currentTimeMillis() + "-" + multipartFile.getOriginalFilename();
        try {
            file = new File(filePath);
            multipartFile.transferTo(file);
        } catch (Exception e) {
            LOGGER.error("文件转换发生异常！fileName:{},,,,filePath:{}", multipartFile.getOriginalFilename(), filePath);
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance("服务器开小差了，请稍后再试");
        }

        AiFileContentAndTitleRespBO respBO = null;
        try {
            respBO = aigcChatManager
                    .extractFileContentAndTitle(operatorUtil.getTenantCode(), operatorUtil.getOperator(),
                            aigcPropertites.getAigcExtractFileContentAndTitlePresupposeText(), file);
        } finally {
            FileUtils.deleteQuietly(file);
        }

        AiFileContentAndTitleRespVO vo = new AiFileContentAndTitleRespVO();
        vo.setTitle(respBO.getTitle());
        vo.setContent(respBO.getContent());
        return ResultModel.success(vo);
    }

    public ResultModel<AigcExtractTitleFromTextResultVO> extractTitleFromText(AigcExtractTitleFromTextParam param) {
        Assert.isTrue(StringUtils.isNotBlank(param.getText()), "输入文本不能为空");

        //1.构建请求参数
        Properties properties = new Properties();
        properties.put(AigcPropertites.TEXT, param.getText());
        //完整预设文案
        String wholePresupposeText = null;
        if (AigcScriptStyleEnum.DOUYIN.getStyle().equals(param.getStyle())) {
            wholePresupposeText = PlaceHolderUtils
                    .resolveValue(aigcPropertites.getAigcExtractTitleStyleDouyinFromText(), properties);
        } else {
            wholePresupposeText = PlaceHolderUtils
                    .resolveValue(aigcPropertites.getAigcExtractTitleFromText(), properties);
        }

        //2.调用ai对话服务传入消息，接口要返回ai的响应结果
        AigcSingleChatRequestBO singleChatRequestBO = new AigcSingleChatRequestBO();
        singleChatRequestBO.setUserId(operatorUtil.getOperator());
        singleChatRequestBO.setUserMessage(wholePresupposeText);
        singleChatRequestBO.setModel(AiChatKimiConst.MOONSHOT_8K);
        singleChatRequestBO.setRespMaxToken(Const.ONE_ZERO_TWO_FOUR);
        AigcSingleChatResponseBO singleChatResponseBO = aigcChatManager
                .singleChat(operatorUtil.getTenantCode(), singleChatRequestBO);

        if (StringUtils.isBlank(singleChatResponseBO.getContent())) {
            LOGGER.error("从文本中提取标题，ai返回的内容为空!!!");
            return ResultModel.success(null);
        }

        AigcExtractTitleFromTextResultVO vo = buildAigcExtractTitleFromTextResultVO(param.getStyle(),
                singleChatResponseBO.getContent());
        return ResultModel.success(vo);
    }

    private static AigcExtractTitleFromTextResultVO buildAigcExtractTitleFromTextResultVO(Integer style,
            String aiRespContent) {
        if (StringUtils.isBlank(aiRespContent)) {
            LOGGER.error("从文本中提取标题，ai返回的内容为空!!!");
        }
        AigcExtractTitleFromTextResultVO vo = new AigcExtractTitleFromTextResultVO();

        //处理抖音风格的标题。
        if (AigcScriptStyleEnum.DOUYIN.getStyle().equals(style)) {
            String[] arr1 = aiRespContent.replaceAll("\n", "").split("副标题：");
            if (arr1.length > 1) {
                vo.setViceTitle(StringUtils.trim(arr1[1]));
            }

            String[] arr2 = arr1[0].split("主标题：");
            if (arr2.length > 1) {
                vo.setTitle(StringUtils.trim(arr2[1]));
            }

            LOGGER.info("从文本中提取标题,,,ai返回标题:{},,,处理后的mainTitle:{},,,viceTitle:{}", aiRespContent, vo.getTitle(),
                    vo.getViceTitle());
            return vo;
        }

        String[] wordsArr = aiRespContent.replaceAll("。", "").split("[，,]");
        StringBuilder resultTitle = new StringBuilder();
        for (int i = 0; i < wordsArr.length; i++) {
            if (i > 1) {
                break;
            }
            if (i == 1) {
                resultTitle.append("\\n");
            }
            resultTitle.append(wordsArr[i]);
        }
        vo.setTitle(resultTitle.toString());
        LOGGER.info("从文本中提取标题,ai返回标题:{},,,处理后的标题:{}", aiRespContent, vo.getTitle());

        return vo;
    }

    public static void main(String[] args) {
        String text = "主标题：3D动作游戏井喷\n\n副标题：国产游戏惊艳全球玩家";

        String[] arr1 = text.replaceAll("\n", "").split("副标题：");
        System.out.println(JSONUtil.toJsonStr(arr1));

        String subTitle = arr1[1];

        String[] arr2 = arr1[0].split("主标题：");
        System.out.println(JSONUtil.toJsonStr(arr2));
        String mainTitle = arr2[1];
        System.out.println("subTitle:" + subTitle);
        System.out.println("mainTitle:" + mainTitle);

    }
}
