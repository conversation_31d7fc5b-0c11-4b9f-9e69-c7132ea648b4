package com.dl.magicvideo.biz.client.deepsound.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsDiffInfo
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 15:55
 * @Version 1.0
 **/
@Data
public class DsDiffInfo implements Serializable {

    private static final long serialVersionUID = 3593481160681924569L;

    /**
     * 错误在原文本位置，从 0 开始。
     */
    @JsonProperty("pos")
    private Integer pos;

    /**
     * 读错误的文本内容。
     */
    @JsonProperty("text")
    private String text;

    /**
     * 枚举类型: 0 错读 1 多读 -1 漏读
     */
    @JsonProperty("type")
    private Integer type;
}
