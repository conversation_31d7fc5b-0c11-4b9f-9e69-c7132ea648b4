package com.dl.magicvideo.biz.manager.visual.bo;

import lombok.Data;

/**
 * @describe: DeliveryPlanBO
 * @author: zhousx
 * @date: 2023/9/5 9:43
 */
@Data
public class DeliveryPlanBO {
    private Long planId;
    /**
     * 名称
     */
    private String name;

    /**
     * 简介
     */
    private String desc;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 关联模板id
     */
    private Long templateId;

    /**
     * 负责人
     */
    private String director;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 触发次数，默认-1 代表无限制
     */
    private Integer limit;

    /**
     * 周期，-1-无，0-天，1-周，2-月
     */
    private Integer period;

    private Integer status;

    private Integer isNotify;

    private String notifyUrl;

    private String callbackUrl;

    private Integer produceWay;

    private Long createBy;

    private Integer hasPeriod;
}
