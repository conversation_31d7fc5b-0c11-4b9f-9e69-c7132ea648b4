package com.dl.magicvideo.biz.manager.subjectmatter.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.subjectmatter.SubjectMatterStockMapper;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterStockPO;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMatterStockManager;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterStockSaveBO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:31
 */
@Component
public class SubjectMatterStockManagerImpl extends ServiceImpl<SubjectMatterStockMapper, SubjectMatterStockPO>
        implements SubjectMatterStockManager {

    @Override
    public void batchSave(Long smBizId, List<SubjectMatterStockSaveBO> stockSaveBOList) {
        this.remove(
                Wrappers.lambdaQuery(SubjectMatterStockPO.class).eq(SubjectMatterStockPO::getSmBizId, smBizId));

        if (CollectionUtils.isEmpty(stockSaveBOList)) {
            return;
        }

        Date now = new Date();
        List<SubjectMatterStockPO> insertList = stockSaveBOList.stream().map(bo -> {
            SubjectMatterStockPO po = new SubjectMatterStockPO();
            po.setCreateDt(now);
            po.setSmBizId(smBizId);
            po.setStockCode(bo.getStockCode());
            po.setStockShortName(bo.getStockShortName());
            return po;
        }).collect(Collectors.toList());
        this.saveBatch(insertList);
    }
}