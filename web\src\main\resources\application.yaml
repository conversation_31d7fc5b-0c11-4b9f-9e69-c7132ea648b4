# Spring Boot Application Configuration
spring:
  application:
    name: dl-magicvideo
  profiles:
    active: prod
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ************************************************************************************************************************************************************************************
          username: pelot
          password: Dingli@0301
          driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com
    port: 6379
    password: r-bp1rgz6nqi3c07bxfn:Dingli@0301
    database: 0
  # Spring Cloud Stream Kafka Configuration
  cloud:
    stream:
      kafka:
        binder:
          brokers: alikafka-pre-cn-fzh4egxzg023-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-fzh4egxzg023-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-fzh4egxzg023-3-vpc.alikafka.aliyuncs.com:9092
#          brokers: alikafka-pre-cn-fzh4egxzg023-1.alikafka.aliyuncs.com:9093,alikafka-pre-cn-fzh4egxzg023-2.alikafka.aliyuncs.com:9093,alikafka-pre-cn-fzh4egxzg023-3.alikafka.aliyuncs.com:9093
          auto-create-topics: true
          auto-add-partitions: true
        bindings:
          output:
            producer:
              configuration:
                key.serializer: org.apache.kafka.common.serialization.StringSerializer
                value.serializer: org.apache.kafka.common.serialization.StringSerializer
      bindings:
        # 消费者通道配置（匹配DlChannels接口中的@Input注解）
        produceshareconfconsumer:
          destination: dl-magicvideo-visual-produce-share
          content-type: application/json
          group: dl-magicvideo-group
        addwithholdconsumer:
          destination: dl-magicvideo-produce-job-created
          content-type: application/json
          group: dl-magicvideo-group
        producefailconsumer:
          destination: dl-magicvideo-produce-fail
          content-type: application/json
          group: dl-magicvideo-group
        producesuccessconsumer:
          destination: dl-magicvideo-produce-success
          content-type: application/json
          group: dl-magicvideo-group
        reducewithholdconsumer:
          destination: dl-magicvideo-reduce-withhold
          content-type: application/json
          group: dl-magicvideo-group
        reducebalanceconsumer:
          destination: dl-magicvideo-reduce-trial-balance
          content-type: application/json
          group: dl-magicvideo-group
        cancelbatchreducewithholdconsumer:
          destination: dl-magicvideo-cancel-batch-withhold
          content-type: application/json
          group: dl-magicvideo-group
        generatepreviewdataconsumer:
          destination: dl-magicvideo-generate-preview
          content-type: application/json
          group: dl-magicvideo-group
        producejobsubordinatettssuccessconsumer:
          destination: dl-magicvideo-tts-success
          content-type: application/json
          group: dl-magicvideo-group
        # 生产者通道配置（匹配DlChannels接口中的@Output注解）
        interactiveconfcopy:
          destination: dl-magicvideo-interactive-conf-copy
          content-type: application/json
        visualproduceshare:
          destination: dl-magicvideo-visual-produce-share
          content-type: application/json
        visualproducejobready:
          destination: dl-magicvideo-produce-job-created
          content-type: application/json
        visualproducebatchcancel:
          destination: dl-magicvideo-batch-cancel
          content-type: application/json
        producefailproducer:
          destination: dl-magicvideo-produce-fail
          content-type: application/json
        generatepreviewdataproducer:
          destination: dl-magicvideo-generate-preview
          content-type: application/json
        producejobsubordinatettssuccessproducer:
          destination: dl-magicvideo-tts-success
          content-type: application/json
server:
  port: 8082
  servlet:
    context-path: /
    multipart:
        max-file-size: 1000MB
        max-request-size: 1000MB
        enabled: true
# Logging Configuration
logging:
  level:
    com.dl.magicvideo: INFO
    org.springframework: WARN
    org.mybatis: DEBUG
    cn.easyes: INFO

# Swagger Configuration
swagger:
  enable: false

# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# SM4 Encryption Configuration
sm4:
  salt: da024ustkidtclu3

# Visual Configuration
visual:
  fileTempPath: /root/backend/data/magicvideo/tmp/visual/
  tmpDir: /root/backend/data/magicvideo/tmp/
  produce:
    segmentNum: 4
    tmpDir: /root/backend/data/magicvideo/producetmp/
    estimate-cost-ratio: 1.0

dl:
  material:
    commonTenantCode: SCK
  aiservice:
    baseUrl: http://************/aiservice
    digitalman:
      callback: http://************:8083/api/v1/batch/callback # 批次回调地址，需要调整
  basicservice:
    baseUrl: http://************/basicservice
  saasmagic:
    baseUrl: http://************/magicvideo
  
  # Redis Configuration
  redis:
    comName: dl-
    appName: magicvideo
  
  # Session Configuration
  session:
    adm:
      expire: 7200
    app:
      expire: 7200
    toCust:
      expire: 7200
  
  # Tencent Cloud Configuration
  tencentcloud:
    cos:
      bucketId: pelotavatar
      region: oss-cn-shanghai
      accessKeyId: LTAI5tJSisnsWjGXMEYvf7Hb
      accessKeySecret: ******************************
  
  # AIGC Configuration
  aigc:
    single-chat:
      presuppose-text: "你是一个投资专家，结合我提供的内容，生成一篇短视频脚本。要求：1.输出结构：标题，开场，正文，总结2.内容信息繁杂，你可自行筛选重点要点3.创作内容要求客观严谨，实事求是，语句要朴实，不要有夸张、比喻等修辞4.表达需要口语化，通俗易懂，逻辑连贯顺畅，让人能耐心阅读下去5.标题不超过15个字6.开场需要简要概括有吸引力，不超过30个字。7.总结要非常简洁，不超过30个字。8.正文一定不能超过500个字。9.请不要出现长英文单词和词组。 内容"
    extract-file:
      contentAndTitle:
        presuppose-text: "你是一个投资专家，请提取这个文件的标题。要求：1.标题一定不能超过15个字  2.内容信息繁杂，你可自行筛选重点要点3.创作内容要求客观严谨，实事求是，语句要朴实，不要有夸张、比喻等修辞。"
  
  # ASR Configuration
  asr:
    channel: 1
  
  # Batch Template Configuration
  batchTemplateIds: "1,2,3"
  
  # Subtitle Mark Replace Words Configuration
  subtitle:
    mark:
      replace-words-map: "{}"

# Authentication Configuration
auth:
  tenants:
    - tenantCode: DL
      privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCbJ759JkFrWSOWenRA0Wfn4jaUMjSX7EtkuY/9IfASnhkRtdxsK0O+8S2lDbEW3SiQmiiumt47GBbKdU+fmn9mo4DAp7/KzFPjoE+OuDaxr8SatFEdfPxkLUNIt7Uj4XgaYwzZXvkZhDaJr0OuLfa2XBtnpYFuDkZb/AqgnPeQsBK3FJKyKkmtb3M7Ljj8F9hdMckG2GRxoyBrl96IKCEdOa1qpg58zZI2FbZI5CEdpr8oZ1wQv6z39YS4022u3THy1c49gAtEld1cSubA/AusgMequ0oaTjHRbZ+CehHiprbhPWn7Pt8IJGj/0a9hqnbFhVUIWI44xcZXB4VGV2ZzAgMBAAECggEAUJofDWjYR2oxYLUQu7ONpRsRe39xbxMkP5lewgPlceaL43V0owT+1qz4J2yreNM/hY9wXvS5Sj3DaT30NVfPo4SBGJSMwE/RrMjVS7FLSJelLTFLimQpwej5sUcuZQt2l06pmCsMgUL8Ch7wrAXYyveijP+f84qa5qJS6wlgWtstIY7n4uzCL5OzRSKoW0HC54qrUFI/L0JYfsgI6MzJxN72CThvKpgqItyBanAz4/H1csbegVKX0kLC2w+R2rpiXevL8JId+yZ54HKiUmIFEtPDP+8fpOSPbT7FAD/kyxcdGj/3zbFY60e/s/1zH8K15KsG9Uh3wW3e+HzaYLS8IQKBgQDe0CovzXV7KSDGkAa4Qe1t6jeSA80Lq//RRF8jKXCuJT3SnangqqWJBvZZ3/NxcUEvAN0c/YowTCeMA7hES1D7WkTi2TyL+fIdNlUAFltKsz+PqI37Zrod5ZrHCq3y5Mew4Y6OPW1X76qJ33Y9D+P0kNMzoUTYqt21b9NTv0fEawKBgQCyQ8r9eaR7SpcrKnxS3El/x2gYOH6PbPHL/oIqyKZgSyBFZF+3PBuEs9E0NHlLFHbRGaCGFdUYx3S5jBPzjIK+7rhlhDCeqtZcxF89YsGpfgIWMHKT7GhEsUMmrOgR2w7MmgepAROxQj94Q+Jsbid+jmDdiBsV1NiHPPwUEg2oGQKBgQChrYVfKIQ9+UsKKkpl+5jQsCrgrhdkh8taS3WJazGTe/yPTs6M8uapNr8d2i6pO5gkBklsFuHmR+xOYgicrdY2fXsM7LtNivHPlrQ5Gv/lhYnysUlNim408X3NPoeYf8ATLqiluBcWvxcNcnQ2vMgZl9lZVoVBf0LfvCQpWdw2vwKBgEE3XAPvhKU2XKeGG4WU4a7FnOd/g42lJbCjo6tTTMrdsSix1/KJIughgN/Acr9s9Sr6XSewxQ0TqzhWbtYjCZIgc4VwHvltNo8pqE4k2wTO/KRxhPlo+5xl3VNA3oXpxjhEAZlqs3Gd8upkq2lPw1Mhc36YVJBgFfcj8HTHRgfBAoGAGkWuthKaxojsmzzah/MNo8IvrzLP9c0SLzLds8773TioOtG13OVL/HekNWvyUQxbX/txEOtqQgjJdui0PJs5gFFvlfU8EDuiLofxLNx2nCnUiTVvQ8P+lvZ0yAbNIYKnyGU50leMJ6/zTQ0YxzxGaQfQFtqDp31KCw/SkZJcgTs=
  ignore:
    urls:
      - /swagger-ui.html
      - /swagger-resources/**
      - /v2/api-docs
      - /webjars/**
      - /actuator/**

# Short URL Configuration
short:
  url:
    domain: https://test.dinglitec.com

# DX Image Configuration
dx:
  image:
    domain: https://magictest.dinglitec.com

# Common Tenant Configuration
common:
  tenantCode: DL

---
# Production Environment


