package com.dl.magicvideo.biz.manager.visual.bo;

import lombok.Data;

import java.util.Date;

@Data
public class ProduceJobMsgBO {

    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 任务状态：-1-未开始 0-排队中 1-合成中 2-合成成功 3-合成失败
     */
    private Integer status;

    /**
     *
     */
    private String name;

    /**
     *
     */
    private String coverUrl;

    /**
     * 合成视频地址
     */
    private String videoUrl;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * job_type=0:模板id;job_type=1:卡片id
     */
    private Long templateId;

    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 合成开始时间
     */
    private Date processDt;

    /**
     * 合成结束时间
     */
    private Date completeDt;

    /**
     * 来源 0-平台触发 1-接口触发
     */
    private Integer source;

    /**
     * 视频时长
     */
    private Long duration;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 智能任务完成时间
     */
    private Date aiCompleteDt;

    /**
     * 作品大小
     */
    private Long size;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 分片状态 0-未分片 1-已分片 2-分片完成
     */
    private Integer segmentStatus;
}
