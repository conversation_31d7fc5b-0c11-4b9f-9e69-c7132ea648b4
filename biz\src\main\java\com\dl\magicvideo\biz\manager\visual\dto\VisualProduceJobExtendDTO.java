package com.dl.magicvideo.biz.manager.visual.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * DTO: 作品扩展信息表
 */
@Data
public class VisualProduceJobExtendDTO {

    @ApiModelProperty("自增主键")
    private Long id;

    @ApiModelProperty("任务id")
    private Long produceJobId;

    @ApiModelProperty("租户编号")
    private String tenantCode;

    /**
     * @see: ShareConfStateEnum
     */
    @ApiModelProperty("转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置")
    private Integer shareConfState;

    @ApiModelProperty("推荐使用状态 0-未启用 1-已启用")
    private Integer recommendState;

    @ApiModelProperty("推荐启用的时间")
    private Date recommendEnableDt;

    @ApiModelProperty("是否删除，0-否，1-是")
    private Integer isDeleted;

    @ApiModelProperty("自研编辑器数字人视频合成方式。 0-模板每个卡片合成1次请求数字人合成视频，并通过ASR识别时间戳。1-模板每个卡片都请求数字人合成视频方式。")
    private Integer dmProduceMode;
}
