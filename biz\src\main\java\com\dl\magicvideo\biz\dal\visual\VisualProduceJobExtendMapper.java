package com.dl.magicvideo.biz.dal.visual;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobSearchBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Entity com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO
 */
@BaseDao
public interface VisualProduceJobExtendMapper extends BaseMapper<VisualProduceJobExtendPO> {
    List<VisualProduceJobPO> recommendJobPage(@Param("param")ProduceJobSearchBO param);

    Long recommendJobTotal (@Param("param")ProduceJobSearchBO param);
}




