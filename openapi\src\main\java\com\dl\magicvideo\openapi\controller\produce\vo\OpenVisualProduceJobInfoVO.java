package com.dl.magicvideo.openapi.controller.produce.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-02 10:25
 */
@Data
@ApiModel("数影作品信息vo")
public class OpenVisualProduceJobInfoVO {

    @ApiModelProperty("任务id")
    private String jobId;

    @ApiModelProperty("任务状态：-1-未开始 0-就绪 1-合成中 2-合成成功 3-合成失败 4-已取消")
    private Integer status;

    @ApiModelProperty("作品名称")
    private String name;

    @ApiModelProperty("合成视频地址")
    private String videoUrl;

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("作品封面")
    private String coverUrl;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("构建开始时间")
    private Date processDt;

    @ApiModelProperty("构建结束时间")
    private Date completeDt;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("1-竖版 2-横版")
    private String resolutionType;

    @ApiModelProperty("视频时长，单位毫秒")
    private Long duration;

    @ApiModelProperty("视频大小，单位 字节")
    private Long size;

    @ApiModelProperty("创建人姓名")
    private String creatorName;

    @ApiModelProperty("外部用户id")
    private String extUserId;

}
