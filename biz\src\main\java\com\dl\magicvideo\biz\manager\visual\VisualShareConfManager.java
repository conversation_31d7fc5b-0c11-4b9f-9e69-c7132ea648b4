package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.visual.po.VisualShareConfPO;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfCopyBO;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfQueryBO;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfSaveBO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualSharePageBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualShareConfDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;

/**
 *
 */
public interface VisualShareConfManager extends IService<VisualShareConfPO> {
    /**
     * 保存转发配置
     */
    void save(ShareConfSaveBO saveBO);

    VisualShareConfDTO info(ShareConfQueryBO shareConfQueryBO);

    /**
     * 查询视频转发设置详情以及视频播放链接
     *
     * @param jobId
     * @return
     */
    VisualShareConfDTO getProduceJobShareConfDetail(Long jobId);

    /**
     * 查询已配置转发设置的视频作品
     *
     * @param queryBO
     * @return
     */
    IPage<VisualProduceJobDTO> pageQueryJob(VisualSharePageBO queryBO);

    IPage<VisualTemplateDTO> pageQueryTemplate(VisualSharePageBO queryBO);
    
    /**
     * 复制转发配置
     */
    void copyShareConf(ShareConfCopyBO shareConfCopyBO);
}
