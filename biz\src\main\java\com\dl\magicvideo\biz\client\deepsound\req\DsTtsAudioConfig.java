package com.dl.magicvideo.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsAudioTrainSource
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 18:17
 * @Version 1.0
 **/
@Data
public class DsTtsAudioConfig implements Serializable {

    private static final long serialVersionUID = -4213730735414879102L;

    /**
     * 音频格式 默认为 audio/L16;rate=44100；
     * 可设置为 audio/L16;rate=8000、audio/L16;rate=16000、audio/L16;rate=24000、audio/L16;rate=44100。
     * 当 audio-encode 设置为 audio/mp3 时，该参数须设置为 audio/L16;rate=44100
     */
    @JsonProperty(value = "audio-format", defaultValue = "audio/L16;rate=44100")
    private String audioFormat = "audio/L16;rate=44100";

    /**
     * 音频编码
     * 可选audio/mp3 audio/wav audio/opus
     * 其中：
     * audio/raw 表示音频为 wav 编码；
     * audio/opus 为不带容器的 Opus 格 式；每一帧头为 2 字节大端格式的整 数，表示帧数据长度。
     */
    @JsonProperty(value = "audio-encode", defaultValue = "audio/mp3")
    private String audioEncode = "audio/mp3";

    /**
     * 返回数据
     * 格式 可选 raw、json、stream。
     * 选 raw 或 stream 时返回音频的二进制 流；
     * 选 json 时返回音频的 URL；其中 raw 和 json 支持时间戳，stream 不支持时间戳。
     */
    @JsonProperty(value = "output-format", defaultValue = "json")
    private String outputFormat = "json";

    /**
     * 音调 默认值：normal；
     * 可选： lower/low/normal/high/higher
     */
    @JsonProperty(value = "pitch", defaultValue = "normal")
    private String pitch = "normal";

    /**
     * 语速 默认值：normal；
     * 可选： lower/low/normal/high/higher
     */
    @JsonProperty(value = "speed", defaultValue = "normal")
    private String speed = "normal";

    /**
     * 音质 训练的音色默认为 high
     */
    @JsonProperty(value = "quality", defaultValue = "high")
    private String quality = "high";

    /**
     * 音量 取值范围 0.0-1.0，1.0 表示最大音量。
     */
    @JsonProperty(value = "volume", defaultValue = "1.0")
    private String volume = "1.0";
}
