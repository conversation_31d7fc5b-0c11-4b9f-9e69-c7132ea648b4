package com.dl.magicvideo.openapi.controller.template.vo;

import com.dl.magicvideo.biz.manager.visual.dto.CrossClipsDTO;
import com.dl.magicvideo.biz.manager.visual.dto.LightEditConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class OpenCardVO {
    @ApiModelProperty("卡片id")
    private String cardId;

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("卡片名称")
    private String name;

    @ApiModelProperty("卡片封面")
    private String coverUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    /**
     * @see LightEditConfigDTO
     */
    @ApiModelProperty("轻编辑配置")
    private List<LightEditConfigDTO> lightEditConfigs;

    /**
     * @see LightEditConfigDTO
     */
    @ApiModelProperty("跨片段组建")
    private List<CrossClipsDTO> crossClips;

    @ApiModelProperty("动态节点")
    private List<OpenDynamicNodeVO> dynamicNodes;
}
