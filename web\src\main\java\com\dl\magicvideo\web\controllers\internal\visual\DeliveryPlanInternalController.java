package com.dl.magicvideo.web.controllers.internal.visual;

import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.manager.visual.DeliveryPlanManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.DeliveryPlanBO;
import com.dl.magicvideo.biz.manager.visual.bo.DeliveryPlanSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.DeliveryPlanDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.param.DeliveryPlanPageQueryParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.AddDeliveryPlanParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.TemplateInternalPageQueryParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.UpdateDeliveryPlanParam;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualTemplateInternalVO;
import com.dl.magicvideo.web.controllers.internal.vo.DeliveryPlanVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @describe: DeliveryPlanInternalController
 * @author: zhousx
 * @date: 2023/9/5 11:41
 */
@RestController
@RequestMapping("/visual/internal/deliveryplan")
public class DeliveryPlanInternalController extends AbstractController {
    @Autowired
    private DeliveryPlanManager deliveryPlanManager;
    @Autowired
    private VisualTemplateManager templateManager;

    @PostMapping("/add")
    public ResultModel<Void> add(@RequestBody @Validated AddDeliveryPlanParam param) {
        DeliveryPlanBO bo = new DeliveryPlanBO();
        bo.setName(param.getName());
        bo.setDesc(param.getDesc());
        bo.setTemplateId(param.getTemplateId());
        bo.setTenantCode(param.getTenantCode());
        bo.setTenantName(param.getTenantName());
        bo.setDirector(param.getDirector());
        bo.setLimit(param.getLimit());
        bo.setPeriod(param.getPeriod());
        bo.setMobile(param.getMobile());
        bo.setIsNotify(param.getIsNotify());
        bo.setCallbackUrl(param.getCallbackUrl());
        bo.setNotifyUrl(param.getNotifyUrl());
        bo.setProduceWay(param.getProduceWay());
        bo.setCreateBy(param.getCreateBy());
        bo.setHasPeriod(param.getHasPeriod());
        deliveryPlanManager.addDeliveryPlan(bo);
        return ResultModel.success(null);
    }

    @PostMapping("/list")
    public ResultPageModel<DeliveryPlanVO> list(@RequestBody DeliveryPlanPageQueryParam param) {
        DeliveryPlanSearchBO searchBO = new DeliveryPlanSearchBO();
        searchBO.setName(param.getName());
        searchBO.setTenantName(param.getTenantName());
        searchBO.setPageIndex(param.getPageIndex());
        searchBO.setPageSize(param.getPageSize());
        ResponsePageQueryDO<List<DeliveryPlanDTO>> result = deliveryPlanManager.pageQuery(searchBO);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<DeliveryPlanVO> vos = result.getDataResult().stream().map(dto -> {
            DeliveryPlanVO vo = new DeliveryPlanVO();
            vo.setPlanId(dto.getPlanId());
            vo.setName(dto.getName());
            vo.setDesc(dto.getDesc());
            vo.setTenantCode(dto.getTenantCode());
            vo.setTenantName(dto.getTenantName());
            vo.setTemplateId(dto.getTemplateId());
            vo.setTemplateName(dto.getTemplateName());
            vo.setTemplateCoverUrl(dto.getTemplateCoverUrl());
            vo.setDirector(dto.getDirector());
            vo.setMobile(dto.getMobile());
            vo.setLimit(dto.getLimit());
            vo.setPeriod(dto.getPeriod());
            vo.setCreateDt(dto.getCreateDt());
            vo.setProduceWay(dto.getProduceWay());
            vo.setIsNotify(dto.getIsNotify());
            vo.setNotifyUrl(dto.getNotifyUrl());
            vo.setCallbackUrl(dto.getCallbackUrl());
            vo.setStatus(dto.getStatus());
            vo.setHasPeriod(dto.getHasPeriod());
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    @PostMapping("/update")
    public ResultModel<Void> update(@RequestBody @Validated UpdateDeliveryPlanParam param) {
        DeliveryPlanBO bo = new DeliveryPlanBO();
        bo.setName(param.getName());
        bo.setDesc(param.getDesc());
        bo.setDirector(param.getDirector());
        bo.setLimit(param.getLimit());
        bo.setPeriod(param.getPeriod());
        bo.setMobile(param.getMobile());
        bo.setPlanId(param.getPlanId());
        bo.setStatus(param.getStatus());
        bo.setIsNotify(param.getIsNotify());
        bo.setCallbackUrl(param.getCallbackUrl());
        bo.setNotifyUrl(param.getNotifyUrl());
        bo.setProduceWay(param.getProduceWay());
        bo.setHasPeriod(param.getHasPeriod());
        deliveryPlanManager.udpateDeliveryPlan(bo);
        return ResultModel.success(null);
    }

    @PostMapping("/templatelist")
    public ResultPageModel<VisualTemplateInternalVO> templateList(@RequestBody @Validated TemplateInternalPageQueryParam param) {
        TemplateSearchBO bo = new TemplateSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setName(param.getName());
        bo.setTenantCode(param.getTenantCode());
        bo.setStatus(Const.ZERO);

        ResponsePageQueryDO<List<VisualTemplateDTO>> result = templateManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<VisualTemplateInternalVO> vos = result.getDataResult().stream().map(dto -> {
            VisualTemplateInternalVO vo = new VisualTemplateInternalVO();
            vo.setName(dto.getName());
            vo.setTenantCode(dto.getTenantCode());
            vo.setTemplateId(dto.getTemplateId() + "");
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }
}
