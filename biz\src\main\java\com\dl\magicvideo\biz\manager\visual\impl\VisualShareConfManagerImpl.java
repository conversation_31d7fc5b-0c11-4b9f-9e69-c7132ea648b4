package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.VisualShareConfMapper;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobShareInfoPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobSharePageQueryPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualShareConfPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualShareConfManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfCopyBO;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfQueryBO;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfSaveBO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualProduceJobExtendBO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualSharePageBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualInteractiveCopyDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualShareConfDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.biz.manager.visual.enums.ShareConfStateEnum;
import com.dl.magicvideo.biz.manager.visual.helper.ProduceJobHelper;
import com.dl.magicvideo.biz.mq.DlChannels;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class VisualShareConfManagerImpl extends ServiceImpl<VisualShareConfMapper, VisualShareConfPO> implements VisualShareConfManager {

    @Resource
    private VisualProduceJobManager visualProduceJobManager;
    @Resource
    private DlChannels dlChannels;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;
    @Resource
    private VisualShareConfMapper visualShareConfMapper;

    @Resource
    private VisualTemplateManager visualTemplateManager;

    @Override
    public void save(ShareConfSaveBO saveBO) {
        VisualShareConfPO existPO = this.getOne(Wrappers.lambdaQuery(VisualShareConfPO.class)
                .eq(VisualShareConfPO::getBizId, saveBO.getBizId())
                .eq(VisualShareConfPO::getBizType, saveBO.getBizType())
                .eq(VisualShareConfPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(existPO)) {
            VisualShareConfPO insertPO = new VisualShareConfPO();
            insertPO.setBizId(saveBO.getBizId());
            insertPO.setBizType(saveBO.getBizType());
            insertPO.setH5ShareCoverImg(saveBO.getH5ShareCoverImg());
            insertPO.setMpShareCoverImg(saveBO.getMpShareCoverImg());
            insertPO.setCoverImg(saveBO.getCoverImg());
            insertPO.setUseCase(saveBO.getUseCase());
            insertPO.setShareTitle(saveBO.getShareTitle());
            insertPO.setShareRemark(saveBO.getShareRemark());
            insertPO.setRecommendShareCnt(saveBO.getRecommendShareCnt());
            insertPO.setNavigationBarTitle(saveBO.getNavigationBarTitle());
            insertPO.setIsDeleted(Const.ZERO);
            this.save(insertPO);
            return;
        }
        existPO.setShareTitle(saveBO.getShareTitle());
        existPO.setShareRemark(saveBO.getShareRemark());
        existPO.setRecommendShareCnt(saveBO.getRecommendShareCnt());
        existPO.setNavigationBarTitle(saveBO.getNavigationBarTitle());
        existPO.setH5ShareCoverImg(saveBO.getH5ShareCoverImg());
        existPO.setMpShareCoverImg(saveBO.getMpShareCoverImg());
        existPO.setCoverImg(saveBO.getCoverImg());
        existPO.setUseCase(saveBO.getUseCase());
        this.updateById(existPO);
    }

    @Override
    public VisualShareConfDTO info(ShareConfQueryBO shareConfQueryBO) {
        VisualShareConfPO po = this.getOne(Wrappers.lambdaQuery(VisualShareConfPO.class)
                .eq(VisualShareConfPO::getBizType, shareConfQueryBO.getBizType())
                .eq(VisualShareConfPO::getBizId, shareConfQueryBO.getBizId())
                .eq(VisualShareConfPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(po)) {
            return null;
        }

        VisualShareConfDTO result = new VisualShareConfDTO();
        result.setBizId(po.getBizId());
        result.setBizType(po.getBizType());
        result.setUseCase(po.getUseCase());
        result.setShareRemark(po.getShareRemark());
        result.setShareTitle(po.getShareTitle());
        result.setRecommendShareCnt(po.getRecommendShareCnt());
        result.setNavigationBarTitle(po.getNavigationBarTitle());
        result.setH5ShareCoverImg(po.getH5ShareCoverImg());
        result.setMpShareCoverImg(po.getMpShareCoverImg());
        result.setCoverImg(po.getCoverImg());
        return result;
    }

    @Override
    public VisualShareConfDTO getProduceJobShareConfDetail(Long jobId) {
        //查询作品获取播放链接以及视频名称
        LambdaQueryWrapper<VisualProduceJobPO> produceJobWrapper = Wrappers.lambdaQuery(VisualProduceJobPO.class);
        produceJobWrapper.eq(VisualProduceJobPO::getJobId, jobId);
        VisualProduceJobPO visualProduceJob = visualProduceJobManager.getOne(produceJobWrapper);
        if (Objects.isNull(visualProduceJob)){
            return null;
        }
        VisualTemplatePO visualTemplatePO = visualTemplateManager.getOne(Wrappers.lambdaQuery(VisualTemplatePO.class)
                .eq(VisualTemplatePO::getTemplateId, visualProduceJob.getTemplateId()));

        //查询转发配置
        ShareConfQueryBO shareConfQueryBO = new ShareConfQueryBO();
        shareConfQueryBO.setBizId(jobId);
        shareConfQueryBO.setBizType(Const.TWO);
        VisualShareConfDTO visualShareConfInfo = this.info(shareConfQueryBO);
        visualShareConfInfo.setVideoUrl(visualProduceJob.getVideoUrl());
        visualShareConfInfo.setDuration(visualProduceJob.getDuration());
        visualShareConfInfo.setVideoName(visualProduceJob.getName());
        visualShareConfInfo.setTenantCode(visualProduceJob.getTenantCode());
        if (StringUtils.isNotBlank(visualTemplatePO.getResolutionType())){
            visualShareConfInfo.setResolutionType(Integer.valueOf(visualTemplatePO.getResolutionType()));
        }
        visualShareConfInfo.setIsDeleted(visualProduceJob.getIsDeleted());
        return visualShareConfInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyShareConf(ShareConfCopyBO copyBO) {
        //1.保存基础转发配置
        ShareConfQueryBO shareConfQueryBO = new ShareConfQueryBO();
        shareConfQueryBO.setBizId(copyBO.getSourceBizId());
        shareConfQueryBO.setBizType(copyBO.getSourceBizType());
        VisualShareConfDTO visualShareConfDTO = this.info(shareConfQueryBO);
        if (Objects.isNull(visualShareConfDTO)) {
            return;
        }

        VisualShareConfPO visualShareConfPO = new VisualShareConfPO();
        BeanUtils.copyProperties(visualShareConfDTO, visualShareConfPO, "createDt", "modifyDt", "id");
        visualShareConfPO.setBizId(copyBO.getTargetBizId());
        visualShareConfPO.setBizType(copyBO.getTargetBizType());

        if (Objects.isNull(operatorUtil.getOperator())) {
            operatorUtil.init(Const.SYS_USER_ID, "", copyBO.getTargetTenantCode(), "");
        }
        this.save(visualShareConfPO);
        if (copyBO.getTargetBizType().equals(Const.TWO)) {
            //2.作品更改基础转发配置标志位
            VisualProduceJobExtendBO visualProduceJobExtendBO = new VisualProduceJobExtendBO();
            visualProduceJobExtendBO.setBizId(copyBO.getTargetBizId());
            visualProduceJobExtendBO.setShareConfState(ShareConfStateEnum.BASH_SHARE.getCode());
            visualProduceJobExtendManager.saveOrUpdateExtend(visualProduceJobExtendBO);
        }

        //发送消息给wc保存交互式配置
        VisualInteractiveCopyDTO target = new VisualInteractiveCopyDTO();
        BeanUtils.copyProperties(copyBO, target);
        sendMQ(target);
    }


    private void sendMQ(VisualInteractiveCopyDTO interactiveSyncDTO) {
        try {
            Message message = MessageBuilder.withPayload(interactiveSyncDTO).build();
            boolean sendResult = dlChannels.interactiveConfCopy().send(message, 1000L);
            log.info("发送复制交互式信息,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
        } catch (Exception e) {
            log.error("发送复制交互式信息发送异常,msgDTO:" + JSONUtil.toJsonStr(interactiveSyncDTO), e);
        }
    }

    @Override
    public IPage<VisualProduceJobDTO> pageQueryJob(VisualSharePageBO queryBO) {
        VisualProduceJobSharePageQueryPO queryPO = new VisualProduceJobSharePageQueryPO();
        queryPO.setName(queryBO.getName());
        queryPO.setSortType(queryBO.getSortType());
        queryPO.setResolutionType(queryBO.getResolutionType());
        queryPO.setTenantCode(queryBO.getTenantCode());
        queryPO.setPageIndex(queryBO.getPageIndex());
        queryPO.setStartTime(queryBO.getStartTime());
        queryPO.setEndTime(queryBO.getEndTime());
        queryPO.setPageSize(queryBO.getPageSize());
        //1.根据查询条件查询已配置转发配置的视频作品
        List<VisualProduceJobShareInfoPO> visualProduceJobShareInfoList = visualShareConfMapper.selectVisualProduceJobExtendInfoPOList(
                queryPO);
        if (CollectionUtils.isEmpty(visualProduceJobShareInfoList)) {
            return new Page<>(queryBO.getPageIndex(), queryBO.getPageSize());
        }
        //2.根据查询条件查询视频作品总数量
        Long total = visualShareConfMapper.selectVisualProduceJobExtendInfoPOTotal(queryPO);

        //3.类型转化
        List<VisualProduceJobDTO> visualProduceJobDTOList = visualProduceJobShareInfoList.stream().map(
                ProduceJobHelper::cnvVisualShareInfoPO2ProduceJobDTO).collect(Collectors.toList());

        log.info("visualProduceJobDTOList = " + JSONUtil.toJsonStr(visualProduceJobDTOList));
        //4.构建分页对象
        Long totalPages = (total + queryBO.getPageSize() - 1) / queryBO.getPageSize();
        Page<VisualProduceJobDTO> result = new Page<>(queryBO.getPageIndex(), queryBO.getPageSize(), total);
        result.setPages(totalPages);
        result.setRecords(visualProduceJobDTOList);
        return result;
    }

    @Override
    public IPage<VisualTemplateDTO> pageQueryTemplate(VisualSharePageBO queryBO) {
        VisualProduceJobSharePageQueryPO queryPO = new VisualProduceJobSharePageQueryPO();
        queryPO.setName(queryBO.getName());
        queryPO.setSortType(queryBO.getSortType());
        queryPO.setResolutionType(queryBO.getResolutionType());
        queryPO.setTenantCode(queryBO.getTenantCode());
        queryPO.setPageIndex(queryBO.getPageIndex());
        queryPO.setPageSize(queryBO.getPageSize());

        //1.根据查询条件查询已配置转发配置的模板
        List<VisualTemplatePO> visualTemplatePOList = visualShareConfMapper.selectVisualShareTemplateList(queryPO);
        if (CollectionUtils.isEmpty(visualTemplatePOList)) {
            return new Page<>(queryBO.getPageIndex(), queryBO.getPageSize());
        }

        //2.根据查询条件查询视频作品总数量
        Long total = visualShareConfMapper.selectVisualProduceJobExtendInfoPOTotal(queryPO);

        //3.类型转化
        List<VisualTemplateDTO> visualTemplateDTOList = visualTemplatePOList.stream().map(this::cnvVisualTemplatePO2DTO)
                .collect(Collectors.toList());

        log.info("visualTemplateDTOList = " + JSONUtil.toJsonStr(visualTemplateDTOList));
        //4.构建分页对象
        Long totalPages = (total + queryBO.getPageSize() - 1) / queryBO.getPageSize();
        Page<VisualTemplateDTO> result = new Page<>(queryBO.getPageIndex(), queryBO.getPageSize(), total);
        result.setPages(totalPages);
        result.setRecords(visualTemplateDTOList);
        return result;
    }

    private VisualTemplateDTO cnvVisualTemplatePO2DTO(VisualTemplatePO templatePO) {
        if (Objects.isNull(templatePO)) {
            return null;
        }
        VisualTemplateDTO templateDTO = new VisualTemplateDTO();
        templateDTO.setTemplateId(templatePO.getTemplateId());
        templateDTO.setStatus(templatePO.getStatus());
        templateDTO.setName(templatePO.getName());
        templateDTO.setCoverUrl(templatePO.getCoverUrl());
        templateDTO.setPreviewVideoUrl(templatePO.getPreviewVideoUrl());
        templateDTO.setShortVideoUrl(templatePO.getShortVideoUrl());
        templateDTO.setResolution(templatePO.getResolution());
        templateDTO.setBgMusic(templatePO.getBgMusic());
        templateDTO.setBgMusicParam(templatePO.getBgMusicParam());
        templateDTO.setTtsParam(templatePO.getTtsParam());
        templateDTO.setReplaceData(templatePO.getReplaceData());
        templateDTO.setResolutionType(templatePO.getResolutionType());
        templateDTO.setTenantCode(templatePO.getTenantCode());
        templateDTO.setDuration(templatePO.getDuration());
        templateDTO.setCreateDt(templatePO.getCreateDt());
        templateDTO.setModifyDt(templatePO.getModifyDt());
        templateDTO.setPreviewVideoUrl(templatePO.getPreviewVideoUrl());
        templateDTO.setShortVideoUrl(templatePO.getShortVideoUrl());
        templateDTO.setShareConfState(templateDTO.getShareConfState());
        return templateDTO;
    }
}




