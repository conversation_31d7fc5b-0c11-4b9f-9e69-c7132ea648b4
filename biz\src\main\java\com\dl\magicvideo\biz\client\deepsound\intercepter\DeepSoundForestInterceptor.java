package com.dl.magicvideo.biz.client.deepsound.intercepter;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.client.deepsound.DeepSoundClient;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DeepSoundForestInterceptor implements Interceptor {

    private static final String X_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
            +
            ".eyJhcHBpZCI6IjAwYTU3MjQ0NjNmZTVlNGM1ODNiNzAxZWU1MjE5YzkzIiwiZXhwIjoiMTcwNzc1MzU5OSIsImlhdCI6IjE2NzYyODAwMDAiLCJpc3MiOiJkZWVwc291bmQuY24iLCJzdWIiOiJ0dHMifQ.MTc3MDNmZmU3YjYzMDYzMTU4ZDAwMjk5Yjk1NmYzNzA5YTE3YTRjNDVlYjA1NDIzYmY4NDExNTJlNDNmYjYzMg";

    @SneakyThrows
    @Override
    public boolean beforeExecute(ForestRequest request) {
        request.addHeader(DeepSoundClient.APP_ID, DeepSoundClient.X_APPID);
        request.addHeader(DeepSoundClient.APP_KEY, DeepSoundClient.X_APP_KEY);
        request.addHeader(DeepSoundClient.APP_TOKEN, X_TOKEN);
        return Boolean.TRUE;
    }

    /**
     * 成功判断方式
     *
     * @param data
     * @param request
     * @param response
     */
    public void onSuccess(Object data, ForestRequest request, ForestResponse response) {
        log.error("调用成功,resp>>{}", JSONUtil.toJsonStr(data));
       /* if (data instanceof DsBaseResponse) {
            DsBaseResponse resp = (DsBaseResponse) data;
            RespUtil.isSuccess(resp);
        } else if (data instanceof DsTtsResponse) {
            DsTtsResponse resp = (DsTtsResponse) data;
            if (!resp.isSuccess()) {
                log.error("调用异常：response:{}", JSONUtil.toJsonStr(resp));
                throw BusinessServiceException.getInstance(resp.getErrCode().toString(), resp.getErrMsg());
            }
        }*/
    }

}
