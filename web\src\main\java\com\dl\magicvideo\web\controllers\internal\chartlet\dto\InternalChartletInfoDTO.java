package com.dl.magicvideo.web.controllers.internal.chartlet.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-12 19:33
 */
@Data
public class InternalChartletInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("bizId")
    private Long bizId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("内容url")
    private String contentUrl;

    @ApiModelProperty("分辨率")
    private String resolutionRatio;

    @ApiModelProperty("封面图")
    private String coverImg;

    @ApiModelProperty("时长，毫秒")
    private Long duration;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("修改人名称")
    private String modifyName;

    @ApiModelProperty("修改人")
    private Long modifyBy;
    
    @ApiModelProperty("修改时间")
    private Date modifyDt;

    @ApiModelProperty("分类编码")
    private Integer category;

    @ApiModelProperty("分类名称")
    private String categoryName;

}
