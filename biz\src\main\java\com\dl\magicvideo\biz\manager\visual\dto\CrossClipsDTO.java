package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Data
public class CrossClipsDTO {
    /**
     * 组件类型
     * 'image' | 'video' | 'anchor' | 'chart' | 'chartlet' | 'text' | 'tts'
     */
    private String type;
    /**
     * 组件 ID
     */
    private String id;
    /**
     * 组件配置名称
     */
    private String name;
    /**
     * 数据源类型
     *
     * @value origin: 原始数据类型
     * @value replace: 外部数据 包括数据网关apiData,自定义数据replaceData
     */
    private String dataSource;
    /**
     * 值
     */
    private List<Integer> crossClips;

    /**
     * 值
     */
    private String value;

    private Map<String, Object> config;
}