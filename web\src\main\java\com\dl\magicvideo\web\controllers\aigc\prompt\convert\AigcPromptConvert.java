package com.dl.magicvideo.web.controllers.aigc.prompt.convert;

import com.alibaba.fastjson2.JSONObject;
import com.dl.magicvideo.biz.dal.aigc.po.AigcPromptPO;
import com.dl.magicvideo.web.controllers.aigc.prompt.vo.AigcPromptContentVO;
import com.dl.magicvideo.web.controllers.aigc.prompt.vo.AigcPromptFileVO;
import com.dl.magicvideo.web.controllers.aigc.prompt.vo.AigcPromptVO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 13:53
 */
public class AigcPromptConvert {

    public static AigcPromptVO cnvAigcPromptPO2VO(AigcPromptPO input) {
        AigcPromptVO result = new AigcPromptVO();
        result.setName(input.getName());
        result.setBizId(input.getBizId() + "");
        result.setSort(input.getSort());
        if (StringUtils.isNotBlank(input.getRelFile())) {
            AigcPromptFileVO fileVO = JSONObject.parseObject(input.getRelFile(), AigcPromptFileVO.class);
            result.setRelFile(fileVO);
        }
        if (StringUtils.isNotBlank(input.getContent())) {
            AigcPromptContentVO contentVO = JSONObject.parseObject(input.getContent(), AigcPromptContentVO.class);
            result.setContent(contentVO);
        }

        return result;
    }

}
