package com.dl.magicvideo.biz.manager.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 数影数据接口工具类
 */
@Slf4j
public class MagicExpressionUtils {
    public static void main(String[] args) {
        String jsonData = "{\"/szb/quarterlyReportSingle/a6dc8e\":{\"result\":{\"rDInputRatioForm\":{\"value\":[[\"占位列\",\"数据列\"],[\"0\",\"0.0045\"]]},\"rDInputRatioFlagTts\":\"<break time=\\\"500ms\\\"/>较去年同期增加，这是注重提升核心竞争力的表现。\",\"operatingRevenueIncreaseFlag\":false}}}";
//        String fontExperssion = "$data.apiData['/szb/quarterlyReportSingle/a6dc8e'].result.rDInputRatioForm != null || $data.apiData['/szb/quarterlyReportSingle/a6dc8e'].result.rDInputRatioFlagTts != \"\"";
        String fontExperssion = "$data.apiData['/szb/quarterlyReportSingle/a6dc8e'].result.operatingRevenueIncreaseFlag";
        boolean evaluate = MagicExpressionUtils.evaluate(jsonData, fontExperssion);
        System.out.println(evaluate);
    }
    /**
     * 前端表达式
     *
     * @param fontExperssion
     * @return
     */
    public static boolean evaluate(String jsonData, String fontExperssion) {
        boolean flag = false;
        try {
            //转化为后端实际表达式
            String[] expressionList = evaluateToken(fontExperssion);
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonData);

            for (String expression : expressionList) {
                flag = evaluateExpression(rootNode, expression);
                if (flag) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("fontExperssion={},执行异常", fontExperssion, e);
        }
        return flag;
    }

    /**
     * 对表达式进行解析 去除前段$data.apiData[' ']的内容
     * todo 需增加&& 的判断
     * @param expression
     * @return
     */
    private static String[] evaluateToken(String expression) {
        String[] tokens = expression.split("\\|\\|");
        String[] finalToken = new String[tokens.length];
        for (int i = 0; i < tokens.length; i++) {
            finalToken[i] = tokens[i].replaceAll("\\$data.apiData\\['", "").replaceAll("']", "");
        }
        return finalToken;
    }

    /**
     * 对前端表达式进行解析
     *
     * @param jsonData
     * @param jsonData
     * @param expression
     * @return
     */
    private static boolean evaluateExpression(JsonNode jsonData, String expression) {
        try {
            String[] parts = expression.trim().split("\\s+"); // 拆分表达式中的字段、操作符和操作数
            //"/users/0/age", ">", "25"
            String field = parts[0];

            String[] splits = field.split("\\.");
            JsonNode resultNode = jsonData.path(splits[0]);
            if (splits.length > 1){
                for (int i = 1; i < splits.length; i++){
                    resultNode = resultNode.path(splits[i]);
                }
            }
            if (parts.length == 1){
                //只有一个表示判断是否true
                return resultNode.isBoolean() && resultNode.asBoolean();
            }
            String operator = parts[1];
            String operand = parts[2];
            //JsonNode resultNode = jsonData.at("/" + field.replace(".", "/")); // 使用 JSON Pointer 获取字段对应的节点值
//            String value = resultNode.asText();

            switch (operator) {
            case ">":
                return resultNode.asLong() > Long.parseLong(operand);
            case "<":
                return resultNode.asLong() < Long.parseLong(operand);
            case "==":
            case "===":
                if (Objects.equals(operand, "null") || Objects.equals(operand, "\"\"")){
                    if (resultNode.isObject()){
                        return resultNode.isEmpty();
                    }
                    if (resultNode.isTextual()){
                        return StringUtils.isBlank(resultNode.asText());
                    }
                }
                if (resultNode.isTextual()){
                    return Objects.equals(resultNode.asText(), operand);
                } else {
                    return resultNode.asLong() == Long.parseLong(operand);
                }
            case "!=":
                if (Objects.equals(operand, "null") || Objects.equals(operand, "\"\"")){
                    if (resultNode.isObject()){
                        return !resultNode.isEmpty();
                    }
                    if (resultNode.isTextual()){
                        return StringUtils.isNotBlank(resultNode.asText());
                    }
                }
                if (resultNode.isTextual()){
                    return !Objects.equals(resultNode.asText(), operand);
                } else {
                    return resultNode.asLong() != Long.parseLong(operand);
                }
            default:
                log.error("Unsupported operator: " + operator);
                //注意，默认返回true，表示展示片段
                return true;
            }
        } catch (Exception e) {
            log.error("转化异常，expression={}", expression, e);
        }
        return false;
    }
}
