package com.dl.magicvideo.biz.manager.account.trial;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;

public interface AccountTenantTrialManager extends IService<AccountTenantTrialPO> {

    Integer checkTenantBalance(String tenantCode, Integer count);

    /**
     * 创建试用账户
     *
     * @param tenantCode
     * @param initBalance 初始余额
     */
    void createAccount(String tenantCode, Long initBalance);

}
