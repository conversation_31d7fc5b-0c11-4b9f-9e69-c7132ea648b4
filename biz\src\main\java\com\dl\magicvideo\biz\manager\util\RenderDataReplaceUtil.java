package com.dl.magicvideo.biz.manager.util;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayDeque;
import java.util.Deque;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-12-18 17:21
 */
public class RenderDataReplaceUtil {

    private static Map<String, String> cosMap = new HashMap<>();

    private static Set<String> domainSet = new HashSet<>();

    private static final String HTTPS = "https://";
    private static final String HTTP = "http://";

    private static Set<String> dmSrcSet = new HashSet<>();


    private static void extractHttpsValues(JsonNode jsonNode) {
        Deque<JsonNode> stack = new ArrayDeque<>();
        stack.push(jsonNode);

        while (!stack.isEmpty()) {
            JsonNode currentNode = stack.pop();
            if (currentNode.isTextual()) {
                if (currentNode.textValue().startsWith("https") || currentNode.textValue().startsWith("http")) {
                    domainSet.add(getHost(currentNode.textValue()));

                    /*if (!dmSrcSet.contains(currentNode.textValue())) {
                        cosMap.put(currentNode.textValue(), "");
                    }else{
                        System.out.println("该资源是数字人src:"+currentNode.textValue());
                    }*/
                    //domainSet.add(getHost(currentNode.textValue()));
                }
            } else if (currentNode.isArray()) {
                for (JsonNode element : currentNode) {
                    stack.push(element);
                }
            } else if (currentNode.isObject()) {
                /*if (Objects.nonNull(currentNode.get("type"))) {
                    if ("Anchor".equals(currentNode.get("type").asText()) || "PreviewAnchor"
                            .equals(currentNode.get("type").asText())) {
                        if (Objects.nonNull(currentNode.get("src"))) {
                            dmSrcSet.add(currentNode.get("src").asText());
                        }
                        if (Objects.nonNull(currentNode.get("config"))) {
                            if (Objects.nonNull(currentNode.get("config").get("src"))) {
                                dmSrcSet.add(currentNode.get("config").get("src").asText());
                            }
                        }
                    }
                }*/
                currentNode.fields().forEachRemaining(field -> stack.push(field.getValue()));
            }
        }
    }

    /**
     * 根据url获取域名
     *
     * @param requestUrl
     * @return
     */
    public static String getHost(String requestUrl) {
        if (org.apache.commons.lang.StringUtils.isEmpty(requestUrl)) {
            return null;
        }
        if (!requestUrl.startsWith(HTTPS) && !requestUrl.startsWith(HTTP)) {
            StringBuffer sbf = new StringBuffer(HTTPS).append(requestUrl);
            requestUrl = sbf.toString();
        }
        try {
            URL url = new URL(requestUrl);
            return url.getHost();
        } catch (MalformedURLException e) {
            return null;
        }
    }

    public static void main(String[] args) throws JsonProcessingException {
        String filePath = "/Users/<USER>/Desktop/1199743842804773325的renderData.txt";

        String content = "";
        try {
            byte[] encodedBytes = Files.readAllBytes(Paths.get(filePath));
            content = new String(encodedBytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }

        System.out.println("开始处理json！！！！！！！！！！！！！！！！！！！");
        System.out.println("开始处理json！！！！！！！！！！！！！！！！！！！");
        System.out.println("开始处理json！！！！！！！！！！！！！！！！！！！");
        System.out.println("开始处理json！！！！！！！！！！！！！！！！！！！");
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(content);
        //解析
        extractHttpsValues(jsonNode);
        System.out.println("处理完毕!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
        System.out.println("处理完毕!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
        System.out.println("处理完毕!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
        System.out.println("处理完毕!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
        //System.out.println(JSONUtil.toJsonStr(dmSrcSet));
        //System.out.println(JSONUtil.toJsonStr(cosMap.keySet()));
        System.out.println(JSONUtil.toJsonStr(domainSet));
        /*String replacedContent = content;
        Iterator<Map.Entry<String,String>> iterator = cosMap.entrySet().iterator();
        while(iterator.hasNext()){
            Map.Entry<String,String> entry = iterator.next();
            String orign = entry.getKey();
            String i = entry.getValue();

            replacedContent = replacedContent.replaceAll(orign, i);
        }
        System.out.println("处理replacedContent!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
        System.out.println("处理replacedContent!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");

        System.out.println(replacedContent)*/;

        //System.out.println("domainSet:" + JSONUtil.toJsonStr(domainSet));
    }

}
