package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName visual_template
 */
@TableName(value ="visual_template")
@Data
public class VisualTemplatePO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板状态 0-启用 1-禁用
     */
    private Integer status;

    /**
     * 模板封面
     */
    private String coverUrl;

    /**
     * 尺寸
     */
    private String resolution;

    /**
     * 横版/竖版
     */
    private String resolutionType;

    /**
     * tts配置
     */
    private String ttsParam;

    /**
     * 背景音乐
     */
    private String bgMusic;

    /**
     * 背景音乐配置
     */
    private String bgMusicParam;

    /**
     * 替换变量
     */
    private String replaceData;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 是否系统模板 0-否 1-是
     */
    private Integer isSys;

    private String creatorName;

    private Long duration;

    private String shortVideoUrl;

    private String previewVideoUrl;

    private String modifyName;

    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     * @see: ShareConfStateEnum
     */
    private Integer shareConfState;

    /**
     * 是否包含理财经理信息 0-未包含 1-包含
     */
    private Integer isManager;

    /**
     * 一级菜单
     */
    private Integer firstCategory;

    /**
     * 二级菜单
     */
    private Integer secondCategory;

    /**
     * 替换变量
     */
    private String apiData;

    /**
     * 是否为PPT 1是ppt 0不是ppt
     */
    @TableField("is_ppt")
    private Integer isPPT;

    /**
     * 模板类型，1-常规模板，2-数据图表模板
     * @see: com.dl.magicvideo.biz.manager.visual.enums.TemplateTypeEnum
     */
    private Integer type;

    /**
     * 组件版本号，默认2.0.0
     */
    private String componentVersion;

    /**
     * 是否展示 0-否,1-是
     */
    private Integer isShow;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}