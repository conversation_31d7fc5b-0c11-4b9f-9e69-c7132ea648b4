package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.dal.visual.TemplateProductMapper;
import com.dl.magicvideo.biz.dal.visual.po.TemplateProductPO;
import com.dl.magicvideo.biz.manager.visual.TemplateProductManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【visual_template】的数据库操作Service实现
 * @createDate 2023-04-24 10:22:23
 */
@Slf4j
@Service
public class TemplateProductManagerImpl extends ServiceImpl<TemplateProductMapper, TemplateProductPO>
        implements TemplateProductManager, CommonService {
}




