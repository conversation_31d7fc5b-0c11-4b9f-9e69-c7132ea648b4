package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.VisualCardMapper;
import com.dl.magicvideo.biz.dal.visual.VisualTemplateMapper;
import com.dl.magicvideo.biz.dal.visual.param.AuthedTemplatePageQueryParam;
import com.dl.magicvideo.biz.dal.visual.param.WorkingTemplatePageQueryParam;
import com.dl.magicvideo.biz.dal.visual.po.*;
import com.dl.magicvideo.biz.manager.cos.CosFileUploadManager;
import com.dl.magicvideo.biz.manager.visual.*;
import com.dl.magicvideo.biz.manager.visual.bo.*;
import com.dl.magicvideo.biz.manager.visual.dto.*;
import com.dl.magicvideo.biz.manager.visual.helper.VisualTemplateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.javacpp.Loader;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【visual_template】的数据库操作Service实现
 * @createDate 2023-04-24 10:22:23
 */
@Slf4j
@Service
public class VisualTemplateManagerImpl extends ServiceImpl<VisualTemplateMapper, VisualTemplatePO>
        implements VisualTemplateManager, CommonService {

    private static final String SYNC_KEY = "template_sync:";
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private VisualCardManager visualCardManager;
    @Autowired
    private VisualDynamicNodeManager visualDynamicNodeManager;
    @Autowired
    private VisualAiConfigManager visualAiConfigManager;
    @Autowired
    private VisualCardMapper visualCardMapper;
    @Value("${visual.produce.tmpDir}")
    private String tmpDir;
    @Autowired
    private CosFileUploadManager cosFileUploadManager;
    @Autowired
    private OperatorUtil operatorUtil;
    @Resource
    private TemplateProductManager templateProductManager;
    @Resource
    private VisualShareConfManager visualShareConfManager;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private VisualTemplateAuthManager visualTemplateAuthManager;
    @Autowired
    private TagManager tagManager;
    @Autowired
    private TemplateTagLinkManager templateTagLinkManager;

    @Override
    public Long add(VisualTemplateBO bo) {
        Assert.isTrue(StringUtils.isNotBlank(bo.getName()), "模板名称不可为空。");
        //校验当前租户有没有同名的模板
        VisualTemplatePO exist = this.lambdaQuery().eq(VisualTemplatePO::getName, bo.getName())
                .eq(VisualTemplatePO::getTenantCode, operatorUtil.getTenantCode())
                .eq(VisualTemplatePO::getIsDeleted, Const.ZERO).one();
        Assert.isNull(exist, "已存在相同名称的模板。");

        Long templateId = hostTimeIdg.generateId().longValue();
        VisualTemplatePO newTemplate = VisualTemplateHelper.cnvVisualTemplateBO2PO(bo);
        newTemplate.setTemplateId(templateId);

        this.save(newTemplate);
        return templateId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(VisualTemplateBO bo) {
        Assert.notNull(bo.getTemplateId(), "模板id不可为空。");
        VisualTemplatePO template = this.lambdaQuery().eq(VisualTemplatePO::getTemplateId, bo.getTemplateId()).one();
        Assert.isTrue(Objects.nonNull(template) && Objects.equals(template.getIsDeleted(), Const.ZERO), "模板不存在或已删除。");

        template.setName(bo.getName());
        template.setCoverUrl(bo.getCoverUrl());
        template.setResolution(bo.getResolution());
        template.setBgMusic(bo.getBgMusic());
        template.setBgMusicParam(bo.getBgMusicParam());
        template.setTtsParam(bo.getTtsParam());
        template.setReplaceData(bo.getReplaceData());
        template.setDuration(bo.getDuration());
        template.setApiData(bo.getApiData());
        this.updateById(template);

        if(CollectionUtils.isEmpty(bo.getCards())) {
            return bo.getTemplateId();
        }

        List<VisualDynamicNodePO> dynamicNodePOs = new ArrayList<>();
        List<VisualAiConfigPO> ttsConfigPOs = new ArrayList<>();
        //新增的卡片
        List<VisualCardPO> newCardPOs = bo.getCards().stream().filter(c -> Objects.isNull(c.getCardId())).map(cardBO -> {
            VisualCardPO visualCardPO = new VisualCardPO();
            Long cardId = hostTimeIdg.generateId().longValue();
            visualCardPO.setCardId(cardId);
            visualCardPO.setTemplateId(cardBO.getTemplateId());
            visualCardPO.setName(cardBO.getName());
            visualCardPO.setCoverUrl(cardBO.getCoverUrl());
            visualCardPO.setDuration(cardBO.getDuration());
            visualCardPO.setRenderData(cardBO.getRenderData());
            visualCardPO.setResolution(cardBO.getResolution());
            visualCardPO.setLightEditConfigs(cardBO.getLightEditConfigs());
            if(CollectionUtils.isNotEmpty(cardBO.getDynamicNodes())) {
                cardBO.getDynamicNodes().forEach(dynamicNodeBO -> {
                    VisualDynamicNodePO visualDynamicNodePO = new VisualDynamicNodePO();
                    Long nodeId = Objects.isNull(dynamicNodeBO.getNodeId())?hostTimeIdg.generateId().longValue():dynamicNodeBO.getNodeId();
                    visualDynamicNodePO.setNodeId(nodeId);
                    visualDynamicNodePO.setCardId(cardId);
                    visualDynamicNodePO.setDuration(dynamicNodeBO.getDuration());
                    visualDynamicNodePO.setType(dynamicNodeBO.getType());
                    visualDynamicNodePO.setTemplateId(cardBO.getTemplateId());
                    visualDynamicNodePO.setIsEnabled(dynamicNodeBO.getIsEnabled());
                    visualDynamicNodePO.setCoverUrl(dynamicNodeBO.getCoverUrl());
                    visualDynamicNodePO.setExpression(dynamicNodeBO.getExpression());
                    visualDynamicNodePO.setExpressionFlag(dynamicNodeBO.getExpressionFlag());
                    dynamicNodePOs.add(visualDynamicNodePO);

                    if(CollectionUtils.isNotEmpty(dynamicNodeBO.getTtsList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getTtsList().stream().map(ttsConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setContent(ttsConfigBO.getContent());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(ttsConfigBO.getTtsId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(ttsConfigBO.getStart());
                            visualAiConfigPO.setType(0);
                            visualAiConfigPO.setEnableSubtitle(ttsConfigBO.getEnableSubtitle());
                            visualAiConfigPO.setMaxLength(ttsConfigBO.getMaxLength());
                            visualAiConfigPO.setEndDelay(ttsConfigBO.getEndDelay());
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(ttsConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setSubtitleKeyWordsHighlight(ttsConfigBO.getSubtitleKeyWordsHighlight());
                            visualAiConfigPO.setKeyTime(ttsConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }

                    if(CollectionUtils.isNotEmpty(dynamicNodeBO.getDmList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getDmList().stream().map(dmConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setContent(dmConfigBO.getContent());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(dmConfigBO.getDmId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(dmConfigBO.getStart());
                            visualAiConfigPO.setType(1);
                            visualAiConfigPO.setEnableSubtitle(dmConfigBO.getEnableSubtitle());
                            visualAiConfigPO.setMaxLength(dmConfigBO.getMaxLength());
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(dmConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setEndDelay(dmConfigBO.getEndDelay());
                            visualAiConfigPO.setKeyTime(dmConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }

                    if (CollectionUtils.isNotEmpty(dynamicNodeBO.getVideoList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getVideoList().stream().map(videoConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(videoConfigBO.getVideoId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(videoConfigBO.getStart());
                            visualAiConfigPO.setType(2);
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(videoConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setVolume(videoConfigBO.getVolume());
                            visualAiConfigPO.setUrl(videoConfigBO.getUrl());
                            visualAiConfigPO.setDuration(videoConfigBO.getDuration());
                            visualAiConfigPO.setCroppedDuration(videoConfigBO.getCroppedDuration());
                            visualAiConfigPO.setContent(videoConfigBO.getContent());
                            visualAiConfigPO.setActiveRotationMode(videoConfigBO.getActiveRotationMode());
                            visualAiConfigPO.setStartDelay(videoConfigBO.getStartDelay());
                            visualAiConfigPO.setKeyTime(videoConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(dynamicNodeBO.getAudioList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getAudioList().stream().map(audioConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(audioConfigBO.getAudioId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(audioConfigBO.getStart());
                            visualAiConfigPO.setType(3);
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(audioConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setVolume(audioConfigBO.getVolume());
                            visualAiConfigPO.setUrl(audioConfigBO.getUrl());
                            visualAiConfigPO.setDuration(audioConfigBO.getDuration());
                            visualAiConfigPO.setCroppedDuration(audioConfigBO.getCroppedDuration());
                            visualAiConfigPO.setContent(audioConfigBO.getContent());
                            visualAiConfigPO.setActiveRotationMode(audioConfigBO.getActiveRotationMode());
                            visualAiConfigPO.setStartDelay(audioConfigBO.getStartDelay());
                            visualAiConfigPO.setFadeInTime(audioConfigBO.getFadeInTime());
                            visualAiConfigPO.setFadeOutTime(audioConfigBO.getFadeOutTime());
                            visualAiConfigPO.setKeyTime(audioConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(dynamicNodeBO.getDataSheetList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getDataSheetList().stream().map(audioConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(audioConfigBO.getDataSheetId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setType(4);
                            visualAiConfigPO.setIsHide(BooleanUtils.isTrue(audioConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setContent(audioConfigBO.getContent());
                            visualAiConfigPO.setKeyTime(audioConfigBO.getKeyTime());
                            visualAiConfigPO.setStart(audioConfigBO.getStart());
                            visualAiConfigPO.setEndDelay(audioConfigBO.getEndDelay());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }
                });
            }
            return visualCardPO;
        }).collect(Collectors.toList());

        //更新的卡片
        List<VisualCardPO> existCardPOs = bo.getCards().stream().filter(c -> Objects.nonNull(c.getCardId())).map(cardBO -> {
            VisualCardPO visualCardPO = new VisualCardPO();
            visualCardPO.setCardId(cardBO.getCardId());
            visualCardPO.setTemplateId(cardBO.getTemplateId());
            visualCardPO.setName(cardBO.getName());
            visualCardPO.setCoverUrl(cardBO.getCoverUrl());
            visualCardPO.setDuration(cardBO.getDuration());
            visualCardPO.setRenderData(cardBO.getRenderData());
            visualCardPO.setResolution(cardBO.getResolution());
            visualCardPO.setLightEditConfigs(cardBO.getLightEditConfigs());
            if(CollectionUtils.isNotEmpty(cardBO.getDynamicNodes())) {
                cardBO.getDynamicNodes().forEach(dynamicNodeBO -> {
                    VisualDynamicNodePO visualDynamicNodePO = new VisualDynamicNodePO();
                    Long nodeId = Objects.isNull(dynamicNodeBO.getNodeId())?hostTimeIdg.generateId().longValue():dynamicNodeBO.getNodeId();
                    visualDynamicNodePO.setNodeId(nodeId);
                    visualDynamicNodePO.setCardId(cardBO.getCardId());
                    visualDynamicNodePO.setDuration(dynamicNodeBO.getDuration());
                    visualDynamicNodePO.setType(dynamicNodeBO.getType());
                    visualDynamicNodePO.setTemplateId(cardBO.getTemplateId());
                    visualDynamicNodePO.setIsEnabled(dynamicNodeBO.getIsEnabled());
                    visualDynamicNodePO.setCoverUrl(dynamicNodeBO.getCoverUrl());
                    visualDynamicNodePO.setExpression(dynamicNodeBO.getExpression());
                    visualDynamicNodePO.setExpressionFlag(dynamicNodeBO.getExpressionFlag());
                    dynamicNodePOs.add(visualDynamicNodePO);

                    if(CollectionUtils.isNotEmpty(dynamicNodeBO.getTtsList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getTtsList().stream().map(ttsConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardBO.getCardId());
                            visualAiConfigPO.setContent(ttsConfigBO.getContent());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(ttsConfigBO.getTtsId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(ttsConfigBO.getStart());
                            visualAiConfigPO.setType(0);
                            visualAiConfigPO.setEnableSubtitle(ttsConfigBO.getEnableSubtitle());
                            visualAiConfigPO.setMaxLength(ttsConfigBO.getMaxLength());
                            visualAiConfigPO.setEndDelay(ttsConfigBO.getEndDelay());
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(ttsConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setSubtitleKeyWordsHighlight(ttsConfigBO.getSubtitleKeyWordsHighlight());
                            visualAiConfigPO.setKeyTime(ttsConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }

                    if(CollectionUtils.isNotEmpty(dynamicNodeBO.getDmList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getDmList().stream().map(dmConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardBO.getCardId());
                            visualAiConfigPO.setContent(dmConfigBO.getContent());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(dmConfigBO.getDmId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(dmConfigBO.getStart());
                            visualAiConfigPO.setType(1);
                            visualAiConfigPO.setEnableSubtitle(dmConfigBO.getEnableSubtitle());
                            visualAiConfigPO.setMaxLength(dmConfigBO.getMaxLength());
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(dmConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setEndDelay(dmConfigBO.getEndDelay());
                            visualAiConfigPO.setKeyTime(dmConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }

                    if (CollectionUtils.isNotEmpty(dynamicNodeBO.getVideoList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getVideoList().stream().map(videoConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardBO.getCardId());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(videoConfigBO.getVideoId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(videoConfigBO.getStart());
                            visualAiConfigPO.setType(2);
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(videoConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setVolume(videoConfigBO.getVolume());
                            visualAiConfigPO.setUrl(videoConfigBO.getUrl());
                            visualAiConfigPO.setDuration(videoConfigBO.getDuration());
                            visualAiConfigPO.setCroppedDuration(videoConfigBO.getCroppedDuration());
                            visualAiConfigPO.setActiveRotationMode(videoConfigBO.getActiveRotationMode());
                            visualAiConfigPO.setStartDelay(videoConfigBO.getStartDelay());
                            visualAiConfigPO.setContent(videoConfigBO.getContent());
                            visualAiConfigPO.setKeyTime(videoConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(dynamicNodeBO.getAudioList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getAudioList().stream().map(audioConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardBO.getCardId());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(audioConfigBO.getAudioId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(audioConfigBO.getStart());
                            visualAiConfigPO.setType(3);
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(audioConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setVolume(audioConfigBO.getVolume());
                            visualAiConfigPO.setUrl(audioConfigBO.getUrl());
                            visualAiConfigPO.setDuration(audioConfigBO.getDuration());
                            visualAiConfigPO.setCroppedDuration(audioConfigBO.getCroppedDuration());
                            visualAiConfigPO.setActiveRotationMode(audioConfigBO.getActiveRotationMode());
                            visualAiConfigPO.setStartDelay(audioConfigBO.getStartDelay());
                            visualAiConfigPO.setContent(audioConfigBO.getContent());
                            visualAiConfigPO.setFadeInTime(audioConfigBO.getFadeInTime());
                            visualAiConfigPO.setFadeOutTime(audioConfigBO.getFadeOutTime());
                            visualAiConfigPO.setKeyTime(audioConfigBO.getKeyTime());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }

                    if (CollectionUtils.isNotEmpty(dynamicNodeBO.getDataSheetList())) {
                        ttsConfigPOs.addAll(dynamicNodeBO.getDataSheetList().stream().map(audioConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardBO.getCardId());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(audioConfigBO.getDataSheetId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setType(4);
                            visualAiConfigPO.setIsHide(BooleanUtils.isTrue(audioConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setContent(audioConfigBO.getContent());
                            visualAiConfigPO.setKeyTime(audioConfigBO.getKeyTime());
                            visualAiConfigPO.setStart(audioConfigBO.getStart());
                            visualAiConfigPO.setEndDelay(audioConfigBO.getEndDelay());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }
                });
            }
            return visualCardPO;
        }).collect(Collectors.toList());

        visualDynamicNodeManager.remove(Wrappers.lambdaQuery(VisualDynamicNodePO.class).eq(VisualDynamicNodePO::getTemplateId, bo.getTemplateId()));
        visualAiConfigManager.remove(Wrappers.lambdaQuery(VisualAiConfigPO.class).eq(VisualAiConfigPO::getTemplateId, bo.getTemplateId()));
        if (CollectionUtils.isNotEmpty(newCardPOs)) {
            visualCardManager.saveBatch(newCardPOs);
        }
        if (CollectionUtils.isNotEmpty(existCardPOs)) {
            visualCardMapper.batchUpdate(existCardPOs);
        }
        if (CollectionUtils.isNotEmpty(dynamicNodePOs)) {
            visualDynamicNodeManager.saveBatch(dynamicNodePOs);
        }
        if (CollectionUtils.isNotEmpty(ttsConfigPOs)) {
            visualAiConfigManager.saveBatch(ttsConfigPOs);
        }

        return bo.getTemplateId();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Long addIntegrity(VisualTemplateBO bo) {
        //创建模板基本信息
        Long templateId = this.add(bo);

        if (CollectionUtils.isEmpty(bo.getCards())) {
            return templateId;
        }

        List<VisualDynamicNodePO> dynamicNodePOs = new ArrayList<>();
        List<VisualAiConfigPO> aiConfigPOs = new ArrayList<>();
        //新增的卡片
        List<VisualCardPO> newCardPOs = bo.getCards().stream().map(cardBO -> {
            VisualCardPO visualCardPO = new VisualCardPO();
            visualCardPO.setTemplateId(templateId);
            Long cardId = hostTimeIdg.generateId().longValue();
            visualCardPO.setCardId(cardId);
            visualCardPO.setName(cardBO.getName());
            visualCardPO.setCoverUrl(cardBO.getCoverUrl());
            visualCardPO.setDuration(cardBO.getDuration());
            visualCardPO.setResolution(cardBO.getResolution());
            visualCardPO.setRenderData(cardBO.getRenderData());
            visualCardPO.setLightEditConfigs(cardBO.getLightEditConfigs());
            if(CollectionUtils.isNotEmpty(cardBO.getDynamicNodes())) {
                cardBO.getDynamicNodes().forEach(dynamicNodeBO -> {
                    VisualDynamicNodePO visualDynamicNodePO = new VisualDynamicNodePO();
                    Long nodeId = hostTimeIdg.generateId().longValue();
                    visualDynamicNodePO.setCardId(cardId);
                    visualDynamicNodePO.setNodeId(nodeId);
                    visualDynamicNodePO.setDuration(dynamicNodeBO.getDuration());
                    visualDynamicNodePO.setType(dynamicNodeBO.getType());
                    visualDynamicNodePO.setTemplateId(cardBO.getTemplateId());
                    visualDynamicNodePO.setIsEnabled(dynamicNodeBO.getIsEnabled());
                    visualDynamicNodePO.setCoverUrl(dynamicNodeBO.getCoverUrl());
                    visualDynamicNodePO.setExpression(dynamicNodeBO.getExpression());
                    visualDynamicNodePO.setExpressionFlag(dynamicNodeBO.getExpressionFlag());
                    dynamicNodePOs.add(visualDynamicNodePO);

                    if(CollectionUtils.isNotEmpty(dynamicNodeBO.getTtsList())) {
                        aiConfigPOs.addAll(dynamicNodeBO.getTtsList().stream().map(ttsConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setContent(ttsConfigBO.getContent());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(ttsConfigBO.getTtsId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(ttsConfigBO.getStart());
                            visualAiConfigPO.setType(0);
                            visualAiConfigPO.setEnableSubtitle(ttsConfigBO.getEnableSubtitle());
                            visualAiConfigPO.setMaxLength(ttsConfigBO.getMaxLength());
                            visualAiConfigPO.setEndDelay(ttsConfigBO.getEndDelay());
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(ttsConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setSubtitleKeyWordsHighlight(ttsConfigBO.getSubtitleKeyWordsHighlight());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }

                    if(CollectionUtils.isNotEmpty(dynamicNodeBO.getDmList())) {
                        aiConfigPOs.addAll(dynamicNodeBO.getDmList().stream().map(dmConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setContent(dmConfigBO.getContent());
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(dmConfigBO.getDmId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(dmConfigBO.getStart());
                            visualAiConfigPO.setType(1);
                            visualAiConfigPO.setEnableSubtitle(dmConfigBO.getEnableSubtitle());
                            visualAiConfigPO.setMaxLength(dmConfigBO.getMaxLength());
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(dmConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setEndDelay(dmConfigBO.getEndDelay());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }

                    if (CollectionUtils.isNotEmpty(dynamicNodeBO.getVideoList())) {
                        aiConfigPOs.addAll(dynamicNodeBO.getVideoList().stream().map(videoConfigBO -> {
                            VisualAiConfigPO visualAiConfigPO = new VisualAiConfigPO();
                            visualAiConfigPO.setCardId(cardId);
                            visualAiConfigPO.setTemplateId(cardBO.getTemplateId());
                            visualAiConfigPO.setTtsId(videoConfigBO.getVideoId());
                            visualAiConfigPO.setNodeId(nodeId);
                            visualAiConfigPO.setStart(videoConfigBO.getStart());
                            visualAiConfigPO.setType(2);
                            visualAiConfigPO.setIsHide(
                                    BooleanUtils.isTrue(videoConfigBO.isHide()) ? Const.ONE : Const.ZERO);
                            visualAiConfigPO.setVolume(videoConfigBO.getVolume());
                            visualAiConfigPO.setUrl(videoConfigBO.getUrl());
                            visualAiConfigPO.setDuration(videoConfigBO.getDuration());
                            visualAiConfigPO.setCroppedDuration(videoConfigBO.getCroppedDuration());
                            visualAiConfigPO.setActiveRotationMode(videoConfigBO.getActiveRotationMode());
                            visualAiConfigPO.setStartDelay(videoConfigBO.getStartDelay());
                            visualAiConfigPO.setContent(videoConfigBO.getContent());
                            return visualAiConfigPO;
                        }).collect(Collectors.toList()));
                    }
                });
            }
            return visualCardPO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(newCardPOs)) {
            visualCardManager.saveBatch(newCardPOs);
        }
        if (CollectionUtils.isNotEmpty(dynamicNodePOs)) {
            visualDynamicNodeManager.saveBatch(dynamicNodePOs);
        }
        if (CollectionUtils.isNotEmpty(aiConfigPOs)) {
            visualAiConfigManager.saveBatch(aiConfigPOs);
        }

        return templateId;
    }

    @Override
    public Long updateTemplateSimpleInfo(TemplateSimpleInfoUpdateBO updateBO) {
        Assert.notNull(updateBO.getTemplateId(), "模板id不可为空。");
        VisualTemplatePO templatePO = this.lambdaQuery().eq(VisualTemplatePO::getTemplateId, updateBO.getTemplateId())
                .one();
        Assert.isTrue(Objects.nonNull(templatePO) && Objects.equals(templatePO.getIsDeleted(), Const.ZERO),
                "模板不存在或已删除。");

        if (StringUtils.isNotBlank(updateBO.getName())) {
            templatePO.setName(updateBO.getName());
        }
        if (StringUtils.isNotBlank(updateBO.getCoverUrl())) {
            templatePO.setCoverUrl(updateBO.getCoverUrl());
        }

        this.updateById(templatePO);

        return updateBO.getTemplateId();
    }

    @Override
    public void delete(Long templateId) {
        this.lambdaUpdate().eq(VisualTemplatePO::getTemplateId, templateId)
                .set(VisualTemplatePO::getIsDeleted, Const.ONE).set(VisualTemplatePO::getModifyDt, new Date())
                .set(VisualTemplatePO::getModifyBy, operatorUtil.getOperator()).update();
    }

    @Override
    public VisualTemplateDTO detail(Long templateId, String tenantCode) {
        Assert.notNull(templateId, "模板id不可为空。");
        VisualTemplatePO templatePO = this.lambdaQuery().eq(VisualTemplatePO::getTemplateId, templateId)
                .eq(VisualTemplatePO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(templatePO, "模板不存在。");

        //非系统模板，校验租户号是否一致
        if (Const.ZERO.equals(templatePO.getIsSys()) && !templatePO.getTenantCode().equals(tenantCode)) {
            throw BusinessServiceException.getInstance("无权查看该模板");
        }

        VisualTemplateDTO visualTemplateDTO = cnvVisualTemplatePO2DTO(templatePO);
        Assert.notNull(visualTemplateDTO, "模板获取异常");
        //查询关联标签
        List<Long> tagIds = templateTagLinkManager.lambdaQuery().eq(TemplateTagLinkPO::getTemplateId, templateId)
                .list().stream().map(TemplateTagLinkPO::getTagId).collect(Collectors.toList());

        //查询标签列表
        if (CollectionUtils.isNotEmpty(tagIds)){
            List<TagPO> tagPOList = tagManager.lambdaQuery().in(TagPO::getTagId, tagIds).list();
            visualTemplateDTO.setTagList(tagPOList.stream().map(tagPO -> {
                TagDTO tagVO = new TagDTO();
                tagVO.setTagId(tagPO.getTagId());
                tagVO.setTagName(tagPO.getName());
                return tagVO;
            }).collect(Collectors.toList()));
        }

        List<VisualCardPO> cardPOList = visualCardManager.lambdaQuery().eq(VisualCardPO::getTemplateId, templateId)
                .eq(VisualCardPO::getIsDeleted, Const.ZERO).list();
        if (CollectionUtils.isNotEmpty(cardPOList)) {
            visualTemplateDTO.setCards(cardPOList.stream().map(cardPO -> {
                VisualCardDTO cardDTO = new VisualCardDTO();
                cardDTO.setCardId(cardPO.getCardId());
                cardDTO.setTemplateId(cardPO.getTemplateId());
                cardDTO.setName(cardPO.getName());
                cardDTO.setCoverUrl(cardPO.getCoverUrl());
                cardDTO.setRenderData(cardPO.getRenderData());
                cardDTO.setResolution(cardPO.getResolution());
                cardDTO.setLightEditConfigs(cardPO.getLightEditConfigs());
                List<VisualDynamicNodePO> dynamicNodePOList = visualDynamicNodeManager.lambdaQuery()
                        .eq(VisualDynamicNodePO::getTemplateId, templateId)
                        .eq(VisualDynamicNodePO::getCardId, cardPO.getCardId()).list();
                if (CollectionUtils.isNotEmpty(dynamicNodePOList)) {
                    cardDTO.setDynamicNodes(dynamicNodePOList.stream().map(visualDynamicNodePO -> {
                        DynamicNodeDTO dynamicNodeDTO = new DynamicNodeDTO();
                        dynamicNodeDTO.setNodeId(visualDynamicNodePO.getNodeId());
                        dynamicNodeDTO.setType(visualDynamicNodePO.getType());
                        dynamicNodeDTO.setDuration(visualDynamicNodePO.getDuration());
                        dynamicNodeDTO.setCoverUrl(visualDynamicNodePO.getCoverUrl());
                        dynamicNodeDTO.setIsEnabled(visualDynamicNodePO.getIsEnabled());
                        List<VisualAiConfigPO> ttsConfigPOList = visualAiConfigManager.lambdaQuery()
                                .eq(VisualAiConfigPO::getTemplateId, templateId)
                                .eq(VisualAiConfigPO::getNodeId, visualDynamicNodePO.getNodeId()).list();
                        if (CollectionUtils.isNotEmpty(ttsConfigPOList)) {
                            dynamicNodeDTO.setTtsList(
                                    ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 0))
                                            .map(visualAiConfigPO -> {
                                                DynamicNodeDTO.TtsConfigDTO ttsConfigDTO = new DynamicNodeDTO.TtsConfigDTO();
                                                ttsConfigDTO.setTtsId(visualAiConfigPO.getTtsId());
                                                ttsConfigDTO.setContent(visualAiConfigPO.getContent());
                                                ttsConfigDTO.setStart(visualAiConfigPO.getStart());
                                                ttsConfigDTO.setEnableSubtitle(visualAiConfigPO.getEnableSubtitle());
                                                ttsConfigDTO.setMaxLength(visualAiConfigPO.getMaxLength());
                                                ttsConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                                ttsConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                                ttsConfigDTO.setSubtitleKeyWordsHighlight(visualAiConfigPO.getSubtitleKeyWordsHighlight());
                                                ttsConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                                return ttsConfigDTO;
                                            }).collect(Collectors.toList()));
                            dynamicNodeDTO.setDmList(
                                    ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 1))
                                            .map(visualAiConfigPO -> {
                                                DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO = new DynamicNodeDTO.DigitalManConfigDTO();
                                                dmConfigDTO.setDmId(visualAiConfigPO.getTtsId());
                                                dmConfigDTO.setContent(visualAiConfigPO.getContent());
                                                dmConfigDTO.setStart(visualAiConfigPO.getStart());
                                                dmConfigDTO.setEnableSubtitle(visualAiConfigPO.getEnableSubtitle());
                                                dmConfigDTO.setMaxLength(visualAiConfigPO.getMaxLength());
                                                dmConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                                dmConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                                return dmConfigDTO;
                                            }).collect(Collectors.toList()));
                            dynamicNodeDTO.setVideoList(
                                    ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 2))
                                            .map(visualAiConfigPO -> {
                                                DynamicNodeDTO.VideoConfigDTO videoConfigDTO = new DynamicNodeDTO.VideoConfigDTO();
                                                videoConfigDTO.setVideoId(visualAiConfigPO.getTtsId());
                                                videoConfigDTO.setStart(visualAiConfigPO.getStart());
                                                videoConfigDTO.setUrl(visualAiConfigPO.getUrl());
                                                videoConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                                videoConfigDTO.setCroppedDuration(visualAiConfigPO.getCroppedDuration());
                                                videoConfigDTO.setActiveRotationMode(visualAiConfigPO.getActiveRotationMode());
                                                videoConfigDTO.setStartDelay(visualAiConfigPO.getStartDelay());
                                                videoConfigDTO.setDuration(visualAiConfigPO.getDuration());
                                                videoConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                                videoConfigDTO.setVolume(visualAiConfigPO.getVolume());
                                                videoConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                                return videoConfigDTO;
                                            }).collect(Collectors.toList()));
                            dynamicNodeDTO.setAudioList(
                                    ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 3))
                                            .map(visualAiConfigPO -> {
                                                DynamicNodeDTO.AudioConfigDTO audioConfigDTO = new DynamicNodeDTO.AudioConfigDTO();
                                                audioConfigDTO.setAudioId(visualAiConfigPO.getTtsId());
                                                audioConfigDTO.setStart(visualAiConfigPO.getStart());
                                                audioConfigDTO.setUrl(visualAiConfigPO.getUrl());
                                                audioConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                                audioConfigDTO.setCroppedDuration(visualAiConfigPO.getCroppedDuration());
                                                audioConfigDTO.setActiveRotationMode(visualAiConfigPO.getActiveRotationMode());
                                                audioConfigDTO.setStartDelay(visualAiConfigPO.getStartDelay());
                                                audioConfigDTO.setDuration(visualAiConfigPO.getDuration());
                                                audioConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                                audioConfigDTO.setVolume(visualAiConfigPO.getVolume());
                                                audioConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                                return audioConfigDTO;
                                            }).collect(Collectors.toList()));
                            dynamicNodeDTO.setDataSheetList(
                                    ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 4))
                                            .map(visualAiConfigPO -> {
                                                DynamicNodeDTO.DataSheetDTO audioConfigDTO = new DynamicNodeDTO.DataSheetDTO();
                                                audioConfigDTO.setDataSheetId(visualAiConfigPO.getTtsId());
                                                audioConfigDTO.setStart(visualAiConfigPO.getStart());
                                                audioConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                                audioConfigDTO.setDuration(visualAiConfigPO.getDuration());
                                                audioConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                                audioConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                                audioConfigDTO.setContent(visualAiConfigPO.getContent());
                                                return audioConfigDTO;
                                            }).collect(Collectors.toList()));
                        }
                        return dynamicNodeDTO;
                    }).collect(Collectors.toList()));
                }
                return cardDTO;
            }).collect(Collectors.toList()));
        }
        return visualTemplateDTO;
    }

    @Override
    public ResponsePageQueryDO<List<VisualTemplateDTO>> pageQuery(TemplateSearchBO bo) {
        ResponsePageQueryDO<List<VisualTemplateDTO>> response = new ResponsePageQueryDO<>();
        WorkingTemplatePageQueryParam query = buildWorkingTemplatePageQueryParam(bo);
        List<VisualTemplatePO> visualTemplatePOList = this.baseMapper.listWorkingTemplates(query);
        Integer count = this.baseMapper.countWorkingTemplates(query);
        //log.info("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(visualTemplatePOList)) {
            return response;
        }

        List<VisualTemplateDTO> dtos = new ArrayList<>();
        dtos.addAll(visualTemplatePOList.stream().map(this::cnvVisualTemplatePO2DTO).collect(Collectors.toList()));
        response.setPageIndex(bo.getPageIndex());
        response.setPageSize(bo.getPageSize());
        response.setTotal(count);
        response.setDataResult(dtos);
        return response;
    }

    private WorkingTemplatePageQueryParam buildWorkingTemplatePageQueryParam(TemplateSearchBO bo) {
        WorkingTemplatePageQueryParam param = new WorkingTemplatePageQueryParam();
        param.setPageIndex(bo.getPageIndex());
        param.setPageSize(bo.getPageSize());
        param.setName(bo.getName());
        param.setStatus(bo.getStatus());
        param.setTenantCode(bo.getTenantCode());
        param.setCreateBy(bo.getCreateBy());
        param.setIsSys(bo.getIsSys());
        param.setResolutionType(bo.getResolutionType());
        param.setSortType(bo.getSortType());
        param.setShareConfState(bo.getShareConfState());
        param.setFirstCategory(bo.getFirstCategory());
        param.setSecondCategory(bo.getSecondCategory());
        param.setIsManager(bo.getIsManager());
        param.setIsPPTType(bo.getIsPPTType());
        param.setTemplateId(bo.getTemplateId());
        param.setTemplateIds(bo.getTemplateIds());
        param.setType(bo.getType());
        param.setIsShow(bo.getIsShow());
        param.setCollectUserId(bo.getCollectUserId());
        return param;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateCopyDTO copy(TemplateCopyBO bo) {
        Assert.notNull(bo.getTemplateId(), "模板id不可为空。");
        VisualTemplatePO source = getVisualTemplatePO(bo.getTemplateId(), bo.getTenantCode());
        Assert.isTrue(Objects.nonNull(source), "模板不存在或已删除。");
        TemplateCopyDTO templateCopyDTO = new TemplateCopyDTO();
        // 模板基本数据
        Long newTemplateId = hostTimeIdg.generateId().longValue();
        templateCopyDTO.setTemplateId(newTemplateId);
        VisualTemplatePO newTemplate = new VisualTemplatePO();
        BeanUtils.copyProperties(source, newTemplate, "id", "createDt", "modifyDt");
        if (Objects.nonNull(bo.getFirstCategory())) {
            newTemplate.setFirstCategory(bo.getFirstCategory());
        }
        if (Objects.nonNull(bo.getSecondCategory())) {
            newTemplate.setSecondCategory(bo.getSecondCategory());
        }
        if (Objects.nonNull(bo.getIsManager())) {
            newTemplate.setIsManager(bo.getIsManager());
        }
        newTemplate.setCreatorName(operatorUtil.getUserName());
        newTemplate.setModifyName(operatorUtil.getUserName());
        newTemplate.setTemplateId(newTemplateId);
        if (StringUtils.isNotBlank(bo.getName())) {
            newTemplate.setName(bo.getName());
        } else if (Objects.equals(bo.getType(), Const.ONE)){
            //直接复制，需要修改下名称
            newTemplate.setName(buildCopyName(source.getName()));
        }
        if(StringUtils.isNotBlank(bo.getCoverUrl())) {
            newTemplate.setCoverUrl(bo.getCoverUrl());
        }
        if(StringUtils.isNotBlank(bo.getPreviewVideoUrl())) {
            newTemplate.setPreviewVideoUrl(bo.getPreviewVideoUrl());
        }
        if(StringUtils.isNotBlank(bo.getTenantCode())) {
            newTemplate.setTenantCode(bo.getTenantCode());
        }
        if(Objects.equals(bo.getCopySource(), 1)){
            newTemplate.setIsPPT(1);
        }
        newTemplate.setIsSys(bo.isSys() ? Const.ONE : Const.ZERO);
        save(newTemplate);

        //处理标签
        if (CollectionUtils.isNotEmpty(bo.getTagIds()) || CollectionUtils.isNotEmpty(bo.getTagNames())){
            handleTag(newTemplateId, bo.getTagIds(), bo.getTagNames());
        }
        // 生成预览短视频
        Long userId = operatorUtil.getOperator();
        if (StringUtils.isNotBlank(bo.getPreviewVideoUrl())) {
            CompletableFuture.runAsync(() -> {
                operatorUtil.init(userId, "", "", "");
                generateShortVideo(bo.getPreviewVideoUrl(), newTemplateId);
            });
        }

        // 卡片数据
        List<VisualCardPO> sourceCards = visualCardManager.lambdaQuery().eq(VisualCardPO::getTemplateId, bo.getTemplateId()).eq(VisualCardPO::getIsDeleted, Const.ZERO).list();
        if(CollectionUtils.isEmpty(sourceCards)) {
            return templateCopyDTO;
        }
        Map<Long, List<VisualDynamicNodePO>> sourceDynamicNodeMap = new HashMap<>();
        Map<Long, List<VisualAiConfigPO>> sourceTtsConfigMap = new HashMap<>();
        // 片段数据
        List<VisualDynamicNodePO> sourceDynamicNodes = visualDynamicNodeManager.lambdaQuery().eq(VisualDynamicNodePO::getTemplateId, bo.getTemplateId()).list();
        if(CollectionUtils.isNotEmpty(sourceDynamicNodes)) {
            sourceDynamicNodeMap.putAll(sourceDynamicNodes.stream().collect(Collectors.groupingBy(VisualDynamicNodePO::getCardId)));
        }
        // tts和数字人配置数据
        List<VisualAiConfigPO> sourceTtsConfigs = visualAiConfigManager.lambdaQuery().eq(VisualAiConfigPO::getTemplateId, bo.getTemplateId()).list();
        if (CollectionUtils.isNotEmpty(sourceTtsConfigs)) {
            sourceTtsConfigMap.putAll(sourceTtsConfigs.stream().collect(Collectors.groupingBy(VisualAiConfigPO::getNodeId)));
        }

        List<VisualCardPO> targetCards = new ArrayList<>();
        List<VisualDynamicNodePO> targetDynamicNodes = new ArrayList<>();
        List<VisualAiConfigPO> tagetTtsConfigs = new ArrayList<>();
        for(VisualCardPO sourceCard:sourceCards) {
            VisualCardPO targetCard = new VisualCardPO();
            Long newCardId = hostTimeIdg.generateId().longValue();
            BeanUtils.copyProperties(sourceCard, targetCard, "id");
            targetCard.setCardId(newCardId);
            targetCard.setTemplateId(newTemplateId);
            targetCards.add(targetCard);
            List<VisualDynamicNodePO> sourceDynamicNodesByCardId = sourceDynamicNodeMap.get(sourceCard.getCardId());
            if(CollectionUtils.isEmpty(sourceDynamicNodesByCardId)) {
                continue;
            }
            for(VisualDynamicNodePO sourceDynamicNode:sourceDynamicNodesByCardId) {
                VisualDynamicNodePO targetDynamicNode = new VisualDynamicNodePO();
                Long newNodeId = hostTimeIdg.generateId().longValue();
                BeanUtils.copyProperties(sourceDynamicNode, targetDynamicNode, "id");
                targetDynamicNode.setNodeId(newNodeId);
                targetDynamicNode.setCardId(newCardId);
                targetDynamicNode.setTemplateId(newTemplateId);
                if (Objects.equals(bo.getCopySource(), 1)) {
                    targetDynamicNode.setCoverUrl(StringUtils.EMPTY);
                }
                targetDynamicNodes.add(targetDynamicNode);
                List<VisualAiConfigPO> sourceTtsConfigsByNodeId = sourceTtsConfigMap.get(sourceDynamicNode.getNodeId());
                if(CollectionUtils.isEmpty(sourceTtsConfigsByNodeId)) {
                    continue;
                }
                for(VisualAiConfigPO sourceTtsConfig:sourceTtsConfigsByNodeId) {
                    VisualAiConfigPO targetTtsConfig = new VisualAiConfigPO();
                    BeanUtils.copyProperties(sourceTtsConfig, targetTtsConfig, "id");
                    targetTtsConfig.setNodeId(newNodeId);
                    targetTtsConfig.setCardId(newCardId);
                    targetTtsConfig.setTemplateId(newTemplateId);
                    tagetTtsConfigs.add(targetTtsConfig);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(targetCards)) {
            //这里实际只有单卡片
            templateCopyDTO.setCardId(targetCards.get(0).getCardId());
            visualCardManager.saveBatch(targetCards);
        }
        if(CollectionUtils.isNotEmpty(targetDynamicNodes)) {
            List<VisualDynamicNodeDTO> dynamicNodeDTOList = targetDynamicNodes.stream().map(a -> {
                VisualDynamicNodeDTO dto = new VisualDynamicNodeDTO();
                dto.setNodeId(a.getNodeId());
                return dto;
            }).collect(Collectors.toList());
            templateCopyDTO.setDynamicNodeDTOList(dynamicNodeDTOList);
            visualDynamicNodeManager.saveBatch(targetDynamicNodes);
        }
        if(CollectionUtils.isNotEmpty(tagetTtsConfigs)) {
            visualAiConfigManager.saveBatch(tagetTtsConfigs);
        }

        //判断是否关联产品
        List<TemplateProductPO> list = templateProductManager.lambdaQuery()
                .eq(TemplateProductPO::getTemplateId, bo.getTemplateId()).list();
        if (CollectionUtils.isNotEmpty(list)) {
            List<TemplateProductPO> copyList = new ArrayList<>();
            for (TemplateProductPO templateProductPO : list) {
                TemplateProductPO copyTemplateProductPO = new TemplateProductPO();
                Long id = hostTimeIdg.generateId().longValue();
                copyTemplateProductPO.setId(id);
                copyTemplateProductPO.setTemplateId(newTemplateId);
                copyTemplateProductPO.setProdCode(templateProductPO.getProdCode());
                copyList.add(copyTemplateProductPO);
            }
            templateProductManager.saveBatch(copyList);
        }


        if (Objects.nonNull(source.getShareConfState()) && !Objects.equals(source.getShareConfState(), Const.ZERO)) {
            //拷贝 基础转发配置 和 交互式配置
            ShareConfCopyBO shareConfCopyBO = new ShareConfCopyBO();
            shareConfCopyBO.setSourceBizId(source.getTemplateId());
            shareConfCopyBO.setSourceBizType(Const.ONE);
            shareConfCopyBO.setTargetBizId(newTemplateId);
            shareConfCopyBO.setTargetBizType(Const.ONE);
            shareConfCopyBO.setSourceTenantCode(source.getTenantCode());
            shareConfCopyBO.setTargetTenantCode(newTemplate.getTenantCode());
            visualShareConfManager.copyShareConf(shareConfCopyBO);
        }
        return templateCopyDTO;
    }

    @Override
    public ResponsePageQueryDO<List<VisualTemplateDTO>> authedTemplatePageQuery(TemplateSearchBO bo) {
        Assert.isTrue(StringUtils.isNotBlank(bo.getTenantCode()), "租户编号不可为空");
        ResponsePageQueryDO<List<VisualTemplateDTO>> response = new ResponsePageQueryDO<>();
        AuthedTemplatePageQueryParam param = new AuthedTemplatePageQueryParam();
        param.setTenantCode(bo.getTenantCode());
        param.setName(bo.getName());
        if(Objects.nonNull(bo.getResolutionType())) {
            param.setResolutionType(bo.getResolutionType() + "");
        }
        param.setTagIds(bo.getTagIds());
        param.setFirstCategory(bo.getFirstCategory());
        param.setSecondCategory(bo.getSecondCategory());
        param.setPageIndex(bo.getPageIndex());
        param.setPageSize(bo.getPageSize());
        param.setStatus(bo.getStatus());
        param.setIsSys(bo.getIsSys());
        param.setIsPPT(bo.getIsPPTType());
        Integer count = baseMapper.countAuthedTemplates(param);
        if (Objects.equals(count, 0)) {
            response.setPageIndex(bo.getPageIndex());
            response.setPageSize(bo.getPageSize());
            return response;
        }
        List<VisualTemplatePO> list = baseMapper.listAuthedTemplates(param);
        response.setDataResult(list.stream().map(this::cnvVisualTemplatePO2DTO).collect(Collectors.toList()));
        response.setPageIndex(param.getPageIndex());
        response.setPageSize(param.getPageSize());
        response.setTotal(count);
        return response;
    }

    private VisualTemplateDTO cnvVisualTemplatePO2DTO(VisualTemplatePO templatePO) {
        if (Objects.isNull(templatePO)) {
            return null;
        }
        VisualTemplateDTO templateDTO = new VisualTemplateDTO();
        templateDTO.setTemplateId(templatePO.getTemplateId());
        templateDTO.setStatus(templatePO.getStatus());
        templateDTO.setName(templatePO.getName());
        templateDTO.setCoverUrl(templatePO.getCoverUrl());
        templateDTO.setPreviewVideoUrl(templatePO.getPreviewVideoUrl());
        templateDTO.setShortVideoUrl(templatePO.getShortVideoUrl());
        templateDTO.setResolution(templatePO.getResolution());
        templateDTO.setBgMusic(templatePO.getBgMusic());
        templateDTO.setBgMusicParam(templatePO.getBgMusicParam());
        templateDTO.setTtsParam(templatePO.getTtsParam());
        templateDTO.setReplaceData(templatePO.getReplaceData());
        templateDTO.setResolutionType(templatePO.getResolutionType());
        templateDTO.setTenantCode(templatePO.getTenantCode());
        templateDTO.setDuration(templatePO.getDuration());
        templateDTO.setCreateDt(templatePO.getCreateDt());
        templateDTO.setModifyDt(templatePO.getModifyDt());
        templateDTO.setShareConfState(templatePO.getShareConfState());
        templateDTO.setIsManager(templatePO.getIsManager());
        templateDTO.setFirstCategory(templatePO.getFirstCategory());
        templateDTO.setSecondCategory(templatePO.getSecondCategory());
        templateDTO.setIsPPT(templatePO.getIsPPT());
        templateDTO.setApiData(templatePO.getApiData());
        templateDTO.setType(templatePO.getType());
        templateDTO.setComponentVersion(templatePO.getComponentVersion());
        return templateDTO;
    }

    @Override
    public void generateShortVideo(String originalVideoUrl, Long templateId) {
        String path = tmpDir + templateId;
        try {
            File dir = new File(path);
            dir.mkdirs();
            String fileName = path + "/" + templateId + "_shortvideo.mp4";

            String ffmpeg = Loader.load(org.bytedeco.ffmpeg.ffmpeg.class);
            List<String> command = new ArrayList<>();
            command.add("sudo");
            command.add(ffmpeg);
            command.add("-i");
            command.add(originalVideoUrl);
            command.add("-an");
            command.add("-filter:v");
            command.add("setpts=0.5*PTS");
            command.add("-ss");
            command.add("00:00:00");
            command.add("-t");
            command.add("00:00:05");
            command.add("-c:v");
            command.add("libvpx-vp9");
            command.add(fileName);

            log.info("短视频处理完整命令：{}", String.join(StringUtils.SPACE, command));
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();
            process.waitFor();
            BufferedReader br1;
            br1 = new BufferedReader(new InputStreamReader(process.getInputStream(), "utf-8"));
            String line1;
            while ((line1 = br1.readLine()) != null) {
                log.info(line1);
            }
            // 关闭Process
            if (process.isAlive()) {
                process.destroy();
            }

            String url = cosFileUploadManager.uploadFile(new File(fileName), null, null, false, true);
            log.info("短视频上传完成，url={}", url);
            if(StringUtils.isNotBlank(url)) {
                this.update(Wrappers.lambdaUpdate(VisualTemplatePO.class).eq(VisualTemplatePO::getTemplateId, templateId).set(VisualTemplatePO::getShortVideoUrl, url).set(VisualTemplatePO::getModifyDt, new Date()));
            }
        } catch (Exception e) {
            log.error("短视频生成失败, newTemplateId="+templateId, e);
        } finally {
            FileUtil.del(path);
        }
    }

    @Override
    public ResponsePageQueryDO<List<VisualTemplateDTO>> syncCloudAuthedTemplatePage(TemplateSearchBO bo) {
        Assert.isTrue(StringUtils.isNotBlank(bo.getTenantCode()), "租户编号不可为空");
        ResponsePageQueryDO<List<VisualTemplateDTO>> response = new ResponsePageQueryDO<>();
        AuthedTemplatePageQueryParam param = new AuthedTemplatePageQueryParam();
        param.setTenantCode(bo.getTenantCode());
        param.setName(bo.getName());
        if(Objects.nonNull(bo.getResolutionType())) {
            param.setResolutionType(bo.getResolutionType() + "");
        }
        param.setFirstCategory(bo.getFirstCategory());
        param.setSecondCategory(bo.getSecondCategory());
        param.setPageIndex(bo.getPageIndex());
        param.setPageSize(bo.getPageSize());
        param.setStatus(bo.getStatus());
        param.setIsSync(Const.ONE);
        Integer count = baseMapper.countAuthedTemplates(param);
        if (Objects.equals(count, 0)) {
            response.setPageIndex(bo.getPageIndex());
            response.setPageSize(bo.getPageSize());
            return response;
        }
        List<VisualTemplatePO> list = baseMapper.listAuthedTemplates(param);
        response.setDataResult(list.stream().map(this::cnvVisualTemplatePO2DTO).collect(Collectors.toList()));
        response.setPageIndex(param.getPageIndex());
        response.setPageSize(param.getPageSize());
        response.setTotal(count);
        return response;
    }

    @Override
    public List<VisualTemplateDTO> syncCloudAuthedTemplateList(TemplateSearchBO bo) {
        AuthedTemplatePageQueryParam param = new AuthedTemplatePageQueryParam();
        param.setTenantCode(bo.getTenantCode());
        param.setStatus(bo.getStatus());
        param.setIsSync(Const.ONE);
        param.setTemplateIds(bo.getTemplateIds());
        List<VisualTemplatePO> list = baseMapper.listAuthedTemplates(param);
        return list.stream().map(this::cnvVisualTemplatePO2DTO).collect(Collectors.toList());
    }

    @Override
    public Integer getTemplateNameMaxIndex(String sourceTemplateName) {
        return this.baseMapper.getTemplateNameMaxIndex(sourceTemplateName);
    }

    /**
     * 根据是否本地化获取模板内容
     *
     * @param templateId
     * @return
     */
    private VisualTemplatePO getVisualTemplatePO(Long templateId, String tenantCode) {
        VisualTemplatePO source = this.lambdaQuery().eq(VisualTemplatePO::getTemplateId, templateId)
                .eq(VisualTemplatePO::getTenantCode, tenantCode).one();
        return source;
    }
    /**
     * 模板标签关联
     * @param newTemplateId
     * @param tagIds
     * @param tagNames
     */
    private void handleTag(Long newTemplateId, List<Long> tagIds, List<String> tagNames){
        List<TagPO> tagPOList = new ArrayList<>();
        //新增标签
        List<Long> newTagIds = new ArrayList<>();
        for (String tagName : tagNames) {
            long newTagId = hostTimeIdg.generateId().longValue();
            TagPO tagPO = new TagPO();
            tagPO.setTagType(1);
            tagPO.setTagId(newTagId);
            tagPO.setName(tagName);
            tagPOList.add(tagPO);
            newTagIds.add(newTagId);
        }
        tagManager.saveBatch(tagPOList);

        tagIds.addAll(newTagIds);
        //移除原来的标签
        templateTagLinkManager.lambdaUpdate().eq(TemplateTagLinkPO::getTemplateId, newTemplateId).remove();

        List<TemplateTagLinkPO> templateTagLinkPOList = new ArrayList<>();
        for (Long tagId : tagIds) {
            TemplateTagLinkPO templateTagLinkPO = new TemplateTagLinkPO();
            templateTagLinkPO.setTemplateId(newTemplateId);
            templateTagLinkPO.setTagId(tagId);
            templateTagLinkPOList.add(templateTagLinkPO);
        }
        //添加新的标签
        templateTagLinkManager.saveBatch(templateTagLinkPOList);
    }

    /**
     * 获取拷贝后的模板名称
     *
     * @param originalTemplateName
     * @return
     */
    private String buildCopyName(String originalTemplateName) {
        int index = getTemplateNameMaxIndex(originalTemplateName + " ") + 1;
        // 生成新模板名称
        return originalTemplateName + " " + index;
    }
}




