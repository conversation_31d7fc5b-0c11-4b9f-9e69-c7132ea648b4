package com.dl.magicvideo.web.controllers.voice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 字幕标记替换词配置类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-07-26 15:02
 */
@Component
public class SubtitleMarkReplaceWordsConfig {

    @Value("#{${dl.subtitle.mark.replace-words-map}}")
    private Map<String, String> replaceWordsMap;

    public Map<String, String> getReplaceWordsMap() {
        return replaceWordsMap;
    }
}
