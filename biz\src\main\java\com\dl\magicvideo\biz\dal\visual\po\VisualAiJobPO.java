package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName visual_ai_job
 */
@TableName(value ="visual_ai_job")
@Data
public class VisualAiJobPO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 视频合成任务id
     */
    private Long produceJobId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * aiservice任务id
     */
    private Long aiJobId;

    /**
     * 片段配置id
     */
    private String configId;

    /**
     * 1-数字人，2-TTS,  3-数字人的tts
     *
     * @see: com.dl.magicvideo.biz.common.enums.AiJobTypeE
     */
    private Integer jobType;

    /**
     * 0-未执行，1-执行中，2-执行成功，3-执行失败
     */
    private Integer jobStatus;

    /**
     * 任务执行结果
     */
    private String mediaInfo;

    /**
     * 字幕
     */
    private String subtitleInfo;

    private String requestInfo;

    private String responseInfo;

    private Date createDt;

    private Long createBy;

    private Date modifyDt;

    private Long duration;

    private String failReason;

    private Integer textLength;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}