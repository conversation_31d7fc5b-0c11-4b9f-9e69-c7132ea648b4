package com.dl.magicvideo.biz.manager.account.trial.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.account.trial.TenantDosageConfigMapper;
import com.dl.magicvideo.biz.dal.account.trial.po.TenantDosageConfigPO;
import com.dl.magicvideo.biz.manager.account.trial.TenantDosageConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TenantDosageConfigImpl extends ServiceImpl<TenantDosageConfigMapper, TenantDosageConfigPO> implements
        TenantDosageConfigManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantDosageConfigImpl.class);


    private static final Long DEFAULT_BALANCE = 20L;

    @Override
    public Integer checkTenantBalance(String tenantCode, Integer count) {
//        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(tenantCode);
//        if (Objects.isNull(tenantInfo) || !Const.ONE.equals(tenantInfo.getIsTrial())) {
//            return null;
//        }
//        AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager.getOne(
//                Wrappers.lambdaQuery(AccountTenantTrialPO.class).eq(AccountTenantTrialPO::getTenantCode, tenantCode)
//                        .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
//        if (Objects.isNull(tenantTrialAccount)) {
//            log.error("未找到试用租户账户 tenantCode = {}", tenantCode);
//            return null;
//        }
//        Long availableBalance = tenantTrialAccount.getBalance() - tenantTrialAccount.getWithhold();
//        Assert.isTrue(count <= availableBalance, "当前用量不足，请联系客户经理获取更多用量。");
//        return (int) (availableBalance - count);
        return 0;
    }
}
