package com.dl.magicvideo.web.controllers.sse;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RestController
@RequestMapping("/visual/sse")
@Api("消息通知接口")
public class SSEController {
    @Resource
    private RedisUtil redisUtil;

    @ApiOperation("提问并流式返回ai回答")
    @PostMapping(value = { "/connect"})
    public void connect(HttpServletResponse resp) throws IOException {
        // 设置响应类型为流式
        resp.setContentType("text/event-stream");
        resp.setCharacterEncoding("UTF-8");
        resp.setHeader("Transfer-Encoding", "chunked");
        resp.setHeader("X-Accel-Buffering", "no");
        OutputStream out = resp.getOutputStream();
        // 发送一个空的 SSE 事件，确保客户端知道连接已建立
        out.write("data: \n\n".getBytes());
        out.flush();
        String sseKey = Const.VISUAL_SSE_QUEUE_KEY_PREFIX + System.currentTimeMillis();
        try {
            //注册一个sseKey
            redisUtil.addSet(Const.VISUAL_SSE_QUEUE_KEY_LIST, sseKey);
            while (true) {
                String msg = redisUtil.rightPop(sseKey);
                if (StringUtils.isNotBlank(msg)) {
                    writeSseEvent(out, msg);
                }
                // 队列为空时，短暂休眠，等待新的消息
                Thread.sleep(1000); // 休眠1秒
            }
        } catch (InterruptedException e) {
            // 重新设置中断状态
            Thread.currentThread().interrupt();
            log.error("消息发送异常", e);
        } catch (Exception e) {
            // 处理 IO 异常，避免意外关闭客户端连接
            log.error("消息发送错误", e);
        } finally {
            redisUtil.removeSet(Const.VISUAL_SSE_QUEUE_KEY_LIST, sseKey);
            redisUtil.expire(sseKey, 1);
            out.close();
        }
    }

    /**
     * 写入SSE事件
     * <p>
     * 内容结构体：
     * {
     * "text": "",
     * "event": ""
     * }
     * <p>
     * event值：
     * cmpl -表示ai回答   text为回答的内容
     * error -表示错误   text为错误信息
     * chat_record_id - 表示聊天记录id  text为聊天记录id
     * all_done - 表示全部ai回答完成  无text
     */
    private void writeSseEvent(OutputStream out, String data) throws IOException {
        if (StringUtils.isBlank(data)) {
            log.info("data为空，不写入流");
            return;
        }
        out.write("data: ".getBytes(StandardCharsets.UTF_8));
        out.write(data.getBytes(StandardCharsets.UTF_8));
        out.write("\n".getBytes(StandardCharsets.UTF_8));
        out.flush();
    }
}