package com.dl.magicvideo.biz.manager.statistics;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.PageQueryDO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.dal.statistics.param.TenantStatisticsTotalDurationQueryParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxTenantCodeParam;
import com.dl.magicvideo.biz.dal.statistics.param.TotalCountParam;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsTotalCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.TenantStatisticsTotalDurationPO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsCountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StatisticsCountManager extends IService<StatisticsCountPO>, CommonService {

    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List<com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO>>
     * @Author: zhousx
     * @Date: 2023/6/18 23:48
     */
    ResponsePageQueryDO<List<StatisticsCountDTO>> pageQuery(PageQueryDO pageQueryDO);

    /**
     * 一段时间内topX 租户
     * @param param
     * @return
     */
    List<StatisticsCountTopMaxPO> topMaxTenantCode(@Param("param") TopMaxTenantCodeParam param);

    /**
     * 一段时间内的总数
     * @param param
     * @return
     */
    List<StatisticsTotalCountPO> totalCount(@Param("param") TotalCountParam param);

    /**
     * 一端时间内租户的数量信息
     *
     * @param param
     * @return
     */
    List<StatisticsMsgPO> topMaxMsg(@Param("param") TopMaxMsgParam param);

    /**
     * 查询指定租户的总合成时长
     *
     * @param param
     * @return
     */
    TenantStatisticsTotalDurationPO queryTenantTotalDuration(TenantStatisticsTotalDurationQueryParam param);

}
