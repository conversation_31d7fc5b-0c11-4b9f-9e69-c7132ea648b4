package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplateAuthPO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TenantTemplateAuthBO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO;

import java.util.List;

/**
 *针对表 visual_template_auth 的数据库操作
 */
public interface VisualTenantTemplateAuthManager extends IService<VisualTemplateAuthPO>, CommonService {
    /**
     * 分页查询
     * @param templateAuthSearchBO
     * @return com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List < com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO>>
     */
    ResponsePageQueryDO<List<TemplateAuthDTO>> pageQuery(TemplateAuthSearchBO templateAuthSearchBO);

    /**
     * 根据传入授权/未授权参数 分页查询
     * @param templateAuthSearchBO
     * @return
     */
    ResponsePageQueryDO<List<TemplateAuthDTO>> pageQueryByStatus(TemplateAuthSearchBO templateAuthSearchBO);

    /**
     * 修改授权
     * @param bo
     */
    void auth(TenantTemplateAuthBO bo);
}
