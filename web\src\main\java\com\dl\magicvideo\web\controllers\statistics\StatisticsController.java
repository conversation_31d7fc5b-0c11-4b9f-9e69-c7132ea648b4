package com.dl.magicvideo.web.controllers.statistics;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.web.controllers.internal.statistics.param.StatisticsAiJobInternalPageParam;
import com.dl.magicvideo.web.controllers.statistics.param.CountParam;
import com.dl.magicvideo.web.controllers.statistics.param.EfficiencyParam;
import com.dl.magicvideo.web.controllers.statistics.param.StatisticsAiJobQueryParam;
import com.dl.magicvideo.web.controllers.statistics.vo.CountVO;
import com.dl.magicvideo.web.controllers.statistics.vo.EfficiencyVO;
import com.dl.magicvideo.web.controllers.statistics.vo.StatisticsAiJobVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @describe: 统计相关接口
 * @author: hongcj
 * @date: 2023/6/17 14:43
 */
@Slf4j
@RestController
@RequestMapping("/visual/statistics")
@Api("生产看板管理")
public class StatisticsController {
    @Autowired
    private StatisticsProcess statisticsProcess;

    @PostMapping("/count")
    @ApiOperation("生产数量统计")
    public ResultModel<List<CountVO>> count(@RequestBody @Validated CountParam param) {
        return ResultModel.success(statisticsProcess.count(param));
    }

    @PostMapping("/efficiency")
    @ApiOperation("生产效率统计")
    @Deprecated
    public ResultModel<List<EfficiencyVO>> efficiency(@RequestBody @Validated EfficiencyParam param) {
        return ResultModel.success(statisticsProcess.efficiency(param));
    }

    @PostMapping("/aijob")
    @ApiOperation("ai任务统计")
    public ResultModel<List<StatisticsAiJobVO>> aiJobStatistics(@RequestBody @Validated StatisticsAiJobQueryParam queryParam) {
        return ResultModel.success(statisticsProcess.aiJobStatistics(queryParam));
    }

}
