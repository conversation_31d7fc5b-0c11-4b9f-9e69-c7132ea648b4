package com.dl.magicvideo.war;


import cn.hutool.core.date.DateUtil;
import com.dl.framework.core.converter.JSONObjectMapper;
import com.dl.magicvideo.biz.manager.visual.vo.DxDailyFinancialReviewVO;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.Date;

public class Test {

    public static void main(String[] args) throws JsonProcessingException {
        DxDailyFinancialReviewVO vo = new DxDailyFinancialReviewVO();
        vo.setEndTts("");
        vo.setBeginName(null);
        JSONObjectMapper objectMapper = new JSONObjectMapper();
        String s = objectMapper.writeValueAsString(vo);
        System.out.println(s);
    }

}
