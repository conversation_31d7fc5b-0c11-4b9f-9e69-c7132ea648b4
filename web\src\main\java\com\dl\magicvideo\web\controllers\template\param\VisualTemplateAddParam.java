package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @describe: TemplateAddParam
 * @author: zhousx
 * @date: 2023/2/1 14:53
 */
@Data
public class VisualTemplateAddParam {
    @ApiModelProperty("模板名称")
    @NotBlank
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("模板尺寸")
    private String resolution;

    @ApiModelProperty("1-竖版 2-横版")
    private String resolutionType = "1";

    @ApiModelProperty("tts配置")
    private String ttsParam;

    @ApiModelProperty("背景音乐")
    private String bgMusic;

    @ApiModelProperty("背景音乐配置")
    private String bgMusicParam;

    @ApiModelProperty("替换变量")
    private String replaceData;

    @ApiModelProperty("模板时长，毫秒")
    private Long duration;

    @ApiModelProperty("组件版本号")
    private String componentVersion;

    @ApiModelProperty("是否展示，0-否，1-是，默认为1")
    private Integer isShow = 1;
}
