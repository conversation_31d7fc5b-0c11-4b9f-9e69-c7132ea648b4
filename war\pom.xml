﻿<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
	http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dl.magicvideo</groupId>
        <artifactId>dl-magicvideo</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dl-magicvideo-war</artifactId>
    <name>dl-magicvideo-war</name>

	<dependencies>
        <dependency>
            <groupId>com.dl.magicvideo</groupId>
            <artifactId>dl-magicvideo-web</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dl.magicvideo</groupId>
            <artifactId>dl-magicvideo-task</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dl.magicvideo</groupId>
            <artifactId>dl-magicvideo-openapi</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 单元测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
<!--                    <executions>-->
<!--                        <execution>-->
<!--                            <id>default</id>-->
<!--                            <goals>-->
<!--                                <goal>build</goal>-->
<!--                            </goals>-->
<!--                        </execution>-->
<!--                    </executions>-->
                <configuration>
                    <repository>${entry.docker.url}/${project.groupId}/${project.artifactId}</repository>
                    <tag>${project.version}</tag>
                    <buildArgs>
                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>