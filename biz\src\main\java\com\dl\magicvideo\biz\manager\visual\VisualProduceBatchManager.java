package com.dl.magicvideo.biz.manager.visual;

import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceBatchPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceBatchSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.ProduceBatchDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【visual_produce_batch(作业批次表)】的数据库操作Service
* @createDate 2023-06-19 11:37:08
*/
public interface VisualProduceBatchManager extends IService<VisualProduceBatchPO>, CommonService {

    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List<com.dl.magicvideo.biz.manager.visual.dto.ProduceBatchDTO>>
     * @Author: zhousx
     * @Date: 2023/6/19 18:01
     */
    ResponsePageQueryDO<List<ProduceBatchDTO>> pageQuery(ProduceBatchSearchBO bo);
    
    /**
     * 功能描述: <br>
     * @Param: [batchId]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/6/20 11:27
     */
    void cancel(Long batchId);
}
