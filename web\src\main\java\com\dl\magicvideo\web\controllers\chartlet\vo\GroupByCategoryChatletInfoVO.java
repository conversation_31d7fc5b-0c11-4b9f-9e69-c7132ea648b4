package com.dl.magicvideo.web.controllers.chartlet.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-27 09:22
 */
@Data
public class GroupByCategoryChatletInfoVO {

    @ApiModelProperty("分类编码")
    private Integer category;

    @ApiModelProperty("分类名称")
    private String categoryName;

    @ApiModelProperty("分类排序")
    private Integer sort;

    @ApiModelProperty("贴图列表")
    private List<ChartletInfoVO> chartletList;
}
