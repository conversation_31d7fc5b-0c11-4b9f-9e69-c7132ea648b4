package com.dl.magicvideo.web.controllers.internal.statistics.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-27 17:17
 */
@Data
public class InternalStatisticsAiJobTopTenantQueryParam {

    @ApiModelProperty("最小时间")
    private Date minDt;

    @ApiModelProperty("最大时间")
    private Date maxDt;

    @ApiModelProperty("ai任务类型，1-数字人，2-TTS")
    private Integer aiJobType;

    @ApiModelProperty("查询前几名。默认为4")
    private Integer topNumber;
}
