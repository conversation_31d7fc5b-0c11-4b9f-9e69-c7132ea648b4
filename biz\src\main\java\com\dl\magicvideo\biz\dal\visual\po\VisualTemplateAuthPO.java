package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

/**
 * 
 * @TableName visual_template_auth
 */
@TableName(value ="visual_template_auth")
@Data
public class VisualTemplateAuthPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    private String creatorName;

    private String modifyName;

    /**
     * 源模板id
     * 对于云端，该字段存的是创建系统模板时选中的DL租户的模板id。
     * 对于本地化，该字段存的是云端的系统模板id。
     */
    private Long sourceTemplateId;

    /**
     * 同步状态
     * 0-同步中，1-同步成功，2-同步失败
     * @see: com.dl.magicvideo.biz.manager.visual.enums.TemplateSyncStatusEnum
     */
    private Integer syncStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}