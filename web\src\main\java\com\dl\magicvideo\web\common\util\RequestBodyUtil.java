package com.dl.magicvideo.web.common.util;

import org.springframework.stereotype.Component;

@Component
public class RequestBodyUtil {

    private static ThreadLocal<String> requestBodyHolder = new ThreadLocal<>();

    public void init(String requestBody) {
        requestBodyHolder.set(requestBody);
    }

    public String getRequestBody() {
        return requestBodyHolder.get();
    }

    public void remove() {
        requestBodyHolder.remove();
    }
}
