package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobDurationBO;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.TtsJobBO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【visual_ai_job】的数据库操作Service
 * @createDate 2023-06-08 16:23:52
 */
public interface VisualAiJobManager extends IService<VisualAiJobPO> {

    Long submitDigitalManJob(DigitalManJobBO bo);

    VisualAiJobPO submitTtsJob(TtsJobBO bo);

    ResponsePageQueryDO<List<VisualAiJobExtPO>> pageExt(VisualAiJobPageBO param);

    Long countDuration(VisualAiJobDurationBO query);
}
