package com.dl.magicvideo.web.controllers.internal.produce.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe: ProduceBatchPageQueryParam
 * @author: zhousx
 * @date: 2023/6/19 17:24
 */
@Data
public class ProduceBatchPageQueryParam extends AbstractPageParam {
    @ApiModelProperty("发起时间-开始")
    private Date startTime;

    @ApiModelProperty("发起时间-结束")
    private Date endTime;

    @ApiModelProperty("批次状态：0-排队中 1-生产中 2-生产完成 3-生产异常 4-已取消")
    private List<Integer> statusList;

    @ApiModelProperty("租户id列表")
    private List<String> tenantCodeList;

    private Long planId;
}
