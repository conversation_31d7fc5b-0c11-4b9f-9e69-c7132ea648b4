package com.dl.magicvideo.web.controllers.aigc.prompt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.aigc.po.AigcPromptPO;
import com.dl.magicvideo.biz.manager.aigc.prompt.AigcPromptManager;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptAddBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptContentBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptFileBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptQueryBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptUptBO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.aigc.prompt.convert.AigcPromptConvert;
import com.dl.magicvideo.web.controllers.aigc.prompt.param.AigcPromptAddParam;
import com.dl.magicvideo.web.controllers.aigc.prompt.param.AigcPromptPageQueryParam;
import com.dl.magicvideo.web.controllers.aigc.prompt.param.AigcPromptUptParam;
import com.dl.magicvideo.web.controllers.aigc.prompt.vo.AigcPromptVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 11:45
 */
@Component
public class AigcPromptProcess extends AbstractController {

    @Resource
    private AigcPromptManager aigcPromptManager;
    @Resource
    private OperatorUtil operatorUtil;

    public ResultPageModel<AigcPromptVO> page(AigcPromptPageQueryParam param) {
        AigcPromptQueryBO queryBO = new AigcPromptQueryBO();
        queryBO.setPageIndex(param.getPageIndex());
        queryBO.setPageSize(param.getPageSize());
        queryBO.setScene(param.getScene());
        IPage<AigcPromptPO> poPageResult = aigcPromptManager.pageQuey(queryBO);

        return pageQueryModel(poPageResult, AigcPromptConvert::cnvAigcPromptPO2VO);
    }

    public ResultModel<String> add(AigcPromptAddParam param) {
        AigcPromptAddBO addBO = new AigcPromptAddBO();
        addBO.setName(param.getName());
        addBO.setSort(param.getSort());
        addBO.setCreateBy(operatorUtil.getOperator());
        addBO.setModifyBy(operatorUtil.getOperator());
        addBO.setCreateDt(new Date());
        addBO.setModifyDt(new Date());
        addBO.setScene(param.getScene());
        if (Objects.nonNull(param.getRelFile())) {
            AigcPromptFileBO fileBO = new AigcPromptFileBO();
            fileBO.setName(param.getRelFile().getName());
            fileBO.setSize(param.getRelFile().getSize());
            fileBO.setType(param.getRelFile().getType());
            fileBO.setTypeFormat(param.getRelFile().getTypeFormat());
            fileBO.setUrl(param.getRelFile().getUrl());
            addBO.setRelFile(fileBO);
        }
        if (Objects.nonNull(param.getContent())) {
            AigcPromptContentBO contentBO = new AigcPromptContentBO();
            contentBO.setText(param.getContent().getText());
            addBO.setContent(contentBO);
        }

        Long bizId = aigcPromptManager.add(addBO);

        return ResultModel.success(String.valueOf(bizId));
    }

    public ResultModel<Void> update(AigcPromptUptParam param) {
        AigcPromptUptBO uptBO = new AigcPromptUptBO();
        uptBO.setBizId(param.getBizId());
        uptBO.setName(param.getName());
        uptBO.setSort(param.getSort());
        uptBO.setModifyBy(operatorUtil.getOperator());
        uptBO.setModifyDt(new Date());
        if (Objects.nonNull(param.getRelFile())) {
            AigcPromptFileBO fileBO = new AigcPromptFileBO();
            fileBO.setName(param.getRelFile().getName());
            fileBO.setType(param.getRelFile().getType());
            fileBO.setSize(param.getRelFile().getSize());
            fileBO.setTypeFormat(param.getRelFile().getTypeFormat());
            fileBO.setUrl(param.getRelFile().getUrl());
            uptBO.setRelFile(fileBO);
        }
        if (Objects.nonNull(param.getContent())) {
            AigcPromptContentBO contentBO = new AigcPromptContentBO();
            contentBO.setText(param.getContent().getText());
            uptBO.setContent(contentBO);
        }
        aigcPromptManager.update(uptBO);

        return ResultModel.success(null);
    }

}
