package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class ShareConfUpdateStateParam {

    /**
     * 业务来源id
     */
    @ApiModelProperty("业务来源id")
    @NotNull(message = "业务来源id不能为空")
    private String bizId;

    /**
     * wc调用修改交互式状态
     *
     * @see: ShareConfStateEnum
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     */
    @Max(2)
    @Min(0)
    private Integer shareConfState;

    /**
     * 业务类型  3定数作品 4定数模板 InteractiveConfBizTypeEnum
     */
    private Integer bizType;
}
