package com.dl.magicvideo.biz.client.deepsound;

import com.dl.magicvideo.biz.client.deepsound.intercepter.DeepSoundForestInterceptor;
import com.dl.magicvideo.biz.client.deepsound.req.DsAudioTrainRequest;
import com.dl.magicvideo.biz.client.deepsound.req.DsTtsRequest;
import com.dl.magicvideo.biz.client.deepsound.resp.DsAudioCheckResponse;
import com.dl.magicvideo.biz.client.deepsound.resp.DsAudioTrainResponse;
import com.dl.magicvideo.biz.client.deepsound.resp.DsBaseResponse;
import com.dl.magicvideo.biz.client.deepsound.resp.DsQueryAudioTrainResponse;
import com.dl.magicvideo.biz.client.deepsound.resp.DsTtsResponse;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Header;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.dtflys.forest.annotation.Request;

@BaseRequest(baseURL = "https://api.deepsound.cn/public/clone/prod", interceptor = DeepSoundForestInterceptor.class)
public interface DeepSoundClient {

    String X_APPID = "00a5724463fe5e4c583b701ee5219c93";

    String X_APP_KEY = "38063898a0d1f5858011a1b4abfbb967";

    /**
     * 用户 ID
     */
    String APP_ID = "X-Appid";

    /**
     * 项目 Appkey
     */
    String APP_KEY = "X-Appkey";

    /**
     * 服务鉴权 Token
     */
    String APP_TOKEN = "X-Token";

    /**
     * X-Curtime timestamp 是 当前系统时间戳 从 1970 年 1 月 1 日零点 到当前 UTC 时间的秒数
     */
    String HEADER_X_CURTIME = "X-Curtime";

    /**
     * 消息内容摘要
     * checksum 的计算方法为：
     * 先拼接字符串 AppId + Now（当前系统时间戳） + body参数的JSON字符串，
     * 再使用标准md5算法， 计算上述拼接字符串的 md5 值，32 位小写；
     * 如： 72cab41e2361c67e2d2 bdadfc7b7fb9b。
     */
    String HEADER_X_CHECKSUM = "X-Checksum";

    /**
     * 环境音检测h
     * 在开始录制前，录制一段 3 秒以上的 环境静音的音频，以检测录制环境是 否符合可录制的标准，只有环境音检 测通过，才可进入文本录制环节。
     *
     * @param url 音频文件存放 url 链接,url 长度不超过 1KB
     * @return
     */
    @Post(url = "/env/check")
    DsBaseResponse envCheck(@Body("url") String url);

    /**
     * 音质检测接口
     * 检测采集的数据是否 符合声音克隆模型训练要求。
     *
     * @param url  音频文件存放 url 链接,url 长度不超过 1KB
     * @param text 音频内容的对应文本，不超过 35 个中文字符
     * @return
     */
    @Post(url = "/audio/check")
    DsAudioCheckResponse audioCheck(@Body("url") String url, @Body("text") String text,
            @Body("language") String language);

    /**
     * 模型训练
     * 提交采集录音数据，训练声音模型。
     *
     * @param request
     * @return
     */
    @Post(url = "/api_rec_v1/recording", dataType = "json")
    DsAudioTrainResponse audioTrain(@JSONBody DsAudioTrainRequest request);

    /**
     * 模型查询
     * 查询已提交训练模型 的状态。
     *
     * @param recordId
     * @return
     */
    @Get(url = "/api_rec_v1/recording")
    DsQueryAudioTrainResponse queryAudioTrain(@Query("record_id") String recordId);

    /**
     * 语音合成
     * 将待合成的文本上传到服务端，服务端返回文本的语音合成结果，开发者需要保证在语音合成结果返回之前连接不中断
     *
     * @param request
     * @return
     */
    @Request(url = "/tts", contentType = "application/json; charset=utf-8", dataType = "json", type = "post")
    DsTtsResponse tts(@Header(HEADER_X_CURTIME) String now, @Header(HEADER_X_CHECKSUM) String checksum,
            @JSONBody DsTtsRequest request);
}
