<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.CommonComponentsMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.visual.po.CommonComponentsPO">
        <result property="id" column="id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="bizId" column="biz_id"/>
        <result property="tagId" column="tag_id"/>
        <result property="url" column="url"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="renderData" column="render_data"/>
    </resultMap>

     <resultMap id="ResultMap" type="com.dl.magicvideo.biz.dal.visual.po.CommonComponentsPO">
        <result property="id" column="id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="bizId" column="biz_id"/>
        <result property="tagId" column="tag_id"/>
        <result property="url" column="url"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="rowNumber" column="row_number"/>
    </resultMap>

    <select id="listCommonComponents" resultMap="BaseResultMap">
        SELECT biz_id, tag_id, url, tenant_code, name
        FROM common_components
        WHERE is_deleted = 0
        <if test="param.tenantCode != null">
            AND tenant_code = #{param.tenantCode}
        </if>
         <if test="param.tagId != null">
            AND tag_id = #{param.tagId}
        </if>
        ORDER BY create_dt DESC
        limit #{param.offset}, #{param.pageSize}
    </select>

    <select id="countCommonComponents" resultType="java.lang.Integer">
        SELECT count(biz_id)
        FROM common_components
        WHERE is_deleted = 0
        <if test="param.tenantCode != null">
            AND tenant_code = #{param.tenantCode}
        </if>
        <if test="param.tagId != null">
            AND tag_id = #{param.tagId}
        </if>
    </select>

    <sql id="Column_List">
        biz_id, tag_id, url, tenant_code, `name`
    </sql>

    <select id="latestComponentsByTagIds"
            resultMap="ResultMap">
        select
        <include refid="Column_List"/>,rn
        from
        (
        select
        <include refid="Column_List"/>,
        ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY create_dt DESC) AS rn
        from common_components
        where
        tenant_code = #{tenantCode}
        <if test="tagIds!=null and tagIds.size() > 0">
            and tag_id in
            <foreach collection="tagIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and is_deleted = 0
        ) AS ranked
        WHERE
        rn &lt;= #{rowNumber};
    </select>
</mapper>
