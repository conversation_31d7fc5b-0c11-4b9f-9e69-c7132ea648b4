package com.dl.magicvideo.web.controllers.produce;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.produce.param.ProduceBatchPageQueryParam;
import com.dl.magicvideo.web.controllers.produce.param.ProduceJobPageQueryParam;
import com.dl.magicvideo.web.controllers.produce.vo.DailyProduceStatisticsVO;
import com.dl.magicvideo.web.controllers.produce.vo.ProduceBatchVO;
import com.dl.magicvideo.web.controllers.produce.vo.ProduceJobVO;
import com.dl.magicvideo.web.controllers.produce.vo.ProduceJobTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @describe: VisualProduceController
 * @author: zhousx
 * @date: 2023/6/19 10:38
 */
@RestController
@RequestMapping("/visual/produce")
@Api("生产作业模块")
public class VisualProduceController {
    @Autowired
    private VisualProduceProcess visualProduceProcess;

    @PostMapping("/batchlist")
    @ApiOperation("批次列表")
    public ResultPageModel<ProduceBatchVO> batchList(@RequestBody ProduceBatchPageQueryParam param) {
        return visualProduceProcess.batchList(param);
    }

    @PostMapping("/joblistbybatchId")
    @ApiOperation("批次下作业列表")
    public ResultPageModel<ProduceJobVO> jobListByBatchId(@RequestBody ProduceJobPageQueryParam param) {
        return visualProduceProcess.jobListByBatchId(param);
    }

    @PostMapping("/cancelbatch/{batchId}")
    @ApiOperation("取消批次")
    public ResultModel<Void> cancelBatch(@PathVariable Long batchId) {
        return visualProduceProcess.cancelBatch(batchId);
    }

    @PostMapping("/jobdetail/{jobId}")
    @ApiOperation("作业详情")
    public ResultModel<ProduceJobVO> jobDetail(@PathVariable Long jobId) {
        return visualProduceProcess.jobDetail(jobId);
    }

    @PostMapping("/dailystatistics")
    @ApiOperation("当日生产统计")
    public ResultModel<DailyProduceStatisticsVO> dailyStatistics() {
        return visualProduceProcess.dailyStatistics();
    }

    @PostMapping("/deleteworks/{jobId}")
    @ApiOperation("删除作品")
    public ResultModel<Void> deleteWorks(@PathVariable Long jobId) {
        return visualProduceProcess.deleteWorks(jobId);
    }

    @PostMapping("/jobdetailwithtemplatesnapshot/{jobId}")
    @ApiOperation("查询作品详情并带模板快照")
    public ResultModel<ProduceJobTemplateVO> jobDetailWithTemplateSnapshot(@PathVariable Long jobId) {
        return visualProduceProcess.jobDetailWithTemplateSnapshot(jobId);
    }
}
