package com.dl.magicvideo.web.controllers.digitalman.enums;

import java.util.Objects;

/**
 * 数字人枚举(批量合成)
 */
public enum DigitalmanForBatchEnums {

    LS("1213249653988474831","9fd9e0380fd548ab965510898331984e",2,"刘帅", "9fd9e0380fd548ab965510898331984e","9fd9e0380fd548ab965510898331984e",1d,"https://dl-prod-1314522657.cos.ap-shanghai.myqcloud.com/content/images/1698831524910123.png","https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1213250933560019740.webm",3680L, "https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1195236535110874552.mp3", 3624L),
    //1.3倍语速
    ZYN("49","custome_100027998498_1702",2,"张忆南", "02e3547e77e8f3a5c955a1f12517e170","02e3547e77e8f3a5c955a1f12517e170",1.3d,"https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/242/1702/2490/model_3169_20230518142951/header.png","https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1213248198543232442.webm",3200L, "https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1213248197552328122.mp3", 3096L),
    //1.2倍速
    CCQ("78","custome_100027998498_1920",2,"陈超群", "034305c277cea61adfd275b0c00a80d3","034305c277cea61adfd275b0c00a80d3",1.2d,"https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/242/1920/4704/model_6082_20230725144414/header.png", "https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1213248198925962683.webm",3520L, "https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1195233799107791178.mp3", 3264L),
    //1.3倍语速
    YX("88","custome_100027998498_3829",2,"雨欣", "50880a630c944cefa78d155bf8f40710","50880a630c944cefa78d155bf8f40710",1.3d,"https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/162/3827/4913/model_6385_20230803103433/header.png","https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1195233800130152778.webm",3200L, "https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1213248197552328122.mp3", 3096L),
    //1.3倍语速
    XH("89","custome_100027998498_3821",2,"湘惠", "c9d48baa5bf74d3f9b2f56bfdc9d36da","c9d48baa5bf74d3f9b2f56bfdc9d36da",1.3d,"https://virtualhuman-cos-test-1251316161.cos.ap-nanjing.myqcloud.com/prod/resource-manager/small/162/3821/4930/model_6404_20230803114124/header.png","https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1195233800223476043.webm",3200L, "https://dl-ivhmedia-1314522657.cos.ap-shanghai.myqcloud.com/1213248197552328122.mp3", 3096L),
    //1.0倍速
    YUNX("81", "yx_green_skirt_suit_stand",2,"云萱", "aea4abf1364744f39699d8e003f73def","963058615cd94f7ebeb01a51f3d1c71e",1d,"https://virtualhuman-cos-prod-1251316161.cos.ap-nanjing.myqcloud.com/virtualman-config/c0948a75-b4fd-461e-88e6-c15ef7c3b8f7-weilan_xyb_fenfuanqun.png","https://dl-ivhmedia-1309667514.cos.ap-shanghai.myqcloud.com/1186226319450690228.webm",4200L, "https://dl-ivhmedia-1309667514.cos.ap-shanghai.myqcloud.com/1186226319654113971.mp3", 3920L);

    /**
     * 数字人ID
     */
    private String bizId;

    private String vmCode;
    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 数字人名称
     */
    private String name;

    /**
     * 场景ID
     */
    private String sceneId;

    /**
     * 声音code
     */
    private String voiceCode;

    /**
     * 语速
     */
    private Double speed;

    private String headImg;

    private String webmUrl;

    private Long webmDuration;

    private String ttsUrl;

    private Long ttsDuration;

    public String getVmCode() {
        return vmCode;
    }

    public Integer getChannel() {
        return channel;
    }

    public String getName() {
        return name;
    }

    public String getSceneId() {
        return sceneId;
    }

    public String getVoiceCode() {
        return voiceCode;
    }

    public Double getSpeed() {
        return speed;
    }

    public String getHeadImg() {
        return headImg;
    }

    public String getWebmUrl() {
        return webmUrl;
    }

    public Long getWebmDuration() {
        return webmDuration;
    }

    public String getTtsUrl() {
        return ttsUrl;
    }

    public Long getTtsDuration() {
        return ttsDuration;
    }

    public String getBizId() {
        return bizId;
    }

    DigitalmanForBatchEnums(String bizId, String vmCode, Integer channel, String name, String sceneId, String voiceCode, Double speed,
            String headImg, String webmUrl, Long webmDuration, String ttsUrl, Long ttsDuration) {
        this.bizId = bizId;
        this.vmCode = vmCode;
        this.channel = channel;
        this.name = name;
        this.sceneId = sceneId;
        this.voiceCode = voiceCode;
        this.speed = speed;
        this.headImg = headImg;
        this.webmUrl = webmUrl;
        this.webmDuration = webmDuration;
        this.ttsUrl = ttsUrl;
        this.ttsDuration = ttsDuration;
    }

    public static DigitalmanForBatchEnums getByVmCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (DigitalmanForBatchEnums e : DigitalmanForBatchEnums.values()) {
            if (e.getVmCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
