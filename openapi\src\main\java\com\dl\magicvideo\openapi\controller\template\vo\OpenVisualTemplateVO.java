package com.dl.magicvideo.openapi.controller.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-09 10:22
 */
@Data
public class OpenVisualTemplateVO {

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty("时长，单位：毫秒")
    private Long duration;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("数据网关替换变量")
    private String apiData;

}
