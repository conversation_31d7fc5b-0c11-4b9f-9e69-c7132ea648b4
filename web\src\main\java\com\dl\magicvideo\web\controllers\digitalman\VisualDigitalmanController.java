package com.dl.magicvideo.web.controllers.digitalman;

import com.dl.aiservice.share.digitalman.DigitalManCallbackDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.web.controllers.digitalman.param.DmAggregationParam;
import com.dl.magicvideo.web.controllers.digitalman.param.DmInfoParam;
import com.dl.magicvideo.web.controllers.digitalman.param.DmProduceParam;
import com.dl.magicvideo.web.controllers.digitalman.param.GetJobDetailParam;
import com.dl.magicvideo.web.controllers.digitalman.param.GetSceneListParam;
import com.dl.magicvideo.web.controllers.digitalman.vo.DigitalManSceneVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DigitalmanForBatchVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DmAggregationVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DmSubtitleResultVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.GenericDigitalmanVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.JobVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.ProduceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @describe: 数字人接口
 * @author: zhousx
 * @date: 2023/6/5 13:58
 */
@Slf4j
@RestController
@RequestMapping("/visual/digitalman")
@Api("数字人")
public class VisualDigitalmanController {
    @Autowired
    private VisualDigitalmanProcess visualDigitalmanProcess;

    @PostMapping("/genericlist")
    @ApiOperation("数字人列表")
    public ResultModel<List<GenericDigitalmanVO>> genericDigitalmanList() {
        return visualDigitalmanProcess.genericDigitalmanList();
    }

    @PostMapping("/info")
    @ApiOperation("数字人信息")
    public ResultModel<GenericDigitalmanVO> info(@RequestBody @Validated DmInfoParam param) {
        return visualDigitalmanProcess.info(param.getVmBizId());
    }

    @PostMapping("/aggregationinfo")
    @ApiOperation("查询单个数字人组合信息（数字人信息、数字人场景、数字人声音信息）")
    public ResultModel<DmAggregationVO> aggregationinfo(@RequestBody @Validated DmAggregationParam param) {
        return visualDigitalmanProcess.aggregationinfo(param.getVmBizId(), param.getSceneId());
    }

    @PostMapping("/scenelist")
    @ApiOperation("数字人对应场景列表")
    public ResultModel<List<DigitalManSceneVO>> sceneList(@RequestBody GetSceneListParam param) {
        return visualDigitalmanProcess.sceneList(param);
    }

    @PostMapping("/produce")
    @ApiOperation("提交数字人合成")
    public ResultModel<ProduceVO> produce(@RequestBody DmProduceParam param) {
        return visualDigitalmanProcess.produce(param);
    }

    @Deprecated
    @PostMapping("/gensubtitle")
    @ApiOperation("获取数字人字幕")
    public ResultModel<DmSubtitleResultVO> generateSubtitle(@RequestBody DmProduceParam param) {
        return visualDigitalmanProcess.generateSubtitle(param);
    }

    @PostMapping("/subtitledetail")
    @ApiOperation("查询数字人字幕")
    public ResultModel<DmSubtitleResultVO> subtitleDetail(@RequestBody GetJobDetailParam param) {
        return visualDigitalmanProcess.subtitleDetail(param);
    }

    @PostMapping("/jobdetail")
    @ApiOperation("查询数字人合成结果")
    public ResultModel<JobVO> jobDetail(@RequestBody GetJobDetailParam param) {
        return visualDigitalmanProcess.jobDetail(param);
    }

    @PostMapping("/callback")
    @NotLogin
    public ResultModel<Boolean> callback(@RequestBody DigitalManCallbackDTO param) {
        return visualDigitalmanProcess.callback(param);
    }

    @PostMapping("/listforbatch")
    @ApiOperation("批量合成里的数字人列表")
    public ResultModel<List<DigitalmanForBatchVO>> listForBatch() {
        return visualDigitalmanProcess.listForBatch();
    }
}
