package com.dl.magicvideo.web.controllers.auth.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @describe: AuthParam
 * @author: zhousx
 * @date: 2023/6/18 21:42
 */
@Data
public class AuthParam {
    @ApiModelProperty(value = "授权模板id", required = true)
    @NotBlank
    private String templateId;

    @ApiModelProperty("系统模板名称")
    private String name;

    @ApiModelProperty("系统模板封面")
    private String coverUrl;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty("授权租户列表")
    private List<TenantInfoParam> tenants;

    @ApiModelProperty("是否包含理财经理信息 1 包含 0 不包含")
    private Integer isManager;

    @ApiModelProperty("一级分类 1.银行，2.证券，3.基金，4.理财子")
    private Integer firstCategory;

    @ApiModelProperty("二级分类")
    private Integer secondCategory;
}
