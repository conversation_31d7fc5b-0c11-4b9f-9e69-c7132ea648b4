package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class SzbAttachmentTwoDTO {
    /**
     * 数据模块指标名
     */
    private String note;
    /**
     * 指标数据单位
     */
    private String unit;
    /**
     * 同比方向（同比为正1，同比为负值-1）
     */
    private Integer tbfx;
    /**
     * 数据是否正确（1为数据正常，如果数据模块其他值为空且dataOk为1时，表示本报告期此数据模块为空
     */
    private Integer dataOk;
    /**
     * 指标数据
     */
    private String value;
    /**
     * 指标同比数据（都是正值）
     */
    private String tb;
    /**
     * 此数据模块文字描述
     */
    private String desc;

    /**
     * 指标环比数据（都是正值）可为空
     */
    private String hb;
    /**
     * 环比方向（参考同比方向）环比为空时，方向也为空
     */
    private Integer hbfx;

}
