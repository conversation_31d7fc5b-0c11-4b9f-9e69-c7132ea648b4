package com.dl.magicvideo.biz.common.constant;

import java.math.MathContext;
import java.math.RoundingMode;

public interface Const {
    String SIMPLE_JWT_SECRET = "ZGluZ2xpa2VqaV9oYW5nemhvdQ==";//JWT秘钥
    String SIMPLE_JWT_PREFIX = "dl_jwt_";//JWT前缀

    /**
     * 默认租户
     */
    String DEFAULT_TENANT_CODE = "DL";

    Long MINUS_ONE_LONG = -1L;

    Integer ZERO = 0;

    Integer ONE = 1;

    Integer TWO = 2;

    Integer THREE = 3;

    Integer FOUR = 4;

    Integer FIVE = 5;

    Integer SIX = 6;

    Integer SEVEN = 7;

    Integer EIGHT = 8;

    Integer NINE = 9;

    Integer TEN = 10;

    Integer FOURTEEN = 14;

    Integer FIFTEEN = 15;

    Integer TWENTY = 20;

    Integer THIRTY = 30;

    Integer FIFTY = 50;

    Integer ONE_HUNDRED = 100;

    Integer TWO_HUNDREDS = 200;

    Integer FIVE_HUNDREDS = 500;

    Integer ONE_ZERO_TWO_FOUR = 1024;

    Integer ONE_FIVE_ZERO_ZERO = 1500;

    Integer TWO_ZERO_FOUR_EIGHT = 2048;

    Integer FIFTEEN_THOUSAND = 15000;

    String ZERO_STR = "0";

    String ONE_STR = "1";

    Long ZERO_LONG = 0L;

    Long ONE_L0NG = 1L;

    Long TWENTY_LONG = 20L;

    String VERTICAL_LINE = "|";

    String SLASH = "/";

    String HTTP_PREFIX = "http";

    String REDISSON_LOCK_KEY = "lock_key:";

    String CUSTOMER_REQUEST_HEADER = "app-id";

    String TENANT_CODE_HEADER = "tenant_code";
    
    String UTF8 = "UTF-8";

    /**
     * 管理端 authorization
     */
    String TOKEN_HEADER_NAME = "X-Authorization";

    /**
     * app端 authorization
     */
    String APP_TOKEN_HEADER_NAME = "X-App-Authorization";

    /**
     * customer端 authorization
     */
    String CUSTOMER_TOKEN_HEADER_NAME = "X-Cust-Authorization";

    String WXWORK_API_SUCCESS_CODE = "0";

    String TIME_RULE_SPLITTER = "-";
    String HMS_RULE_SPLITTER = ":";

    String RESP_CODE_OK = "ok";

    /**
     * 会话存档es index名称前缀
     */
    String CONVERSATION_ES_INDEX_NAME_PREFIX = "conversation_index_";

    /**
     * 通讯录相关回调
     */
    String EVENT_EMPLOYEE = "change_contact";

    /**
     * 客户相关回调
     */
    String EVENT_CONTACT = "change_external_contact";

    /**
     * 客户群相关回调
     */
    String EVENT_CHAT = "change_external_chat";

    /**
     * 客户标签相关回调
     */
    String EVENT_CONTACT_TAG = "change_external_tag";

    Long SYS_USER_ID = 0L;

    String INDEX_CODE = "H30269.csi";

    /**
     * 默认密码
     */
    String ADM_USER_DEFAULT_PWD = "123456";

    String SM4_INIT = "dinglitec@666666";

    String GO_RIGHT_NOW = "立即前往";

    String NULL = "null";

    /**
     * 净值、收益率等5位有效数字
     */
    MathContext MATH_CONTEXT = new MathContext(5, RoundingMode.HALF_UP);

    /**
     * 新华智云上传文件接口文件分片最大个数
     */
    int XH_SPLITFILE_MAX_PART_SIZE = 100;

    /**
     * 新华智云上传文件接口文件分片单个文件最大容量
     */
    long XH_SPLITFILE_PERSIZE = 20 * 1000 * 1000;

    String COS_PATH_CONTENT_VIDEO = "visual/mavideo";

    String COS_PATH_CONTENT_IMAGE = "visual/maimage";

    String COS_PATH_CONTENT_FILE = "visual/file";

    String COS_PATH_RESOURCE_IMAGE = "resource/images";
    String HOTWORDS_ES_INDEX = "dl-hotwords";

    /**
     * 系统编码
     */
    String SYSTEM_CODE = "X-System-code";

    /**
     * magicvideo的系统编码
     */
    String DL_MAGIC_VIDEO = "dl-magicvideo";

    String BIND_DATA_HEADER = "userDefine.";

    /**
     * 预览数据的缓存key
     */
    String JOB_PREVIEW_KEY_PREFIX = "dl.job.preview.key.";

    /**
     * 预览数据的缓存时间 单位s
     */
    Long JOB_PREVIEW_CACHE_SECONDS = 24 * 60 * 60L;

    String VISUAL_SSE_QUEUE_KEY_PREFIX = "dl-visual-sse-queue-";

    String VISUAL_SSE_QUEUE_KEY_LIST = "dl-visual-sse-key-list";

}
