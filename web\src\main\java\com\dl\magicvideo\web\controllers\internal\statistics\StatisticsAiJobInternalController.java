package com.dl.magicvideo.web.controllers.internal.statistics;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsAiJobInternalDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsAiJobTenantInternalDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsAiJobTenantSummaryInternalDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.param.InternalStatisticsAiJobTopTenantQueryParam;
import com.dl.magicvideo.web.controllers.internal.statistics.param.StatisticsAiJobInternalPageParam;
import com.dl.magicvideo.web.controllers.internal.statistics.param.StatisticsAiJobTenantSummaryInternalQueryParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:28
 */
@RestController
@RequestMapping("/visual/internal/statistics/aijob")
public class StatisticsAiJobInternalController {

    @Resource
    private StatisticsAiJobInternalProcess statisticsAiJobInternalProcess;

    @PostMapping("/page")
    public ResultPageModel<StatisticsAiJobInternalDTO> page(@RequestBody StatisticsAiJobInternalPageParam pageParam) {
        return statisticsAiJobInternalProcess.page(pageParam);
    }

    @PostMapping("/querytoptenantstatistics")
    @ApiOperation("查询前几名租户的ai任务数据统计")
    public ResultModel<List<StatisticsAiJobTenantInternalDTO>> queryTopTenantStatistics(
            @RequestBody InternalStatisticsAiJobTopTenantQueryParam param) {
        return statisticsAiJobInternalProcess.queryTopTenantStatistics(param);
    }

    @PostMapping("/specifictenantsummary")
    @ApiOperation("指定租户的ai任务数据统计汇总")
    public ResultModel<StatisticsAiJobTenantSummaryInternalDTO> specificTenantSummary(
            @RequestBody @Validated StatisticsAiJobTenantSummaryInternalQueryParam param) {
        return statisticsAiJobInternalProcess.specificTenantSummary(param);
    }

}
