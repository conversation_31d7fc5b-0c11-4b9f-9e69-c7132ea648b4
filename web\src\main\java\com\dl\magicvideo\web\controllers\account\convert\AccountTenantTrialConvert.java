package com.dl.magicvideo.web.controllers.account.convert;

import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.web.controllers.account.vo.AccountTenantTrialVO;

import java.util.Objects;

public class AccountTenantTrialConvert {
    public static AccountTenantTrialVO cnvAccountTenantTrialPO2VO(AccountTenantTrialPO input){
        if (Objects.isNull(input)){
            return null;
        }
        AccountTenantTrialVO result = new AccountTenantTrialVO();
        result.setTenantCode(input.getTenantCode());
        result.setWithhold(input.getWithhold().intValue());
        result.setBalance(input.getBalance().intValue());
        result.setIsDeleted(input.getIsDeleted());
        return result;
    }
}
