package com.dl.magicvideo.biz.manager.visual.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DxDailyFinancialReviewVO {
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String coverDate;

    @ApiModelProperty("开始姓名")
    private String beginName;
    @ApiModelProperty("开始证券名称")
    private String beginSecurities;
    @ApiModelProperty("开始职业编号")
    private String beginNumber;
    @ApiModelProperty("开始tts")
    private String beginTts;

    @ApiModelProperty("结束姓名")
    private String endName;
    @ApiModelProperty("结束证券名称")
    private String endSecurities;
    @ApiModelProperty("结束职业编号")
    private String endNumber;
    @ApiModelProperty("结束内容")
    private String endText;
    @ApiModelProperty("结束tts")
    private String endTts;


    /**
     * 上证指数k线图
     */
    @ApiModelProperty("上证指数k线图")
    private ValueFormVO<Object> kLineShCompIdx;
    /**
     * 深证成指k线图
     */
    @ApiModelProperty("深证成指k线图")
    private ValueFormVO<Object> kLineSzIdx;
    /**
     * k线图口播
     */
    @ApiModelProperty("k线图口播")
    private String kLineTts;

    /**
     * 上证指数分时图
     */
    @ApiModelProperty("上证指数分时图")
    private ValueFormVO<String> lineChartShComp;

    /**
     * 深证成指分时图
     */
    @ApiModelProperty("深证成指分时图")
    private ValueFormVO<String> lineChartSzIdx;

    /**
     * 上证指数收盘价
     */
    @ApiModelProperty("上证指数收盘价")
    private ValueFormVO<String> lineCloseShComp;

    /**
     * 深证成指收盘价
     */
    @ApiModelProperty("深证成指收盘价")
    private ValueFormVO<String> lineCloseSzIdx;

    /**
     * 上证指数分时图口播
     */
    @ApiModelProperty("上证指数分时图口播")
    private String lineCloseTts;

    /**
     * 个股涨跌情况 市场情绪 - 市场条形
     */
    @ApiModelProperty("个股涨跌情况 市场情绪 - 市场条形")
    private ValueFormVO<Object> gainsLossesChart;

    /**
     * 个股涨跌情况 市场情绪 - 市场条形口播
     */
    @ApiModelProperty("个股涨跌情况 市场情绪 - 市场条形口播")
    private String gainsLossesTts;

    /**
     * 指数表格1 沪深300
     */
    @ApiModelProperty("指数表格1 沪深300")
    private ValueFormVO<Object> coreIdxCSI300;

    /**
     * 指数表格2 深证成指
     */
    @ApiModelProperty("指数表格2 深证成指")
    private ValueFormVO<Object> coreIdxSZIDX;

    /**
     * 指数表格3 创业板指
     */
    @ApiModelProperty("指数表格3 创业板指")
    private ValueFormVO<Object> coreIdxChiNext;

    /**
     * 指数表格4 上证50
     */
    @ApiModelProperty("指数表格4 上证50")
    private ValueFormVO<Object> coreIdxSSE50;

    /**
     * 指数表格5 科创50
     */
    @ApiModelProperty("指数表格5 科创50")
    private ValueFormVO<Object> coreIdxSTAR50;

    /**
     * 指数表格6 北证50
     */
    @ApiModelProperty("指数表格6 北证50")
    private ValueFormVO<Object> coreIdxBSE50;

    /**
     * 口播：
     */
    @ApiModelProperty("口播：")
    private String coreIdxTts;

    /**
     * 指数表格1 纳斯达克综合指数
     */
    @ApiModelProperty("指数表格1 纳斯达克综合指数")
    private ValueFormVO<Object> foreignIdxIXICUS;

    /**
     * 指数表格2 道琼斯工业平均指数
     */
    @ApiModelProperty("指数表格2 道琼斯工业平均指数")
    private ValueFormVO<Object> foreignIdxDJIUS;

    /**
     * 指数表格3 恒生指数
     */
    @ApiModelProperty("指数表格3 恒生指数")
    private ValueFormVO<Object> foreignIdxHSIHK;

    /**
     * 口播：
     */
    @ApiModelProperty("口播：")
    private String foreignIdxTts;

    /**
     * 全部A股成交额
     */
    @ApiModelProperty("全部A股成交额")
    private ValueFormVO<String> tradingValueForm;

    /**
     * 全市场成交（亿元）
     */
    @ApiModelProperty("全市场成交（亿元）")
    private String tradingValueAllMarket;

    /**
     * 较上个交易日（亿元）变化
     */
    @ApiModelProperty("较上个交易日（亿元）变化")
    private String tradingValueTradingDay;

    /**
     * 成交额口播文案
     */
    @ApiModelProperty("成交额口播文案")
    private String tradingValueTts;

    /**
     * 上证+深证
     * 大盘主力
     */
    @ApiModelProperty("大盘主力")
    private ValueFormVO<String> netCapitalInflowMarketForm;

    /**
     * 大盘主力 资金流向
     */
    @ApiModelProperty("大盘主力 资金流向")
    private String netCapitalInflowMarket;

    /**
     * 上证指数（亿元） 资金流向
     */
    @ApiModelProperty("上证指数（亿元） 资金流向")
    private String netCapitalInflowShComp;

    /**
     * 深证成指（亿元） 资金流向
     */
    @ApiModelProperty("深证成指（亿元） 资金流向")
    private String netCapitalInflowSzIdx;

    /**
     * 创业板指（亿元） 资金流向
     */
    @ApiModelProperty("创业板指（亿元） 资金流向")
    private String netCapitalInflowChiNext;
    /**
     * 大盘主力资金流向口播
     */
    @ApiModelProperty("大盘主力资金流向口播")
    private String netCapitalInflowMarketTts;


    /**
     * 行业资金流向涨跌排名
     */
    @ApiModelProperty("行业资金流向涨跌排名")
    private ValueFormVO<String> netCapitalInflowIndustryForm;

    /**
     * 行业资金流向涨跌排名口播
     */
    @ApiModelProperty("行业资金流向涨跌排名口播")
    private String netCapitalInflowIndustryTts;

    /**
     * 北向资金
     */
    @ApiModelProperty("北向资金")
    private ValueFormVO<String> northboundCapitalForm;

    /**
     * 北向资金流入流出情况
     */
    @ApiModelProperty("北向资金流入流出情况")
    private String northboundCapital;

    /**
     * 北向资金流入流出情况
     */
    @ApiModelProperty("北向资金流入流出情况")
    private String northboundCapitalTts;

    /**
     * 领涨行业是否展示
     */
    @ApiModelProperty("领涨行业是否展示")
    private boolean industryLeadingFlag;
    /**
     * 领涨行业
     */
    @ApiModelProperty("领涨行业")
    private ValueFormVO<Object> industryLeadingForm;

    /**
     * 领涨行业口播
     */
    @ApiModelProperty("领涨行业口播")
    private String industryLeadingTts;

    /**
     * 领跌行业是否展示
     */
    @ApiModelProperty("领跌行业是否展示")
    private boolean industryLaggingFlag;
    /**
     * 领跌行业
     */
    @ApiModelProperty("领跌行业")
    private ValueFormVO<Object> industryLaggingForm;

    /**
     * 领跌行业口播
     */
    @ApiModelProperty("领跌行业口播")
    private String industryLaggingTts;

    /**
     * 领涨主题是否展示
     */
    @ApiModelProperty("领涨主题是否展示")
    private boolean themesLeadingFlag;
    /**
     * 领涨主题
     */
    @ApiModelProperty("领涨主题")
    private ValueFormVO<Object> themesLeadingForm;

    /**
     * 领涨主题口播
     */
    @ApiModelProperty("领涨主题口播")
    private String themesLeadingTts;

    /**
     * 领跌主题是否展示
     */
    @ApiModelProperty("领跌主题是否展示")
    private boolean themesLaggingFlag;
    /**
     * 领跌主题
     */
    @ApiModelProperty("领跌主题")
    private ValueFormVO<Object> themesLaggingForm;

    /**
     * 领跌主题口播
     */
    @ApiModelProperty("领跌主题口播")
    private String themesLaggingTts;

    /**
     * ETF表现-规模指数排名
     */
    @ApiModelProperty("ETF表现-规模指数排名")
    private ValueFormVO<Object> capitalizationIndexETFForm;

    /**
     * ETF表现-规模指数排名口播
     */
    @ApiModelProperty("ETF表现-规模指数排名口播")
    private String capitalizationIndexETFTts;

    /**
     * ETF表现-主题指数排名
     */
    @ApiModelProperty("ETF表现-主题指数排名")
    private ValueFormVO<Object> themeIndexETFForm;

    /**
     * ETF表现-主题指数口播
     */
    @ApiModelProperty("ETF表现-主题指数口播")
    private String themeIndexETFTts;
}
