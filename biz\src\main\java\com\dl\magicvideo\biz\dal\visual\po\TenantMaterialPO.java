package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName tenant_material
 */
@TableName(value ="tenant_material")
@Data
public class TenantMaterialPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 素材id
     */
    private Long bizId;

    /**
     * 文件夹ID
     */
    private Long folderId;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图
     */
    private String logoImg;

    /**
     * 素材类型：3-视频，6-图片，7-音频
     */
    private Integer materialType;

    /**
     * 对应的url
     */
    private String url;

    /**
     * 对应的webmUrl
     */
    private String webmUrl;

    /**
     * 素材大小
     */
    private Long size;

    /**
     * 分辨率,1920*1680
     */
    private String resolutionRatio;

    /**
     * 时长，单位秒
     */
    private Long duration;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    private String creatorName;

    private String modifyName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}