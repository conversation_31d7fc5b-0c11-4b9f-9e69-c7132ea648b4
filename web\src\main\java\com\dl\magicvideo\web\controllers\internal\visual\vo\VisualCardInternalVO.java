package com.dl.magicvideo.web.controllers.internal.visual.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VisualCardInternalVO {
    /**
     * 卡片id
     */
    private Long cardId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 卡片标题
     */
    private String name;

    /**
     * 卡片封面
     */
    private String coverUrl;

    /**
     * 尺寸
     */
    private String resolution;

    /**
     * 时长，单位秒
     */
    private Long duration;

    /**
     * 0-启用 1-禁用
     */
    private Integer status;

    /**
     *
     */
    private String renderData;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 轻编辑配置
     */
    private String lightEditConfigs;
}
