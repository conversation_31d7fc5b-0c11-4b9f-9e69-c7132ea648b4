package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.SysTenantClient;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.client.basicservice.param.TenantInfoParam;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceFailMsgDTO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Objects;

@Data
@Slf4j
@Component
public class ReduceWithholdConsumer {

    @Autowired
    private TenantInfoManager tenantInfoManager;

    @Autowired
    private AccountTenantTrialManager accountTenantTrialManager;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    private static final String REDUCE_WITHHOLD_KEY = "reduce_withhold_key:";

    /**
     * 1天
     */
    private static final Long LOCK_TIME = 24 * 60 * 60L;

    private String genKey(Long jobId) {
        return REDUCE_WITHHOLD_KEY + jobId;
    }

    @StreamListener("reducewithholdconsumer")
    public void consume(@Payload VisualProduceFailMsgDTO input) {
        try {
            log.info("收到视频合成失败消息，进行预扣额度返还 input = {}", JSONUtil.toJsonStr(input));
            Assert.isTrue(StringUtils.isNotBlank(input.getTenantCode()), "租户号不能为空");
            String lockKey = genKey(input.getJobId());
            if (!redisUtil.tryLockAndSetTimeout(lockKey, LOCK_TIME)) {
                log.warn("已处理过该作品的预扣额度返回！jobId:{}", input.getJobId());
                return;
            }

            //判断作品类型
            VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                    .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                            .eq(VisualProduceJobExtendPO::getProduceJobId, input.getJobId()));
            //若是数据图表作品，则不涉及试用账户余额
            if (Objects.nonNull(jobExtendPO) && JobTypeEnum.DATA_CHART.getType().equals(jobExtendPO.getType())) {
                return;
            }

            AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfoFromCache(input.getTenantCode());
            if (!Const.ONE.equals(tenantInfo.getIsTrial())) {
                return;
            }
            AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager
                    .getOne(Wrappers.lambdaQuery(AccountTenantTrialPO.class)
                            .eq(AccountTenantTrialPO::getTenantCode, input.getTenantCode())
                            .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
            if (Objects.isNull(tenantTrialAccount)) {
                log.error("未找到试用账户,input:{}", JSONUtil.toJsonStr(input));
                return;
            }

            tenantTrialAccount.setWithhold(tenantTrialAccount.getWithhold() - 1);
            accountTenantTrialManager.updateById(tenantTrialAccount);
            log.info("视频合成失败。返还试用账户预扣成功 jobId:{}", input.getJobId());
        }catch (Exception e){
            log.error("返还预扣账户失败 jobId:{},,,e:{}", input.getJobId(), e);
        }

    }

}
