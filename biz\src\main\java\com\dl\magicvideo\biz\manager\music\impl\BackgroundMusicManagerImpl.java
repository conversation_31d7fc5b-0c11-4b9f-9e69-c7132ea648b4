package com.dl.magicvideo.biz.manager.music.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.music.BackgroundMusicMapper;
import com.dl.magicvideo.biz.dal.music.po.BackgroundMusicPO;
import com.dl.magicvideo.biz.manager.music.BackgroundMusicManager;
import com.dl.magicvideo.biz.manager.music.convert.BackgroundMusicConvert;
import com.dl.magicvideo.biz.manager.music.dto.BackgroundMusicDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class BackgroundMusicManagerImpl extends ServiceImpl<BackgroundMusicMapper, BackgroundMusicPO> implements
        BackgroundMusicManager {

    @Override
    public List<BackgroundMusicDTO> backgroundMusicList(Integer type) {
        List<BackgroundMusicPO> backgroundMusicList = this.list(
                Wrappers.lambdaQuery(BackgroundMusicPO.class).eq(Objects.nonNull(type),BackgroundMusicPO::getType,type).eq(BackgroundMusicPO::getIsDeleted, Const.ZERO).orderByDesc(BackgroundMusicPO::getCreateDt));
        return backgroundMusicList.stream().map(BackgroundMusicConvert::cnvBackGroundMusicPO2DTO).collect(Collectors.toList());
    }
}
