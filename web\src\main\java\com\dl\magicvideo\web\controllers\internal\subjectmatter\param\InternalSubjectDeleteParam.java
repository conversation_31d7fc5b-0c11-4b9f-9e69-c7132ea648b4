package com.dl.magicvideo.web.controllers.internal.subjectmatter.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class InternalSubjectDeleteParam {

    /**
     * 题材根id
     */
    @NotNull(message = "题材id不能为空")
    private Long bizId;

    /**
     * 操作人名称
     */
    @NotBlank(message = "操作人名称不能为空")
    private String operatorName;

    /**
     * 操作人id
     */
    @NotNull(message = "操作人id不能为空")
    private Long operatorId;
}
