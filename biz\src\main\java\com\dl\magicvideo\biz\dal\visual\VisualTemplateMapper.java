package com.dl.magicvideo.biz.dal.visual;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.param.AuthedTemplatePageQueryParam;
import com.dl.magicvideo.biz.dal.visual.param.WorkingTemplatePageQueryParam;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSearchBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【visual_template】的数据库操作Mapper
* @createDate 2023-04-24 10:22:23
* @Entity com.dl.magicvideo.biz.dal.visual.po.VisualTemplate
*/
@BaseDao
public interface VisualTemplateMapper extends BaseMapper<VisualTemplatePO> {

    List<VisualTemplatePO> listAuthedTemplates(@Param("param") AuthedTemplatePageQueryParam param);

    Integer countAuthedTemplates(@Param("param") AuthedTemplatePageQueryParam param);

    Integer getTemplateNameMaxIndex(@Param("sourceTemplateName") String sourceTemplateName);

    /**
     * 获取工作台上的模板
     * @param param
     * @return
     */
    List<VisualTemplatePO> listWorkingTemplates(@Param("param") WorkingTemplatePageQueryParam param);

    Integer countWorkingTemplates(@Param("param") WorkingTemplatePageQueryParam param);
}




