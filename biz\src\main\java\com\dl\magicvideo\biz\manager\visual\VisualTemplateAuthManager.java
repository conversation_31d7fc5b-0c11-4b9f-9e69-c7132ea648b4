package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplateAuthPO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthTenantCodeSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【visual_template_auth】的数据库操作Service
* @createDate 2023-06-18 10:34:13
*/
public interface VisualTemplateAuthManager extends IService<VisualTemplateAuthPO>, CommonService {

    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/6/18 22:23
     */
    void auth(TemplateAuthBO bo);

    /**
     * 功能描述: <br> 分页查询
     *
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List < com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO>>
     * @Author: zhousx
     * @Date: 2023/6/18 23:48
     */
    ResponsePageQueryDO<List<TemplateAuthDTO>> pageQuery(TemplateAuthSearchBO bo);

    /**
     * 功能描述: 根据授权租户编号 分页查询系统模板及其授权租户列表
     */
    ResponsePageQueryDO<List<TemplateAuthDTO>> pageQueryByAuthTenantCode(TemplateAuthTenantCodeSearchBO bo);
}
