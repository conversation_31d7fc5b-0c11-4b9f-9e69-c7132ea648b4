package com.dl.magicvideo.web.controllers.produce.convert;

import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.web.controllers.produce.vo.ProduceJobVO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-21 15:55
 */
public class VisualProduceConvert {

    public static ProduceJobVO fillProduceJobVO(VisualProduceJobPO job, VisualTemplatePO templatePO, ProduceJobVO vo) {
        vo.setJobId(job.getJobId() + "");
        vo.setName(job.getName());
        vo.setStatus(job.getStatus());
        vo.setVideoUrl(job.getVideoUrl());
        vo.setCoverUrl(job.getCoverUrl());
        vo.setSource(job.getSource());
        vo.setCreateDt(job.getCreateDt());
        vo.setProcessDt(job.getProcessDt());
        vo.setCompleteDt(job.getCompleteDt());
        vo.setReplaceData(job.getReplaceData());
        vo.setDuration(job.getDuration());
        vo.setCreatorName(job.getCreatorName());
        vo.setSize(job.getSize());
        vo.setResolution(templatePO.getResolution());
        vo.setResolutionType(templatePO.getResolutionType());
        vo.setTemplateName(templatePO.getName());
        vo.setTemplateId(job.getTemplateId() + "");
        return vo;
    }
}
