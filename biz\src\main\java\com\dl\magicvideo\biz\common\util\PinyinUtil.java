package com.dl.magicvideo.biz.common.util;

import com.dl.framework.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class PinyinUtil {

    /**
     * 音调放在拼音后
     * @param text
     * @return
     */
    public static List<String> polyphonicWithToneNumber(String text) {
        Assert.isTrue(text.toCharArray().length < 2, "请选择一个汉字");
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setToneType(HanyuPinyinToneType.WITH_TONE_NUMBER);
        char[] chars = text.toCharArray();
        char c = chars[0];

        List<String> strings = new ArrayList<>();

        try {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);

            if (pinyinArray == null || pinyinArray.length == 0) {
                // 字符不是汉字，直接追加到结果中
                strings.add(String.valueOf(c));
            } else {
                // 获取最后一个拼音，将语调放在最后
                strings.addAll(Arrays.asList(pinyinArray));
            }
        } catch (Exception e) {
            log.error("获取拼音异常", e);
        }
        return strings;
    }

    /**
     * 音调放在拼音上
     * @param text
     * @return
     */
    public static List<String> polyphonicWithToneMark(String text) {
        Assert.isTrue(text.toCharArray().length < 2, "请选择一个汉字");
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setToneType(HanyuPinyinToneType.WITH_TONE_MARK);
        format.setVCharType(HanyuPinyinVCharType.WITH_U_UNICODE);
        char[] chars = text.toCharArray();
        char c = chars[0];

        List<String> strings = new ArrayList<>();

        try {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);

            if (pinyinArray == null || pinyinArray.length == 0) {
                // 字符不是汉字，直接追加到结果中
                strings.add(String.valueOf(c));
            } else {
                // 获取最后一个拼音，将语调放在最后
                strings.addAll(Arrays.asList(pinyinArray));
            }
        } catch (Exception e) {
            log.error("获取拼音异常", e);
        }
        return strings;
    }

    public static void main(String[] args) {
        String text = "行";
        List<String> polyphonic = polyphonicWithToneMark(text);
        System.out.println(JsonUtils.toJSON(polyphonic));
    }
}
