package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.constant.OpenApiConstant;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.util.CeilingUtils;
import com.dl.magicvideo.biz.common.util.OpenApiSignUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobCallbackManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobCallbackDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobCallbackResultDTO;
import com.dl.magicvideo.biz.manager.visual.enums.TenantProduceTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 10:20
 */
@Component
public class VisualProduceJobCallbackManagerImpl implements VisualProduceJobCallbackManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(VisualProduceJobCallbackManagerImpl.class);

    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    @Resource
    private TenantInfoManager tenantInfoManager;

    @Resource
    private WebClient webClient;

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    @Resource
    private VisualAiJobManager visualAiJobManager;

    @Override
    public void callbackThird(Long jobId, String tenantCode, VisualProduceJobPO jobPO) {
        //1.查询租户信息
        AdmTenantInfoDTO tenantInfoDTO = tenantInfoManager.getTenantInfoFromCache(tenantCode);
        //2.判断接入数影的方式。只有含API模式的才会进行回调
        if (!TenantProduceTypeEnum.PURE_API.getType().equals(tenantInfoDTO.getProduceType())
                && !TenantProduceTypeEnum.PAGE_AND_API.getType().equals(tenantInfoDTO.getProduceType())) {
            return;
        }
        if (StringUtils.isBlank(tenantInfoDTO.getVideoCallbackUrl())) {
            LOGGER.error("配置错误，该租户未配置视频回调地址!,jobId:{},,,tenantInfoDTO:{}", jobId, JSONUtil.toJsonStr(tenantInfoDTO));
            return;
        }

        //3.查询作品信息
        if (Objects.isNull(jobPO)) {
            jobPO = visualProduceJobManager
                    .getOne(Wrappers.lambdaQuery(VisualProduceJobPO.class).eq(VisualProduceJobPO::getJobId, jobId)
                            .select(VisualProduceJobPO.class,
                                    po -> !"replace_data".equals(po.getColumn()) && !"preview_data"
                                            .equals(po.getColumn()) && !"template_data".equals(po.getColumn())
                                            && !"api_data".equals(po.getColumn())));
        }

        //4.查询作品扩展信息
        VisualProduceJobExtendPO extendPO = visualProduceJobExtendManager
                .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                        .eq(VisualProduceJobExtendPO::getProduceJobId, jobId)
                        .eq(VisualProduceJobExtendPO::getIsDeleted, Const.ZERO));

        //5.查询数字人任务
        List<VisualAiJobPO> dmJobList = visualAiJobManager
                .list(Wrappers.lambdaQuery(VisualAiJobPO.class).eq(VisualAiJobPO::getProduceJobId, jobId)
                        .eq(VisualAiJobPO::getJobType, AiJobTypeE.DIGITAL_MAN.getCode()));

        //外部用户id
        String extUserId = Objects.nonNull(extendPO) ? extendPO.getExtUserId() : null;

        //5.构建回调参数和签名
        VisualProduceJobCallbackDTO callbackDTO = this.buildCallbackDTO(jobPO, extUserId, dmJobList);

        String accessKey = tenantCode;
        String secretKey = tenantInfoDTO.getId().toString();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String param = JSON.toJSONString(callbackDTO, SerializerFeature.MapSortField);
        String sign = OpenApiSignUtil.getSignature(accessKey, secretKey, timestamp, param);
        LOGGER.info("回调第三方，请求地址:{},,,,accessKey:{},,,,timestamp:{},,,,sign:{},,,,callbackDTO:{}",
                tenantInfoDTO.getVideoCallbackUrl(), accessKey, timestamp, sign, param);

        //6.使用webClient异步调用
        webClient.post().uri(tenantInfoDTO.getVideoCallbackUrl()).contentType(MediaType.APPLICATION_JSON)
                .header(OpenApiConstant.ACCESS_KEY, accessKey).header(OpenApiConstant.TIMESTAMP, timestamp)
                .header(OpenApiConstant.SIGNATURE, sign).bodyValue(callbackDTO).retrieve().toBodilessEntity().retry(3)
                .timeout(Duration.ofSeconds(10)).doOnError(error -> {
            LOGGER.error("视频回调第三方发生异常! jobId:{} ,error.message:{},,,error.stacktrace:{}", jobId, error.getMessage(),
                    error.getStackTrace());
            visualProduceJobManager
                    .update(Wrappers.lambdaUpdate(VisualProduceJobPO.class).eq(VisualProduceJobPO::getJobId, jobId)
                            .set(VisualProduceJobPO::getPushStatus, Const.TWO));
        }).subscribe(response -> {
            HttpStatus httpStatus = response.getStatusCode();
            if (httpStatus.is2xxSuccessful()) {
                LOGGER.info("视频回调第三方成功，jobId:{}", jobId);
                visualProduceJobManager
                        .update(Wrappers.lambdaUpdate(VisualProduceJobPO.class).eq(VisualProduceJobPO::getJobId, jobId)
                                .set(VisualProduceJobPO::getPushStatus, Const.ONE));
            } else {
                LOGGER.error("视频回调第三方失败，jobId:{},HTTP状态码:{}", jobId, httpStatus);
                visualProduceJobManager
                        .update(Wrappers.lambdaUpdate(VisualProduceJobPO.class).eq(VisualProduceJobPO::getJobId, jobId)
                                .set(VisualProduceJobPO::getPushStatus, Const.TWO));
            }
        });
    }

    private VisualProduceJobCallbackDTO buildCallbackDTO(VisualProduceJobPO jobPO, String extUserId,
            List<VisualAiJobPO> dmJobList) {
        VisualProduceJobCallbackDTO callbackDTO = new VisualProduceJobCallbackDTO();
        callbackDTO.setJobId(String.valueOf(jobPO.getJobId()));
        callbackDTO.setName(jobPO.getName());
        callbackDTO.setJobStatus(jobPO.getStatus());
        callbackDTO.setSynthStarted(Objects.nonNull(jobPO.getProcessDt()) ? jobPO.getProcessDt().getTime() : null);
        callbackDTO.setSynthUpdated(Objects.nonNull(jobPO.getCompleteDt()) ? jobPO.getCompleteDt().getTime() : null);
        callbackDTO.setExtUserId(extUserId);

        VisualProduceJobCallbackResultDTO jobResult = new VisualProduceJobCallbackResultDTO();
        jobResult.setDuration(jobPO.getDuration());
        jobResult.setCoverUrl(jobPO.getCoverUrl());
        jobResult.setVideoUrl(jobPO.getVideoUrl());

        if (CollectionUtils.isNotEmpty(dmJobList)) {
            Long dmTotalCeilingMinutes = 0L;
            for (VisualAiJobPO dmJobPO : dmJobList) {
                dmTotalCeilingMinutes += CeilingUtils.millsToMinutes(dmJobPO.getDuration());
            }
            jobResult.setDmTotalCeilingMinutes(dmTotalCeilingMinutes);
        }

        callbackDTO.setJobResult(jobResult);
        return callbackDTO;
    }

}
