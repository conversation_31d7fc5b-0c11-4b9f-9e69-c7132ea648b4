package com.dl.magicvideo.openapi.controller.produce.convert;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.CeilingUtils;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.util.ProduceFailUtil;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateLightEditConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceJobDetailVO;
import com.dl.magicvideo.openapi.controller.produce.vo.OpenVisualProduceJobInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-09 17:14
 */
@Slf4j
public class OpenVisualProduceJobConvert {

    public static OpenVisualProduceJobInfoVO buildOpenVisualProduceJobInfoVO(VisualProduceJobPO job,
            VisualTemplatePO templatePO, VisualProduceJobExtendPO jobExtendPO) {
        OpenVisualProduceJobInfoVO vo = new OpenVisualProduceJobInfoVO();
        fillOpenVisualProduceJobInfoVO(job, templatePO, jobExtendPO, vo);
        return vo;
    }

    public static OpenVisualProduceJobDetailVO buildOpenVisualProduceJobDetailVO(VisualProduceJobPO job,
            VisualTemplatePO templatePO, VisualProduceJobExtendPO jobExtendPO, List<VisualAiJobPO> dmJobList,
            DigitalManJobBO digitalManJobBO, Map<String, DaVirtualManScenesDTO> dmSceneMap) {
        OpenVisualProduceJobDetailVO vo = new OpenVisualProduceJobDetailVO();
        fillOpenVisualProduceJobInfoVO(job, templatePO, jobExtendPO, vo);
        //包装错误原因
        vo.setFailReason(ProduceFailUtil.wrapperFailReason(jobExtendPO));

        if (CollectionUtils.isNotEmpty(dmJobList)) {
            Long dmTotalCeilingMinutes = 0L;
            Long dmTotalDuration = 0L;
            for (VisualAiJobPO dmJobPO : dmJobList) {
                Long dmDuration = Objects.nonNull(dmJobPO.getDuration()) ? dmJobPO.getDuration() : Const.ZERO_LONG;
                dmTotalCeilingMinutes += CeilingUtils.millsToMinutes(dmDuration);
                dmTotalDuration += dmDuration;
            }
            vo.setDmTotalCeilingMinutes(dmTotalCeilingMinutes);
            vo.setDmTotalDuration(dmTotalDuration);

            if (Objects.nonNull(digitalManJobBO)) {
                vo.setChannelName(ServiceChannelEnum.getNameByCode(digitalManJobBO.getChannel()));
                vo.setSceneId(digitalManJobBO.getSceneId());
                DaVirtualManScenesDTO scenesDTO = dmSceneMap
                        .get(digitalManJobBO.getChannel() + "-" + digitalManJobBO.getSceneId());
                if (Objects.nonNull(scenesDTO)) {
                    vo.setDmName(scenesDTO.getVmName());
                    vo.setGender(scenesDTO.getGender());
                    vo.setSceneName(scenesDTO.getSceneName());
                } else {
                    log.warn("该数字人任务查找不到数字人场景信息,aiJobId:{},channel:{},sceneId:{}", job.getJobId(),
                            digitalManJobBO.getChannel(), digitalManJobBO.getSceneId());
                }
            }
        }

        VisualTemplateDTO visualTemplateDTO = JSONUtil
                .toBean(job.getTemplateData(), VisualTemplateDTO.class);
        if (CollectionUtils.isNotEmpty(visualTemplateDTO.getCards())) {
            visualTemplateDTO.getCards().forEach(visualCardDTO -> {
                if (StringUtils.isNotEmpty(visualCardDTO.getLightEditConfigs())) {
                    TemplateLightEditConfigDTO configDTO = JSONUtil
                            .toBean(visualCardDTO.getLightEditConfigs(), TemplateLightEditConfigDTO.class);
                    vo.setLightEditConfigs(configDTO.getLightEditConfigs());
                    vo.setCrossClips(configDTO.getCrossClips());
                }
            });
        }
        return vo;
    }

    public static void fillOpenVisualProduceJobInfoVO(VisualProduceJobPO job, VisualTemplatePO templatePO,
            VisualProduceJobExtendPO jobExtendPO, OpenVisualProduceJobInfoVO vo) {
        vo.setJobId(job.getJobId() + "");
        vo.setName(job.getName());
        vo.setStatus(job.getStatus());
        vo.setTemplateId(job.getTemplateId() + "");
        vo.setVideoUrl(job.getVideoUrl());
        vo.setCoverUrl(job.getCoverUrl());
        vo.setCreateDt(job.getCreateDt());
        vo.setProcessDt(job.getProcessDt());
        vo.setCompleteDt(job.getCompleteDt());
        vo.setDuration(job.getDuration());
        vo.setCreatorName(job.getCreatorName());
        vo.setSize(job.getSize());
        vo.setResolution(templatePO.getResolution());
        vo.setResolutionType(templatePO.getResolutionType());
        vo.setExtUserId(Objects.nonNull(jobExtendPO) ? jobExtendPO.getExtUserId() : null);
    }

    public static OpenVisualProduceJobInfoVO buildOpenVisualProduceJobInfoVO(VisualProduceJobDTO job,
            VisualProduceJobExtendPO jobExtendPO) {
        OpenVisualProduceJobInfoVO vo = new OpenVisualProduceJobInfoVO();
        vo.setJobId(job.getJobId() + "");
        vo.setName(job.getName());
        vo.setTemplateId(job.getTemplateId() + "");
        vo.setStatus(job.getStatus());
        vo.setVideoUrl(job.getVideoUrl());
        vo.setCoverUrl(job.getCoverUrl());
        vo.setCreateDt(job.getCreateDt());
        vo.setProcessDt(job.getProcessDt());
        vo.setCompleteDt(job.getCompleteDt());
        vo.setCreatorName(job.getCreatorName());
        vo.setDuration(job.getDuration());
        vo.setSize(job.getSize());
        vo.setResolution(job.getResolution());
        vo.setResolutionType(job.getResolutionType());
        vo.setExtUserId(Objects.nonNull(jobExtendPO) ? jobExtendPO.getExtUserId() : null);
        return vo;
    }

}
