package com.dl.magicvideo.web.controllers.internal.visual.vo;

import lombok.Data;

@Data
public class VisualDynamicNodeInternalVO {

    /**
     * 节点id
     */
    private Long nodeId;

    /**
     * 卡片id
     */
    private Long cardId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 时长
     */
    private Long duration;

    /**
     * 片段封面
     */
    private String coverUrl;

    /**
     * 是否启用 0否 1是
     */
    private Integer isEnabled;
}
