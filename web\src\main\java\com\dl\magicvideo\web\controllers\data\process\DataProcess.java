package com.dl.magicvideo.web.controllers.data.process;

import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.visual.po.DataInterfacePO;
import com.dl.magicvideo.biz.manager.visual.DataInterfaceManager;
import com.dl.magicvideo.biz.manager.visual.vo.FundMarketDetailVO;
import com.dl.magicvideo.biz.manager.visual.vo.ManagerDetailVO;
import com.dl.magicvideo.web.controllers.data.param.DataListParam;
import com.dl.magicvideo.web.controllers.data.param.FundMarketDetailParam;
import com.dl.magicvideo.web.controllers.data.param.ManagerDetailParam;
import com.dl.magicvideo.web.controllers.data.vo.DataListVO;
import com.dl.magicvideo.web.controllers.data.vo.ParamVO;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DataProcess {
    @Resource
    private DataInterfaceManager dataInterfaceManager;

    public List<DataListVO> interfaceList(DataListParam param) {
        List<DataInterfacePO> list = dataInterfaceManager.lambdaQuery().eq(DataInterfacePO::getIsDeleted, Const.ZERO)
                .like(StringUtils.isNotBlank(param.getName()), DataInterfacePO::getName, param.getName()).list();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(dto -> {
                DataListVO vo = new DataListVO();
                vo.setUrl(dto.getInterfaceUrl());
                vo.setName(dto.getName());
                vo.setParamList(JsonUtils.fromJSON(dto.getParam(), new TypeReference<Map<String, ParamVO>>() {
                }));
                return vo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<ManagerDetailVO> managerDetail(ManagerDetailParam param) {
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getCode()), "code不能为空");
        return param.getCode().stream().map(dto -> {
            ManagerDetailVO vo = new ManagerDetailVO();
            vo.setAge(18);
            vo.setCode(dto);
            vo.setName("测试数据:" + dto);
            return vo;
        }).collect(Collectors.toList());
    }

    public List<FundMarketDetailVO> fundMarketDetail(FundMarketDetailParam param) {
        String code = param.getCode();
        Assert.isTrue(StringUtils.isNotEmpty(code), "code不能为空");
        Assert.isTrue(Objects.nonNull(param.getTime()), "time不能为空");

        FundMarketDetailVO vo = new FundMarketDetailVO();
        vo.setTime(param.getTime());
        vo.setCode(code);
        vo.setName("测试数据:" + code);
        vo.setShow(true);

        return Collections.singletonList(vo);
    }
}
