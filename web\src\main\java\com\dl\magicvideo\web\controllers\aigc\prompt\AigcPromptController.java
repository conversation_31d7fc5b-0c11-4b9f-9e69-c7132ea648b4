package com.dl.magicvideo.web.controllers.aigc.prompt;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.aigc.prompt.param.AigcPromptAddParam;
import com.dl.magicvideo.web.controllers.aigc.prompt.param.AigcPromptPageQueryParam;
import com.dl.magicvideo.web.controllers.aigc.prompt.param.AigcPromptUptParam;
import com.dl.magicvideo.web.controllers.aigc.prompt.vo.AigcPromptVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 11:44
 */
@Api("aigc-提示控制器")
@RestController
@RequestMapping("/visual/aigc/prompt")
public class AigcPromptController {

    @Resource
    private AigcPromptProcess aigcPromptProcess;

    @ApiOperation("分页查询提示")
    @PostMapping("/page")
    public ResultPageModel<AigcPromptVO> page(@RequestBody AigcPromptPageQueryParam param) {
        return aigcPromptProcess.page(param);
    }

    @ApiOperation("新增提示")
    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody @Validated AigcPromptAddParam param) {
        return aigcPromptProcess.add(param);
    }

    @ApiOperation("修改提示")
    @PostMapping("/update")
    public ResultModel<Void> update(@RequestBody @Validated AigcPromptUptParam param) {
        return aigcPromptProcess.update(param);
    }

}
