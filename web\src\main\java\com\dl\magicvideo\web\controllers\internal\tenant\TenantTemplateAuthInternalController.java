package com.dl.magicvideo.web.controllers.internal.tenant;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.internal.tenant.dto.TenantTemplateAuthDTO;
import com.dl.magicvideo.web.controllers.internal.tenant.param.TenantAuthParam;
import com.dl.magicvideo.web.controllers.internal.tenant.param.TenantTemplateAuthPageQueryParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/visual/internal/tenanttemplateauth")
@Api("租户模板授权模块")
public class TenantTemplateAuthInternalController {
    @Autowired
    private TenantTemplateAuthInternalProcess tenantTemplateAuthInternalProcess;
    @PostMapping("/list")
    @ApiOperation("模板列表")
    public ResultPageModel<TenantTemplateAuthDTO> list(@RequestBody @Validated TenantTemplateAuthPageQueryParam param) {
        return tenantTemplateAuthInternalProcess.list(param);
    }

    @PostMapping("/auth")
    @ApiOperation("修改授权")
    public ResultModel<Void> auth(@RequestBody @Validated TenantAuthParam param) {
        return tenantTemplateAuthInternalProcess.auth(param);
    }

}
