package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.visual.TemplateCollectMapper;
import com.dl.magicvideo.biz.dal.visual.po.TemplateCollectPO;
import com.dl.magicvideo.biz.manager.visual.TemplateCollectManager;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【tenant_material_folder】的数据库操作Service实现
 * @createDate 2023-04-25 17:03:16
 */
@Service
public class TemplateCollectManagerImpl extends ServiceImpl<TemplateCollectMapper, TemplateCollectPO> implements TemplateCollectManager {

}