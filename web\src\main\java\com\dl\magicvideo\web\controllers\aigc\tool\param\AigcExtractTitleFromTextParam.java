package com.dl.magicvideo.web.controllers.aigc.tool.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-06 11:38
 */
@Data
@ApiModel("aigc-从文本中提取标题-参数")
public class AigcExtractTitleFromTextParam {

    @ApiModelProperty("风格，1-抖音")
    private Integer style;

    @NotBlank(message = "文本不能为空")
    @ApiModelProperty("文本")
    private String text;

}
