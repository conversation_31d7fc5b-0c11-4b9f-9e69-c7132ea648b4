package com.dl.magicvideo.web.controllers.voice.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查询数字人的声音信息
 *
 * @describe: QueryVmVoiceParam
 * @author: zhousx
 * @date: 2023/5/25 11:53
 */
@Data
public class QueryVmVoiceParam {

    @NotNull(message = "数字人bizId不能为空")
    @ApiModelProperty("数字人bizId")
    private Long vmBizId;

    @NotBlank(message = "音色不能为空")
    @ApiModelProperty("音色")
    private String defaultVoiceKey;

    /**
     * 声音厂商
     * 部分老模板中，renderData里没有声音厂商，故可能为空
     */
    @ApiModelProperty("声音厂商")
    private Integer voiceChannel;

    @Deprecated
    @ApiModelProperty("渠道 2-腾讯云克隆音 4-阿里云 6-火山")
    private List<Integer> channels;
}
