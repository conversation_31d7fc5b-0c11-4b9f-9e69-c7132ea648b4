package com.dl.magicvideo.web.controllers.aigc.chat.convert;

import cn.hutool.json.JSONUtil;
import com.dl.aiservice.share.aichat.consts.AiChatKimiConst;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordContentFileBO;
import com.dl.magicvideo.web.controllers.aigc.chat.param.AigcChatSendMessageParam;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 19:10
 */
public class AiChatConvert {

    public static AigcSingleChatRequestBO buildAigcSingleChatRequestBO(String aigcPresupposeText,
            List<AigcChatSendMessageParam> params,Long userId) {
        AigcSingleChatRequestBO requestBO = new AigcSingleChatRequestBO();
        requestBO.setUserId(userId);
        requestBO.setModel(AiChatKimiConst.MOONSHOT_32K);
        requestBO.setRespMaxToken(Const.ONE_ZERO_TWO_FOUR);

        for (AigcChatSendMessageParam messageParam : params) {
            AigcChatRecordContentTypeEnum contentTypeEnum = AigcChatRecordContentTypeEnum
                    .parse(messageParam.getContentType());
            if (Objects.isNull(contentTypeEnum)) {
                throw BusinessServiceException.getInstance("内容类型不存在");
            }
            switch (contentTypeEnum) {
            case TEXT:
                requestBO.setUserMessage(aigcPresupposeText + SymbolE.COLON_EN.getValue() + messageParam.getContent());
                continue;
            case PDF:
            case DOC:
            case XLSX:
            case PPT:
            case TXT_FILE:
            case IMG:
                AigcChatRecordContentFileBO fileBO = JSONUtil
                        .toBean(messageParam.getContent(), AigcChatRecordContentFileBO.class);
                requestBO.setFileUrl(fileBO.getUrl());
                continue;
            default:
                continue;
            }
        }

        return requestBO;
    }



}
