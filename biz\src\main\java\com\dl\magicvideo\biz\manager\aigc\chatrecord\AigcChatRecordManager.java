package com.dl.magicvideo.biz.manager.aigc.chatrecord;

import cn.easyes.core.biz.EsPageInfo;
import com.dl.magicvideo.biz.es.aigc.chatrecord.po.EsIndexAigcChatRecord;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordSearchBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordUpdateBO;

import java.util.List;

/**
 * aigc-聊天记录管理器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 14:54
 */
public interface AigcChatRecordManager {

    /**
     * 分页搜索
     *
     * @param searchBO
     * @return
     */
    EsPageInfo<EsIndexAigcChatRecord> pageSearch(AigcChatRecordSearchBO searchBO);

    /**
     * 添加一条聊天记录
     *
     * @param addBO 聊天记录
     * @return 是否成功
     */
    EsIndexAigcChatRecord add(AigcChatRecordAddBO addBO);

    /**
     * 添加多条聊天记录
     *
     * @param addBOList 聊天记录列表  ***调用方需自行保证消息顺序!***
     * @return 是否全部添加成功
     */
    List<EsIndexAigcChatRecord> batchAdd(List<AigcChatRecordAddBO> addBOList);

    /**
     * 修改聊天记录的内容
     *
     * @param updateBO
     * @return
     */
    void updateContent(AigcChatRecordUpdateBO updateBO);

    /**
     * 根据recordId查询聊天记录
     *
     * @return
     */
    EsIndexAigcChatRecord getOne(Long recordId);

    /**
     * 根据recordId查询聊天记录
     *
     * @return
     */
    void deleteRecord(EsIndexAigcChatRecord record);

}