package com.dl.magicvideo.openapi.controller.produce.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @describe: ProduceParam
 * @author: zhousx
 * @date: 2023/6/25 10:07
 */
@Data
public class OpenDigitalManParam {

    @ApiModelProperty(value = "数字人形象代码")
    private String vmCode;

    @ApiModelProperty(value = "场景id")
    private String sceneId;

    @ApiModelProperty(value = "数字人声音代码")
    private String vmVoiceKey;

    @ApiModelProperty(value = "渠道：1 硅基")
    private Integer channel;

    @ApiModelProperty(value = "1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快")
    private Double speed;

}
