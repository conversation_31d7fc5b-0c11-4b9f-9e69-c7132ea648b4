package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName data_product
 */
@TableName(value ="visual_template_tag")
@Data
public class TagPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 名称
     */
    private String name;

    /**
     *类型，1 模板标签 2.常用组合标签
     */
    private Integer tagType;
}