package com.dl.magicvideo.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceBatchPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.transaction.TransactionProxyManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceBatchManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.mq.producer.ProduceFailProducer;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 任务合成超时的定时任务
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-07-19 17:55
 */
@Component
public class ProduceJobTimeoutTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProduceJobTimeoutTask.class);

    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    @Resource
    private VisualProduceBatchManager visualProduceBatchManager;

    @Resource
    private ProduceFailProducer produceFailProducer;

    @Resource
    private TransactionProxyManager transactionProxyManager;

    /**
     * 任务合成超时阈值 24H
     */
    private static final Integer TIMEOUT_THRESHOLD_HOURS = 24;

//    @XxlJob("produceJobTimeoutTask")
    public void produceJobTimeoutTask() {
        Date timeoutCreateDt = DateUtil.addHour(-TIMEOUT_THRESHOLD_HOURS, new Date());
        LOGGER.info("开始处理produceJob超时的定时任务，timeoutCreateDt:{}", DateUtil.format(timeoutCreateDt, DateUtil.Y_M_D_H_M_S));
        LambdaQueryWrapper<VisualProduceJobPO> queryWrapper = Wrappers.lambdaQuery(VisualProduceJobPO.class)
                .select(VisualProduceJobPO.class,
                        po -> !"replace_data".equals(po.getColumn()) && !"preview_data".equals(po.getColumn())
                                && !"template_data".equals(po.getColumn()) && !"api_data".equals(po.getColumn()))
                .in(VisualProduceJobPO::getStatus,
                        Lists.newArrayList(JobStatusE.INIT.getCode(), JobStatusE.READY.getCode(),
                                JobStatusE.PROCESSING.getCode())).eq(VisualProduceJobPO::getIsDeleted, Const.ZERO)
                .le(VisualProduceJobPO::getCreateDt, timeoutCreateDt).orderByAsc(VisualProduceJobPO::getId);

        Page<VisualProduceJobPO> pageQuery = new Page<>(Const.ONE_L0NG, Const.TWENTY_LONG);
        IPage<VisualProduceJobPO> pageResult = visualProduceJobManager.page(pageQuery, queryWrapper);
        List<VisualProduceJobPO> processingJobList = pageResult.getRecords();
        LOGGER.info("查询到的processingJobList.size:{},,,pageResult.getPages:{},,,pageResult.getTotal:{}",
                processingJobList.size(), pageResult.getPages(), pageResult.getTotal());
        while (CollectionUtils.isNotEmpty(processingJobList)) {
            //遍历处理任务超时
            processingJobList.forEach(jobPO -> transactionProxyManager.process(() -> handleJobTimeout(jobPO)));

            //因为在handleJobTimeout()中会将任务状态改为合成失败，所以一直查询第一页
            pageResult = visualProduceJobManager.page(pageQuery, queryWrapper);
            processingJobList = pageResult.getRecords();
        }
        LOGGER.info("produceJob超时的定时任务处理完成，timeoutCreateDt:{}", DateUtil.format(timeoutCreateDt, DateUtil.Y_M_D_H_M_S));
    }

    private void handleJobTimeout(VisualProduceJobPO jobPO) {
        LOGGER.info("任务超时！做失败处理！jobId:{}", jobPO.getJobId());
        //1.更新任务状态
        visualProduceJobManager
                .update(Wrappers.lambdaUpdate(VisualProduceJobPO.class).eq(VisualProduceJobPO::getId, jobPO.getId())
                        .set(VisualProduceJobPO::getStatus, JobStatusE.FAILED.getCode())
                        .set(VisualProduceJobPO::getModifyDt, new Date()));

        //2.更新批次状态  尽管在模型上一个批次对应多个任务，但是目前只有交付计划是一个批次对应多个任务的，而交付计划压根就没用起来
        // 因此此处直接更新任务对应的批次为失败状态
        visualProduceBatchManager.update(Wrappers.lambdaUpdate(VisualProduceBatchPO.class)
                .eq(VisualProduceBatchPO::getBatchId, jobPO.getBatchId())
                .set(VisualProduceBatchPO::getStatus, JobStatusE.FAILED.getCode())
                .set(VisualProduceBatchPO::getModifyDt, new Date()));

        //3.发生失败mq
        produceFailProducer.sendProduceFailMsg(jobPO.getJobId(), jobPO.getTenantCode(), "视频合成超时");
        LOGGER.info("任务超时！做失败处理完成！jobId:{}", jobPO.getJobId());
    }

}
