package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName data_product
 */
@TableName(value = "template_tag_link")
@Data
public class TemplateTagLinkPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer isDeleted;
}