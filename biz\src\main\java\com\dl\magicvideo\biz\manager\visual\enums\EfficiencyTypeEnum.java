package com.dl.magicvideo.biz.manager.visual.enums;

public enum EfficiencyTypeEnum {
    ALL(1,"合成效率","从用户点击合成到视频合成成功"), SINGLE(2, "合成效率","视频开始合成到视频合成成功"),
    DIGITAL_MAN_TYPE(3, "数字人版合成效率","数字人版合成效率"), PICTURE(4, "照片版合成效率","照片版合成效率");

    private Integer code;

    private String name;
    private String desc;

    EfficiencyTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
