package com.dl.magicvideo.biz.dal.visual;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.param.CommonComponentsPageQueryParam;
import com.dl.magicvideo.biz.dal.visual.po.CommonComponentsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【visual_template】的数据库操作Mapper
 * @createDate 2023-04-24 10:22:23
 * @Entity com.dl.magicvideo.biz.dal.visual.po.VisualTemplate
 */
@BaseDao
public interface CommonComponentsMapper extends BaseMapper<CommonComponentsPO> {
    List<CommonComponentsPO> listCommonComponents(@Param("param") CommonComponentsPageQueryParam param);

    Integer countCommonComponents(@Param("param") CommonComponentsPageQueryParam param);

    List<CommonComponentsPO> latestComponentsByTagIds(@Param("tenantCode") String tenantCode,
        @Param("tagIds") List<Long> tagId, @Param("rowNumber") Integer rowNumber);
}




