package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-08 10:53
 */
@Data
public class ExcelBatchProduceParam {

    @NotBlank(message = "前置校验通过的令牌不能为空")
    @ApiModelProperty("前置校验通过的令牌")
    private String checkPassToken;

    @ApiModelProperty(value = "模板id", required = true)
    @NotBlank(message = "模板id不能为空")
    private String templateId;

    @ApiModelProperty("标题")
    @NotBlank(message = "标题不能为空")
    private String title;

    /**
     * 接口名称和url映射，key-interfaceName
     */
    @ApiModelProperty("接口名称和url映射")
    @NotEmpty(message = "接口名称和url映射不能为空")
    private Map<String, String> interfaceNameUrlMap;

}
