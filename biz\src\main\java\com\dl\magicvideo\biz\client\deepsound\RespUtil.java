package com.dl.magicvideo.biz.client.deepsound;

import cn.hutool.json.JSONUtil;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.deepsound.enums.DsAudioCheckErrCodeEnum;
import com.dl.magicvideo.biz.client.deepsound.enums.DsEvnCheckErrCodeEnum;
import com.dl.magicvideo.biz.client.deepsound.resp.DsAudioCheckResponse;
import com.dl.magicvideo.biz.client.deepsound.resp.DsBaseResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

public class RespUtil {

    private final static Logger logger = LoggerFactory.getLogger(RespUtil.class);
    private final static Integer CONST_ZERO = 0;

    public static boolean isSuccess(DsBaseResponse baseResponse) {
        if (Objects.isNull(baseResponse)) {
            logger.error("调用异常,resp is Empty!");
            return Boolean.FALSE;
        }
        if (Objects.equals(CONST_ZERO, baseResponse.getCode())) {
            return Boolean.TRUE;
        }
        throw createException(baseResponse);
    }

    private static BusinessServiceException createException(DsBaseResponse resp) {
        logger.error("调用异常：response:{}", JSONUtil.toJsonStr(resp));
        if (resp instanceof DsAudioCheckResponse) {
            DsAudioCheckErrCodeEnum errCodeEnum = DsAudioCheckErrCodeEnum.errorCode(resp.getCode());
            if (errCodeEnum == DsAudioCheckErrCodeEnum.UNKNOWN) {
                return BusinessServiceException.getInstance(resp.getCode().toString(), resp.getErrMsg());
            } else {
                return BusinessServiceException.getInstance(errCodeEnum.getErrorCode().toString(),
                        errCodeEnum.getErrorDesc());
            }
        } else {
            DsEvnCheckErrCodeEnum errCodeEnum = DsEvnCheckErrCodeEnum.errorCode(resp.getCode());
            if (errCodeEnum == DsEvnCheckErrCodeEnum.UNKNOWN) {
                return BusinessServiceException.getInstance(resp.getCode().toString(), resp.getErrMsg());
            } else {
                return BusinessServiceException.getInstance(errCodeEnum.getErrorCode().toString(),
                        errCodeEnum.getErrorDesc());
            }
        }
    }

}
