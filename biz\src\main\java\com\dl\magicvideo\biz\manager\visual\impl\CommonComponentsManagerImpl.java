package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.dal.visual.CommonComponentsMapper;
import com.dl.magicvideo.biz.dal.visual.param.CommonComponentsPageQueryParam;
import com.dl.magicvideo.biz.dal.visual.po.CommonComponentsPO;
import com.dl.magicvideo.biz.manager.visual.CommonComponentsManager;
import com.dl.magicvideo.biz.manager.visual.bo.CommonCompentsBO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【tenant_material_folder】的数据库操作Service实现
* @createDate 2023-04-25 17:03:16
*/
@Service
public class CommonComponentsManagerImpl extends ServiceImpl<CommonComponentsMapper, CommonComponentsPO> implements CommonComponentsManager {
    @Override
    public ResponsePageQueryDO<List<CommonComponentsPO>> pageQuery(CommonCompentsBO bo) {
        Assert.isTrue(StringUtils.isNotBlank(bo.getTenantCode()), "租户编号不可为空");
        ResponsePageQueryDO<List<CommonComponentsPO>> response = new ResponsePageQueryDO<>();
        CommonComponentsPageQueryParam param = new CommonComponentsPageQueryParam();
        param.setTenantCode(bo.getTenantCode());
        param.setPageIndex(bo.getPageIndex());
        param.setPageSize(bo.getPageSize());
        param.setTagId(bo.getTagId());
        Integer count = baseMapper.countCommonComponents(param);
        if (Objects.equals(count, 0)) {
            response.setPageIndex(bo.getPageIndex());
            response.setPageSize(bo.getPageSize());
            return response;
        }
        List<CommonComponentsPO> list = baseMapper.listCommonComponents(param);
        response.setDataResult(list);
        response.setPageIndex(param.getPageIndex());
        response.setPageSize(param.getPageSize());
        response.setTotal(count);
        return response;
    }

    @Override
    public List<CommonComponentsPO> latestComponentsByTagIds(String tenantCode, List<Long> tagIds, Integer rowNumber) {
        return baseMapper.latestComponentsByTagIds(tenantCode, tagIds, rowNumber);
    }
}




