package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class SzbAttachmentThreeDTO {
    /**
     * 数据模块指标名
     */
    private String note;
    /**
     * 指标数据单位
     */
    private String unit;
    private List<DataDTO> data;
    /**
     * 数据是否正确（1为数据正常，如果数据模块其他值为空且dataOk为1时，表示本报告期此数据模块为空
     */
    private Integer dataOk;
    /**
     * 货币单位
     */
    private String moneyUnit;
    /**
     * 指标数据
     */
    private Double value;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 名称
         */
        private String name;
        /**
         * 值
         */
        private String value;
        /**
         * 百分比
         */
        private String percent;
    }
}