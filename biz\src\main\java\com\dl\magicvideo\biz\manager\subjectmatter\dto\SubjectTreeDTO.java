package com.dl.magicvideo.biz.manager.subjectmatter.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description：题材树
 * @author： Pelot
 * @create： 2024/5/13 16:11
 */
@Data
public class SubjectTreeDTO {
    //题材名称
    private String name;
    //题材bizId
    private String bizId;
    //题材级别，1-一级题材，2-二级题材 3-三级题材
    private Integer level;
    //父级Id
    private String parentId;
    //子集
    private List<SubjectTreeDTO> children;
    //影响因子
    private String effectReason;
    //影响结果
    private String effectResult;

    public void addChild(SubjectTreeDTO child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }
}
