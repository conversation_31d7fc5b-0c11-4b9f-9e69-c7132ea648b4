package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.visual.po.VisualDynamicNodePO;
import com.dl.magicvideo.biz.manager.visual.VisualDynamicNodeManager;
import com.dl.magicvideo.biz.dal.visual.VisualDynamicNodeMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【visual_dynamic_node】的数据库操作Service实现
* @createDate 2023-04-25 17:03:16
*/
@Service
public class VisualDynamicNodeManagerImpl extends ServiceImpl<VisualDynamicNodeMapper, VisualDynamicNodePO>
    implements VisualDynamicNodeManager {

}




