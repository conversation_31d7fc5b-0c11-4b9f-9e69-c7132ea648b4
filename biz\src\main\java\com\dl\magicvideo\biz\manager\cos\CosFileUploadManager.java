package com.dl.magicvideo.biz.manager.cos;

import com.dl.magicvideo.biz.manager.cos.dto.MediaInfo;
import com.qcloud.cos.model.ciModel.mediaInfo.MediaInfoResponse;

import java.io.File;
import java.util.List;

/**
 * @ClassName CosFileUploadManager
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 17:57
 * @Version 1.0
 **/
public interface CosFileUploadManager {

    /**
     * 上传文件到腾讯云cos存储
     *
     * @param file
     * @param type
     * @param random 是否需要加随机数
     * @param  deleteFile 是否删除文件，默认需要删除
     * @return
     */
    String uploadFile(File file, String type, String path, Boolean random, Boolean deleteFile);

    /**
     * 输入文件在cos中的位置
     * 例 cos根目录下的1.txt文件  则object = 1.txt
     *    cos根目录下test文件夹中的1.txt文件 object = test/1.txt
     */
    MediaInfo generateMediainfo(String path);

}
