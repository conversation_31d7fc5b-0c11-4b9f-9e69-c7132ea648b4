package com.dl.magicvideo.openapi.controller.exception;

import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.constant.OpenApiConstant;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/visual/openapi/exception")
public class ExceptionController {
    @Resource
    private HttpServletRequest request;

    /**
     * 重新抛出异常
     */
    @RequestMapping("/rethrow")
    @NotLogin
    public void rethrow() throws Throwable {
        throw (Throwable) request.getAttribute(OpenApiConstant.OPENAPI_EXCEPTION);
    }
}
