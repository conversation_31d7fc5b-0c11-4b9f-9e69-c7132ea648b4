package com.dl.magicvideo.biz.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class ApplicationUtil implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext=applicationContext;
    }
    public static <T> T getBean(Class<T> tClass){
        if(applicationContext==null){
            throw new RuntimeException("尚未完成初始化");
        }
        return applicationContext.getBean(tClass);
    }
}
