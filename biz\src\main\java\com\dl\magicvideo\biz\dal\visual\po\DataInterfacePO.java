package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName data_product
 */
@TableName(value ="data_interface")
@Data
public class DataInterfacePO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 接口code
     */
    private String interfaceCode;

    /**
     * 接口名称
     */
    private String name;
    /**
     * 接口url
     */
    private String interfaceUrl;

    /**
     * 接口参数
     */
    private String param;

    /**
     * 分类 0通用 1招行
     */
    private Integer category;

    /**
     * 是否删除，1删除 0未删除
     */
    private Integer isDeleted;
}