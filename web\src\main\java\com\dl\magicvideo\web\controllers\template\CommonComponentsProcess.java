package com.dl.magicvideo.web.controllers.template;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.CommonComponentsPO;
import com.dl.magicvideo.biz.dal.visual.po.TagPO;
import com.dl.magicvideo.biz.dal.visual.po.TenantMaterialFolderPO;
import com.dl.magicvideo.biz.manager.visual.CommonComponentsManager;
import com.dl.magicvideo.biz.manager.visual.TagManager;
import com.dl.magicvideo.biz.manager.visual.bo.CommonCompentsBO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.template.convert.CommonComponentsConvert;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsAddParam;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsDetailParam;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsGroupParam;
import com.dl.magicvideo.web.controllers.template.param.CommonComponentsPageParam;
import com.dl.magicvideo.web.controllers.template.vo.CommonComponentsDetailVO;
import com.dl.magicvideo.web.controllers.template.vo.CommonComponentsGroupVO;
import com.dl.magicvideo.web.controllers.template.vo.CommonComponentsListVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2024/11/19 14:11
 */
@Component
public class CommonComponentsProcess extends AbstractController {
    @Resource
    private CommonComponentsManager commonComponentsManager;
    @Resource
    private TagManager tagManager;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private HostTimeIdg hostTimeIdg;

    public ResultPageModel<CommonComponentsListVO> list(CommonComponentsPageParam param) {
        CommonCompentsBO bo = new CommonCompentsBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setTagId(param.getTagId());
        ResponsePageQueryDO<List<CommonComponentsPO>> result = commonComponentsManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<CommonComponentsListVO> vos = result.getDataResult().stream().map(CommonComponentsConvert::cnvCommonComponentsListVO)
                .collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    public ResultModel<CommonComponentsDetailVO> detail(CommonComponentsDetailParam param) {
        CommonComponentsPO one = commonComponentsManager.lambdaQuery().eq(CommonComponentsPO::getBizId, param.getBizId()).one();
        Assert.notNull(one, "未查询到常用组件");
        CommonComponentsDetailVO vo = new CommonComponentsDetailVO();
        vo.setUrl(one.getUrl());
        vo.setBizId(one.getBizId().toString());
        vo.setRenderData(one.getRenderData());
        vo.setTagId(one.getTagId().toString());
        vo.setName(one.getName());
        TagPO tag = tagManager.lambdaQuery().eq(TagPO::getTagId, one.getTagId()).one();
        if (Objects.nonNull(tag)){
            vo.setTagName(tag.getName());
        }
        return ResultModel.success(vo);
    }

    public ResultModel<Void> add(CommonComponentsAddParam param) {
        CommonComponentsPO po = new CommonComponentsPO();
        po.setUrl(param.getUrl());
        po.setBizId(hostTimeIdg.generateId().longValue());
        po.setTenantCode(operatorUtil.getTenantCode());
        po.setRenderData(param.getRenderData());
        po.setName(param.getName());
        if (Objects.nonNull(param.getTagId())){
            po.setTagId(param.getTagId());
        } else {
            long tagId = hostTimeIdg.generateId().longValue();
            TagPO tagPO = new TagPO();
            tagPO.setTagId(tagId);
            tagPO.setTagType(2);
            tagPO.setName(param.getTagName());
            tagManager.save(tagPO);
            po.setTagId(tagId);
        }
        commonComponentsManager.save(po);
        return ResultModel.success(null);
    }

    public ResultModel<List<CommonComponentsGroupVO>> groupList(CommonComponentsGroupParam param) {
        List<CommonComponentsGroupVO> voList = new ArrayList<>();
        String tenantCode = operatorUtil.getTenantCode();
        List<TagPO> tagPOList = new ArrayList<>();

        TagPO baseTagPO = new TagPO();
        baseTagPO.setTagId(Const.ZERO_LONG);
        baseTagPO.setName("常用组合");
        tagPOList.add(baseTagPO);
        if (Const.DEFAULT_TENANT_CODE.equals(tenantCode)){
            List<TagPO> list = tagManager.lambdaQuery().eq(TagPO::getTagType, 2).list();
            if (CollectionUtils.isNotEmpty(list)){
                tagPOList.addAll(list);
            }
        }
        List<Long> tagIdList = tagPOList.stream().map(TagPO::getTagId).collect(Collectors.toList());
        List<CommonComponentsPO> commonComponentsPOS = commonComponentsManager.latestComponentsByTagIds(tenantCode, tagIdList, param.getLatestMaterialNumber());
        Map<Long, List<CommonComponentsPO>>  commonComponentsListMap = commonComponentsPOS.stream()
                .collect(Collectors.groupingBy(CommonComponentsPO::getTagId));

        tagPOList.forEach(tagPO -> {
            CommonComponentsGroupVO componentsGroupVO = new CommonComponentsGroupVO();
            componentsGroupVO.setTagId(tagPO.getTagId().toString());
            componentsGroupVO.setTagName(tagPO.getName());
            voList.add(componentsGroupVO);

            List<CommonComponentsPO> commonComponentsPOList = commonComponentsListMap.get(tagPO.getTagId());
            if (CollectionUtils.isEmpty(commonComponentsPOList)) {
                componentsGroupVO.setCommonComponentsList(Collections.emptyList());
            } else {
                List<CommonComponentsListVO> materialList = commonComponentsPOList.stream().map(commonComponentsPO -> {
                    CommonComponentsListVO commonComponentsListVO = new CommonComponentsListVO();
                    commonComponentsListVO.setBizId(commonComponentsPO.getBizId().toString());
                    commonComponentsListVO.setUrl(commonComponentsPO.getUrl());
                    commonComponentsListVO.setName(commonComponentsPO.getName());
                    return commonComponentsListVO;
                }).collect(Collectors.toList());
                componentsGroupVO.setCommonComponentsList(materialList);
            }
        });

        return ResultModel.success(voList);
    }
}
