<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.subjectmatter.SubjectMatterMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizId" column="biz_id"/>
        <result property="name" column="name"/>
        <result property="imgUrl" column="img_url"/>
        <result property="excelUrl" column="excel_url"/>
        <result property="level" column="level"/>
        <result property="parentId" column="parent_id"/>
        <result property="isHaveChild" column="is_have_child"/>
        <result property="creatorName" column="creator_name"/>
        <result property="modifyName" column="modify_name"/>
        <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
        <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="modifyDt" column="modify_dt" jdbcType="TIMESTAMP"/>
        <result property="modifyBy" column="modify_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,produce_job_id,tenant_code,
        share_conf_state,recommend_state,recommend_enable_dt,
        is_deleted,create_dt,create_by,
        modify_dt,modify_by
    </sql>

    <select id="joinPageCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM subject_matter AS sm
        INNER JOIN subject_matter_stock AS sms on sm.biz_id = sms.sm_biz_id
        WHERE sm.is_deleted = 0
        <if test="param.stockCode != null and param.stockCode != ''">
            AND sms.stock_code = #{param.stockCode}
        </if>
        <if test="param.level != null">
            AND sm.level = #{param.level}
        </if>
        <if test="param.name != null and param.name != ''">
            AND sm.name like concat('%', #{param.name},'%')
        </if>
        <if test="param.type != null">
            AND sm.type = #{param.type}
        </if>
    </select>

    <select id="joinPageList" resultMap="BaseResultMap">
        SELECT
        sm.*
        FROM subject_matter AS sm
        INNER JOIN subject_matter_stock AS sms on sm.biz_id = sms.sm_biz_id
        WHERE sm.is_deleted = 0
        <if test="param.stockCode != null and param.stockCode != ''">
            AND sms.stock_code = #{param.stockCode}
        </if>
        <if test="param.level != null">
            AND sm.level = #{param.level}
        </if>
        <if test="param.name != null and param.name != ''">
            AND sm.name like concat('%', #{param.name},'%')
        </if>
        <if test="param.type != null">
            AND sm.type = #{param.type}
        </if>
        ORDER BY sm.modify_dt DESC, sm.id DESC
        LIMIT #{param.offset},#{param.pageSize}
    </select>

</mapper>
