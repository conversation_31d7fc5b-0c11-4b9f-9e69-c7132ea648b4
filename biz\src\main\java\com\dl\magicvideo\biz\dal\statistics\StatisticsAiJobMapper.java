package com.dl.magicvideo.biz.dal.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxTenantCodeParam;
import com.dl.magicvideo.biz.dal.statistics.param.TotalCountParam;
import com.dl.magicvideo.biz.dal.statistics.po.AiStatisticsTotalCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsAiJobPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobTenantSummaryQueryBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobTenantSummaryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-13 16:32
 */
@BaseDao
public interface StatisticsAiJobMapper extends BaseMapper<StatisticsAiJobPO> {

    /**
     * 一段时间内topX 租户
     * @param param
     * @return
     */
    List<StatisticsCountTopMaxPO> topMaxTenantCode(@Param("param") TopMaxTenantCodeParam param);

    /**
     * 一段时间内的总数
     *
     * @param param
     * @return
     */
    List<AiStatisticsTotalCountPO> totalCount(@Param("param") TotalCountParam param);

    /**
     * 一端时间内租户的数量信息
     * @param param
     * @return
     */
    List<StatisticsMsgPO> topMaxMsg(@Param("param") TopMaxMsgParam param);

    /**
     * 指定租户的ai任务数据统计汇总
     *
     * @param param
     * @return
     */
    StatisticsAiJobTenantSummaryDTO specificTenantSummary(@Param("param") StatisticsAiJobTenantSummaryQueryBO param);

}
