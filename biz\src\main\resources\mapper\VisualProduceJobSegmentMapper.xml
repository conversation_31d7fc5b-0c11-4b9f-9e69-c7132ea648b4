<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.VisualProduceJobSegmentMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobSegmentPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="segmentId" column="segment_id" jdbcType="BIGINT"/>
            <result property="produceJobId" column="produce_job_id" jdbcType="BIGINT"/>
            <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
            <result property="processHost" column="process_host" jdbcType="VARCHAR"/>
            <result property="processBegin" column="process_begin" jdbcType="TIMESTAMP"/>
            <result property="processEnd" column="process_end" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
            <result property="modifyDt" column="modify_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,segment_id,produce_job_id,
        video_url,process_host,process_begin,
        process_end,status,create_dt,
        modify_dt
    </sql>
</mapper>
