package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.share.digitalman.DigitalManComposeRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManComposeResponseDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.dal.visual.VisualAiJobMapper;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobDurationBO;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.manager.util.VisualProduceJobUtil;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.TtsJobBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【visual_ai_job】的数据库操作Service实现
 * @createDate 2023-06-08 16:23:52
 */
@Service
public class VisualAiJobManagerImpl extends ServiceImpl<VisualAiJobMapper, VisualAiJobPO>
        implements VisualAiJobManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(VisualAiJobManagerImpl.class);

    @Resource
    private AiServiceClient aiServiceClient;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Value("${dl.aiservice.digitalman.callback}")
    private String callbackUrl;

    @Override
    public Long submitDigitalManJob(DigitalManJobBO bo) {
        Long jobId = hostTimeIdg.generateId().longValue();
        VisualAiJobPO newAiJob = new VisualAiJobPO();
        newAiJob.setJobId(jobId);
        newAiJob.setTemplateId(bo.getTemplateId());
        newAiJob.setProduceJobId(bo.getProduceJobId());
        newAiJob.setJobStatus(JobStatusE.INIT.getCode());
        newAiJob.setJobType(AiJobTypeE.DIGITAL_MAN.getCode());
        newAiJob.setCreateDt(new Date());
        newAiJob.setModifyDt(new Date());
        newAiJob.setCreateBy(bo.getUserId());
        newAiJob.setRequestInfo(JSONUtil.toJsonStr(bo));
        newAiJob.setConfigId(bo.getConfigId());
        this.save(newAiJob);

        DigitalManComposeRequestDTO request = new DigitalManComposeRequestDTO();
        request.setVirtualmanKey(bo.getSceneId());
        request.setSceneId(bo.getSceneId());
        request.setWorksBizId(jobId);
        request.setType(Objects.nonNull(bo.getDriveType()) ? bo.getDriveType() : Const.ONE);
        request.setText(bo.getText());
        request.setAudioUrl(bo.getAudioUrl());
        request.setCallbackUrl(callbackUrl);
        request.setSpeakerId(bo.getVoiceCode());
        request.setSpeed(bo.getSpeed());
        request.setCustomStoreUrl(Const.ONE);
        ResultModel<DigitalManComposeResponseDTO> result = aiServiceClient.videoCreate(bo.getChannel(), request);
        if (Objects.isNull(result) || Objects.isNull(result.getDataResult()) || Objects
                .isNull(result.getDataResult().getMediaJobId())) {
            newAiJob.setJobStatus(JobStatusE.FAILED.getCode());
            this.updateById(newAiJob);

            LOGGER.error("合成数字人视频失败，channel:{},,,request:{},,,result:{}", bo.getChannel(), JSONUtil.toJsonStr(request),
                    JSONUtil.toJsonStr(result));
            throw BusinessServiceException.getInstance("数字人视频合成失败");
        }
        DigitalManComposeResponseDTO responseDTO = result.getDataResult();
        newAiJob.setJobStatus(JobStatusE.PROCESSING.getCode());
        newAiJob.setAiJobId(responseDTO.getMediaJobId());
        newAiJob.setResponseInfo(JSONUtil.toJsonStr(result));
        this.updateById(newAiJob);
        return jobId;
    }

    @Override
    public VisualAiJobPO submitTtsJob(TtsJobBO bo) {
        //1、准备ai任务对象
        VisualAiJobPO newAiJob = new VisualAiJobPO();
        Long jobId = hostTimeIdg.generateId().longValue();
        newAiJob.setJobId(jobId);
        newAiJob.setProduceJobId(bo.getProduceJobId());
        newAiJob.setTemplateId(bo.getTemplateId());
        newAiJob.setJobType(Objects.nonNull(bo.getAiJobType()) ? bo.getAiJobType() : AiJobTypeE.TTS.getCode());
        newAiJob.setCreateDt(new Date());
        newAiJob.setModifyDt(new Date());
        newAiJob.setCreateBy(bo.getUserId());
        newAiJob.setRequestInfo(JSONUtil.toJsonStr(bo));
        newAiJob.setConfigId(bo.getConfigId());
        newAiJob.setTextLength(bo.getText().length());

        //2、构建tts请求对象
        TTSProduceParamDTO ttsProduceParamDTO = new TTSProduceParamDTO();
        ttsProduceParamDTO.setVoiceName(bo.getVoiceName());
        ttsProduceParamDTO
                .setWorksBizId(Objects.nonNull(bo.getProduceJobId()) ? bo.getProduceJobId() : bo.getTemplateId());
        String script = VisualProduceJobUtil.removeTTSSpeakLable(bo.getText());
        script = VisualProduceJobUtil.removeLineBreak(script);
        if(StringUtils.isBlank(script)){
            throw BusinessServiceException.getInstance("语音合成失败:文本不能为空");
        }
        ttsProduceParamDTO.setText(script);
        String audioEncode = decideTTsAudioEncode(bo);
        ttsProduceParamDTO.setAudioEncode(audioEncode);
        ttsProduceParamDTO.setMaxLength(bo.getMaxLength());
        ttsProduceParamDTO.setVolume(bo.getVolume());
        ttsProduceParamDTO.setSpeed(bo.getSpeed());
        ttsProduceParamDTO.setCustomStoreUrl(bo.getCustomStoreUrl());
        ttsProduceParamDTO.setPitch(bo.getPitch());
        ttsProduceParamDTO.setNeedSubtitle(bo.getEnableSubtitle());
        ttsProduceParamDTO.setSubtitleKeyWordsHighlight(bo.getSubtitleKeyWordsHighlight());

        //3.调用tts接口
        ResultModel<TTSResponseDTO> resultModel = aiServiceClient.tts(bo.getChannel(), ttsProduceParamDTO);
        if (Objects.isNull(resultModel) || Objects.isNull(resultModel.getDataResult())) {
            LOGGER.error("语音合成失败，channel:{},,,ttsProduceParamDTO:{},,,result:{}", bo.getChannel(),
                    JSONUtil.toJsonStr(ttsProduceParamDTO), JSONUtil.toJsonStr(resultModel));
            newAiJob.setJobStatus(JobStatusE.FAILED.getCode());
            newAiJob.setResponseInfo(JSONUtil.toJsonStr(resultModel));
            this.save(newAiJob);
            String errorMsg = Objects.isNull(resultModel) || StringUtils.isBlank(resultModel.getMessage()) ?
                    "语音合成失败" :
                    resultModel.getMessage();
            throw BusinessServiceException.getInstance(errorMsg);
        }

        TTSResponseDTO ttsResponseDTO = resultModel.getDataResult();
        newAiJob.setAiJobId(ttsResponseDTO.getMediaJobId());
        newAiJob.setJobStatus(JobStatusE.SUCCESS.getCode());
        newAiJob.setResponseInfo(JSONUtil.toJsonStr(resultModel));
        newAiJob.setMediaInfo(ttsResponseDTO.getAudioUrl());
        newAiJob.setDuration(Math.round(ttsResponseDTO.getDuration() * 1000));
        if (Objects.equals(bo.getEnableSubtitle(), Const.ONE) && CollectionUtils
                .isNotEmpty(ttsResponseDTO.getSubtitles())) {
            newAiJob.setSubtitleInfo(JSONUtil.toJsonStr(ttsResponseDTO.getSubtitles()));
        }
        this.save(newAiJob);

        return newAiJob;
    }

    @Override
    public ResponsePageQueryDO<List<VisualAiJobExtPO>> pageExt(VisualAiJobPageBO param) {
        ResponsePageQueryDO<List<VisualAiJobExtPO>> response = new ResponsePageQueryDO<>();
        response.setPageIndex(param.getPageIndex());
        response.setPageSize(param.getPageSize());
        Integer count = baseMapper.countExt(param);
        if (count == 0) {
            return response;
        }
        List<VisualAiJobExtPO> taskPOList = baseMapper.pageExt(param);
        response.setTotal(count);
        response.setDataResult(taskPOList);
        return response;
    }

    @Override
    public Long countDuration(VisualAiJobDurationBO query) {
        return baseMapper.countDuration(query);
    }

    private String decideTTsAudioEncode(TtsJobBO ttsJobBO) {
        //深声科技音频格式
        if (ServiceChannelEnum.DEEP_SOUND.getCode().equals(ttsJobBO.getChannel())) {
            return "audio/mp3";
        } else {
            return "mp3";
        }

    }
}




