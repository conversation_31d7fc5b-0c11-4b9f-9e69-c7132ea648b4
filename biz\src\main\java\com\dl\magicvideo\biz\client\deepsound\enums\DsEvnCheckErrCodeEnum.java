package com.dl.magicvideo.biz.client.deepsound.enums;

import java.util.Objects;

public enum DsEvnCheckErrCodeEnum {

    ERROR_CODE_0(0, "响应成功"),
    ERROR_CODE_1001(1001, "背景噪声过大，不满足要求"),
    ERROR_CODE_1002(1002, "音频时长过短，小于 3s"),
    ERROR_CODE_1003(1003, "文件格式错误"),
    ERROR_CODE_1010(1010, "音频文件编码格式错误"),
    ERROR_CODE_400000(400000, "参数有误"),
    ERROR_CODE_400001(400001, "身份校验错误"),
    ERROR_CODE_400004(400004, "找不到请求所对应的数据资源"),
    ERROR_CODE_400005(400005, "请求方法错误"),
    ERROR_CODE_400006(400006, "因商务授权策略原因禁用"),
    ERROR_CODE_400007(400007, "克隆文本未经审核"),
    ERROR_CODE_400010(400010, "同一个业务方录音编号不能重复提交"),
    ERROR_CODE_400110(400110, "同一个音色名称不能重复提交"),
    //  如果偶现，可以忽略；重复请联系 <EMAIL>
    ERROR_CODE_500000(500000, "系统内部错误"),
    // 如果偶现，可以忽略；重复请联系 <EMAIL>
    ERROR_CODE_500003(500003, "拒绝服务"),
    UNKNOWN(9999999, "未知异常");

    private final Integer errorCode;
    private final String errorDesc;

    DsEvnCheckErrCodeEnum(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    public static String getErrorDesc(Integer errorCode) {
        for (DsEvnCheckErrCodeEnum wxErrorCodeEnum : values()) {
            if (Objects.equals(wxErrorCodeEnum.errorCode, errorCode)) {
                return wxErrorCodeEnum.errorDesc;
            }
        }
        return null;
    }

    public static DsEvnCheckErrCodeEnum errorCode(Integer code) {
        for (DsEvnCheckErrCodeEnum value : values()) {
            if (Objects.nonNull(code)) {
                if (value.getErrorCode().equals(code)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorDesc() {
        return errorDesc;
    }
}
