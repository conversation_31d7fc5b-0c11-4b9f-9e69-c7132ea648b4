package com.dl.magicvideo.web.controllers.shorturl;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.manager.shorturl.ShortURLManager;
import com.dl.magicvideo.biz.manager.shorturl.dto.ShortURLDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-16 11:31
 */
@RestController
@RequestMapping("/")
@Api("企微端-运营中心短链")
public class ShortURLController {

    @Resource
    private ShortURLManager shortURLManager;

    @NotLogin
    @RequestMapping("/ms/{code}")
    @ApiOperation("获取目标地址")
    public ResultModel<String> getTargetURL(@PathVariable("code") String code, HttpServletResponse response)
            throws IOException {
        ShortURLDTO shortURLDTO = shortURLManager.getTargetURL(code);
        if (Objects.isNull(shortURLDTO)) {
            return ResultModel.success(SymbolE.BLANK.getValue());
        }
        response.sendRedirect(shortURLDTO.getTargetURL());
        return ResultModel.success(shortURLDTO.getTargetURL());
    }

}
