package com.dl.magicvideo.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.mq.DlChannels;
import com.dl.magicvideo.biz.mq.dto.ProduceJobSubordinateTtsSuccessDTO;
import com.dl.magicvideo.biz.mq.dto.TtsJobDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频任务下属的tts合成成功 消息生产者
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-03 14:50
 */
@Component
public class ProduceJobSubordinateTtsSuccessProducer {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProduceJobSubordinateTtsSuccessProducer.class);

    @Autowired
    private DlChannels dlChannels;

    /**
     * 发送视频任务下属的tts合成成功的消息
     *
     * @param jobId
     * @param templateId
     * @param ttsJobPOList
     */
    public void sendTtsSuccessMsg(Long jobId, Long templateId, List<VisualAiJobPO> ttsJobPOList) {
        if (CollectionUtils.isEmpty(ttsJobPOList)) {
            LOGGER.info("该视频任务下不含tts任务。jobId:{}", jobId);
            return;
        }

        LOGGER.info("准备发送视频任务下属的tts合成成功的消息 jobId = {}", jobId);
        ProduceJobSubordinateTtsSuccessDTO dto = new ProduceJobSubordinateTtsSuccessDTO();
        dto.setJobId(jobId);
        dto.setTemplateId(templateId);
        dto.setTtsJobList(ttsJobPOList.stream().map(po -> cnvTtsJobPO2DTO(po)).collect(Collectors.toList()));
        Message<ProduceJobSubordinateTtsSuccessDTO> message = MessageBuilder.withPayload(dto).build();
        try {
            boolean sendResult = dlChannels.produceJobSubordinateTtsSuccessProducer().send(message, 2000L);
            LOGGER.info("发送视频任务下属的tts合成成功的消息,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
        } catch (Exception e) {
            LOGGER.error("发送视频任务下属的tts合成成功的消息发生异常,message:{},e:{}", JSONUtil.toJsonStr(message), e);
        }
    }

    private static TtsJobDTO cnvTtsJobPO2DTO(VisualAiJobPO input) {
        TtsJobDTO result = new TtsJobDTO();
        result.setId(input.getId());
        result.setJobId(input.getJobId());
        result.setProduceJobId(input.getProduceJobId());
        result.setTemplateId(input.getTemplateId());
        result.setAiJobId(input.getAiJobId());
        result.setConfigId(input.getConfigId());
        result.setJobType(input.getJobType());
        result.setJobStatus(input.getJobStatus());
        result.setMediaInfo(input.getMediaInfo());
        result.setSubtitleInfo(input.getSubtitleInfo());
        result.setRequestInfo(input.getRequestInfo());
        result.setResponseInfo(input.getResponseInfo());
        result.setCreateDt(input.getCreateDt());
        result.setCreateBy(input.getCreateBy());
        result.setModifyDt(input.getModifyDt());
        result.setDuration(input.getDuration());
        return result;
    }
}
