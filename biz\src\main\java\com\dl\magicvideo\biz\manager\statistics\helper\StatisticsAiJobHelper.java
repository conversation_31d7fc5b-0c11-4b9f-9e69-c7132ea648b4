package com.dl.magicvideo.biz.manager.statistics.helper;

import com.dl.magicvideo.biz.dal.statistics.po.StatisticsAiJobPO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobDTO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:24
 */
public class StatisticsAiJobHelper {

    public static StatisticsAiJobDTO cnvStatisticsAiJobPO2DTO(StatisticsAiJobPO input) {
        StatisticsAiJobDTO result = new StatisticsAiJobDTO();
        result.setRecordId(input.getRecordId());
        result.setTenantCode(input.getTenantCode());
        result.setAiJobType(input.getAiJobType());
        result.setJobCount(input.getJobCount());
        result.setTotalCeilingMinutes(input.getTotalCeilingMinutes());
        result.setTotalTimeMillis(input.getTotalTimeMillis());
        result.setStatisticsTime(input.getStatisticsTime());
        result.setCreateDt(input.getCreateDt());
        result.setModifyDt(input.getModifyDt());
        result.setIsDeleted(input.getIsDeleted());
        result.setTenantName(input.getTenantName());
        return result;
    }
}
