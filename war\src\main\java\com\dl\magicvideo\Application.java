package com.dl.magicvideo;

import cn.easyes.starter.config.EasyEsConfigProperties;
import cn.easyes.starter.config.EsAutoConfiguration;
import cn.easyes.starter.register.EsMapperScan;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.mq.DlChannels;
import com.dtflys.forest.springboot.annotation.ForestScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import org.springframework.cloud.stream.annotation.EnableBinding;

@MapperScan(annotationClass = BaseDao.class)
@EsMapperScan("com.dl.magicvideo.biz.es")
@SpringBootApplication(exclude = { EsAutoConfiguration.class })
@EnableConfigurationProperties({ EasyEsConfigProperties.class })

@ForestScan(basePackages = "com.dl.magicvideo.biz.client")
@EnableBinding({ DlChannels.class })
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
