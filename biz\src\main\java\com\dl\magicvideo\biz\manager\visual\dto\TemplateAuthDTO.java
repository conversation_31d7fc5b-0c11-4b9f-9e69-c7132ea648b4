package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe: TemplateAuthDTO
 * @author: zhousx
 * @date: 2023/6/18 23:45
 */
@Data
public class TemplateAuthDTO {
    private Long templateId;

    private String name;

    private String coverUrl;

    private String previewVideoUrl;

    private String shortVideoUrl;

    private Integer status;

    private String resolution;

    private String resolutionType;

    private String ttsParam;

    private String bgMusic;

    private String bgMusicParam;

    private String replaceData;

    private String tenantCode;

    private List<Tenant> authTenantList;

    private String creatorName;

    private Date createDt;

    private Long duration;

    private String modifyName;

    private Date modifyDt;

    private Long sourceTemplateId;

    @Data
    public static class Tenant {
        private String tenantCode;

        private String tenantName;
    }
}
