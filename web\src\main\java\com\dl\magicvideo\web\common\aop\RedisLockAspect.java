package com.dl.magicvideo.web.common.aop;

import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.properties.DLRedisProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @describe: 分布式锁切面
 * @author: zhousx
 * @date: 2022/5/23 11:18
 */
@Slf4j
@Aspect
@Component
public class RedisLockAspect {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private DLRedisProperties dlRedisProperties;

    @Deprecated
    @Value("${spring.application.name}")
    private String applicationName;

    private static final String LOCK_NAMESPACE = ":lock:";

    @Around("@annotation(com.dl.magicvideo.web.common.aop.RedisLock)")
    public Object redisLock(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Object[] args = joinPoint.getArgs();
        RedisLock redisLock = method.getAnnotation(RedisLock.class);

        //获取被拦截方法参数名列表(使用Spring支持类库)
        LocalVariableTableParameterNameDiscoverer localVariableTable = new LocalVariableTableParameterNameDiscoverer();
        String[] paraNameArr = localVariableTable.getParameterNames(method);
        //使用SPEL进行key的解析
        ExpressionParser parser = new SpelExpressionParser();
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        //把方法参数放入SPEL上下文中
        for(int i = 0; i < paraNameArr.length; i++) {
            context.setVariable(paraNameArr[i], args[i]);
        }

        String key = dlRedisProperties.getPrefix() + LOCK_NAMESPACE + redisLock.prefix();
        if(StringUtils.isNotBlank(redisLock.dynamicVal())) {
            key = key + parser.parseExpression(redisLock.dynamicVal()).getValue(context, String.class);
        }

        log.info("distributedLock, key:{}", key);

        RLock lock = redissonClient.getLock(key);
        try {
            // 尝试获取锁
            if (!lock.tryLock(redisLock.waitTime(), redisLock.leaseTime(), redisLock.timeUnit())) {
                log.warn("redisLock tryLock fail");
                throw BusinessServiceException.getInstance("请稍后重试");
            }
        } catch (InterruptedException e) {
            log.error("redisLock tryLock error", e);
            throw e;
        }
        try {
            // 执行业务
            return joinPoint.proceed();
        } catch (Throwable e) {
            log.error("redisLock proceed error", e);
            throw e;
        } finally {
            // 最后强制释放锁
            lock.forceUnlock();
            log.info("redisLock unlock key:{}", key);
        }
    }
}
