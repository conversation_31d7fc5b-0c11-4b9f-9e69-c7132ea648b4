package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

/**
 * 
 * @TableName visual_dynamic_node
 */
@TableName(value ="visual_dynamic_node")
@Data
public class VisualDynamicNodePO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId
    private Long id;

    /**
     * 节点id
     */
    private Long nodeId;

    /**
     * 卡片id
     */
    private Long cardId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 时长
     */
    private Long duration;

    /**
     * 片段封面
     */
    private String coverUrl;

    /**
     * 是否启用 0否 1是
     */
    private Integer isEnabled;

    /**
     * 节点程序选择表达式
     */
    private String expression;

    /**
     * 是否启用程序选择
     */
    private Integer expressionFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}