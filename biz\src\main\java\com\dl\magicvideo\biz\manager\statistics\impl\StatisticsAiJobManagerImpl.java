package com.dl.magicvideo.biz.manager.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.dal.statistics.StatisticsAiJobMapper;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxTenantCodeParam;
import com.dl.magicvideo.biz.dal.statistics.param.TotalCountParam;
import com.dl.magicvideo.biz.dal.statistics.po.AiStatisticsTotalCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsAiJobPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.manager.statistics.StatisticsAiJobManager;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobPageBO;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobTenantSummaryQueryBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobDTO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobTenantSummaryDTO;
import com.dl.magicvideo.biz.manager.statistics.helper.StatisticsAiJobHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-13 16:35
 */
@Component
public class StatisticsAiJobManagerImpl extends ServiceImpl<StatisticsAiJobMapper, StatisticsAiJobPO>
        implements StatisticsAiJobManager, CommonService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatisticsAiJobManagerImpl.class);

    @Override
    public ResponsePageQueryDO<List<StatisticsAiJobDTO>> pageQuery(StatisticsAiJobPageBO bo) {
        ResponsePageQueryDO<List<StatisticsAiJobDTO>> response = new ResponsePageQueryDO<>();

        LambdaQueryWrapper<StatisticsAiJobPO> queryWrapper = Wrappers.lambdaQuery(StatisticsAiJobPO.class);
        queryWrapper.eq(StatisticsAiJobPO::getIsDeleted, Const.ZERO)
                .eq(Objects.nonNull(bo.getTenantCode()), StatisticsAiJobPO::getTenantCode, bo.getTenantCode())
                .ge(Objects.nonNull(bo.getMinDt()), StatisticsAiJobPO::getStatisticsTime, bo.getMinDt())
                .le(Objects.nonNull(bo.getMaxDt()), StatisticsAiJobPO::getStatisticsTime, bo.getMaxDt())
                .eq(Objects.nonNull(bo.getAiJobType()), StatisticsAiJobPO::getAiJobType, bo.getAiJobType())
                .orderByDesc(StatisticsAiJobPO::getStatisticsTime);

        IPage<StatisticsAiJobPO> pageResult = baseMapper.selectPage(convert(bo), queryWrapper);
        List<StatisticsAiJobPO> data = pageResult.getRecords();

        LOGGER.info("StatisticsAiJobManagerImpl.baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }
        
        response.setDataResult(
                data.stream().map(StatisticsAiJobHelper::cnvStatisticsAiJobPO2DTO).collect(Collectors.toList()));
        return response;
    }

    @Override
    public List<StatisticsCountTopMaxPO> topMaxTenantCode(TopMaxTenantCodeParam param) {
        return this.baseMapper.topMaxTenantCode(param);
    }

    @Override
    public List<AiStatisticsTotalCountPO> totalCount(TotalCountParam param) {
        return this.baseMapper.totalCount(param);
    }

    @Override
    public List<StatisticsMsgPO> topMaxMsg(TopMaxMsgParam param) {
        return this.baseMapper.topMaxMsg(param);
    }

    @Override
    public StatisticsAiJobTenantSummaryDTO specificTenantSummary(StatisticsAiJobTenantSummaryQueryBO bo) {
        return this.baseMapper.specificTenantSummary(bo);
    }

}
