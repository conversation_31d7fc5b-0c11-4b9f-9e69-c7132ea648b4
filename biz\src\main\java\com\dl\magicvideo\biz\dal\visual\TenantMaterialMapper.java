package com.dl.magicvideo.biz.dal.visual;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.po.LatestTenantMaterialPO;
import com.dl.magicvideo.biz.dal.visual.po.TenantMaterialPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【tenant_material】的数据库操作Mapper
 * @createDate 2023-07-06 16:23:52
 * @Entity TenantMaterialPO
 */
@BaseDao
public interface TenantMaterialMapper extends BaseMapper<TenantMaterialPO> {

    List<LatestTenantMaterialPO> latestMaterialByFolderIds(@Param("tenantCode") String tenantCode,
            @Param("folderIds") Set<Long> folderIds, @Param("rowNumber") Integer rowNumber,
            @Param("materialType") Integer materialType);

}




