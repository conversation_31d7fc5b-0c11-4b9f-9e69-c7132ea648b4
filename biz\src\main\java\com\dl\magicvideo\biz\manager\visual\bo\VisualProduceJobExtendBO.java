package com.dl.magicvideo.biz.manager.visual.bo;

import lombok.Data;

import java.util.Date;

/**
 * DTO: 作品扩展信息表
 */
@Data
public class VisualProduceJobExtendBO {

    /**
     * 作品jd
     */
    private Long bizId;

    /**
     * @see: ShareConfStateEnum
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     */
    private Integer shareConfState;

    /**
     * 推荐使用状态 0-未启用 1-已启用
     */
    private Integer recommendState;

    /**
     * 推荐启用的时间
     */
    private Date recommendEnableDt;

    /**
     * 数字人视频合成方式。 0-模板每个卡片合成1次请求数字人合成视频，并通过ASR识别时间戳。1-模板每个卡片都请求数字人合成视频方式。
     */
    private Integer dmProduceMode;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 外部用户id
     */
    private String extUserId;

    /**
     * 作品类型，1-常规作品，2-数据图表作品
     * @see: com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum
     */
    private Integer type;

    /**
     * 尺寸
     */
    private String resolution;
}
