package com.dl.magicvideo.biz.manager.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.PageQueryDO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.dal.statistics.StatisticsCountMapper;
import com.dl.magicvideo.biz.dal.statistics.param.TenantStatisticsTotalDurationQueryParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxTenantCodeParam;
import com.dl.magicvideo.biz.dal.statistics.param.TotalCountParam;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsTotalCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.TenantStatisticsTotalDurationPO;
import com.dl.magicvideo.biz.manager.statistics.StatisticsCountManager;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsCountDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【visual_ai_job】的数据库操作Service实现
 * @createDate 2023-06-08 16:23:52
 */
@Service
@Slf4j
public class StatisticsCountManagerImpl extends ServiceImpl<StatisticsCountMapper, StatisticsCountPO>
        implements StatisticsCountManager {
    @Override
    public ResponsePageQueryDO<List<StatisticsCountDTO>> pageQuery(PageQueryDO pageQueryDO) {
        ResponsePageQueryDO<List<StatisticsCountDTO>> response = new ResponsePageQueryDO<>();
        LambdaQueryWrapper<StatisticsCountPO> queryWrapper = Wrappers.lambdaQuery(StatisticsCountPO.class);
        queryWrapper.orderByDesc(StatisticsCountPO::getId);

        IPage<StatisticsCountPO> pageResult = baseMapper.selectPage(convert(pageQueryDO), queryWrapper);
        List<StatisticsCountPO> data = pageResult.getRecords();
        log.debug("StatisticsCountManager.baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        List<StatisticsCountDTO> dtos = new ArrayList<>(data.stream().map(po -> {
            StatisticsCountDTO dto = new StatisticsCountDTO();
            dto.setId(po.getId());
            dto.setStatisticsTime(po.getStatisticsTime());
            dto.setStatisticsValue(po.getStatisticsValue());
            dto.setTenantCode(po.getTenantCode());
            return dto;
        }).collect(Collectors.toList()));
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        response.setDataResult(dtos);
        return response;
    }

    @Override
    public List<StatisticsCountTopMaxPO> topMaxTenantCode(TopMaxTenantCodeParam param) {
        return this.baseMapper.topMaxTenantCode(param);
    }

    @Override
    public List<StatisticsTotalCountPO> totalCount(TotalCountParam param) {
        return this.baseMapper.totalCount(param);
    }

    @Override
    public List<StatisticsMsgPO> topMaxMsg(TopMaxMsgParam param) {
        return this.baseMapper.topMaxMsg(param);
    }

    @Override
    public TenantStatisticsTotalDurationPO queryTenantTotalDuration(TenantStatisticsTotalDurationQueryParam param) {
        return this.baseMapper.queryTenantTotalDuration(param);
    }
}




