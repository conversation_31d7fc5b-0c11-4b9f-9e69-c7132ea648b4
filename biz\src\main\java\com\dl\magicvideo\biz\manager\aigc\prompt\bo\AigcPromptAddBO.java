package com.dl.magicvideo.biz.manager.aigc.prompt.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 09:29
 */
@Data
public class AigcPromptAddBO {

    /**
     * 提示名称
     */
    private String name;

    /**
     * 提示排序
     */
    private Integer sort;

    /**
     * 场景，1-热点咨询 2-热点题材事件
     *
     * @see:com.dl.magicvideo.biz.manager.aigc.prompt.enums.AigcPromptScene
     */
    private Integer scene;

    /**
     * 关联文件
     */
    private AigcPromptFileBO relFile;

    /**
     * 内容
     */
    private AigcPromptContentBO content;

    public Date createDt;

    public Long createBy;

    public Date modifyDt;

    public Long modifyBy;

}
