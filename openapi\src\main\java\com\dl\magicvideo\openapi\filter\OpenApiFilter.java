package com.dl.magicvideo.openapi.filter;

import cn.hutool.extra.servlet.ServletUtil;
import com.dl.magicvideo.biz.common.constant.OpenApiConstant;
import com.dl.magicvideo.openapi.config.ApiWrapper;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 扫描包路径，凡是在/openapi/*下的接口都会被拦截
 */
@WebFilter(urlPatterns = "/openapi/*", filterName = "apiFilter")
public class OpenApiFilter extends GenericFilterBean {

    private static final String ERROR_RETHROW = "/visual/openapi/exception/rethrow";

    private static final String CALL_BACK_URL = "callback";
    private static final String UPLOAD_URL = "upload";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            //这里只针对post请求，也可其它请求
            if (ServletUtil.METHOD_POST.equals(httpServletRequest.getMethod())) {
                try {
                    ApiWrapper requestWrapper = new ApiWrapper((HttpServletRequest) request);
                    chain.doFilter(requestWrapper, response);
                } catch (Exception e) {
                    //由于Filter异常SpringBoot无法进行统一处理，所以需要手动处理一下异常
                    //捕获异常栈，然后转发到ErrorController
                    //然后被com.dl.framework.core.interceptor.ExceptionResolver异常统一处理
                    request.setAttribute(OpenApiConstant.OPENAPI_EXCEPTION, e);
                    request.getRequestDispatcher(ERROR_RETHROW).forward(request, response);
                }
            }
        }
    }
}
