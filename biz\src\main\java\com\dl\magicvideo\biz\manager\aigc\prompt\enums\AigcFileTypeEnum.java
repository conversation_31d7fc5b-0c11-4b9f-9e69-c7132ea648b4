package com.dl.magicvideo.biz.manager.aigc.prompt.enums;

/**
 * aigc文件的类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 15:46
 */
public enum AigcFileTypeEnum {

    //为便于前后端处理，文件类型枚举值与AigcChatRecordContentTypeEnum中的文件类型枚举值保持一致。
    //2-pdf 3-doc 4-xlsx 5-ppt 6-txt 7-图片
    PDF(2, "pdf"),
    DOC(3, "doc"),
    XLSX(4, "xlsx"),
    PPT(5, "ppt"),
    TXT_FILE(6, "txt"),
    IMG(7, "图片"),
    ;

    private Integer type;

    private String desc;

    AigcFileTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
