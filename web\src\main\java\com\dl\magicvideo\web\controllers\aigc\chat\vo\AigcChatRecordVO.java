package com.dl.magicvideo.web.controllers.aigc.chat.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 15:01
 */
@Data
@ApiModel("aigc-聊天记录视图对象")
public class AigcChatRecordVO {

    @ApiModelProperty("记录id")
    private String recordId;

    /**
     * aigc聊天记录类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum
     */
    @ApiModelProperty("aigc聊天记录类型，1-发送，2-接收，3-系统消息")
    private Integer type;

    /**
     * 聊天内容
     * 当内容类型是1:文本时，存的是字符串
     * 当内容类型是2~7:文件时，存的是AigcChatRecordContentFileBO对应的json串
     * 当内容类型是8:视频合成成功时，存的是AigcChatRecordProduceSuccessBO对应的json串
     * 当内容类型是9:视频合成失败时，存的是AigcChatRecordProduceFailBO对应的json串
     * 当内容类型是10:视频合成中时，存的是AigcChatRecordProduceIngBO对应的json串
     * 当内容类型是11:热点事件题材提问时，存的是AigcChatRecordHotEventSubjectMatterAskBO对应的json串
     * 当内容类型是12:热点事件题材回答时，存的是AigcChatRecordHotEventSubjectMatterAnswerBO对应的json串
     * 当内容类型是13:文本转视频时，存的是AigcChatRecordText2VideoBO对应的json串
     */
    @ApiModelProperty("聊天内容")
    private String content;

    /**
     * 聊天内容类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum
     */
    @ApiModelProperty(
            "聊天内容类型,1-文本 2-pdf 3-doc 4-xlsx 5-ppt 6-txt 7-图片 8-视频合成成功 9-视频合成失败 10-视频合成中 11-热点事件题材提问 12-热点事件题材回答 13-文本转视频")
    private Integer contentType;

    /**
     * 消息发送时间
     */
    @ApiModelProperty("消息发送时间")
    private Date sendDt;

    @ApiModelProperty("是否能生产视频")
    private Integer canProduce;

    /**
     * 聊天记录的来源是哪个工具
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum
     */
    @ApiModelProperty("聊天记录的来源是哪个工具 1-通用工具（AI创作、文本转视频）2-热点事件题材工具")
    private Integer fromTool;
}
