package com.dl.magicvideo.biz.mq.temporary;

import com.dl.magicvideo.biz.manager.visual.dto.preview.PreviewCardDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-16 15:27
 */
@Data
public class VisualMergeDmJobAndAsrExtTempBO extends VisualMergeDmJobExtTempBO {
    private static final long serialVersionUID = -7387384222757659258L;

    /**
     * 脚本中的开头停顿时长
     */
    private Long scriptStartBreakTime;

    /**
     * 脚本中的结尾停顿时长
     */
    private Long scriptEndBreakTime;

    /**
     * asr中的开始时间
     */
    private Integer asrStartTime;

    /**
     * asr中的结尾时间
     */
    private Integer asrEndTime;

    /**
     * 字幕
     */
    private List<PreviewCardDTO.SubtitleDTO> subtitleDTOList;
}
