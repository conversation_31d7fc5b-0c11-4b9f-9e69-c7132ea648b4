package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName visual_card
 */
@TableName(value ="visual_card")
@Data
public class VisualCardPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 卡片id
     */
    private Long cardId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 卡片标题
     */
    private String name;

    /**
     * 卡片封面
     */
    private String coverUrl;

    /**
     * 尺寸
     */
    private String resolution;

    /**
     * 时长，单位秒
     */
    private Long duration;

    /**
     * 0-启用 1-禁用
     */
    private Integer status;

    /**
     * 
     */
    private String renderData;

    /**
     * 轻编辑配置
     */
    private String lightEditConfigs;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}