package com.dl.magicvideo.web.controllers.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: PreviewVO
 * @author: zhousx
 * @date: 2023/4/25 16:23
 */
@Data
public class PreviewVO {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("横版/竖版")
    private String resolutionType;

    @ApiModelProperty("背景音乐")
    private String bgMusic;

    @ApiModelProperty("背景音乐配置")
    private String bgMusicParam;

    @ApiModelProperty("tts配置")
    private String ttsParam;

    @ApiModelProperty("替换数据")
    private String replaceData;

    @ApiModelProperty("接口数据")
    private String apiData;

    @ApiModelProperty("来源 0-平台触发(即页面点击合成按钮) 1-批量合成接口触发 2-excel批量合成接口触发 3-开放平台合成接口触发 4-内部合成接口触发 5-交付计划触发")
    private Integer source;

    @ApiModelProperty("卡片数据")
    private List<PreviewCardVO> cards;
    /**
     * 组件版本号，默认2.0.0
     */
    private String componentVersion;
}
