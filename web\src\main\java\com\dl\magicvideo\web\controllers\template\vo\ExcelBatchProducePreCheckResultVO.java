package com.dl.magicvideo.web.controllers.template.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-08 10:50
 */
@Data
@ApiModel("excel批量合成视频的前置校验结果")
public class ExcelBatchProducePreCheckResultVO {

    @ApiModelProperty("总行数")
    private Integer totalRows;

    @ApiModelProperty("有空数据的行号集合")
    private Set<Integer> blankDataRows;

    @ApiModelProperty("校验通过的令牌")
    private String checkPassToken;

}
