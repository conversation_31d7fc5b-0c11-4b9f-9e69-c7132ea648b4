package com.dl.magicvideo.biz.dal.statistics.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName statistics_efficiency
 */
@TableName(value ="statistics_efficiency")
@Data
public class StatisticsEfficiencyPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计ID
     */
    private Long efficiencyId;

    /**
     * 统计value
     */
    private String statisticsValue;

    /**
     * 统计时间
     */
    private String statisticsTime;


    /**
     * 统计维度，1.从用户点击合成到视频合成成功 2.视频开始合成到视频合成成功
     */
    private Integer type;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}