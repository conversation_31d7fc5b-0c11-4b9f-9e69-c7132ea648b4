package com.dl.magicvideo.biz.common.enums;

import java.util.Objects;

/**
 * @describe: ReplaceTypeE
 * @author: zhousx
 * @date: 2023/3/1 9:30
 */
public enum ReplaceTypeE {
    TTS("TTS", "文字转语音"),

    ASR("ASR", "语音转文字"),;

    private String code;

    private String desc;

    ReplaceTypeE(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public static ReplaceTypeE getByCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (ReplaceTypeE e : ReplaceTypeE.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
