package com.dl.magicvideo.biz.manager.cos.impl;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyuncs.exceptions.ClientException;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

import java.io.File;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2025/8/28 10:30
 */
public class Test {
    public static void main(String[] args) throws Exception {
// yourEndpoint填写Bucket所在地域对应的Endpoint。
        String endpoint = "https://oss-cn-shanghai.aliyuncs.com";

        // 填写Endpoint对应的Region信息，例如cn-hangzhou。
        String region = "cn-shanghai";
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        DefaultCredentialProvider credentialsProvider = null;
        try {
            credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider("LTAI5tJSisnsWjGXMEYvf7Hb", "******************************");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 指定Bucket名称。
        String bucketName = "pelotavatar";
        // 如果视频位于Bucket根目录，则直接填写视频名称。如果视频不在Bucket根目录，需携带视频完整路径，例如exampledir/example.mp4。
        String key = "test/a1.webm";

        // 创建OSSClient实例。
        // 当OSSClient实例不再使用时，调用shutdown方法以释放资源。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();

        try {
            // 构建视频信息提取的处理指令。
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, key);
            getObjectRequest.setProcess("video/info");

            // 使用getObject方法，并通过process参数传入处理指令。
            OSSObject ossObject = ossClient.getObject(getObjectRequest);

            // 读取并打印视频信息。
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = ossObject.getObjectContent().read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            String videoInfo = baos.toString("UTF-8");
            System.out.println("Video Info:");
            System.out.println(videoInfo);
        } catch (IOException e) {
            System.out.println("Error: " + e.getMessage());
        } finally {
            // 关闭OSSClient。
            ossClient.shutdown();
        }
    }
}
