package com.dl.magicvideo.biz.manager.visual.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: TemplateAuthBO
 * @author: zhousx
 * @date: 2023/6/18 22:03
 */
@Data
public class TemplateAuthBO {
    private Long templateId;

    private String name;

    private String coverUrl;

    private String previewVideoUrl;

    private List<Tenant> tenants;

    private Integer isManager;

    private Integer firstCategory;

    private Integer secondCategory;

    @ApiModelProperty("已存在的标签列表")
    List<Long> tagIds;

    @ApiModelProperty("新增的标签名")
    List<String> tagNames;

    @Data
    public static class Tenant {
        private String tenantCode;

        private String tenantName;
    }
}
