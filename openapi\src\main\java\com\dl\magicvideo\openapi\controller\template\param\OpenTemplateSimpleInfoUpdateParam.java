package com.dl.magicvideo.openapi.controller.template.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 11:50
 */
@Data
public class OpenTemplateSimpleInfoUpdateParam {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 外部用户id
     */
    private String extUserId;

    /**
     * 模板名称
     */
    @Length(min = 0, max = 20, message = "模板名称不得超过20个字符")
    private String name;

    /**
     * 模板封面
     */
    private String coverUrl;
}
