package com.dl.magicvideo.web.controllers.internal.auth.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-15 14:02
 */
@Data
@ApiModel("修改来源模板")
public class ChangeSourceTemplateParam {

    @NotBlank(message = "模板id不能为空")
    @ApiModelProperty(value = "模板id")
    private String templateId;

    @NotBlank(message = "来源模板id不能为空")
    @ApiModelProperty(value = "新的来源模板id")
    private String newSourceTemplateId;

}