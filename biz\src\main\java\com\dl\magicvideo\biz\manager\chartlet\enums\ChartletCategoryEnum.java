package com.dl.magicvideo.biz.manager.chartlet.enums;


import java.util.Objects;

/**
 * 贴图分类枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-26 17:58
 */
public enum ChartletCategoryEnum {

    STOCK_MARKET_BOND_BASIS(1, "股市债基", 1),
    MONEY(2, "钱币钞票", 2),
    INDICATE(3, "指示", 3),
    SHELTER(4, "遮挡", 4),
    TITLE_BAR(5, "标题栏", 5),
    TEXT_BOX(6, "文本框", 6),
    NAME_BOX(7, "姓名框", 7),
    BUBBLE_BOX(8, "气泡框", 8),
    INTERACTION(9, "互动", 9);

    private Integer code;

    private String name;

    private Integer sort;

    ChartletCategoryEnum(Integer code, String name, Integer sort) {
        this.code = code;
        this.name = name;
        this.sort = sort;
    }

    public static ChartletCategoryEnum parse(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (ChartletCategoryEnum categoryEnum : ChartletCategoryEnum.values()) {
            if (code.equals(categoryEnum.getCode())) {
                return categoryEnum;
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code){
        if (Objects.isNull(code)) {
            return "";
        }
        for (ChartletCategoryEnum categoryEnum : ChartletCategoryEnum.values()) {
            if (code.equals(categoryEnum.getCode())) {
                return categoryEnum.getName();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getSort() {
        return sort;
    }
}
