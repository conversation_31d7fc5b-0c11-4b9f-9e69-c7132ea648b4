package com.dl.magicvideo.web.controllers.produce;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.VisualProduceBatchManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceBatchSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.ProduceBatchDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.produce.convert.VisualProduceConvert;
import com.dl.magicvideo.web.controllers.produce.param.ProduceBatchPageQueryParam;
import com.dl.magicvideo.web.controllers.produce.param.ProduceJobPageQueryParam;
import com.dl.magicvideo.web.controllers.produce.vo.DailyProduceStatisticsVO;
import com.dl.magicvideo.web.controllers.produce.vo.ProduceBatchVO;
import com.dl.magicvideo.web.controllers.produce.vo.ProduceJobTemplateVO;
import com.dl.magicvideo.web.controllers.produce.vo.ProduceJobVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @describe: VisualProduceProcess
 * @author: zhousx
 * @date: 2023/6/19 17:23
 */
@Slf4j
@Component
public class VisualProduceProcess extends AbstractController {
    @Value("${visual.produce.estimate-cost-ratio}")
    private float estimateCostRatio;
    @Autowired
    private VisualProduceBatchManager visualProduceBatchManager;
    @Autowired
    private VisualProduceJobManager visualProduceJobManager;
    @Autowired
    private VisualTemplateManager visualTemplateManager;

    @Autowired
    private OperatorUtil operatorUtil;

    public ResultPageModel<ProduceBatchVO> batchList(ProduceBatchPageQueryParam param) {
        ProduceBatchSearchBO bo = new ProduceBatchSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setStatusList(param.getStatusList());
        bo.setTenantCodeList(param.getTenantCodeList());
        bo.setStartTime(param.getStartTime());
        bo.setEndTime(param.getEndTime());

        if (CollectionUtils.isEmpty(bo.getTenantCodeList())){
            ArrayList<String> tenantCodeList = Lists.newArrayList();
            tenantCodeList.add(operatorUtil.getTenantCode());
            bo.setTenantCodeList(tenantCodeList);
        }
        ResponsePageQueryDO<List<ProduceBatchDTO>> result = visualProduceBatchManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<ProduceBatchVO> vos = result.getDataResult().stream().map(dto -> {
            ProduceBatchVO vo = new ProduceBatchVO();
            vo.setStatus(dto.getStatus());
            vo.setBatchId(dto.getBatchId() + "");
            vo.setBatchName(dto.getBatchName());
            vo.setCreateDt(dto.getCreateDt());
            vo.setTenantCode(dto.getTenantCode());
            vo.setTenantName(dto.getTenantName());
            vo.setCreatorName(dto.getCreatorName());
            vo.setJobTotalNum(dto.getJobNumTotal());
            vo.setJobSuccessNum(dto.getJobNumSuccess());
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    public ResultPageModel<ProduceJobVO> jobListByBatchId(ProduceJobPageQueryParam param) {
        ProduceJobSearchBO bo = new ProduceJobSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setBatchId(Long.valueOf(param.getBatchId()));
        bo.setStatusList(param.getStatusList());
        bo.setEncludeDeleted(true);

        ResponsePageQueryDO<List<VisualProduceJobDTO>> result = visualProduceJobManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<VisualTemplatePO> templates = visualTemplateManager.lambdaQuery().in(VisualTemplatePO::getTemplateId, result.getDataResult().stream().map(VisualProduceJobDTO::getTemplateId).distinct().collect(Collectors.toList())).list();
        Map<Long, String> templateMap = templates.stream().collect(Collectors.toMap(VisualTemplatePO::getTemplateId, VisualTemplatePO::getName));

        List<ProduceJobVO> vos = result.getDataResult().stream().map(dto -> {
            ProduceJobVO vo = new ProduceJobVO();
            vo.setJobId(dto.getJobId() + "");
            vo.setTemplateId(dto.getTemplateId() + "");
            vo.setName(dto.getName());
            vo.setCoverUrl(dto.getCoverUrl());
            vo.setStatus(dto.getStatus());
            vo.setVideoUrl(dto.getVideoUrl());
            vo.setCreateDt(dto.getCreateDt());
            vo.setTemplateName(templateMap.get(dto.getTemplateId()));
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    public ResultModel<ProduceJobVO> jobDetail(Long jobId) {
        VisualProduceJobPO job = visualProduceJobManager
                .getOne(Wrappers.lambdaQuery(VisualProduceJobPO.class).eq(VisualProduceJobPO::getJobId, jobId)
                        .select(VisualProduceJobPO.class,
                                po -> !"preview_data".equals(po.getColumn()) && !"template_data".equals(po.getColumn())
                                        && !"api_data".equals(po.getColumn())));
        Assert.notNull(job, "视频不存在");

        VisualTemplatePO templatePO = visualTemplateManager.getOne(Wrappers.lambdaQuery(VisualTemplatePO.class)
                .eq(VisualTemplatePO::getTemplateId, job.getTemplateId()));

        ProduceJobVO vo = new ProduceJobVO();
        VisualProduceConvert.fillProduceJobVO(job, templatePO, vo);
        return ResultModel.success(vo);
    }

    public ResultModel<Void> cancelBatch(Long batchId) {
        visualProduceBatchManager.cancel(batchId);
        return ResultModel.success(null);
    }

    public ResultModel<DailyProduceStatisticsVO> dailyStatistics() {
        List<VisualProduceJobPO> jobs = visualProduceJobManager.lambdaQuery().select(VisualProduceJobPO::getJobId, VisualProduceJobPO::getStatus, VisualProduceJobPO::getTemplateId)
                .eq(VisualProduceJobPO::getTenantCode, operatorUtil.getTenantCode())
                .ge(VisualProduceJobPO::getCreateDt, DateUtil.getMinDate(new Date())).list();
        DailyProduceStatisticsVO vo = new DailyProduceStatisticsVO();
        if(CollectionUtils.isEmpty(jobs)) {
            return ResultModel.success(vo);
        }
        List<VisualProduceJobPO> queuedJobs = jobs.stream().filter(job -> Objects.equals(job.getStatus(), JobStatusE.INIT.getCode()) || Objects.equals(job.getStatus(), JobStatusE.READY.getCode())).collect(Collectors.toList());
        List<VisualProduceJobPO> processJobs = jobs.stream().filter(job -> Objects.equals(job.getStatus(), JobStatusE.PROCESSING.getCode())).collect(Collectors.toList());
        vo.setQueuedNum(queuedJobs.size());
        vo.setSuccessNum((int) jobs.stream().filter(job -> Objects.equals(job.getStatus(), JobStatusE.SUCCESS.getCode())).count());
        vo.setFailedNum((int) jobs.stream().filter(job -> Objects.equals(job.getStatus(), JobStatusE.FAILED.getCode())).count());
        vo.setProcessNum(processJobs.size());

        List<VisualProduceJobPO> unCompleteList = new ArrayList<>();
        unCompleteList.addAll(queuedJobs);
        unCompleteList.addAll(processJobs);
        if(CollectionUtils.isNotEmpty(unCompleteList)) {
            List<VisualTemplatePO> templates = visualTemplateManager.lambdaQuery().in(VisualTemplatePO::getTemplateId, unCompleteList.stream().map(VisualProduceJobPO::getTemplateId).distinct().collect(Collectors.toList())).list();
            Map<Long, Long> templateMap = templates.stream().collect(Collectors.toMap(VisualTemplatePO::getTemplateId, VisualTemplatePO::getDuration));
            long estimateTime = 0L;
            for(VisualProduceJobPO jobPO:unCompleteList) {
                Long duration = templateMap.get(jobPO.getTemplateId());
                if(Objects.nonNull(duration)) {
                    estimateTime += duration * estimateCostRatio;
                }
            }
            vo.setEstimateTime(estimateTime);
        }

        return ResultModel.success(vo);
    }

    public ResultModel<Void> deleteWorks(Long jobId) {
        VisualProduceJobPO job = visualProduceJobManager.lambdaQuery().eq(VisualProduceJobPO::getJobId, jobId).one();
        Assert.notNull(job, "作品不存在");

        job.setIsDeleted(Const.ONE);
        visualProduceJobManager.updateById(job);
        return ResultModel.success(null);
    }

    public ResultModel<ProduceJobTemplateVO> jobDetailWithTemplateSnapshot(Long jobId) {
        VisualProduceJobPO job = visualProduceJobManager.lambdaQuery().eq(VisualProduceJobPO::getJobId, jobId)
                .eq(VisualProduceJobPO::getTenantCode, operatorUtil.getTenantCode())
                .eq(VisualProduceJobPO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(job, "视频不存在");

        VisualTemplatePO templatePO = visualTemplateManager.getOne(Wrappers.lambdaQuery(VisualTemplatePO.class)
                .eq(VisualTemplatePO::getTemplateId, job.getTemplateId()));

        ProduceJobTemplateVO vo = new ProduceJobTemplateVO();
        VisualProduceConvert.fillProduceJobVO(job, templatePO, vo);
        vo.setTemplateData(job.getTemplateData());

        return ResultModel.success(vo);
    }
}
