package com.dl.magicvideo.web.controllers.internal.auth.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe: TemplateAuthVO
 * @author: zhousx
 * @date: 2023/6/18 23:37
 */
@Data
public class TemplateAuthDTO {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("横版/竖版")
    private String resolutionType;

    @ApiModelProperty("背景音乐")
    private String bgMusic;

    @ApiModelProperty("背景音乐配置")
    private String bgMusicParam;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty("短视频")
    private String shortVideoUrl;

    @ApiModelProperty("tts配置")
    private String ttsParam;

    @ApiModelProperty("替换数据")
    private String replaceData;

    @ApiModelProperty("状态 0-启用 1-禁用")
    private Integer status;

    @ApiModelProperty("创建人姓名")
    private String creatorName;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("模板时长 毫秒")
    private Long duration;

    @ApiModelProperty("授权租户列表")
    private List<Tenant> authTenantList;
    @ApiModelProperty("修改人姓名")
    private String modifyName;

    @ApiModelProperty("修改时间")
    private Date modifyDt;

    @ApiModelProperty(value = "来源模板id")
    private String sourceTemplateId;

    @Data
    public static class Tenant {
        private String tenantCode;

        private String tenantName;
    }
}
