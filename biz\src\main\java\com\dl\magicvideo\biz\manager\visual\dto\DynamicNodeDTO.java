package com.dl.magicvideo.biz.manager.visual.dto;

import com.dl.magicvideo.biz.manager.visual.bo.DynamicNodeBO;
import lombok.Data;

import java.util.List;

/**
 * @describe: DynamicNodeBO
 * @author: zhousx
 * @date: 2023/4/25 17:38
 */
@Data
public class DynamicNodeDTO {
    private Long nodeId;

    private String type;

    private Long duration;

    private String coverUrl;

    private Integer isEnabled;

    private List<TtsConfigDTO> ttsList;

    private List<DigitalManConfigDTO> dmList;

    private List<VideoConfigDTO> videoList;

    private List<AudioConfigDTO> audioList;

    private List<DataSheetDTO> dataSheetList;

    private String expression;

    private Integer expressionFlag;

    @Data
    public static class BaseConfigDTO{

        /**
         * 入场延迟
         */
        private Long start;

        /**
         * 出场延迟
         */
        private Long endDelay;

        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;
    }

    @Data
    public static class TtsConfigDTO extends BaseConfigDTO{
        private String ttsId;

        private String content;

        private Integer enableSubtitle;

        private Integer maxLength;

        /**
         * 是否需要字幕中关键词高亮，0-否，1-是
         */
        private Integer subtitleKeyWordsHighlight;
    }

    @Data
    public static class DigitalManConfigDTO extends BaseConfigDTO{
        private String dmId;

        private String content;

        private Integer enableSubtitle;

        private Integer maxLength;

    }

    @Data
    public static class VideoConfigDTO extends BaseConfigDTO{
        /**
         * id
         */
        private String videoId;

        /**
         * 视频链接
         */
        private String url;

        /**
         * 音量
         */
        private String volume;

        /**
         * 时长，单位毫秒
         */
        private Long duration;

        /**
         * 裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频裁剪开始的时间
         */
        private Long startDelay;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失
         */
        private String activeRotationMode;
    }

    @Data
    public static class AudioConfigDTO extends BaseConfigDTO {
        /**
         * id
         */
        private String audioId;

        /**
         * 视频链接
         */
        private String url;

        /**
         * 音量
         */
        private String volume;

        /**
         * 时长，单位毫秒
         */
        private Long duration;

        /**
         * 裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频裁剪开始的时间
         */
        private Long startDelay;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失
         */
        private String activeRotationMode;
        /**
         * 淡入时间
         */
        private String fadeInTime;

        /**
         * 淡出时间
         */
        private String fadeOutTime;
    }

    @Data
    public static class DataSheetDTO extends BaseConfigDTO {
        private String dataSheetId;

        /**
         * 内容 {"replaceKey":"替换变量key"}
         */
        private String content;

        /**
         * 时长，单位毫秒
         */
        private Long duration;
    }
}
