package com.dl.magicvideo.web.controllers.aigc.chat.convert;

import com.dl.magicvideo.biz.es.aigc.chatrecord.po.EsIndexAigcChatRecord;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;
import com.dl.magicvideo.web.controllers.aigc.chat.param.AigcAddChatRecordParam;
import com.dl.magicvideo.web.controllers.aigc.chat.param.AigcChatSendMessageParam;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AigcChatRecordVO;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 15:11
 */
public class AigcChatRecordConvert {

    public static AigcChatRecordVO cnvEsIndexAigcChatRecord2AigcChatRecordVO(EsIndexAigcChatRecord input) {
        AigcChatRecordVO result = new AigcChatRecordVO();
        result.setRecordId(String.valueOf(input.getRecordId()));
        result.setType(input.getType());
        result.setContent(input.getContent());
        result.setContentType(input.getContentType());
        result.setSendDt(input.getSendDt());
        result.setCanProduce(input.getCanProduce());
        result.setFromTool(input.getFromTool());
        return result;
    }

    public static List<AigcChatRecordAddBO> buildAigcChatRecordAddBOList(List<AigcChatSendMessageParam> inputList,
            Long userId, String tenantCode, Integer recordType, Integer fromTool) {
        Date now = new Date();
        List<AigcChatRecordAddBO> resultList = inputList.stream().map(input -> {
            AigcChatRecordAddBO result = buildAigcChatRecordAddBO(userId, tenantCode, recordType, now, input, fromTool);
            return result;
        }).collect(Collectors.toList());
        return resultList;
    }

    public static AigcChatRecordAddBO buildAigcChatRecordAddBO(Long userId, String tenantCode, Integer recordType,
            Date now, AigcChatSendMessageParam input, Integer fromTool) {
        AigcChatRecordAddBO result = new AigcChatRecordAddBO();
        result.setUserId(userId);
        result.setTenantCode(tenantCode);
        result.setContent(input.getContent());
        result.setContentType(input.getContentType());
        result.setType(recordType);
        result.setSendDt(now);
        result.setCanProduce(input.getCanProduce());
        result.setFromTool(fromTool);
        return result;
    }

    public static AigcChatRecordAddBO cnvAigcAddChatRecordParam2BO(AigcAddChatRecordParam input, Long userId,
            String tenantCode, Integer fromTool) {
        AigcChatRecordAddBO result = new AigcChatRecordAddBO();
        result.setUserId(userId);
        result.setTenantCode(tenantCode);
        result.setType(input.getType());
        result.setContent(input.getContent());
        result.setContentType(input.getContentType());
        result.setSendDt(new Date());
        result.setCanProduce(input.getCanProduce());
        result.setFromTool(fromTool);
        return result;
    }
}
