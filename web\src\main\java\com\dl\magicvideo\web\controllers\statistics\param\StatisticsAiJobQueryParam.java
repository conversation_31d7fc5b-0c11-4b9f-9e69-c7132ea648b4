package com.dl.magicvideo.web.controllers.statistics.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:32
 */
@Data
public class StatisticsAiJobQueryParam {

    @NotNull(message = "最小时间不能为空")
    @ApiModelProperty("最小时间")
    private Date minDt;

    @NotNull(message = "最大时间不能为空")
    @ApiModelProperty("最大时间")
    private Date maxDt;

    @NotNull(message = "ai任务类型不能为空")
    @ApiModelProperty("ai任务类型，1-数字人，2-TTS")
    private Integer aiJobType;

}
