package com.dl.magicvideo.web.controllers.internal.visual.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class VisualAiConfigInternalVO {

    /**
     * tts_id
     */
    private String ttsId;

    /**
     * 所属节点id
     */
    private Long nodeId;

    /**
     * 所属卡片id
     */
    private Long cardId;

    /**
     * 所属模板id
     */
    private Long templateId;

    /**
     *
     */
    private Integer type;

    /**
     * 入场延迟
     */
    private Long start;

    /**
     *
     */
    private String content;

    /**
     * 是否开启字幕，0-否 1-是
     */
    private Integer enableSubtitle;

    /**
     * 字幕分段最大长度
     */
    private Integer maxLength;

    /**
     * 出场延迟
     */
    private Long endDelay;

    /**
     * 隐藏
     */
    private Integer isHide;

}
