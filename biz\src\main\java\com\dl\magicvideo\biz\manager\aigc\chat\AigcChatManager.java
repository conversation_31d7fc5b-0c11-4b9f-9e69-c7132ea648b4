package com.dl.magicvideo.biz.manager.aigc.chat;

import com.dl.magicvideo.biz.manager.aigc.chat.bo.AiFileContentAndTitleRespBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcMultiChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatResponseBO;

import java.io.File;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 17:46
 */
public interface AigcChatManager {

    /**
     * 单条对话
     *
     * @param tenantCode
     * @param requestBO
     * @return
     */
    AigcSingleChatResponseBO singleChat(String tenantCode, AigcSingleChatRequestBO requestBO);

    /**
     * 流式地单条对话
     *
     * @param tenantCode
     * @param requestBO
     * @return
     */
    InputStream singleChatStream(String tenantCode, AigcSingleChatRequestBO requestBO);

    /**
     * 提取文件内容和标题
     *
     * @param tenantCode     必传
     * @param userId         必传
     * @param presupposeText 可空
     * @param file           必传
     * @return
     */
    AiFileContentAndTitleRespBO extractFileContentAndTitle(String tenantCode, Long userId, String presupposeText,
            File file);

    /**
     * 多条对话，最后的响应以流式返回
     *
     * @param tenantCode
     * @param requestBO
     * @return
     */
    InputStream multiChatFinalStream(String tenantCode, AigcMultiChatRequestBO requestBO);
}
