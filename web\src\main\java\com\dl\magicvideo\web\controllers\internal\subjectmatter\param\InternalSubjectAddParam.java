package com.dl.magicvideo.web.controllers.internal.subjectmatter.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class InternalSubjectAddParam {
    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 标题名称
     */
    @NotBlank(message = "标题名称不能为空")
    private String title;

    /**
     * 题材名称
     */
    private String name;

    /**
     * 简介
     */
    private String intro;

    /**
     * XMind文件图片地址
     */
    @NotBlank(message = "XMind图片地址不能为空")
    private String imgUrl;

    /**
     * XMind文件地址
     */
    @NotBlank(message = "XMind文件地址不能为空")
    private String xmindUrl;

    /**
     * 父级题材bizId
     */
    private Long parentId;

    /**
     * XMind文件
     */
    @NotNull(message = "XMind文件不能为空")
    private XMindParam xmindParam;

    /**
     * XMind文件json
     */
    private String xmindStr;

    /**
     * 关联素材
     */
    private List<MaterialAddParam> materialList;

    /**
     * prompt
     */
    private String prompt;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人id
     */
    private Long operatorId;
}
