package com.dl.magicvideo.biz.common.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-02 14:44
 */
@Component
public class EnvUtil {

    @Autowired
    private Environment env;

    public boolean isDev() {
        List<String> list = Arrays.stream(env.getActiveProfiles()).collect(Collectors.toList());
        return list.contains("dev") || list.contains("cdev");
    }

    public boolean isTest() {
        List<String> list = Arrays.stream(env.getActiveProfiles()).collect(Collectors.toList());
        return list.contains("test");
    }

    public boolean isProd() {
        List<String> list = Arrays.stream(env.getActiveProfiles()).collect(Collectors.toList());
        return list.contains("prod");
    }
}
