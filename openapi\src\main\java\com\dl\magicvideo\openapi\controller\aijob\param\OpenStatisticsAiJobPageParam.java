package com.dl.magicvideo.openapi.controller.aijob.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:32
 */
@Data
public class OpenStatisticsAiJobPageParam extends AbstractPageParam {

    @NotNull(message = "最小时间不能为空")
    @ApiModelProperty("最小时间")
    private Date minDt;

    @NotNull(message = "最大时间不能为空")
    @ApiModelProperty("最大时间")
    private Date maxDt;

}
