package com.dl.magicvideo.web.controllers.internal.visual;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualShareConfPO;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualShareConfManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualShareConfDTO;
import com.dl.magicvideo.web.controllers.internal.visual.convert.VisualProduceJobInternalConvert;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualProduceInternalBatchParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualProduceInternalParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualProduceJobGenerateParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualProduceJobInternalPageParam;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualCreateProduceJobInternalVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalDetailVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalPageVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalShareConfVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = { "/internal/producejob", "/visual/internal/producejob" })
public class VisualProduceJobInternalController {

    @Resource
    private VisualShareConfManager visualShareConfManager;

    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    @Resource
    private VisualProduceJobInternalProcess visualProduceJobInternalProcess;

    @NotLogin
    @PostMapping("/detail")
    @ApiOperation("查询数影作品详情以及转发配置")
    public ResultModel<VisualProduceJobInternalDetailVO> detail(@RequestBody @Validated VisualProduceInternalParam param) {
        VisualShareConfDTO visualShareConfDTO = visualShareConfManager.getProduceJobShareConfDetail(param.getBizId());
        return ResultModel.success(VisualProduceJobInternalConvert.cnvVisualShareConfDTO2InternalDetailVO(visualShareConfDTO));
    }

    @NotLogin
    @PostMapping("/batchinfo")
    @ApiOperation("批量查询数影作品信息")
    public ResultModel<List<VisualProduceJobInternalVO>> batchInfo(
            @RequestBody @Validated VisualProduceInternalBatchParam param) {
        List<VisualProduceJobPO> poList = visualProduceJobManager.list(Wrappers.lambdaQuery(VisualProduceJobPO.class)
                .in(VisualProduceJobPO::getJobId, param.getBizIdList()));
        return ResultModel.success(VisualProduceJobInternalConvert.cnvVisualProduceJobPOList2InternalVOList(poList));
    }

    @NotLogin
    @PostMapping("/batchqueryshareconf")
    @ApiOperation("批量查询转发配置")
    public ResultModel<List<VisualProduceJobInternalShareConfVO>> batchQueryShareConf(
            @RequestBody @Validated VisualProduceInternalBatchParam param) {
        List<VisualShareConfPO> poList = visualShareConfManager.list(Wrappers.lambdaQuery(VisualShareConfPO.class)
                .in(VisualShareConfPO::getBizId, param.getBizIdList()).eq(VisualShareConfPO::getIsDeleted, Const.ZERO));

        return ResultModel.success(
                poList.stream().map(VisualProduceJobInternalConvert::cnvSharePO2VO).collect(Collectors.toList()));
    }

    /**
     * 合成视频
     *
     * @param param
     * @return
     */
    @NotLogin
    @PostMapping("/generate")
    @ApiOperation("生成视频")
    public ResultModel<VisualCreateProduceJobInternalVO> generate(@RequestBody VisualProduceJobGenerateParam param) {
        return visualProduceJobInternalProcess.generate(param);
    }

    @NotLogin
    @PostMapping("/info")
    @ApiOperation("查询数影作品信息")
    public ResultModel<VisualProduceJobInternalVO> info(@RequestBody @Validated VisualProduceInternalParam param) {
        VisualProduceJobPO produceJobPO = visualProduceJobManager.getOne(Wrappers.lambdaQuery(VisualProduceJobPO.class)
                .eq(VisualProduceJobPO::getJobId, param.getBizId()));
        return ResultModel.success(VisualProduceJobInternalConvert.cnvVisualProduceJobPOList2InternalVO(produceJobPO));
    }

    @PostMapping("/page")
    @ApiOperation("分页查询作品")
    public ResultPageModel<VisualProduceJobInternalPageVO> page(
            @RequestBody @Validated VisualProduceJobInternalPageParam param) {
        return visualProduceJobInternalProcess.page(param);
    }
}
