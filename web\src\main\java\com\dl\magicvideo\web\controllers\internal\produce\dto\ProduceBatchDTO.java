package com.dl.magicvideo.web.controllers.internal.produce.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @describe: ProduceBatchVO
 * @author: zhousx
 * @date: 2023/6/19 17:36
 */
@Data
public class ProduceBatchDTO {
    @ApiModelProperty("批次id")
    private String batchId;

    @ApiModelProperty("批次名称")
    private String batchName;

    @ApiModelProperty("批次状态：0-排队中 1-生产中 2-生产完成 3-生产异常 4-已取消")
    private Integer status;

    @ApiModelProperty("作业总数")
    private Integer jobTotalNum;

    @ApiModelProperty("作业完成数")
    private Integer jobSuccessNum;

    @ApiModelProperty("租户id")
    private String tenantCode;

    @ApiModelProperty("租户名称")
    private String tenantName;

    @ApiModelProperty("生产人")
    private String creatorName;

    @ApiModelProperty("发起时间")
    private Date createDt;
}
