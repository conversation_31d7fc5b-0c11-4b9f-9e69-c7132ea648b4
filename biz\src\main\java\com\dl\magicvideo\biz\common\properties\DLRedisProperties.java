package com.dl.magicvideo.biz.common.properties;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "dl.redis")
@Data
@Configuration
public class DLRedisProperties {

    private String comName = "dl-";

    private String appName = "wealthcenter";

    public String getPrefix() {
        return comName + (StringUtils.isBlank(appName) ? "app" : appName) + "-";
    }
}
