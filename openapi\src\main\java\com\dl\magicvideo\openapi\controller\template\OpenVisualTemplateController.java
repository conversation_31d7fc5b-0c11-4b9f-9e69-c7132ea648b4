package com.dl.magicvideo.openapi.controller.template;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.openapi.aspect.annotation.ExtUserIdConvert;
import com.dl.magicvideo.openapi.controller.template.param.OpenTemplateDetailParam;
import com.dl.magicvideo.openapi.controller.template.param.OpenTemplatePageParam;
import com.dl.magicvideo.openapi.controller.template.param.OpenTemplateSimpleInfoUpdateParam;
import com.dl.magicvideo.openapi.controller.template.vo.OpenDigitalManVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenVisualTemplateDetailVO;
import com.dl.magicvideo.openapi.controller.template.vo.OpenVisualTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/openapi/template")
@Api("开放平台-模板管理")
public class OpenVisualTemplateController {

    @Resource
    private OpenVisualTemplateProcess openVisualTemplateProcess;

    @PostMapping("/detail")
    @ApiOperation("开放平台-模板详情")
    public ResultModel<OpenVisualTemplateDetailVO> detail(@RequestBody @Validated OpenTemplateDetailParam template) {
        return openVisualTemplateProcess.detail(Long.valueOf(template.getTemplateId()));
    }

    @PostMapping("/page")
    @ApiOperation("开放平台-分页查询模板")
    public ResultPageModel<OpenVisualTemplateVO> page(@RequestBody OpenTemplatePageParam param) {
        return openVisualTemplateProcess.page(param);
    }

    @PostMapping("/dmlist")
    @ApiOperation("数字人列表")
    public ResultModel<List<OpenDigitalManVO>> dmList() {
        return ResultModel.success(openVisualTemplateProcess.dmList());
    }

    @ExtUserIdConvert
    @PostMapping("/updatesimpleinfo")
    @ApiOperation("修改模板简单信息")
    public ResultModel<Void> updateSimpleInfo(@RequestBody @Validated OpenTemplateSimpleInfoUpdateParam param) {
        return openVisualTemplateProcess.updateSimpleInfo(param);
    }

}
