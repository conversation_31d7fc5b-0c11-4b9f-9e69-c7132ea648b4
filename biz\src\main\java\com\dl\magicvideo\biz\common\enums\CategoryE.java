package com.dl.magicvideo.biz.common.enums;

public enum CategoryE {
    BANK(null, 1, "银行"),
    SECURITY(null, 2, "证券"),

    FUND(null, 3, "基金"),

    WEALTH_MANAGEMENT_SUB(null, 4, "理财子"),

    //产品介绍，客户服务
    BANK_PRODUCT_INTRODUCE(101, 1, "产品介绍"),

    BANK_CUSTOMER_SERVICE(102, 1, "客户服务"),

    SECURITY_CUSTOMER_SERVICE(201, 2, "客户服务"),

    SECURITY_MARKET_INTERPRETATION(202, 2, "市场解读"),

    FUND_PRODUCT_INTRODUCE(301, 3, "产品介绍"),

    FUND_CUSTOMER_SERVICE(302, 3, "客户服务"),

    WEALTH_MANAGEMENT_SUB_PRODUCT_INTRODUCE(401, 4, "理财子");

    private Integer code;

    private Integer parentCode;

    private String desc;

    CategoryE(Integer code, Integer parentCode, String desc) {
        this.code = code;
        this.parentCode = parentCode;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public Integer getParentCode() {
        return parentCode;
    }

    public void setParentCode(Integer parentCode) {
        this.parentCode = parentCode;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
