package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 10:28
 */
@Data
public class VisualProduceJobCallbackDTO {

    /**
     * 作品id
     */
    private String jobId;

    /**
     * 合成结果
     */
    private VisualProduceJobCallbackResultDTO jobResult;

    /**
     * @see: com.dl.magicvideo.biz.common.enums.JobStatusE
     */
    private Integer jobStatus;

    /**
     * 合成视频名称
     */
    private String name;

    /**
     * 合成发起时间
     */
    private Long synthStarted;

    /**
     * 合成完成时间
     */
    private Long synthUpdated;

    /**
     * 外部用户id
     */
    private String extUserId;

}
