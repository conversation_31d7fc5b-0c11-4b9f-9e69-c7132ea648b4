package com.dl.magicvideo.biz.dal.font.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.util.Date;

@Data
@TableName("pattern_font")
public class PatternFontPO extends BasePO {

    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务id
     */
    @TableField("biz_id")
    private Long bizId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 分类
     */
    @TableField("font_type")
    private Integer fontType;

    /**
     * 样式内容
     */
    @TableField("styles")
    private String styles;

    /**
     * 封面图
     */
    @TableField("cover_Img")
    private String coverImg;

    /**
     * 是否删除 0否 1是
     */
    @TableField("is_deleted")
    private int isDeleted;

}
