package com.dl.magicvideo.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DsAudioTrainSource
 * @Description
 * <AUTHOR>
 * @Date 2023/2/8 18:17
 * @Version 1.0
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DsAudioTrainSource implements Serializable {

    private static final long serialVersionUID = -4213730735414879102L;

    /**
     * 录音文本，需不多于 35 个中文字
     */
    @JsonProperty("text")
    private String text;

    /**
     * 录音音频链接，必须为公网可访问的 wav 格式音频文件链接；音频文件须满 足 3.1 音频传入标准：采样率为 16000Hz，位深度为 16bit，单声道
     */
    @JsonProperty("link")
    private String link;

}
