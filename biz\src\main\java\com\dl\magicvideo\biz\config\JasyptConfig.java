package com.dl.magicvideo.biz.config;

import com.dl.framework.common.encryptor.SM4StringEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JasyptConfig {

    @Value("${sm4.salt}")
    private String salt;

    @Bean("sm4StringEncryptor")
    public SM4StringEncryptor sm4StringEncryptor() {
        SM4StringEncryptor stringEncryptor = new SM4StringEncryptor();
        stringEncryptor.setSalt(salt);
        return stringEncryptor;
    }

    public static void main(String[] args) {
        SM4StringEncryptor stringEncryptor = new SM4StringEncryptor();
        stringEncryptor.setSalt("da024ustkidtclu3");
        System.out.println(stringEncryptor.encrypt("prod_postgresql"));
        System.out.println(stringEncryptor.encrypt("Dl0301--=="));
        System.out.println(stringEncryptor.decrypt("1c6b08de4dcbb2b9571f58962f2e2a81"));
        System.out.println(stringEncryptor.decrypt("7e51efa8ad3c76d44f10b519dad1e882"));
    }

}
