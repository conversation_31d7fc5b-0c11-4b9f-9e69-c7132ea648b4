package com.dl.magicvideo.web.controllers.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe: TemplateVO
 * @author: zhousx
 * @date: 2023/2/1 14:55
 */
@Data
public class VisualTemplateVO {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("预览视频")
    private String previewVideoUrl;

    @ApiModelProperty("短视频")
    private String shortVideoUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("横版/竖版")
    private String resolutionType;

    @ApiModelProperty("背景音乐")
    private String bgMusic;

    @ApiModelProperty("背景音乐配置")
    private String bgMusicParam;

    @ApiModelProperty("tts配置")
    private String ttsParam;

    @ApiModelProperty("替换数据")
    private String replaceData;

    @ApiModelProperty("模板时长，毫秒")
    private Long duration;

    @ApiModelProperty("状态 0-启用 1-禁用")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("修改时间")
    private Date modifyDt;

    @ApiModelProperty("卡片列表")
    private List<CardVO> cards;


    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     * @see: ShareConfStateEnum
     */
    @ApiModelProperty("转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置")
    private Integer shareConfState;

    @ApiModelProperty("是否包含理财经理信息 1 包含 0 不包含")
    private Integer isManager;

    @ApiModelProperty("一级分类 1.银行，2.证券，3.基金，4.理财子")
    private Integer firstCategory;

    @ApiModelProperty("二级分类")
    private Integer secondCategory;

    @ApiModelProperty("是否包含理财经理信息 1 包含 0 不包含")
    private Integer isPPTType;

    @ApiModelProperty("组件版本号")
    private String componentVersion;
    @ApiModelProperty("是否被收藏")
    private boolean collect;
}
