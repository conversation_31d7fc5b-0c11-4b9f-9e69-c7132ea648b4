package com.dl.magicvideo.biz.mq;


import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

public interface DlChannels {

    /*****************消费者***************/
    @Input("produceshareconfconsumer")
    SubscribableChannel visualProduceShareConsumer();

    @Input("reducewithholdconsumer")
    SubscribableChannel reducewithholdconsumer();

    @Input("addwithholdconsumer")
    SubscribableChannel producejobcreatedconsumer();

    @Input("reducebalanceconsumer")
    SubscribableChannel reducebalanceconsumer();

    @Input("cancelbatchreducewithholdconsumer")
    SubscribableChannel cancelbatchreducewithholdconsumer();

    @Input("producefailconsumer")
    SubscribableChannel producefailconsumer();

    @Input("producesuccessconsumer")
    SubscribableChannel producesuccessconsumer();

    @Input("generatepreviewdataconsumer")
    SubscribableChannel generatepreviewdataconsumer();

    @Input("producejobsubordinatettssuccessconsumer")
    SubscribableChannel producejobsubordinatettssuccessconsumer();

    /*****************生产者***************/
    @Output("interactiveconfcopy")
    MessageChannel interactiveConfCopy();

    @Output("visualproduceshare")
    MessageChannel visualproduceshare();

    @Output("visualproducejobready")
    MessageChannel visualproduceJobready();

    @Output("visualproducebatchcancel")
    MessageChannel visualproducebatchcancel();

    /**
     * 视频合成失败消息生产者
     *
     * @return
     */
    @Output("producefailproducer")
    MessageChannel producefailproducer();

    @Output("generatepreviewdataproducer")
    MessageChannel generatePreviewDataProducer();

    /**
     * 视频任务下属的tts合成成功 消息生产者
     *
     * @return
     */
    @Output("producejobsubordinatettssuccessproducer")
    MessageChannel produceJobSubordinateTtsSuccessProducer();

}
