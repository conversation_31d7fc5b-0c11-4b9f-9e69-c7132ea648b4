package com.dl.magicvideo.biz.manager.visual.bo;

import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateLightEditConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProduceJobBO {
    private Long templateId;
    private String replaceData;
    private Integer source;
    private TemplateLightEditConfigDTO lightEditConfigs;
    private String extUserId;
    private DigitalManParamBO digitalManParamBO;
    private String renderData;
    private String apiData;
    private String produceJobName;
    private List<DynamicNodeDTO> dynamicNodes;
    /**
     * 尺寸
     */
    private String resolution;
    /**
     * 作品封面
     */
    private String jobCoverUrl;
}
