package com.dl.magicvideo.web.controllers.statistics.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 10:29
 */
@Data
public class StatisticsAiJobVO implements Serializable {
    private static final long serialVersionUID = -536670224217298501L;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * ai任务类型:1-数字人，2-TTS
     */
    private Integer aiJobType;

    /**
     * ai任务数
     */
    private Integer jobCount;

    /**
     * 总的毫秒
     */
    private Long totalTimeMillis;

    /**
     * 统计时间
     */
    private Date statisticsTime;

}
