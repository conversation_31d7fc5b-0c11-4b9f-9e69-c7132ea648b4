<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.VisualAiJobMapper">

    <resultMap id="ResultMap" type="com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO">
        <result property="jobId" column="job_id"/>
        <result property="produceJobId" column="produce_job_id"/>
        <result property="templateId" column="template_id"/>
        <result property="jobType" column="job_type"/>
        <result property="jobStatus" column="job_status"/>
        <result property="duration" column="duration"/>
        <result property="requestInfo" column="request_info"/>
        <result property="createDt" column="create_dt"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="createBy" column="create_by"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <sql id="Column_List">
        aj.job_id,aj.produce_job_id,aj.template_id, aj.job_type,aj.job_status,aj.duration,aj.request_info,aj.create_dt,aj.create_by, aj.text_length, t.tenant_code
    </sql>

    <sql id="query_condition">
        <if test="query.tenantCode != null and query.tenantCode != ''">
            and t.tenant_code = #{query.tenantCode}
        </if>
        <if test="query.aiJobType != null">
            and aj.job_type = #{query.aiJobType}
        </if>
        <if test="query.aiJobTypeList != null and query.aiJobTypeList.size() > 1">
            and aj.job_type in
             <foreach collection="query.aiJobTypeList" index="index" item="item" open="(" close=")" separator=",">
                 #{item}
             </foreach>
        </if>
        <if test="query.maxDt != null">
            and aj.create_dt &lt;= #{query.maxDt}
        </if>
        <if test="query.minDt != null">
            and aj.create_dt &gt;= #{query.minDt}
        </if>
        <if test="query.jobStatusList != null and query.jobStatusList.size() > 0">
            and aj.job_status in
            (
            <foreach collection="query.jobStatusList" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.needQueryDeleted != null">
            <if test="query.needQueryDeleted == 0">
                and t.is_deleted = 0
            </if>
        </if>
    </sql>

    <select id="countExt" parameterType="com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO"
            resultType="integer">
        select count(1)
        from visual_ai_job aj
        inner join visual_template t
        on aj.template_id = t.template_id
        <where>
            <include refid="query_condition"/>
        </where>
    </select>


    <select id="pageExt" parameterType="com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO"
            resultMap="ResultMap">
        select
        <include refid="Column_List"/>
        from visual_ai_job aj
        inner join visual_template t
        on aj.template_id = t.template_id
        <where>
            <include refid="query_condition"/>
        </where>
        order by aj.id desc
        limit #{query.offset},#{query.pageSize}
    </select>

    <select id="countDuration" parameterType="com.dl.magicvideo.biz.dal.visual.param.VisualAiJobDurationBO"
            resultType="long">
        SELECT
        <if test="query.jobType == 1">
            SUM(duration) AS total_duration
        </if>
        <if test="query.jobType == 2">
            SUM(text_length) AS total_text_length
        </if>
        FROM
        visual_ai_job
        WHERE
        job_type = #{query.jobType}
        AND produce_job_id = #{query.produceJobId};
    </select>
</mapper>
