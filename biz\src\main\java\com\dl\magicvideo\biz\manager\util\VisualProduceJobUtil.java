package com.dl.magicvideo.biz.manager.util;

import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-02 15:57
 */
public class VisualProduceJobUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(VisualProduceJobUtil.class);

    private static final Pattern pattern = Pattern.compile("\\{%(.*?)=('?(.*?)'?)?%\\}");

    public static String getText(Map<String, Object> configMap, String replaceData, String apiData) {
        String text = null;
        Object plainText = configMap.get("text");
        if (Objects.nonNull(plainText)) {
            text = plainText.toString();
        }
        Object replaceKeyObj = configMap.get("replaceKey");
        if (Objects.isNull(replaceKeyObj)) {
            return text;
        }
        String replaceKey = replaceKeyObj.toString();
        Map<String, Object> replaceMap;
        if (StringUtils.isNotBlank(replaceData) && !"{}".equals(replaceData)) {
            replaceMap = new HashMap<>(JsonUtils.getMap4Json(replaceData));
            //新的数据结构定义了userDefine一级目录，老数据只有一级目录，需要做下兼容
            Object replaceValue = replaceMap.get(replaceKey.replace(Const.BIND_DATA_HEADER,""));
            if (Objects.nonNull(replaceValue) && StringUtils.isNotBlank(replaceValue.toString())) {
                text = replaceValue.toString();

                //判断text中是否含有占位符，若有则处理
                Matcher matcher = pattern.matcher(text);
                while (matcher.find()) {
                    String placeholderKey = matcher.group(1).trim();
                    String defaultValue = matcher.group(2);

                    LOGGER.info("占位符名: {},完整占位符带默认值:{}", placeholderKey, matcher.group());
                    //从relaceMap中取占位符key对应的值
                    Object replaceValueObj = replaceMap.get(placeholderKey);
                    //若存在且非空串，则替换text中的完整占位符
                    if (Objects.nonNull(replaceValueObj) && StringUtils.isNotBlank(replaceValueObj.toString())) {
                        text = text.replaceAll(Pattern.quote(matcher.group()), replaceValueObj.toString());
                        //若relaceMap中不存在占位符key对应的值，则用默认值
                    } else if (Objects.nonNull(defaultValue)) {
                        // 去除默认值中的单引号（如果存在）
                        defaultValue = defaultValue.replaceAll("'", "").trim();
                        text = text.replaceAll(Pattern.quote(matcher.group()), defaultValue);
                    }
                }
                return text;
            }
        }

        if(StringUtils.isNotBlank(apiData) && !"{}".equals(apiData)){
            replaceMap = new HashMap<>(JsonUtils.getMap4Json(apiData));
            //接口绑定时绑定参数为下面的分级/common/fund/marketdetail/118144.result.code
            String[] splitReplaceKey = replaceKey.split("\\.");
            Object replaceValue = null;
            for (int i = 0; i < splitReplaceKey.length; i++) {
                if (i != splitReplaceKey.length - 1) {
                    replaceMap = (HashMap) replaceMap.get(splitReplaceKey[i]);
                    if (Objects.isNull(replaceMap)) {
                        return text;
                    }
                } else {
                    //非最后一个，则取最后的值
                    replaceValue = replaceMap.get(splitReplaceKey[i]);
                    if (Objects.isNull(replaceValue)) {
                        return text;
                    }
                }
            }
            if (Objects.nonNull(replaceValue) && StringUtils.isNotBlank(replaceValue.toString())) {
                text = replaceValue.toString();
                return text;
            }
        }

        return text;
    }

    /**
     * 移除speak标签
     * 老的tts片段脚本，是包含speak标签的，需要移除
     *
     * @param text
     * @return
     */
    public static String removeTTSSpeakLable(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        text = text.replaceAll("<speak>", "");
        text = text.replaceAll("</speak>", "");
        return text;
    }

    /**
     * 移除换行符\n和\r
     *
     * @param s
     * @return
     */
    public static String removeLineBreak(String s) {
        if (StringUtils.isBlank(s)) {
            return SymbolE.BLANK.getValue();
        }
        return s.replaceAll("\n", "").replaceAll("\r", "");
    }

    public static String rmHtml(String s) {
        if (StringUtils.isBlank(s)) {
            return SymbolE.BLANK.getValue();
        }

        return s.replaceAll("<[.[^<]]*>", "");
    }

    /**
     * 移除标点符号
     *
     * @param text
     * @return
     */
    public static String rmPunctuation(String text) {
        if (StringUtils.isBlank(text)) {
            return SymbolE.BLANK.getValue();
        }

        return text.replaceAll("[\\pP+~$`^=|<>～｀＄＾＋＝｜＜＞￥×]", "");
    }

}
