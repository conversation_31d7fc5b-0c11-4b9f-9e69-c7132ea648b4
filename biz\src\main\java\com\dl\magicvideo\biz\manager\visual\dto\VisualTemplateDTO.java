package com.dl.magicvideo.biz.manager.visual.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe: TemplateDTO
 * @author: zhousx
 * @date: 2023/2/1 15:31
 */
@Data
public class VisualTemplateDTO {

    private Long templateId;

    private String name;

    private String coverUrl;

    private String previewVideoUrl;

    private String shortVideoUrl;

    private Integer status;

    private String resolution;

    private String resolutionType;

    private String ttsParam;

    private String bgMusic;

    private String bgMusicParam;

    private String replaceData;

    private String tenantCode;

    private Long duration;

    private Date createDt;

    private Date modifyDt;

    private List<VisualCardDTO> cards;


    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     * @see: ShareConfStateEnum
     */
    private Integer shareConfState;

    private Integer isManager;

    private Integer firstCategory;

    private Integer secondCategory;

    private String apiData;

    /**
     * 是否为PPT 1是ppt 0不是ppt
     */
    private Integer isPPT;

    /**
     * 模板类型，1-常规模板，2-数据图表模板
     *
     * @see: com.dl.magicvideo.biz.manager.visual.enums.TemplateTypeEnum
     */
    private Integer type;

    /**
     * 组件版本号，默认2.0.0
     */
    private String componentVersion;

    @ApiModelProperty("卡片列表")
    private List<TagDTO> tagList;
}
