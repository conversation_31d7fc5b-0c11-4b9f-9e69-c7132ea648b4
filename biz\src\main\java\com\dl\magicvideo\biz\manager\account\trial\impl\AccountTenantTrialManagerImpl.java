package com.dl.magicvideo.biz.manager.account.trial.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.account.trial.AccountTenantTrialMapper;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class AccountTenantTrialManagerImpl extends ServiceImpl<AccountTenantTrialMapper, AccountTenantTrialPO> implements
        AccountTenantTrialManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountTenantTrialManagerImpl.class);

    @Resource
    private TenantInfoManager tenantInfoManager;
    @Resource
    private AccountTenantTrialManager accountTenantTrialManager;

    private static final Long DEFAULT_BALANCE = 20L;

    @Override
    public Integer checkTenantBalance(String tenantCode, Integer count) {
        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(tenantCode);
        if (Objects.isNull(tenantInfo) || !Const.ONE.equals(tenantInfo.getIsTrial())) {
            return null;
        }
        AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager.getOne(
                Wrappers.lambdaQuery(AccountTenantTrialPO.class).eq(AccountTenantTrialPO::getTenantCode, tenantCode)
                        .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(tenantTrialAccount)) {
            log.error("未找到试用租户账户 tenantCode = {}", tenantCode);
            return null;
        }
        Long availableBalance = tenantTrialAccount.getBalance() - tenantTrialAccount.getWithhold();
        Assert.isTrue(count <= availableBalance, "当前用量不足，请联系客户经理获取更多用量。");
        return (int) (availableBalance - count);
    }

    @Override
    public void createAccount(String tenantCode, Long initBalance) {
        AccountTenantTrialPO existRecord = this.getOne(Wrappers.lambdaQuery(AccountTenantTrialPO.class)
                .eq(AccountTenantTrialPO::getTenantCode, tenantCode).eq(AccountTenantTrialPO::getIsDeleted,Const.ZERO));
        if(Objects.nonNull(existRecord)){
            LOGGER.info("该租户已有试用账户，无需再次创建。tenantCode:{}", tenantCode);
            return;
        }
        AccountTenantTrialPO insertPO = new AccountTenantTrialPO();
        insertPO.setTenantCode(tenantCode);
        insertPO.setBalance(Objects.nonNull(initBalance)?initBalance:DEFAULT_BALANCE);
        insertPO.setCreateBy(0L);
        insertPO.setModifyBy(0L);
        insertPO.setCreateDt(new Date());
        insertPO.setModifyDt(new Date());
        this.save(insertPO);
    }
}
