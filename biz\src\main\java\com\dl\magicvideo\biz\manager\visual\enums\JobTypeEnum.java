package com.dl.magicvideo.biz.manager.visual.enums;


import java.util.Objects;

/**
 * 作品类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-16 16:10
 */
public enum JobTypeEnum {

    NORMAL(1,"常规作品"),
    DATA_CHART(2,"数据图表作品"),
    AIGC(3,"AIGC作品")
    ;

    private Integer type;

    private String desc;

    JobTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByType(Integer type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (JobTypeEnum jobTypeEnum : JobTypeEnum.values()) {
            if (jobTypeEnum.getType().equals(type)) {
                return jobTypeEnum.desc;
            }
        }
        return "";
    }
}
