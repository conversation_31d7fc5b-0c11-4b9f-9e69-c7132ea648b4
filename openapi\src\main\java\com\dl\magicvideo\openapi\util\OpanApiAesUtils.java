package com.dl.magicvideo.openapi.util;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

public class OpanApiAesUtils {

    //加密方式
    private static final String AES_TYPE = "AES";
    //秘钥最大长度16位
    private static final int BYTE_LENGTH = 16;

    /**
     * 加密AES
     *
     * @param value 字符串
     * @param key   秘钥
     * @return String
     */
    private static String encryptAES(String key, String value) {
        try {
            byte[] keyBytes = Arrays.copyOf(key.getBytes(StandardCharsets.US_ASCII), BYTE_LENGTH);
            SecretKey keyStr = new SecretKeySpec(keyBytes, AES_TYPE);
            Cipher cipher = Cipher.getInstance(AES_TYPE);
            cipher.init(Cipher.ENCRYPT_MODE, keyStr);
            byte[] cleartext = value.getBytes(StandardCharsets.UTF_8);
            byte[] ciphertextBytes = cipher.doFinal(cleartext);
            return new String(Hex.encodeHex(ciphertextBytes)).toUpperCase();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * 解密AES
     *
     * @param encrypted 字符串
     * @param key       秘钥
     * @return String
     */
    private static String decryptAES(String key, String encrypted) {
        try {
            byte[] keyBytes = Arrays.copyOf(key.getBytes(StandardCharsets.US_ASCII), BYTE_LENGTH);
            SecretKey keyStr = new SecretKeySpec(keyBytes, AES_TYPE);
            Cipher cipher = Cipher.getInstance(AES_TYPE);
            cipher.init(Cipher.DECRYPT_MODE, keyStr);
            byte[] content = Hex.decodeHex(encrypted.toCharArray());
            byte[] ciphertextBytes = cipher.doFinal(content);
            return new String(ciphertextBytes);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * 加密
     *
     * @param value 字符串
     * @param pwd   秘钥
     * @return String
     */
    public static String encryptValue(String value, String pwd) {
        return StrUtil.isEmpty(value) ? null : encryptAES(pwd, value.trim());
    }

    /**
     * 解密
     *
     * @param value 字符串
     * @param pwd   秘钥
     * @return String
     */
    public static String decryptValue(String value, String pwd) {
        if (StrUtil.isNotEmpty(value)) {
            String value2 = decryptAES(pwd, value.toLowerCase());
            value = StrUtil.isEmpty(value2) ? value : value2;
        }
        return value;
    }

    /**
     * main方法
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        //加密测试
        System.out.println(encryptValue("张三", "123#deeplink567%"));
        //解密测试
        System.out.println(decryptValue("1783E17375CADA8133DC4093388B2C49", "123#deeplink567%"));
    }

}
