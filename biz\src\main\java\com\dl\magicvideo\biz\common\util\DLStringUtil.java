package com.dl.magicvideo.biz.common.util;

import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.regex.Pattern;

public class DLStringUtil {

    static final String CHINESE = "[\u0391-\uFFE5]";

    public static int lengthOfUTF8(String value) {
        int i = 0;
        if (!StringUtils.hasLength(value)) {
            return i;
        }
        for (char c : value.toCharArray()) {
            if (Pattern.matches(CHINESE, String.valueOf(c))) {
                i += 3;
            } else {
                i += 1;
            }
        }
        return i;
    }

    public static String tail(String str, int maxSize, String suffix) {
        if (suffix == null) {
            suffix = "";
        }
        if (str.length() > maxSize) {
            return str.substring(0, maxSize) + suffix;
        }
        return str;
    }

    public static String notNull(BigDecimal bigDecimal){
        if(StringUtils.isEmpty(bigDecimal)){
            return "";
        }
        return bigDecimal.toString();
    }

}
