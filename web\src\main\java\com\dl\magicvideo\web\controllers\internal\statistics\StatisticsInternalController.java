package com.dl.magicvideo.web.controllers.internal.statistics;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.CountVO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.EfficiencyVO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.InternalFailJobStatisticsDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsJobTenantSummaryInternalDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.param.CountParam;
import com.dl.magicvideo.web.controllers.internal.statistics.param.EfficiencyParam;
import com.dl.magicvideo.web.controllers.internal.statistics.param.InternalFailJobStatisticsParam;
import com.dl.magicvideo.web.controllers.internal.statistics.param.StatisticsJobTenantSummaryInternalQueryParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @describe: 统计相关接口
 * @author: hongcj
 * @date: 2023/6/17 14:43
 */
@Slf4j
@RestController
@RequestMapping("/visual/internal/statistics")
@Api("生产看板管理")
public class StatisticsInternalController {
    @Autowired
    private StatisticsInternalProcess statisticsInternalProcess;

    @PostMapping("/count")
    @ApiOperation("生产数量统计")
    public ResultModel<List<CountVO>> count(@RequestBody @Validated CountParam param) {
        return ResultModel.success(statisticsInternalProcess.count(param));
    }

    @PostMapping("/efficiency")
    @ApiOperation("生产效率统计")
    public ResultModel<List<EfficiencyVO>> efficiency(@RequestBody @Validated EfficiencyParam param) {
        return ResultModel.success(statisticsInternalProcess.efficiency(param));
    }

    @PostMapping("/failstatistics")
    @ApiOperation("生产失败数据统计")
    public ResultModel<List<InternalFailJobStatisticsDTO>> failStatistics(
            @RequestBody @Validated InternalFailJobStatisticsParam param) {
        return ResultModel.success(statisticsInternalProcess.failStatistics(param));
    }

    @PostMapping("/specifictenantsummary")
    @ApiOperation("指定租户的任务数据统计汇总")
    public ResultModel<StatisticsJobTenantSummaryInternalDTO> specificTenantSummary(
            @RequestBody @Validated StatisticsJobTenantSummaryInternalQueryParam param) {
        return statisticsInternalProcess.specificTenantSummary(param);
    }
}
