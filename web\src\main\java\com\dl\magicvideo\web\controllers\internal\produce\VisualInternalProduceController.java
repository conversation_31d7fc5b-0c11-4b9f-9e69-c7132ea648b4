package com.dl.magicvideo.web.controllers.internal.produce;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.internal.produce.param.ProduceBatchPageQueryParam;
import com.dl.magicvideo.web.controllers.internal.produce.param.ProduceJobPageQueryParam;
import com.dl.magicvideo.web.controllers.internal.produce.dto.DailyProduceStatisticsDTO;
import com.dl.magicvideo.web.controllers.internal.produce.dto.ProduceBatchDTO;
import com.dl.magicvideo.web.controllers.internal.produce.dto.ProduceJobDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/visual/internal/produce")
@Api("生产作业模块")
public class VisualInternalProduceController {
    @Autowired
    private VisualProduceInternalProcess visualProduceInternalProcess;

    @PostMapping("/batchlist")
    @ApiOperation("批次列表")
    public ResultPageModel<ProduceBatchDTO> batchList(@RequestBody ProduceBatchPageQueryParam param) {
        return visualProduceInternalProcess.batchList(param);
    }

    @PostMapping("/joblistbybatchId")
    @ApiOperation("批次下作业列表")
    public ResultPageModel<ProduceJobDTO> jobListByBatchId(@RequestBody ProduceJobPageQueryParam param) {
        return visualProduceInternalProcess.jobListByBatchId(param);
    }

    @PostMapping("/cancelbatch/{batchId}")
    @ApiOperation("取消批次")
    public ResultModel<Void> cancelBatch(@PathVariable Long batchId) {
        return visualProduceInternalProcess.cancelBatch(batchId);
    }

    @PostMapping("/jobdetail/{jobId}")
    @ApiOperation("作业详情")
    public ResultModel<ProduceJobDTO> jobDetail(@PathVariable Long jobId) {
        return visualProduceInternalProcess.jobDetail(jobId);
    }

    @PostMapping("/dailystatistics")
    @ApiOperation("当日生产统计")
    public ResultModel<DailyProduceStatisticsDTO> dailyStatistics() {
        return visualProduceInternalProcess.dailyStatistics();
    }

    @PostMapping("/deleteworks/{jobId}")
    @ApiOperation("删除作品")
    public ResultModel<Void> deleteWorks(@PathVariable Long jobId) {
        return visualProduceInternalProcess.deleteWorks(jobId);
    }
}
