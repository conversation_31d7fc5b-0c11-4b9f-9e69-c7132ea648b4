package com.dl.magicvideo.web.controllers.internal.visual.convert;

import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceListQueryDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceListQueryPairDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.util.CeilingUtils;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.TtsJobBO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.VisualDmJobInternalInfoDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.VisualTtsJobInternalInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 15:02
 */
public class VisualAiJobInternalConvert {
    private static final Logger LOGGER = LoggerFactory.getLogger(VisualAiJobInternalConvert.class);

    public static final String EDIT = "编辑";

    public static final String VIDEO_PRODUCE = "视频合成";

    public static final String TEMPLATE_PREVIEW = "模板制作预览";

    public static VisualDmJobInternalInfoDTO buildVisualDmJobInternalInfoDTO(VisualAiJobExtPO aiJobExtPO,
            Map<Long, VisualTemplatePO> templateMap, Map<Long, String> creatorIdNameMap,
            DigitalManJobBO digitalManJobBO, Map<String, DaVirtualManScenesDTO> dmSceneMap) {
        VisualDmJobInternalInfoDTO result = new VisualDmJobInternalInfoDTO();
        result.setJobId(String.valueOf(aiJobExtPO.getJobId()));
        result.setProduceJobId(
                Objects.nonNull(aiJobExtPO.getProduceJobId()) ? String.valueOf(aiJobExtPO.getProduceJobId()) : "");
        result.setTemplateId(String.valueOf(aiJobExtPO.getTemplateId()));
        VisualTemplatePO templatePO = templateMap.get(aiJobExtPO.getTemplateId());
        if (Objects.nonNull(templatePO)) {
            result.setTemplateName(templatePO.getName());
        }
        result.setAiJobType(aiJobExtPO.getJobType());
        result.setJobStatus(aiJobExtPO.getJobStatus());
        result.setCreateDt(aiJobExtPO.getCreateDt());
        result.setCreateBy(Objects.nonNull(aiJobExtPO.getCreateBy()) ? String.valueOf(aiJobExtPO.getCreateBy()) : "");
        result.setCreatorName(creatorIdNameMap.get(aiJobExtPO.getCreateBy()));
        result.setDuration(aiJobExtPO.getDuration());
        result.setTenantCode(aiJobExtPO.getTenantCode());

        result.setCeilingMinutes(CeilingUtils.millsToMinutes(aiJobExtPO.getDuration()));

        if (StringUtils.isBlank(result.getProduceJobId())) {
            result.setCreateSourceDesc(result.getTemplateName() + SymbolE.MINUS.getValue() + EDIT);
        } else {
            result.setCreateSourceDesc(result.getTemplateName() + SymbolE.MINUS.getValue() + VIDEO_PRODUCE);
        }

        if (Objects.nonNull(digitalManJobBO)) {
            result.setChannel(digitalManJobBO.getChannel());
            result.setChannelName(ServiceChannelEnum.getNameByCode(digitalManJobBO.getChannel()));
            result.setSceneId(digitalManJobBO.getSceneId());

            DaVirtualManScenesDTO scenesDTO = dmSceneMap
                    .get(digitalManJobBO.getChannel() + "-" + digitalManJobBO.getSceneId());
            if (Objects.nonNull(scenesDTO)) {
                result.setDmBizId(String.valueOf(scenesDTO.getBizId()));
                result.setDmName(scenesDTO.getVmName());
                result.setGender(scenesDTO.getGender());
                result.setSceneName(scenesDTO.getSceneName());
            } else {
                LOGGER.warn("该数字人任务查找不到数字人场景信息,aiJobId:{},channel:{},sceneId:{}", aiJobExtPO.getJobId(),
                        digitalManJobBO.getChannel(), digitalManJobBO.getSceneId());
            }
        }

        return result;
    }

    public static VisualTtsJobInternalInfoDTO buildVisualTtsJobInternalInfoDTO(VisualAiJobExtPO aiJobExtPO,
            Map<Long, VisualTemplatePO> templateMap, Map<Long, String> creatorIdNameMap, TtsJobBO ttsJobBO,
            Map<String, DaVirtualVoiceBaseInfoDTO> voiceMap) {
        VisualTtsJobInternalInfoDTO result = new VisualTtsJobInternalInfoDTO();
        result.setJobId(String.valueOf(aiJobExtPO.getJobId()));
        result.setProduceJobId(
                Objects.nonNull(aiJobExtPO.getProduceJobId()) ? String.valueOf(aiJobExtPO.getProduceJobId()) : "");
        result.setTemplateId(String.valueOf(aiJobExtPO.getTemplateId()));
        VisualTemplatePO templatePO = templateMap.get(aiJobExtPO.getTemplateId());
        if (Objects.nonNull(templatePO)) {
            result.setTemplateName(templatePO.getName());
        }
        result.setAiJobType(aiJobExtPO.getJobType());
        result.setJobStatus(aiJobExtPO.getJobStatus());
        result.setCreateDt(aiJobExtPO.getCreateDt());
        result.setCreateBy(Objects.nonNull(aiJobExtPO.getCreateBy()) ? String.valueOf(aiJobExtPO.getCreateBy()) : "");
        result.setCreatorName(creatorIdNameMap.get(aiJobExtPO.getCreateBy()));
        result.setDuration(aiJobExtPO.getDuration());
        result.setTenantCode(aiJobExtPO.getTenantCode());

        result.setCeilingMinutes(CeilingUtils.millsToMinutes(aiJobExtPO.getDuration()));

        if (StringUtils.isBlank(result.getProduceJobId())) {
            result.setCreateSourceDesc(result.getTemplateName() + SymbolE.MINUS.getValue() + EDIT);
            result.setCreateReason(TEMPLATE_PREVIEW);
        } else {
            result.setCreateSourceDesc(result.getTemplateName() + SymbolE.MINUS.getValue() + VIDEO_PRODUCE);
            result.setCreateReason(VIDEO_PRODUCE);
        }

        if (Objects.nonNull(ttsJobBO)) {
            result.setChannel(ttsJobBO.getChannel());
            result.setChannelName(ServiceChannelEnum.getNameByCode(ttsJobBO.getChannel()));

            DaVirtualVoiceBaseInfoDTO voiceBaseInfoDTO = voiceMap
                    .get(ttsJobBO.getChannel() + "||" + ttsJobBO.getVoiceName());
            if (Objects.nonNull(voiceBaseInfoDTO)) {
                result.setVoiceName(voiceBaseInfoDTO.getVoiceName());
                result.setVoiceKey(voiceBaseInfoDTO.getVoiceKey());
                result.setVoiceGender(voiceBaseInfoDTO.getGender());
                result.setVoiceType(voiceBaseInfoDTO.getVoiceType());
            } else {
                LOGGER.warn("该tts任务查找不到智能语音信息,aiJobId:{},channel:{},voiceKey:{}", aiJobExtPO.getJobId(),
                        ttsJobBO.getChannel(), ttsJobBO.getVoiceName());
            }
        }
        result.setTextLength(Objects.nonNull(aiJobExtPO.getTextLength()) ? aiJobExtPO.getTextLength() : 0);
        return result;
    }

    public static DaVirtualVoiceListQueryDTO buildDaVirtualVoiceListQueryDTO(Set<String> channelAndVoiceKeySet) {
        DaVirtualVoiceListQueryDTO queryDTO = new DaVirtualVoiceListQueryDTO();
        List<DaVirtualVoiceListQueryPairDTO> pairList = new ArrayList<>();
        queryDTO.setPairList(pairList);
        channelAndVoiceKeySet.forEach(str -> {
            String[] strArr = str.split("\\|\\|");
            String channelStr = strArr[0];
            String voiceKey = strArr[1];

            DaVirtualVoiceListQueryPairDTO pairDTO = new DaVirtualVoiceListQueryPairDTO();
            pairDTO.setVoiceKey(voiceKey);
            pairDTO.setChannel(Integer.valueOf(channelStr));
            pairList.add(pairDTO);
        });
        return queryDTO;
    }

}
