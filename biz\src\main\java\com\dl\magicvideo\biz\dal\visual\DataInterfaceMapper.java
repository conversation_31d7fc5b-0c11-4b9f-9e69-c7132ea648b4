package com.dl.magicvideo.biz.dal.visual;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.po.DataInterfacePO;
import com.dl.magicvideo.biz.dal.visual.po.DataProductPO;

/**
* <AUTHOR>
* @description 针对表【visual_template】的数据库操作Mapper
* @createDate 2023-04-24 10:22:23
* @Entity com.dl.magicvideo.biz.dal.visual.po.VisualTemplate
*/
@BaseDao
public interface DataInterfaceMapper extends BaseMapper<DataInterfacePO> {
}




