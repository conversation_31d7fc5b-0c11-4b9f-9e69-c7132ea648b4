package com.dl.magicvideo.biz.common.util;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

public class BigDecimalUtils {
    public static int getSign(BigDecimal number) {
        return number.signum();
    }

    /**
     * 是否正数
     * @param number
     * @return
     */
    public static boolean isPositive(BigDecimal number) {
        return number.signum() == 1;
    }

    /**
     * 是否负数
     * @param number
     * @return
     */
    public static boolean isNegative(BigDecimal number) {
        return number.signum() == -1;
    }

    /**
     * 是否0
     * @param number
     * @return
     */
    public static boolean isZero(BigDecimal number) {
        return number.signum() == 0;
    }

    /**
     * 比较绝对值大小
     * @param number1
     * @param number2
     * 如果返回值为负数，表示absNumber1小于absNumber2；
     * 如果返回值为零，表示absNumber1等于absNumber2；
     * 如果返回值为正数，表示absNumber1大于absNumber2。
     * @return
     */
    public static int compareAbsoluteValues(BigDecimal number1, BigDecimal number2) {
        BigDecimal absNumber1 = number1.abs();
        BigDecimal absNumber2 = number2.abs();

        return absNumber1.compareTo(absNumber2);
    }

    /**
     * 比较绝对值大小，并且返回绝对值大的BigDecimal
     * @param number1
     * @param number2
     * @return
     */
    public static BigDecimal compareAbsoluteValuesMax(BigDecimal number1, BigDecimal number2) {
        BigDecimal absNumber1 = number1.abs();
        BigDecimal absNumber2 = number2.abs();

        if(absNumber1.compareTo(absNumber2) >= 0 ){
            return absNumber1;
        }else {
            return absNumber2;
        }
    }

    /**
     * 加法
     * @param number1
     * @param number2
     * @return
     */
    public static BigDecimal add(BigDecimal number1, BigDecimal number2) {
        return number1.add(number2);
    }

    /**
     * 减法
     * @param number1
     * @param number2
     * @return
     */
    public static BigDecimal subtract(BigDecimal number1, BigDecimal number2) {
        return number1.subtract(number2);
    }

    public static String set2Scale(String source) {
        return setDecimalDigitForNumberStr(source, 2);
    }

    public static String setDecimalDigitForNumberStr(String source, int scale) {
        if (StringUtils.isBlank(source)) {
            return "";
        }
        try {
            return new BigDecimal(source).setScale(scale, RoundingMode.HALF_UP).stripTrailingZeros().toString();
        } catch (Exception e) {
            return source;
        }
    }

    public static List<String> set2Scale(List<String> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return sourceList;
        }
        try {
            return sourceList.stream().map(BigDecimalUtils::set2Scale).collect(Collectors.toList());
        } catch (Exception e) {
            return sourceList;
        }
    }
}
