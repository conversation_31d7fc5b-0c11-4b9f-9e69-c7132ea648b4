package com.dl.magicvideo.web.controllers.subjectmatter.helper;

import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.web.controllers.subjectmatter.vo.SubjectMatterVO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-01 14:37
 */
public class SubjectMatterHelper {

    public static SubjectMatterVO cnvSubjectMatterPO2VO(SubjectMatterPO input) {
        if(Objects.isNull(input)){
            return null;
        }
        SubjectMatterVO result = new SubjectMatterVO();
        result.setBizId(input.getBizId() + "");
        result.setName(input.getName());
        result.setExcelUrl(input.getExcelUrl());
        result.setImgUrl(input.getImgUrl());
        result.setLevel(input.getLevel());
        result.setParentId(input.getParentId() + "");
        result.setIsHaveChild(input.getIsHaveChild());
        result.setJsonUrl(input.getJsonUrl());
        return result;
    }
}
