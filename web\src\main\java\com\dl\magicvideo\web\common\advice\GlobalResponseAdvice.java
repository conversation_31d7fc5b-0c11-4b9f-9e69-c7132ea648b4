package com.dl.magicvideo.web.common.advice;

import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.web.common.util.RequestBodyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@Slf4j
@RestControllerAdvice
public class GlobalResponseAdvice implements ResponseBodyAdvice {
    @Autowired
    private RequestBodyUtil requestBodyUtil;

    private static ThreadLocal<Long> TIME = new ThreadLocal<>();

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass,
            ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
//        log.info("接口:{}, 请求参数:{}, 返回参数:{}", serverHttpRequest.getURI(), requestBodyUtil.getRequestBody(),
//                    JsonUtils.toJSON(o));
        return o;
    }
}
