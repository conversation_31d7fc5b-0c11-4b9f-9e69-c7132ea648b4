package com.dl.magicvideo.web.controllers.internal.visual.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class VisualProduceJobInternalVO implements Serializable {
    private static final long serialVersionUID = -2020173603138223703L;

    @ApiModelProperty(value = "视频ID")
    private Long jobId;

    @ApiModelProperty(value = "模板ID")
    private Long templateId;

    @ApiModelProperty(value = "视频名称")
    private String videoName;

    @ApiModelProperty(value = "视频URL")
    private String url;

    @ApiModelProperty(value = "视频封面图")
    private String coverImg;

    @ApiModelProperty("视频时长")
    private Long duration;

    @ApiModelProperty("租户编号")
    private String tenantCode;

    @ApiModelProperty("是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    private Date createDt;

    @ApiModelProperty(value = "修改时间")
    private Date modifyDt;

    @ApiModelProperty(value = "完成时间")
    private Date completeDt;

    /**
     * @see com.dl.magicvideo.biz.common.enums.JobStatusE
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

}
