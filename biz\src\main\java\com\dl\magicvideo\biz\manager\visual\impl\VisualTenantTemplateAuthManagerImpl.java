package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.VisualTemplateAuthMapper;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplateAuthPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.VisualTenantTemplateAuthManager;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TenantTemplateAuthBO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO;
import com.dl.magicvideo.biz.manager.visual.enums.TemplateSyncStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VisualTenantTemplateAuthManagerImpl extends ServiceImpl<VisualTemplateAuthMapper, VisualTemplateAuthPO> implements VisualTenantTemplateAuthManager {
    @Autowired
    private VisualTemplateManager visualTemplateManager;

    @Autowired
    private OperatorUtil operatorUtil;

    @Override
    public ResponsePageQueryDO<List<TemplateAuthDTO>> pageQuery(TemplateAuthSearchBO bo) {
        //响应对象
        ResponsePageQueryDO<List<TemplateAuthDTO>> response = new ResponsePageQueryDO<>();
        LambdaQueryWrapper<VisualTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        //构造查询条件
        queryWrapper.like(StringUtils.isNotBlank(bo.getName()), VisualTemplatePO::getName, bo.getName())
                .eq(Objects.nonNull(bo.getTemplateId()),VisualTemplatePO::getTemplateId,bo.getTemplateId())
                .eq(VisualTemplatePO::getStatus, Const.ZERO)
                .eq(VisualTemplatePO::getIsDeleted, Const.ZERO)
                .eq(VisualTemplatePO::getIsSys, Const.ONE);

        //分页查询所有的模板(如果有条件根据条件查询模板列表)
        IPage<VisualTemplatePO> pageResult = visualTemplateManager.getBaseMapper().selectPage(convert(bo), queryWrapper);

        //分页后本页所有模板列表
        List<VisualTemplatePO> data = pageResult.getRecords();
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        log.info("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        //PO转DTO
        List<TemplateAuthDTO> authDTOList = data.stream().map(this::cnvVisualTemplatePO2DTO).collect(Collectors.toList());
        //查询本租户下的模板
        List<VisualTemplateAuthPO> visualTemplateAuthPOS = baseMapper.selectList(new LambdaQueryWrapper<VisualTemplateAuthPO>()
                .eq(VisualTemplateAuthPO::getTenantCode, bo.getTenantCode()));
        //查询本租户下的模板id集合
        Set<Long> tenantTemplateIds = visualTemplateAuthPOS.stream().map(VisualTemplateAuthPO::getTemplateId).collect(Collectors.toSet());
        //遍历模板集合并根据条件给状态赋值 授权/未授权
        authDTOList.forEach(dto -> {
            if (tenantTemplateIds.contains(dto.getTemplateId())) {
                dto.setStatus(Const.ZERO);
            } else {
                dto.setStatus(Const.ONE);
            }
        });
        response.setDataResult(authDTOList);
        return response;
    }

    @Override
    public ResponsePageQueryDO<List<TemplateAuthDTO>> pageQueryByStatus(TemplateAuthSearchBO bo) {
        //响应对象
        ResponsePageQueryDO<List<TemplateAuthDTO>> response = new ResponsePageQueryDO<>();

        //查询Auth表中本租户的模板id数量
        LambdaQueryWrapper<VisualTemplateAuthPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VisualTemplateAuthPO::getTenantCode, bo.getTenantCode());
        Integer count = baseMapper.selectCount(queryWrapper);

        response.setPageIndex(bo.getPageIndex());
        response.setPageSize(bo.getPageSize());
        //本租户没有被授权的模板
        if (Objects.equals(count, 0)) {
            return response;
        }
        //查询本租户下的模板
        List<VisualTemplateAuthPO> visualTemplateAuthPOS = baseMapper.selectList(queryWrapper);
        //获取本租户下的模板id集合
        List<Long> templateIds = visualTemplateAuthPOS.stream().map(VisualTemplateAuthPO::getTemplateId).collect(Collectors.toList());
        List<TemplateAuthDTO> authDTOList = null;
        if (Objects.equals(bo.getStatus(), Const.ZERO)) {
            //根据模板id集合去查询模板表中的模板
            LambdaQueryWrapper<VisualTemplatePO> wrapper = new LambdaQueryWrapper<VisualTemplatePO>()
                    .like(StringUtils.isNotBlank(bo.getName()), VisualTemplatePO::getName, bo.getName())
                    .eq(Objects.nonNull(bo.getTemplateId()),VisualTemplatePO::getTemplateId,bo.getTemplateId())
                    .eq(VisualTemplatePO::getStatus, Const.ZERO)
                    .eq(VisualTemplatePO::getIsDeleted, Const.ZERO)
                    .eq(VisualTemplatePO::getIsSys, Const.ONE)
                    .in(VisualTemplatePO::getTemplateId, templateIds);
            IPage<VisualTemplatePO> pageResult = visualTemplateManager.getBaseMapper().selectPage(convert(bo), wrapper);
            //分页后本页所有模板列表
            List<VisualTemplatePO> data = pageResult.getRecords();
            response.setPageIndex(pageResult.getCurrent());
            response.setPageSize(pageResult.getSize());
            response.setTotal(pageResult.getTotal());
            log.info("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
            if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                return response;
            }
            //PO转DTO
            authDTOList = data.stream().map(this::cnvVisualTemplatePO2DTO).collect(Collectors.toList());
            authDTOList.forEach(dto -> dto.setStatus(Const.ZERO));
        } else {
            //查询模板表中所有模板
            IPage<VisualTemplatePO> pageResult = visualTemplateManager.getBaseMapper().selectPage(convert(bo), new LambdaQueryWrapper<VisualTemplatePO>()
                    .like(StringUtils.isNotBlank(bo.getName()), VisualTemplatePO::getName, bo.getName())
                    .eq(Objects.nonNull(bo.getTemplateId()),VisualTemplatePO::getTemplateId,bo.getTemplateId())
                    .eq(VisualTemplatePO::getStatus, Const.ZERO)
                    .eq(VisualTemplatePO::getIsDeleted, Const.ZERO)
                    .eq(VisualTemplatePO::getIsSys, Const.ONE)
                    .notIn(VisualTemplatePO::getTemplateId, templateIds));

            //分页后本页所有模板列表
            List<VisualTemplatePO> data = pageResult.getRecords();
            response.setPageIndex(pageResult.getCurrent());
            response.setPageSize(pageResult.getSize());
            response.setTotal(pageResult.getTotal());
            log.info("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
            if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                return response;
            }

            //把状态设置为未授权
            authDTOList = data.stream().map(this::cnvVisualTemplatePO2DTO).collect(Collectors.toList());
            authDTOList.forEach(dto -> dto.setStatus(Const.ONE));
        }
        response.setDataResult(authDTOList);
        return response;
    }

    @Override
    public void auth(TenantTemplateAuthBO bo) {
        Assert.notNull(bo.getTemplateId(), "模板Id不可为空");
        //获取源模版id
        List<VisualTemplateAuthPO> existAuthList = list(Wrappers.lambdaQuery(VisualTemplateAuthPO.class)
                .eq(VisualTemplateAuthPO::getTemplateId, bo.getTemplateId()));
        //根据existAuthList获取一源模板id
        Long sourceTemplateId = existAuthList.stream().filter(po -> Objects.nonNull(po.getSourceTemplateId()))
                .map(VisualTemplateAuthPO::getSourceTemplateId).findFirst().orElse(null);
        //授权
        VisualTemplateAuthPO visualTemplateAuthPO = new VisualTemplateAuthPO();
        if (Objects.equals(bo.getStatus(), Const.ZERO)) {
            visualTemplateAuthPO.setTemplateId(bo.getTemplateId());
            visualTemplateAuthPO.setTenantName(bo.getTenantName());
            visualTemplateAuthPO.setTenantCode(bo.getTenantCode());
            visualTemplateAuthPO.setCreatorName(operatorUtil.getUserName());
            visualTemplateAuthPO.setModifyName(operatorUtil.getUserName());
            visualTemplateAuthPO.setSourceTemplateId(sourceTemplateId);
            visualTemplateAuthPO.setSyncStatus(TemplateSyncStatusEnum.SUCCESS.getStatus());
            save(visualTemplateAuthPO);
        } else {
            //取消授权
            remove(new LambdaQueryWrapper<VisualTemplateAuthPO>()
                    .eq(VisualTemplateAuthPO::getTemplateId,bo.getTemplateId())
                    .eq(VisualTemplateAuthPO::getTenantCode,bo.getTenantCode()));
        }
    }

    /**
     * PO对象转换为DTO对象
     */
    private TemplateAuthDTO cnvVisualTemplatePO2DTO(VisualTemplatePO po) {
        TemplateAuthDTO dto = new TemplateAuthDTO();
        dto.setTemplateId(po.getTemplateId());
        dto.setName(po.getName());
        dto.setCoverUrl(po.getCoverUrl());
        dto.setPreviewVideoUrl(po.getPreviewVideoUrl());
        dto.setCreateDt(po.getCreateDt());
        dto.setStatus(po.getStatus());
        return dto;
    }
}
