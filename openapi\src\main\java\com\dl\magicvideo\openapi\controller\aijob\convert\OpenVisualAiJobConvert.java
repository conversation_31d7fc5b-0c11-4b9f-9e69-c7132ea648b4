package com.dl.magicvideo.openapi.controller.aijob.convert;

import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.magicvideo.biz.common.util.CeilingUtils;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.openapi.controller.aijob.vo.OpenVisualDmJobInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 17:14
 */
public class OpenVisualAiJobConvert {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpenVisualAiJobConvert.class);

    public static final String EDIT = "-编辑";

    public static final String VIDEO_PRODUCE = "-视频合成";

    public static OpenVisualDmJobInfoVO buildVisualDmJobInternalInfoDTO(VisualAiJobExtPO aiJobExtPO,
            Map<Long, VisualTemplatePO> templateMap, Map<Long, String> creatorIdNameMap,
            DigitalManJobBO digitalManJobBO, Map<String, DaVirtualManScenesDTO> dmSceneMap,
            Map<Long, VisualProduceJobExtendPO> produceJobExtendMap) {
        OpenVisualDmJobInfoVO result = new OpenVisualDmJobInfoVO();
        result.setJobId(aiJobExtPO.getJobId()+"");
        result.setProduceJobId(aiJobExtPO.getProduceJobId()+"");
        result.setTemplateId(aiJobExtPO.getTemplateId()+"");
        VisualTemplatePO templatePO = templateMap.get(aiJobExtPO.getTemplateId());
        if (Objects.nonNull(templatePO)) {
            result.setTemplateName(templatePO.getName());
        }
        result.setAiJobType(aiJobExtPO.getJobType());
        result.setJobStatus(aiJobExtPO.getJobStatus());
        result.setCreateDt(aiJobExtPO.getCreateDt());
        result.setCreatorName(creatorIdNameMap.get(aiJobExtPO.getCreateBy()));

        result.setCeilingMinutes(CeilingUtils.millsToMinutes(aiJobExtPO.getDuration()));

        if (StringUtils.isBlank(result.getProduceJobId())) {
            result.setCreateSourceDesc(result.getTemplateName() + EDIT);
        } else {
            result.setCreateSourceDesc(result.getTemplateName() + VIDEO_PRODUCE);
        }

        if (Objects.nonNull(digitalManJobBO)) {
            result.setChannelName(ServiceChannelEnum.getNameByCode(digitalManJobBO.getChannel()));
            result.setSceneId(digitalManJobBO.getSceneId());
            DaVirtualManScenesDTO scenesDTO = dmSceneMap
                    .get(digitalManJobBO.getChannel() + "-" + digitalManJobBO.getSceneId());
            if (Objects.nonNull(scenesDTO)) {
                result.setDmName(scenesDTO.getVmName());
                result.setGender(scenesDTO.getGender());
                result.setSceneName(scenesDTO.getSceneName());
            } else {
                LOGGER.warn("该数字人任务查找不到数字人场景信息,aiJobId:{},channel:{},sceneId:{}", aiJobExtPO.getJobId(),
                        digitalManJobBO.getChannel(), digitalManJobBO.getSceneId());
            }
        }

        if (Objects.nonNull(produceJobExtendMap.get(aiJobExtPO.getProduceJobId()))) {
            result.setExtUserId(produceJobExtendMap.get(aiJobExtPO.getProduceJobId()).getExtUserId());
        }
        return result;
    }
}
