package com.dl.magicvideo.web.controllers.voice.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-07-24 11:08
 */
@Getter
@Setter
@ApiModel("tts字幕标记入参")
public class TtsSubtitleMarkParam {

    @NotEmpty(message = "字幕列表不能为空")
    @ApiModelProperty(value = "字幕列表")
    private List<TtsSubtitleParam> subtitles;

    @NotEmpty(message = "需要标记的词列表不能为空")
    @ApiModelProperty(value = "需要标记的词列表")
    private List<String> needToMarkWords;

}
