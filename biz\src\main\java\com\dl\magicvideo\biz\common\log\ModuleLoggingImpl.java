package com.dl.magicvideo.biz.common.log;

import com.dl.framework.common.logging.ModuleLogging;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
public class ModuleLoggingImpl implements ModuleLogging {
    @Autowired
    private HttpServletRequest request;

    @Override
    public void writeExceptionByInfo(Exception ex) {
    }

    @Override
    public void writeSimpleExceptionByInfo(Exception ex) {
    }

    @Override
    public void writeExceptionByWarn(Exception ex) {
    }

    @Override
    public void writeExceptionByError(Exception ex) {
    }

    @Override
    public void writeEmailLogByError(Exception ex) {
        log.error(ex.getMessage(), ex);
    }

}
