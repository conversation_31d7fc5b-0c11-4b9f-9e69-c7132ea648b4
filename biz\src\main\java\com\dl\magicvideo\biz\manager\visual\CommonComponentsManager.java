package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.common.SearchBO;
import com.dl.magicvideo.biz.dal.visual.po.CommonComponentsPO;
import com.dl.magicvideo.biz.manager.visual.bo.CommonCompentsBO;
import io.micrometer.core.instrument.search.Search;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【TenantMaterialFolderPO】的数据库操作Service
 * @createDate 2023-06-08 16:23:52
 */
public interface CommonComponentsManager extends IService<CommonComponentsPO> {
    /**
     * 分页组件列表
     *
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List < com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO>>
     * @Author: zhousx
     * @Date: 2023/4/24 13:46
     */
    ResponsePageQueryDO<List<CommonComponentsPO>> pageQuery(CommonCompentsBO bo);

    /**
     * 根据标签id查询最新素材
     * @param tenantCode
     * @param tagIds
     * @param rowNumber
     * @return
     */
    List<CommonComponentsPO> latestComponentsByTagIds(String tenantCode, List<Long> tagIds, Integer rowNumber);
}
