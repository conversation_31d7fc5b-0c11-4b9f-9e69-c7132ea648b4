package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.visual.TenantMaterialMapper;
import com.dl.magicvideo.biz.dal.visual.po.LatestTenantMaterialPO;
import com.dl.magicvideo.biz.dal.visual.po.TenantMaterialPO;
import com.dl.magicvideo.biz.manager.visual.TenantMaterialManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【tenant_material】的数据库操作Service实现
 * @createDate 2023-04-25 17:03:16
 */
@Service
public class TenantMaterialManagerImpl extends ServiceImpl<TenantMaterialMapper, TenantMaterialPO>
        implements TenantMaterialManager {

    @Override
    public List<LatestTenantMaterialPO> latestMaterialByFolderIds(String tenantCode, Set<Long> folderIds,
            Integer rowNumber, Integer materialType) {
        Assert.isTrue(CollectionUtils.isNotEmpty(folderIds), "文件夹id列表不能为空");

        return this.getBaseMapper().latestMaterialByFolderIds(tenantCode, folderIds, rowNumber, materialType);
    }
}




