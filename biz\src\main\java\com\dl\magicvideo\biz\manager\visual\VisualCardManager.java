package com.dl.magicvideo.biz.manager.visual;

import com.dl.magicvideo.biz.dal.visual.po.VisualCardPO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.manager.visual.bo.CardBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualCardDTO;

/**
* <AUTHOR>
* @description 针对表【visual_card】的数据库操作Service
* @createDate 2023-04-24 16:16:02
*/
public interface VisualCardManager extends IService<VisualCardPO> {
    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: com.dl.magicvideo.biz.manager.visual.dto.VisualCardDTO
     * @Author: zhousx
     * @Date: 2023/4/24 17:16
     */
    VisualCardDTO add(CardBO bo);

    /**
     * 功能描述: <br>
     * @Param: [cardId]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/4/24 17:32
     */
    void delete(Long cardId);

    void update(CardBO bo);
}
