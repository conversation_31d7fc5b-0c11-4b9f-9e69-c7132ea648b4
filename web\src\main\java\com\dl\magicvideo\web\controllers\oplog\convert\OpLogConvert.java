package com.dl.magicvideo.web.controllers.oplog.convert;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.client.basicservice.dto.OpLogDTO;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogPageReq;
import com.dl.magicvideo.biz.manager.oplog.consts.OpLogConsts;
import com.dl.magicvideo.biz.manager.oplog.enums.MagicOpTypeEnum;
import com.dl.magicvideo.web.controllers.oplog.param.OpLogPageParam;
import com.dl.magicvideo.web.controllers.oplog.vo.OpLogVO;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-18 11:31
 */
public class OpLogConvert {

    public static OpLogPageReq cnvOpLogPageParam2Req(OpLogPageParam param, String tenantCode) {
        OpLogPageReq req = new OpLogPageReq();
        req.setTenantCode(tenantCode);
        req.setOpUserId(param.getOpUserId());
        req.setPageIndex(param.getPageIndex());
        req.setPageSize(param.getPageSize());
        req.setOpObject(param.getOpObject());
        req.setOpKey(param.getOpKey());
        req.setOpSince(param.getOpSince());
        req.setOpUntil(param.getOpUntil());
        //根据数影操作类型 转换成标准操作类型
        if (StringUtils.isNotBlank(param.getMagicOpType())) {
            MagicOpTypeEnum magicOpTypeEnum = MagicOpTypeEnum.convert(param.getMagicOpType());
            if (Objects.nonNull(magicOpTypeEnum)) {
                req.setOpObject(magicOpTypeEnum.getOpObjectEnum().getCode());
                req.setOpType(magicOpTypeEnum.getOpTypeEnum().getOpType());
            }
        }
        return req;
    }

    public static OpLogVO cnvOpLogDTO2VO(OpLogDTO input) {
        OpLogVO result = new OpLogVO();
        result.setLogId(String.valueOf(input.getLogId()));
        result.setTenantCode(input.getTenantCode());
        result.setBizCode(input.getBizCode());
        result.setOpObject(input.getOpObject());
        result.setOpKey(input.getOpKey());
        result.setOpType(input.getOpType());
        MagicOpTypeEnum magicOpTypeEnum = MagicOpTypeEnum.convert(input.getOpObject(), input.getOpType());
        if (Objects.nonNull(magicOpTypeEnum)) {
            result.setMagicOpType(magicOpTypeEnum.getType());
            result.setMagicOpTypeDesc(magicOpTypeEnum.getDesc());
        }
        result.setOpBefore(input.getOpBefore());
        result.setOpAfter(input.getOpAfter());
        result.setRemark(input.getRemark());
        result.setOpUserId(input.getOpUserId());
        result.setOpUserName(input.getOpUserName());
        result.setOpDt(input.getOpDt());
        if (StringUtils.isNotBlank(input.getExtData())) {
            Map<String, String> extDataMap = JSONUtil.toBean(input.getExtData(), Map.class);
            result.setIp(extDataMap.get(OpLogConsts.IP));
        }
        return result;
    }
}
