package com.dl.magicvideo.biz.dal.visual.param;

import com.dl.framework.common.bo.PageQueryDO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-13 16:43
 */
@Data
public class VisualAiJobPageBO extends PageQueryDO {

    private String tenantCode;

    private Date minDt;

    private Date maxDt;

    private Integer aiJobType;

    private List<Integer> aiJobTypeList;

    private List<Integer> jobStatusList;

    /**
     * 是否需要查询已删除的数据
     * 0-否  即只查询未删除的数据
     * 1-是  即查询所有数据
     * 默认为0
     */
    private Integer needQueryDeleted = 0;

}
