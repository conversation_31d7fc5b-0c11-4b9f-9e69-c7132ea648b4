package com.dl.magicvideo.web.controllers.data;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.manager.visual.vo.FundMarketDetailVO;
import com.dl.magicvideo.biz.manager.visual.vo.ManagerDetailVO;
import com.dl.magicvideo.web.controllers.data.param.DataListParam;
import com.dl.magicvideo.web.controllers.data.param.FundMarketDetailParam;
import com.dl.magicvideo.web.controllers.data.param.ManagerDetailParam;
import com.dl.magicvideo.web.controllers.data.process.DataProcess;
import com.dl.magicvideo.web.controllers.data.vo.DataListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @describe: 数据相关
 * @author: zhousx
 * @date: 2023/5/6 11:47
 */
@Slf4j
@RestController
@RequestMapping("/visual/data")
@Api("数据相关")
public class DataController {
    @Autowired
    private DataProcess dataProcess;

    /**
     * 接口列表
     *
     * @param param
     * @return
     */
    @PostMapping("/interfacelist")
    @ApiOperation("接口列表")
    public ResultModel<List<DataListVO>> interfaceList(@RequestBody @Validated DataListParam param) {
        return ResultModel.success(dataProcess.interfaceList(param));
    }

    /**
     * 基金经理基础信息
     *
     * @param param
     * @return
     */
    @PostMapping("/common/manager/detail")
    @ApiOperation("理财经理")
    public ResultModel<List<ManagerDetailVO>> managerDetail(@RequestBody @Validated ManagerDetailParam param) {
        return ResultModel.success(dataProcess.managerDetail(param));
    }

    /**
     * 基金行情信息
     *
     * @param param
     * @return
     */
    @PostMapping("/common/fund/marketdetail")
    @ApiOperation("基金行情详情")
    public ResultModel<List<FundMarketDetailVO>> fundMarketDetail(@RequestBody @Validated FundMarketDetailParam param) {
        return ResultModel.success(dataProcess.fundMarketDetail(param));
    }
}
