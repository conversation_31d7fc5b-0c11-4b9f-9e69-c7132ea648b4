package com.dl.magicvideo.biz.dal.statistics.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName statistics_efficiency
 */
@TableName(value ="statistics_count")
@Data
public class StatisticsCountPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计ID
     */
    private Long countId;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 租户Name
     */
    private String tenantName;

    /**
     * 统计value 合成成功的数量
     */
    private String statisticsValue;

    /**
     * 统计时间
     */
    private String statisticsTime;

    /**
     * 合成失败的数量
     */
    private Integer failNum;

    /**
     * 合成成功的视频总时长，单位：ms
     */
    private Long totalDuration;

    /**
     * 总的字符
     */
    private Long totalTextLength;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}