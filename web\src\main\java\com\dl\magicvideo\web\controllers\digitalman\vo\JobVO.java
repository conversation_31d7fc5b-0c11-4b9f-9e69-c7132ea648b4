package com.dl.magicvideo.web.controllers.digitalman.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: JobVO
 * @author: zhousx
 * @date: 2023/6/9 13:35
 */
@Data
public class JobVO {
    @ApiModelProperty("任务id")
    private String jobId;

    @ApiModelProperty("任务状态 0-未开始 1-进行中 2-合成成功 3-合成失败")
    private Integer jobStatus;

    @ApiModelProperty("数字人视频地址")
    private String mediaInfo;

    @ApiModelProperty("数字人视频时长，毫秒")
    private Integer duration;

    @ApiModelProperty("字幕")
    private List<DmSubtitleVO> subtitles;
}
