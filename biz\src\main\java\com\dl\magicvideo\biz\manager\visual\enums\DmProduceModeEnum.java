package com.dl.magicvideo.biz.manager.visual.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-02 10:49
 */
public enum DmProduceModeEnum {

    //拼接数字人文本，生成一个数字人视频，再利用ASR拆解
    MERGE_GEN_DM_THEN_ASR(0,"模板所有数字人卡片合并为1个请求，合成1个数字人视频，并通过ASR识别时间戳进行片段分割"),
    SEPARATE_GEN_DM(1,"模板每个数字人卡片都单独请求合成数字人视频。"),
    //通过TTS合并驱动数字人
    MERGE_GEN_DM_WITH_TTS(2,"模板每个数字人卡片都先生成音频，再将各个音频合并成大的音频，驱动生成1个数字人视频");

    private Integer mode;

    private String desc;

    DmProduceModeEnum(Integer mode, String desc) {
        this.mode = mode;
        this.desc = desc;
    }

    public Integer getMode() {
        return mode;
    }

    public String getDesc() {
        return desc;
    }
}
