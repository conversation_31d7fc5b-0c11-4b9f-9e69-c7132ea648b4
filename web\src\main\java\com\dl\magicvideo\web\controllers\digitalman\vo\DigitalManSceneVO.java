package com.dl.magicvideo.web.controllers.digitalman.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @describe: DigitalManSceneVO
 * @author: zhousx
 * @date: 2023/6/9 17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DigitalManSceneVO {
    @ApiModelProperty("数字人id")
    private String bizId;

    @ApiModelProperty("场景id(重要，视频合成必填ID)")
    private String sceneId;

    @ApiModelProperty("场景名称")
    private String sceneName;

    @ApiModelProperty("服装信息：0 个人服饰 1 黑衣服 2 蓝礼服")
    private Integer cloth;

    @ApiModelProperty("姿态信息：1 坐姿; 2 半身站姿; 3 全身站姿")
    private Integer pose;

    @ApiModelProperty("分辨率：1 1080x1920; 2 1920x1080")
    private Integer resolution;

    @ApiModelProperty("场景封面地址")
    private String coverUrl;

    @ApiModelProperty("场景样例视频地址")
    private String exampleUrl;

    @ApiModelProperty("场景样例文本")
    private String exampleText;

    @ApiModelProperty("场景样例时长，毫秒")
    private Integer exampleDuration;
}
