package com.dl.magicvideo.biz.manager.visual.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-04 14:10
 */
public enum VisualProduceJobSourceEnum {
    //来源 0-平台触发(即页面点击合成按钮) 1-批量合成接口触发 2-excel批量合成接口触发 3-开放平台合成接口触发 4-内部合成接口触发 5-交付计划触发
    /**
     * 0-平台触发(即页面点击合成按钮)
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController.produce(java.lang.Long)
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController.produce(com.dl.magicvideo.web.controllers.template.param.ProduceParam)
     */
    PLATFORM(0,"平台触发"),
    /**
     * 1-批量合成接口触发
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController.batchProduce
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController.batchProduceNew
     */
    BATCH_PRODUCE(1,"批量合成接口触发"),
    /**
     * 2-excel批量合成接口触发
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController.excelBatchProduce
     */
    EXCEL_BATCH_PRODUCE(2,"excel批量合成接口触发"),
    /**
     * 3-开放平台合成接口触发
     * com.dl.magicvideo.openapi.controller.produce.OpenProduceController.produce
     */
    OPEN_PRODUCE(3,"开放平台合成接口触发"),
    /**
     * 4-内部合成接口触发
     * com.dl.magicvideo.web.controllers.internal.visual.VisualProduceJobInternalController.generate
     */
    INTERNAL_PRODUCE(4, "内部合成接口触发"),
    /**
     * 5-交付计划触发
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController#produceWithPlan(com.dl.magicvideo.web.controllers.template.param.PlanParam)
     */
    PRODUCE_WITH_PLAN(5, "交付计划触发"),

    /**
     * 6-AIGC小助手合成接口-通用工具触发
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController#aigcProduce(com.dl.magicvideo.web.controllers.template.param.AigcProduceParam)
     */
    AIGC_PRODUCE_COMMON_TOOL(6, "AIGC小助手合成接口-通用工具触发"),

    /**
     * 7-AIGC小助手合成接口-热点事件题材工具触发
     * com.dl.magicvideo.web.controllers.template.VisualTemplateController#aigcProduce(com.dl.magicvideo.web.controllers.template.param.AigcProduceParam)
     */
    AIGC_PRODUCE_HOT_EVENT_SUBJECT_MATTER_TOOL(7,"AIGC小助手合成接口-热点事件题材工具触发")

    ;

    private Integer code;

    private String Desc;

    VisualProduceJobSourceEnum(Integer code, String desc) {
        this.code = code;
        Desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return Desc;
    }
}
