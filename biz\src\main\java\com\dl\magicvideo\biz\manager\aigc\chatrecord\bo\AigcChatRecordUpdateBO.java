package com.dl.magicvideo.biz.manager.aigc.chatrecord.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 14:42
 */
@Data
public class AigcChatRecordUpdateBO {

    /**
     * 记录id
     */
    private Long recordId;

    /**
     * 聊天内容
     *
     * 当内容类型是1:文本时，存的是字符串
     * 当内容类型是2~7:文件时，存的是AigcChatRecordContentFileBO对应的json串
     * 当内容类型是8:视频合成成功时，存的是AigcChatRecordProduceSuccessBO对应的json串
     * 当内容类型是9:视频合成失败时，存的是AigcChatRecordProduceFailBO对应的json串
     * 当内容类型是10:视频合成中时，存的是AigcChatRecordProduceIngBO对应的json串
     * 当内容类型是11:热点事件题材提问时，存的是AigcChatRecordHotEventSubjectMatterAskBO对应的json串
     * 当内容类型是12:热点事件题材回答时，存的是AigcChatRecordHotEventSubjectMatterAnswerBO对应的json串
     * 当内容类型是13:文本转视频时，存的是AigcChatRecordText2VideoBO对应的json串
     */
    private String content;

    /**
     * 聊天内容类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum
     */
    private Integer contentType;

}
