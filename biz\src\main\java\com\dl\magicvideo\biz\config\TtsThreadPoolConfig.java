package com.dl.magicvideo.biz.config;

import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.magicvideo.biz.common.util.EnvUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-26 14:29
 */
@Data
@Configuration
public class TtsThreadPoolConfig {

    @Resource
    private EnvUtil envUtil;

    private ExecutorService commonTtsPool;

    private ExecutorService commonTtsSerialPool;

    private ExecutorService volcEngineTtsPool;

    private ExecutorService aliyunTtsPool;

    private ExecutorService ivhTtsPool;

    @Bean("commonTtsPool")
    public ExecutorService commonTtsPool() {
        ExecutorService commonTtsPool = new ThreadPoolExecutor(2, 2, 30, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(512),
                new ThreadFactoryBuilder().setNameFormat("common-tts-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        this.commonTtsPool = commonTtsPool;
        return commonTtsPool;
    }

    @Bean("commonTtsSerialPool")
    public ExecutorService commonTtsSerialPool() {
        ExecutorService commonTtsSerialPool = new ThreadPoolExecutor(1, 1, 30, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(512),
                new ThreadFactoryBuilder().setNameFormat("common-tts-serial-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        this.commonTtsSerialPool = commonTtsSerialPool;
        return commonTtsSerialPool;
    }

    @Bean("ivhTtsPool")
    public ExecutorService ivhTtsPool() {
        ExecutorService ivhTtsPool = new ThreadPoolExecutor(2, 2, 30, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(512),
                new ThreadFactoryBuilder().setNameFormat("ivh-tts-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        this.ivhTtsPool = ivhTtsPool;
        return ivhTtsPool;
    }

    @Bean("volcEngineTtsPool")
    public ExecutorService volcEngineTtsPool() {
        ExecutorService volcEngineTtsPool = new ThreadPoolExecutor(1, 1, 30, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(512),
                new ThreadFactoryBuilder().setNameFormat("volc-engine-tts-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        this.volcEngineTtsPool = volcEngineTtsPool;
        return volcEngineTtsPool;
    }

    @Bean("aliyunTtsPool")
    public ExecutorService aliyunTtsPool() {
        ExecutorService aliyunTtsPool = new ThreadPoolExecutor(4, 20, 30, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(16),
                new ThreadFactoryBuilder().setNameFormat("aliyun-tts-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        this.aliyunTtsPool = aliyunTtsPool;
        return aliyunTtsPool;
    }

    /**
     * 获取tts线程池
     *
     * @param channel
     * @return
     */
    public ExecutorService getTtsThreadPool(Integer channel) {
        //阿里云合成音 返回独立线程池
        if (ServiceChannelEnum.ALIYUN.getCode().equals(channel)) {
            return aliyunTtsPool;
        }
        //火山引擎 返回独立线程池
        if (ServiceChannelEnum.VOLC_ENGINE.getCode().equals(channel)) {
            return volcEngineTtsPool;
        }
        //腾讯云数智人TTS
        if (ServiceChannelEnum.IVH.getCode().equals(channel)) {
            //测试环境 需要串行合成
            if (envUtil.isTest()) {
                return commonTtsSerialPool;
            }
            return ivhTtsPool;
        }
        //孚嘉科技的腾讯云数智人只有1路
        if (ServiceChannelEnum.FUJIA_IVH.getCode().equals(channel)) {
            return commonTtsSerialPool;
        }
        //定力数影数字人，也只有一路
        if (Objects.equals(16, channel)) {
            return commonTtsSerialPool;
        }

        return commonTtsPool;
    }

}
