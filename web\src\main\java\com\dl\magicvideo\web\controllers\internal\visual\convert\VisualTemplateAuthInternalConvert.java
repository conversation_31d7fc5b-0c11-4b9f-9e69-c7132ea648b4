package com.dl.magicvideo.web.controllers.internal.visual.convert;

import com.dl.magicvideo.biz.dal.visual.po.VisualAiConfigPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualCardPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualDynamicNodePO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualAiConfigInternalVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualCardInternalVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualDynamicNodeInternalVO;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class VisualTemplateAuthInternalConvert {

    public static VisualAiConfigInternalVO cnvVisualAiConfigInternalPO2InternalVO(VisualAiConfigPO input){
        if (Objects.isNull(input)){
            return null;
        }
        VisualAiConfigInternalVO result = new VisualAiConfigInternalVO();
        result.setTemplateId(input.getTemplateId());
        result.setTtsId(input.getTtsId());
        result.setContent(input.getContent());
        result.setCardId(input.getCardId());
        result.setStart(input.getStart());
        result.setNodeId(input.getNodeId());
        result.setMaxLength(input.getMaxLength());
        result.setType(input.getType());
        result.setEndDelay(input.getEndDelay());
        result.setIsHide(input.getIsHide());
        result.setEnableSubtitle(input.getEnableSubtitle());
        return result;
    }

    public static VisualCardInternalVO cnvVisualCardPO2InternalVO(VisualCardPO input){
        if (Objects.isNull(input)){
            return null;
        }
        VisualCardInternalVO result = new VisualCardInternalVO();
        result.setDuration(input.getDuration());
        result.setName(input.getName());
        result.setSort(input.getSort());
        result.setResolution(input.getResolution());
        result.setCoverUrl(input.getCoverUrl());
        result.setStatus(input.getStatus());
        result.setIsDeleted(input.getIsDeleted());
        result.setRenderData(input.getRenderData());
        result.setTemplateId(input.getTemplateId());
        result.setCardId(input.getCardId());
        result.setLightEditConfigs(input.getLightEditConfigs());
        return result;
    }

    public static VisualDynamicNodeInternalVO cnvVisualDynamicNodePO2InternalVO(VisualDynamicNodePO input){
        if (Objects.isNull(input)){
            return null;
        }
        VisualDynamicNodeInternalVO result = new VisualDynamicNodeInternalVO();
        result.setIsEnabled(input.getIsEnabled());
        result.setType(input.getType());
        result.setDuration(input.getDuration());
        result.setCoverUrl(input.getCoverUrl());
        result.setNodeId(input.getNodeId());
        result.setCardId(input.getCardId());
        result.setTemplateId(input.getTemplateId());
        return result;
    }

}
