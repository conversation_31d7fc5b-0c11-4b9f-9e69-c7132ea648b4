package com.dl.magicvideo.biz.manager.subjectmatter.util;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.SubjectTreeDTO;

import java.util.*;

public class CustomNameComparator implements Comparator<SubjectTreeDTO> {
    private final List<String> sortOrderList;

    public CustomNameComparator(List<String> sortOrderList) {
        this.sortOrderList = sortOrderList;
    }

    @Override
    public int compare(SubjectTreeDTO s1, SubjectTreeDTO s2) {
        OptionalInt index1 = sortOrderList.stream()
                .filter(name -> s1.getName().contains(name))
                .mapToInt(sortOrderList::indexOf)
                .findFirst();

        OptionalInt index2 = sortOrderList.stream()
                .filter(name -> s2.getName().contains(name))
                .mapToInt(sortOrderList::indexOf)
                .findFirst();

        // 如果都有匹配项
        if (index1.isPresent() && index2.isPresent()) {
            return Integer.compare(index1.getAsInt(), index2.getAsInt());
        }
        // 如果一个有匹配项，另一个没有，则有匹配项的排前面
        else if (index1.isPresent()) {
            return -1;
        }
        else if (index2.isPresent()) {
            return 1;
        }
        // 都没有匹配项，按默认字符串比较
        else {
            return s1.getName().compareTo(s2.getName());
        }
    }

    public static void main(String[] args) {
        String originalData = "";
        SubjectTreeDTO subjectTreeDTO = JSONUtil.toBean(originalData, SubjectTreeDTO.class);
        List<String> dynamicOrder = new ArrayList<>(Arrays.asList("中游", "下游", "上游"));
        CustomNameComparator comparator = new CustomNameComparator(dynamicOrder);

        Collections.sort(subjectTreeDTO.getChildren(), comparator);
        System.out.println("subjectTreeDTO = " + JSONUtil.toJsonStr(subjectTreeDTO));
    }
}
