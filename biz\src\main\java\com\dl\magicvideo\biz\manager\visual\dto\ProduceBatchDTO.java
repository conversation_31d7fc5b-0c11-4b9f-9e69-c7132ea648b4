package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;

import java.util.Date;

/**
 * @describe: ProduceBatchDTO
 * @author: zhousx
 * @date: 2023/6/19 18:00
 */
@Data
public class ProduceBatchDTO {
    /**
     * 批次id
     */
    private Long batchId;

    /**
     * 批次名称
     */
    private String batchName;

    /**
     * 任务状态：-1-未开始 0-排队中 1-生产中 2-生产完成 3-生产异常 4-已取消
     */
    private Integer status;

    /**
     * 批次下中作业数
     */
    private Integer jobNumTotal;

    /**
     * 批次下已完成作业数
     */
    private Integer jobNumSuccess;

    /**
     * 租户id
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 生产人名称
     */
    private String creatorName;

    /**
     *
     */
    private Long createBy;

    /**
     *
     */
    private Long modifyBy;

    /**
     *
     */
    private Date createDt;

    /**
     *
     */
    private Date modifyDt;
}
