package com.dl.magicvideo.biz.manager.subjectmatter.convert;

import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectAddBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterSaveBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.XMindBO;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.NodeEffectDTO;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.SubjectTreeDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:45
 */
public class SubjectMatterConvert {

    public static SubjectMatterPO cvnSubjectMatterSaveBO2PO(SubjectMatterSaveBO input) {
        SubjectMatterPO result = new SubjectMatterPO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setImgUrl(input.getImgUrl());
        result.setExcelUrl(input.getExcelUrl());
        result.setLevel(input.getLevel());
        result.setParentId(input.getParentId());
        result.setJsonUrl(input.getJsonUrl());
        result.setModifyName(input.getOperatorName());
        result.setModifyBy(input.getOperatorId());
        result.setModifyDt(new Date());
        result.setIsDeleted(Const.ZERO);
        return result;
    }

    public static SubjectMatterPO cvnSubjectAddBO2PO(SubjectAddBO input) {
        SubjectMatterPO result = new SubjectMatterPO();
        result.setBizId(input.getBizId());
        result.setTitle(input.getTitle());
        //xMind文件一级题材名称
        result.setName(input.getXmindBO().getName());
        result.setImgUrl(input.getImgUrl());
        result.setXmindUrl(input.getXmindUrl());
        result.setXmindJson(input.getXmindStr());
        result.setIntro(input.getIntro());
        result.setPrompt(input.getPrompt());
        result.setModifyName(input.getOperatorName());
        result.setModifyBy(input.getOperatorId());
        result.setModifyDt(new Date());
        return result;
    }

    public static List<SubjectMatterPO> convertXMindBOToSubjectMatterPO(XMindBO xmindBO, Long rootId, Long parentId,
                                                                        Integer level, List<SubjectMatterPO> resultList, String parentSubjectPath, SubjectAddBO subjectAddBO) {
        if (xmindBO != null) {
            if (level == 1) {
                for (XMindBO childBO : xmindBO.getChildren()) {
                    // 递归调用以处理子节点，同时更新parentId和level
                    resultList.addAll(convertXMindBOToSubjectMatterPO(childBO, rootId, rootId, level + 1, resultList, rootId.toString(), subjectAddBO));
                }
            } else {
                HostTimeIdg hostTimeIdg = new HostTimeIdg();
                SubjectMatterPO subjectMatterPO = new SubjectMatterPO();
                subjectMatterPO.setBizId(hostTimeIdg.generateId().longValue());
                subjectMatterPO.setName(xmindBO.getName());
                subjectMatterPO.setLevel(xmindBO.getLevel());
                subjectMatterPO.setParentId(parentId);
                subjectMatterPO.setRootId(rootId);
                subjectMatterPO.setSubjectPath(parentSubjectPath + Const.VERTICAL_LINE + subjectMatterPO.getBizId());
                // 是否有子节点 1（有子节点）0（无子节点）
                subjectMatterPO.setIsHaveChild(xmindBO.getChildren().isEmpty() ? 0 : 1);
                subjectMatterPO.setType(Const.TWO);

                subjectMatterPO.setCreateDt(new Date());
                subjectMatterPO.setCreatorName(subjectAddBO.getOperatorName());
                subjectMatterPO.setCreateBy(subjectAddBO.getOperatorId());
                subjectMatterPO.setModifyName(subjectAddBO.getOperatorName());
                subjectMatterPO.setModifyBy(subjectAddBO.getOperatorId());
                subjectMatterPO.setModifyDt(new Date());

                resultList.add(subjectMatterPO);

                for (XMindBO childBO : xmindBO.getChildren()) {
                    // 递归调用以处理子节点，同时更新parentId和level
                    resultList.addAll(convertXMindBOToSubjectMatterPO(childBO, rootId, subjectMatterPO.getBizId(), level + 1, resultList, subjectMatterPO.getSubjectPath(), subjectAddBO));
                }
            }
        }
        return resultList;

    }

    /**
     * 构建节点树
     * @param nodes
     * @param nodeEffectDTOMap
     * @return
     */
    public static SubjectTreeDTO buildTree(List<SubjectMatterPO> nodes, Map<String, NodeEffectDTO> nodeEffectDTOMap) {
        Map<String, SubjectTreeDTO> nodeMap = new HashMap<>();
        for (SubjectMatterPO po : nodes) {
            SubjectTreeDTO node = new SubjectTreeDTO();
            node.setBizId(po.getBizId().toString());
            node.setName(po.getName());
            node.setParentId(po.getParentId().toString());
            node.setLevel(po.getLevel());
            if (nodeEffectDTOMap != null && nodeEffectDTOMap.get(node.getName()) != null){
                NodeEffectDTO effectDTO = nodeEffectDTOMap.get(node.getName());
                node.setEffectReason(effectDTO.getEffectReason());
                node.setEffectResult(effectDTO.getEffectResult());
            }
            nodeMap.put(node.getBizId(), node);
        }
        for (SubjectTreeDTO node : nodeMap.values()) {
            String parentId = node.getParentId();
            if (parentId != null && nodeMap.containsKey(parentId)) {
                nodeMap.get(parentId).addChild(node);
            }
        }

        SubjectTreeDTO root = nodeMap.values().stream().filter(node -> Objects.equals(node.getParentId(), "0"))
                .findFirst().orElseThrow(() -> new RuntimeException("无法找到根节点"));

        //子节点排序
        sortChildren(root);

        return root;
    }

    /**
     * 子节点排序
     *
     * @param fatherTreeDTO
     */
    private static void sortChildren(SubjectTreeDTO fatherTreeDTO) {
        if (CollectionUtils.isEmpty(fatherTreeDTO.getChildren())) {
            return;
        }

        fatherTreeDTO.setChildren(
                fatherTreeDTO.getChildren().stream().sorted(Comparator.comparing(SubjectTreeDTO::getBizId))
                        .collect(Collectors.toList()));

        for (SubjectTreeDTO sonTree : fatherTreeDTO.getChildren()) {
            sortChildren(sonTree);
        }
    }

}
