package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName data_product
 */
@TableName(value ="common_components")
@Data
public class CommonComponentsPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long bizId;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 预览封面图
     */
    private String url;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 渲染数据
     */
    private String renderData;

    /**
     * 数据名称
     */
    private String name;
}