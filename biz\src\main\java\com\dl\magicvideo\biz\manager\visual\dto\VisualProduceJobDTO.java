package com.dl.magicvideo.biz.manager.visual.dto;

import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * @describe: VideoProduceJobDTO
 * @author: zhousx
 * @date: 2023/2/13 10:15
 */
@Data
public class VisualProduceJobDTO {
    /**
     * 任务id
     */
    private Long jobId;

    /**
     * 第三方平台任务id
     */
    private String extJobId;

    /**
     * 任务状态：0-未开始 1-合成中 2-合成成功 3-合成失败
     */
    private Integer status;

    /**
     * 合成视频地址
     */
    private String videoUrl;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 执行结果
     */
    private String jobResult;

    /**
     * 成品名称（模板名称）
     */
    private String name;

    /**
     * 封面图
     */
    private String coverUrl;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 媒资id
     */
    private String mediaId;

    /**
     * 视频时长
     */
    private Long duration;

    /**
     * 视频大小
     */
    private Long size;

    /**
     * 横版/竖版
     */
    private String resolutionType;

    /**
     * 横版/竖版
     */
    private String resolution;

    /**
     * 构建开始时间
     */
    private Date processDt;

    private Date completeDt;

    private String creatorName;

    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     *
     * @see: ShareConfStateEnum
     */
    private Integer shareConfState;

    /**
     * 推荐使用状态 0-未启用 1-已启用
     */
    private Integer recommendState;

    /**
     * 推送状态 0-未推送 1-推送成功 2-推送失败
     */
    private Integer pushStatus;

    /**
     * 其他格式的视频地址
     */
    private String otherFormatVideoUrl;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 类型
     *
     * @see JobTypeEnum
     */
    private Integer type;

    /**
     * 修改时间
     */
    private Date modifyDt;

    /**
     * 是否删除
     */
    private Integer isDeleted;

}
