package com.dl.magicvideo.web.controllers.internal.statistics.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-27 17:32
 */
@Data
public class StatisticsJobTenantSummaryInternalQueryParam implements Serializable {
    private static final long serialVersionUID = 4976794849100117753L;

    @ApiModelProperty("最小时间")
    private Date minDt;

    @ApiModelProperty("最大时间")
    private Date maxDt;

    @NotBlank(message = "租户编码不能为空")
    @ApiModelProperty("租户编码")
    private String tenantCode;
}
