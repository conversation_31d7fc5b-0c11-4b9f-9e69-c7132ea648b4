package com.dl.magicvideo.biz.manager.visual.bo;

import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateLightEditConfigDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ProduceJobContextBO {
    /**
     * 模板id
     */
    private Long templateId;
    /**
     * 动态参数
     */
    private String replaceData;
    /**
     * 批次id
     */
    private Long batchId;
    /**
     * 作品来源
     */
    private Integer source;
    /**
     * 名称
     */
    private String templateName;
    /**
     * tts配置
     */
    private TtsParamBO ttsParam;
    /**
     * 数字人视频合成方式。 0-模板每个卡片合成1次请求数字人合成视频，并通过ASR识别时间戳。1-模板每个卡片都请求数字人合成视频方式。
     */
    private Integer dmProduceMode;
    /**
     * 数字人参数
     */
    private DigitalManParamBO digitalManParamBO;
    /**
     * 交付计划id
     */
    private Long planId;
    /**
     * 替换变量
     */
    private String apiData;
    /**
     * 轻编辑配置
     */
    private TemplateLightEditConfigDTO lightEditConfigs;
    /**
     * 外部用户id
     */
    private String extUserId;
    /**
     * 渲染数据
     */
    private String renderData;
    /**
     * 动态节点
     */
    private List<DynamicNodeDTO> dynamicNodes;
    /**
     * 尺寸
     */
    private String resolution;
    /**
     * 作品封面
     */
    private String jobCoverUrl;
}
