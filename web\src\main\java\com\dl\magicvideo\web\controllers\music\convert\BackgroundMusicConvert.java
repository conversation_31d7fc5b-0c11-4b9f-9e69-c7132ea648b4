package com.dl.magicvideo.web.controllers.music.convert;

import com.dl.magicvideo.biz.dal.music.po.BackgroundMusicPO;
import com.dl.magicvideo.biz.manager.music.dto.BackgroundMusicDTO;
import com.dl.magicvideo.web.controllers.music.vo.BackgroundMusicVO;

import java.util.Objects;

public class BackgroundMusicConvert {

    public static BackgroundMusicVO cnvBackgroundMusicDTO2VO(BackgroundMusicDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        BackgroundMusicVO result = new BackgroundMusicVO();
        result.setBizId(input.getBizId() + "");
        result.setName(input.getName());
        result.setType(input.getType());
        result.setUrl(input.getUrl());
        result.setDuration(input.getDuration());
        return result;
    }

    public static BackgroundMusicVO cnvBackgroundMusicPO2VO(BackgroundMusicPO input){
        if (Objects.isNull(input)){
            return null;
        }
        BackgroundMusicVO result = new BackgroundMusicVO();
        result.setBizId(input.getBizId() + "");
        result.setName(input.getName());
        result.setType(input.getType());
        result.setDuration(input.getDuration());
        result.setUrl(input.getUrl());
        return result;
    }

}
