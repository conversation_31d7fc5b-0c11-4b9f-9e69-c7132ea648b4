package com.dl.magicvideo.biz.dal.subjectmatter;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.subjectmatter.param.SubjectMatterJoinPageParam;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:27
 */
@BaseDao
public interface SubjectMatterMapper extends BaseMapper<SubjectMatterPO> {

    Long joinPageCount(@Param("param") SubjectMatterJoinPageParam param);

    List<SubjectMatterPO> joinPageList(@Param("param") SubjectMatterJoinPageParam param);
}
