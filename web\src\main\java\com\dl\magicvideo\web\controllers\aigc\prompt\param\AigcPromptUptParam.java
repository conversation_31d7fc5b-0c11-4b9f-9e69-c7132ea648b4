package com.dl.magicvideo.web.controllers.aigc.prompt.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 09:50
 */
@Data
public class AigcPromptUptParam {

    @NotNull(message = "提示id不能为空")
    @ApiModelProperty("提示id")
    private Long bizId;

    @NotBlank(message = "提示名称不能为空")
    @ApiModelProperty("提示名称")
    private String name;

    @NotNull(message = "提示排序不能为空")
    @ApiModelProperty("提示排序")
    private Integer sort;

    @ApiModelProperty("关联文件")
    private AigcPromptFileParam relFile;

    @ApiModelProperty("内容")
    private AigcPromptContentParam content;
}
