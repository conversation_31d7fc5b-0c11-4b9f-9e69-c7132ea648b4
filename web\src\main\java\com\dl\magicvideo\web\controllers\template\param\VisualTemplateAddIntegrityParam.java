package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 17:37
 */
@Data
public class VisualTemplateAddIntegrityParam {

    @NotBlank
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("模板尺寸")
    private String resolution;

    @ApiModelProperty("1-竖版 2-横版")
    private String resolutionType;

    @ApiModelProperty("tts配置")
    private String ttsParam;

    @ApiModelProperty("背景音乐")
    private String bgMusic;

    @ApiModelProperty("背景音乐配置")
    private String bgMusicParam;

    @ApiModelProperty("替换变量")
    private String replaceData;

    @ApiModelProperty("模板时长，毫秒")
    private Long duration;

    @ApiModelProperty("卡片")
    private List<CardUpdateParam> cards;

    /**
     * 接口信息列表(对应数据库的api_data)
     */
    @ApiModelProperty("数据网关替换变量")
    private String apiDataList;
}
