package com.dl.magicvideo.web.controllers.internal.statistics;

import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxTenantCodeParam;
import com.dl.magicvideo.biz.dal.statistics.param.TotalCountParam;
import com.dl.magicvideo.biz.dal.statistics.po.AiStatisticsTotalCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.manager.statistics.StatisticsAiJobManager;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobPageBO;
import com.dl.magicvideo.biz.manager.statistics.bo.StatisticsAiJobTenantSummaryQueryBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobDTO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsAiJobTenantSummaryDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsAiJobInternalDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsAiJobInternalSimpleDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsAiJobTenantInternalDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.StatisticsAiJobTenantSummaryInternalDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.param.InternalStatisticsAiJobTopTenantQueryParam;
import com.dl.magicvideo.web.controllers.internal.statistics.param.StatisticsAiJobInternalPageParam;
import com.dl.magicvideo.web.controllers.internal.statistics.param.StatisticsAiJobTenantSummaryInternalQueryParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 11:02
 */
@Component
public class StatisticsAiJobInternalProcess extends AbstractController {

    @Resource
    private StatisticsAiJobManager statisticsAiJobManager;

    public ResultPageModel<StatisticsAiJobInternalDTO> page(StatisticsAiJobInternalPageParam pageParam) {

        StatisticsAiJobPageBO pageBO = new StatisticsAiJobPageBO();
        pageBO.setTenantCode(pageParam.getTenantCode());
        pageBO.setMinDt(pageParam.getMinDt());
        pageBO.setMaxDt(pageParam.getMaxDt());
        pageBO.setAiJobType(pageParam.getAiJobType());
        pageBO.setPageIndex(pageParam.getPageIndex());
        pageBO.setPageSize(pageParam.getPageSize());

        ResponsePageQueryDO<List<StatisticsAiJobDTO>> responseDO = statisticsAiJobManager.pageQuery(pageBO);
        if (CollectionUtils.isEmpty(responseDO.getDataResult())) {
            return pageQueryModel(responseDO, Collections.emptyList());
        }

        return pageQueryModel(responseDO, responseDO.getDataResult().stream().map(input -> {
            StatisticsAiJobInternalDTO result = new StatisticsAiJobInternalDTO();
            result.setRecordId(input.getRecordId());
            result.setTenantCode(input.getTenantCode());
            result.setAiJobType(input.getAiJobType());
            result.setJobCount(input.getJobCount());
            result.setTotalCeilingMinutes(input.getTotalCeilingMinutes());
            result.setTotalTimeMillis(input.getTotalTimeMillis());
            result.setStatisticsTime(input.getStatisticsTime());
            result.setCreateDt(input.getCreateDt());
            result.setModifyDt(input.getModifyDt());
            result.setIsDeleted(input.getIsDeleted());
            result.setTenantName(input.getTenantName());
            return result;
        }).collect(Collectors.toList()));
    }

    public ResultModel<List<StatisticsAiJobTenantInternalDTO>> queryTopTenantStatistics(
            InternalStatisticsAiJobTopTenantQueryParam param) {
        Assert.notNull(param.getMinDt(),"最小时间不能为空");
        Assert.notNull(param.getMaxDt(),"最大时间不能为空");
        Assert.isTrue(param.getMinDt().before(param.getMaxDt()), "开始时间不能大于结束时间");
        Assert.isTrue(DateUtil.between(param.getMinDt(), param.getMaxDt(), Calendar.DAY_OF_MONTH) < 180, "查询范围不能超过半年");
        List<StatisticsAiJobTenantInternalDTO> dtoList = new ArrayList<>();

        //间隔的日期列表
        List<Date> dateList = DateUtil.getRangeDays(param.getMinDt(), param.getMaxDt());

        String startTime = DateUtil.format(param.getMinDt(), DateUtil.Y_M_D_H_M_S);
        String endTime = DateUtil.format(param.getMaxDt(), DateUtil.Y_M_D_H_M_S);
        //查询出周期内的TOP4 和 总量
        TopMaxTenantCodeParam topMaxTenantCodeParam = new TopMaxTenantCodeParam();
        topMaxTenantCodeParam.setStartTime(startTime);
        topMaxTenantCodeParam.setEndTime(endTime);
        topMaxTenantCodeParam.setCount(Objects.nonNull(param.getTopNumber()) ? param.getTopNumber() : 4);
        List<StatisticsCountTopMaxPO> statisticsCountTopMaxPOS = statisticsAiJobManager
                .topMaxTenantCode(topMaxTenantCodeParam);

        if (CollectionUtils.isEmpty(statisticsCountTopMaxPOS)) {
            StatisticsAiJobTenantInternalDTO dto = new StatisticsAiJobTenantInternalDTO();
            dto.setTenantCode("");
            dto.setTenantName("生产总量");
            dto.setStatisticsList(this.completeData(dateList, null));
            dtoList.add(dto);
            return ResultModel.success(dtoList);
        }

        List<String> tenantCodeList = statisticsCountTopMaxPOS.stream().map(StatisticsCountTopMaxPO::getTenantCode)
                .collect(Collectors.toList());

        TotalCountParam totalCountParam = new TotalCountParam();
        totalCountParam.setStartTime(startTime);
        totalCountParam.setEndTime(endTime);
        //总量数据
        List<AiStatisticsTotalCountPO> totalCount = statisticsAiJobManager.totalCount(totalCountParam);

        StatisticsAiJobTenantInternalDTO dto = new StatisticsAiJobTenantInternalDTO();
        dto.setTenantCode("");
        dto.setTenantName("生产总量");

        List<StatisticsAiJobInternalSimpleDTO> statisticsList = new ArrayList<>();
        totalCount.forEach(e -> {
            StatisticsAiJobInternalSimpleDTO countDTO = new StatisticsAiJobInternalSimpleDTO();
            countDTO.setStatisticsTime(DateUtil.parse(e.getStatisticsTime(), DateUtil.Y_M_D_H_M_S));
            countDTO.setTotalTimeMillis(e.getSumValue());
            statisticsList.add(countDTO);
        });

        dto.setStatisticsList(this.completeData(dateList, statisticsList));
        dtoList.add(dto);

        TopMaxMsgParam topMaxMsgParam = new TopMaxMsgParam();
        topMaxMsgParam.setStartTime(startTime);
        topMaxMsgParam.setEndTime(endTime);
        topMaxMsgParam.setTenantCodeList(tenantCodeList);
        //top系列租户
        List<StatisticsMsgPO> statisticsMsgPOS = statisticsAiJobManager.topMaxMsg(topMaxMsgParam);

        //租户，租户信息分组
        Map<String, List<StatisticsMsgPO>> map = statisticsMsgPOS.stream()
                .collect(Collectors.groupingBy(StatisticsMsgPO::getTenantCode));
        map.keySet().forEach(e -> {
            StatisticsAiJobTenantInternalDTO mapCountVO = new StatisticsAiJobTenantInternalDTO();
            mapCountVO.setTenantCode(e);
            List<StatisticsMsgPO> tentCountMsg = map.get(e);
            mapCountVO.setTenantName(tentCountMsg.get(0).getTenantName());
            List<StatisticsAiJobInternalSimpleDTO> countMsgList = new ArrayList<>();
            tentCountMsg.forEach(a -> {
                StatisticsAiJobInternalSimpleDTO countDTO = new StatisticsAiJobInternalSimpleDTO();
                countDTO.setStatisticsTime(DateUtil.parse(a.getStatisticsTime(), DateUtil.Y_M_D_H_M_S));
                countDTO.setTotalTimeMillis(Long.parseLong(a.getStatisticsValue()));
                countMsgList.add(countDTO);
            });

            mapCountVO.setStatisticsList(this.completeData(dateList, countMsgList));
            dtoList.add(mapCountVO);
        });

        return ResultModel.success(dtoList);
    }

    public ResultModel<StatisticsAiJobTenantSummaryInternalDTO> specificTenantSummary(
            StatisticsAiJobTenantSummaryInternalQueryParam param) {
        StatisticsAiJobTenantSummaryQueryBO bo = new StatisticsAiJobTenantSummaryQueryBO();
        bo.setMinDt(param.getMinDt());
        bo.setMaxDt(param.getMaxDt());
        bo.setAiJobTypeList(param.getAiJobTypeList());
        bo.setTenantCode(param.getTenantCode());
        StatisticsAiJobTenantSummaryDTO summary = statisticsAiJobManager.specificTenantSummary(bo);
        if(Objects.isNull(summary)){
            StatisticsAiJobTenantSummaryInternalDTO result = new StatisticsAiJobTenantSummaryInternalDTO();
            result.setSummaryTimeMillis(0L);
            result.setLatestModifyDt(DateUtil.getMinDate(new Date()));
            return ResultModel.success(result);
        }

        StatisticsAiJobTenantSummaryInternalDTO result = new StatisticsAiJobTenantSummaryInternalDTO();
        result.setSummaryTimeMillis(summary.getSummaryTimeMillis());
        result.setLatestModifyDt(summary.getLatestModifyDt());
        result.setSummaryTextLength(summary.getTotalTextLength());
        return ResultModel.success(result);
    }

    /**
     * 补全数据
     *
     * @param existSimpleDataList 未补全的列表
     * @return 补全后的列表
     */
    private List<StatisticsAiJobInternalSimpleDTO> completeData(List<Date> dateList,
            List<StatisticsAiJobInternalSimpleDTO> existSimpleDataList) {
        List<StatisticsAiJobInternalSimpleDTO> newList = new ArrayList<>();
        Map<Date, StatisticsAiJobInternalSimpleDTO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(existSimpleDataList)) {
            map = existSimpleDataList.stream().collect(
                    Collectors.toMap(StatisticsAiJobInternalSimpleDTO::getStatisticsTime, Function.identity()));
        }

        Map<Date, StatisticsAiJobInternalSimpleDTO> finalMap = map;
        dateList.forEach(c -> {
            if (finalMap.containsKey(c)) {
                newList.add(finalMap.get(c));
            } else {
                //没有这一天的数据，默认补0
                newList.add(new StatisticsAiJobInternalSimpleDTO(c, Const.ZERO_LONG));
            }
        });
        return newList;
    }

}
