package com.dl.magicvideo.web.controllers.digitalman.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-21 10:26
 */
@Data
public class DmAggregationParam {

    @NotNull(message = "数字人唯一标识必填")
    @ApiModelProperty(value = "数字人唯一标识bizId", required = true)
    private Long vmBizId;

    @ApiModelProperty(value = "数字人场景唯一标识bizId", required = false)
    private String sceneId;
}
