package com.dl.magicvideo.biz.common.util;

import com.dl.magicvideo.biz.manager.visual.dto.AudioInfoDTO;
import com.dl.magicvideo.biz.manager.visual.dto.ImageInfoDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VideoInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.javacpp.Loader;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FrameGrabber;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FfmpegUtil {

    /**
     * mov和mp4转化为webm
     *
     * @param sourceFilePath 可以为本地路径，也可以是远程url路径
     * @param targetFilePath
     * @return
     */
    public static void ffmpegToWebm(String sourceFilePath, String targetFilePath)
            throws IOException, InterruptedException {
        String ffmpeg = Loader.load(org.bytedeco.ffmpeg.ffmpeg.class);
        List<String> command = new ArrayList<>();
        command.add(ffmpeg);
        // case2 只有背景音乐，返回背景音乐url
        command.add("-i");
        command.add(sourceFilePath);
        command.add("-c:v");
        command.add("libvpx");
        //command.add("libvpx-vp9");
        command.add("-b:v");
        command.add("8M");
        command.add("-speed");
        command.add("4");
        command.add("-cpu-used");
        command.add("4");
        //command.add("1");
        command.add(targetFilePath);

        log.info("mov和mp4转化为webm处理完整命令：{}", String.join(StringUtils.SPACE, command));

        /*ProcessBuilder pb = new ProcessBuilder(command);
        pb.inheritIO().start().waitFor();*/

        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true);
        Process process = pb.start();
        process.waitFor();
        BufferedReader br1;
        br1 = new BufferedReader(new InputStreamReader(process.getInputStream(), "utf-8"));
        String line1;
        while ((line1 = br1.readLine()) != null) {
            log.info(line1);
        }
        // 关闭Process
        if (process.isAlive()) {
            process.destroy();
        }
    }

    /**
     * ffmpeg读取视频信息
     *
     * @param videoFilePath
     * @return
     */
    public static VideoInfoDTO extractVideoInfo(String videoFilePath) {
        VideoInfoDTO videoInfoDTO = new VideoInfoDTO();
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoFilePath)) {
            grabber.start(); // 开始读取媒体文件
            // 获取并打印一些媒体文件信息
            videoInfoDTO.setDuration(grabber.getLengthInTime() / 1000);
            videoInfoDTO.setImageWidth(grabber.getImageWidth());
            videoInfoDTO.setImageHeight(grabber.getImageHeight());
            videoInfoDTO.setBitrate(grabber.getVideoBitrate() / 1000);
            grabber.stop(); // 停止读取媒体文件
        } catch (FrameGrabber.Exception e) {
            log.error("视频读取异常", e);
        }
        return videoInfoDTO;
    }

    /**
     * ffmpeg读取图片信息
     *
     * @param imageFilePath
     * @return
     */
    public static ImageInfoDTO extractImageInfo(String imageFilePath) {
        ImageInfoDTO imageInfoDTO = new ImageInfoDTO();
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(imageFilePath)) {
            grabber.start(); // 开始读取媒体文件
            // 获取并打印一些媒体文件信息
            imageInfoDTO.setImageWidth(grabber.getImageWidth());
            imageInfoDTO.setImageHeight(grabber.getImageHeight());
            grabber.stop(); // 停止读取媒体文件
        } catch (FrameGrabber.Exception e) {
            log.error("图片读取异常", e);
        }
        return imageInfoDTO;
    }

    /**
     * 使用ffmpeg读取音频信息
     *
     * @param audioFilePath 音频文件路径
     * @return AudioInfoDTO 包含音频信息的对象
     */
    public static AudioInfoDTO extractAudioInfo(String audioFilePath) {
        AudioInfoDTO audioInfoDTO = new AudioInfoDTO();
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(audioFilePath)) {
            grabber.start(); // 开始读取媒体文件
            // 获取并设置一些音频文件信息
            audioInfoDTO.setDuration(grabber.getLengthInTime() / 1000);
            audioInfoDTO.setBitrate(grabber.getVideoBitrate() / 1000);
            grabber.stop(); // 停止读取媒体文件
        } catch (FrameGrabber.Exception e) {
            log.error("音频读取异常", e);
        }
        return audioInfoDTO;
    }


}
