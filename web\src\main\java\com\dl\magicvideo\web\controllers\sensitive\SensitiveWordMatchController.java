package com.dl.magicvideo.web.controllers.sensitive;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.web.controllers.sensitive.param.SensitiveWordBatchMatchParam;
import com.dl.magicvideo.web.controllers.sensitive.vo.SensitiveWordBatchMatchRespVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@RestController
@RequestMapping("/visual/sensitivewordmatch")
public class SensitiveWordMatchController {

    @ApiOperation("敏感词批量匹配--预留接口")
    @PostMapping("/batchmatch")
    public ResultModel<SensitiveWordBatchMatchRespVO> batchMatch(@RequestBody SensitiveWordBatchMatchParam param) {
        SensitiveWordBatchMatchRespVO respVO = new SensitiveWordBatchMatchRespVO();
        respVO.setSensitiveWordMap(new HashMap<>());
        return ResultModel.success(respVO);
    }
}
