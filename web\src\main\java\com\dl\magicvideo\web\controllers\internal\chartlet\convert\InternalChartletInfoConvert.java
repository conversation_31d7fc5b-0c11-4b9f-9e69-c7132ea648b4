package com.dl.magicvideo.web.controllers.internal.chartlet.convert;

import com.dl.magicvideo.biz.dal.chartlet.po.ChartletInfoPO;
import com.dl.magicvideo.biz.manager.chartlet.enums.ChartletCategoryEnum;
import com.dl.magicvideo.web.controllers.internal.chartlet.dto.InternalChartletInfoDTO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-13 10:08
 */
public class InternalChartletInfoConvert {

    public static InternalChartletInfoDTO cnvChartletInfoPO2DTO(ChartletInfoPO input) {
        InternalChartletInfoDTO result = new InternalChartletInfoDTO();
        result.setName(input.getName());
        result.setBizId(input.getBizId());
        result.setContentUrl(input.getContentUrl());
        result.setResolutionRatio(input.getResolutionRatio());
        result.setCoverImg(input.getCoverImg());
        result.setDuration(input.getDuration());
        result.setCreatorName(input.getCreatorName());
        result.setCreateBy(input.getCreateBy());
        result.setCreateDt(input.getCreateDt());
        result.setModifyName(input.getModifyName());
        result.setModifyBy(input.getModifyBy());
        result.setModifyDt(input.getModifyDt());
        result.setCategory(input.getCategory());
        result.setCategoryName(ChartletCategoryEnum.getNameByCode(input.getCategory()));
        return result;
    }
}
