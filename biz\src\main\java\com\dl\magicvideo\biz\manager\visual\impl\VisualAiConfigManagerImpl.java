package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiConfigPO;
import com.dl.magicvideo.biz.manager.visual.VisualAiConfigManager;
import com.dl.magicvideo.biz.dal.visual.VisualAiConfigMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【visual_tts_config】的数据库操作Service实现
* @createDate 2023-04-25 17:03:16
*/
@Service
public class VisualAiConfigManagerImpl extends ServiceImpl<VisualAiConfigMapper, VisualAiConfigPO>
    implements VisualAiConfigManager {

}




