package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.share.media.dto.MediaProduceJobRequestDTO;
import com.dl.aiservice.share.media.dto.MediaProduceJobResponseDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleAndReviseRequestDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleWordsDTO;
import com.dl.aiservice.share.subtitle.dto.ReviseAsrRequestDTO;
import com.dl.aiservice.share.subtitle.dto.RevisedAsrResponseDTO;
import com.dl.aiservice.share.subtitle.dto.RevisedAsrSubtitleDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.enums.BatchStatusE;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceBatchPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobSegmentPO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.transaction.TransactionProxyManager;
import com.dl.magicvideo.biz.manager.util.VisualProduceJobUtil;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceBatchManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobSegmentManager;
import com.dl.magicvideo.biz.manager.visual.bo.DigitalManJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.RevisedAsrSubtitleBO;
import com.dl.magicvideo.biz.manager.visual.bo.TtsJobBO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO.AudioConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO.BaseConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO.DigitalManConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO.TtsConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO.VideoConfigDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO.DataSheetDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualCardDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.biz.manager.visual.dto.preview.PreviewCacheDTO;
import com.dl.magicvideo.biz.manager.visual.dto.preview.PreviewCardDTO;
import com.dl.magicvideo.biz.manager.visual.dto.preview.PreviewCardDTO.SubtitleDTO;
import com.dl.magicvideo.biz.manager.visual.dto.preview.PreviewDTO;
import com.dl.magicvideo.biz.manager.visual.enums.DmProduceModeEnum;
import com.dl.magicvideo.biz.mq.dto.ClipDurationDTO;
import com.dl.magicvideo.biz.mq.dto.DynamicExtendDTO;
import com.dl.magicvideo.biz.mq.dto.GenenratePreviewDataDTO;
import com.dl.magicvideo.biz.mq.producer.ProduceFailProducer;
import com.dl.magicvideo.biz.mq.temporary.TempSubtitleWordsBO;
import com.dl.magicvideo.biz.mq.temporary.VisualMergeDmJobAndAsrExtTempBO;
import com.dl.magicvideo.biz.mq.temporary.VisualMergeDmJobExtTempBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.javacpp.Loader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @describe: GeneratePreviewDataTask
 * @author: zhousx
 * @date: 2023/6/15 9:19
 */
@Slf4j
@Component
public class GeneratePreviewDataConsumer {
    @Autowired
    private VisualProduceJobManager visualProduceJobManager;
    @Autowired
    private VisualAiJobManager visualAiJobManager;
    @Autowired
    private VisualProduceBatchManager visualProduceBatchManager;
    @Autowired
    private OperatorUtil operatorUtil;
    @Resource
    private AiServiceClient aiServiceClient;
    @Resource
    private TenantInfoManager tenantInfoManager;
    @Autowired
    private AccountTenantTrialManager accountTenantTrialManager;
    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;
    @Resource
    private ProduceFailProducer produceFailProducer;
    @Autowired
    private VisualProduceJobSegmentManager segmentManager;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private TransactionProxyManager transactionProxyManager;

    @Value("${visual.produce.segmentNum}")
    private Integer segmentNum;

    @Value("${dl.asr.channel}")
    private Integer asrChannel;
    @Value("${visual.fileTempPath}")
    public String localPathPrefix;

    private static final String VISUAL_SEGMENT_QUEUE_KEY = "dl-visual-segment-queue";

    private final static String END_BREAK_TIME_PATTERN = "(<break[^>]*\\btime=\"(\\d+)\"[^>]*><\\/break>|<break[^>]*\\btime=\"(\\d+)\"[^>]*\\/>)(?:(<break[^>]*\\btime=\"(\\d+)\"[^>]*><\\/break>|<break[^>]*\\btime=\"(\\d+)\"[^>]*\\/>))*$";

    private final static String END_BREAK_TIME_PATTERN_MS = "(<break[^>]*\\btime=\"(\\d+)ms\"[^>]*><\\/break>|<break[^>]*\\btime=\"(\\d+)ms\"[^>]*\\/>)(?:(<break[^>]*\\btime=\"(\\d+)ms\"[^>]*><\\/break>|<break[^>]*\\btime=\"(\\d+)ms\"[^>]*\\/>))*$";

    private final static String START_BREAK_TIME_PATTERN = "^((<break\\s*time=\"(\\d+)\"\\s*><\\/break>|<break[^>]*\\btime=\"(\\d+)\"[^>]*\\/>)+)";

    private final static String START_BREAK_TIME_PATTERN_MS = "^((<break\\s*time=\"(\\d+)ms\"\\s*><\\/break>|<break[^>]*\\btime=\"(\\d+)ms\"[^>]*\\/>)+)";

    private final static String TIME_PATTERN = "time=\"(\\d+)\"";

    private final static String TIME_PATTERN_MS = "time=\"(\\d+)ms\"";

    private final static String DIGITAL_MAN_FAIL = "数字人合成失败";

    private final static String TTS_FAIL = "TTS合成失败";

    @StreamListener("generatepreviewdataconsumer")
    public void generatePreviewData(@Payload GenenratePreviewDataDTO input) {
        log.info("收到生成预览数据的消息，input:{}", JSONUtil.toJsonStr(input));
        VisualProduceJobPO produceJob = visualProduceJobManager.lambdaQuery()
                .eq(VisualProduceJobPO::getJobId, input.getJobId()).one();
        if (Objects.isNull(produceJob)) {
            log.error("未找到该视频!,input:{}", JSONUtil.toJsonStr(input));
            return;
        }
        if (!JobStatusE.READY.getCode().equals(produceJob.getStatus())) {
            log.info("该视频状态非就绪，不处理!,produceJob:{}", JSONUtil.toJsonStr(produceJob));
            return;
        }
        operatorUtil.init(produceJob.getCreateBy(), produceJob.getCreatorName(), produceJob.getTenantCode(),
                produceJob.getTenantName());

        try {
            log.info("generatePreviewData,开始处理。produceJobId:{}", produceJob.getJobId());
            VisualTemplateDTO visualTemplateDTO = JSONUtil
                    .toBean(produceJob.getTemplateData(), VisualTemplateDTO.class);

            List<VisualAiJobPO> aiJobs = visualAiJobManager.lambdaQuery()
                    .eq(VisualAiJobPO::getProduceJobId, produceJob.getJobId()).list();
            //判断ai任务是否成功
            if (!judgeAiJobIsSuccess(produceJob, aiJobs)) {
                return;
            }

            //查询任务扩展信息
            VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                    .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                            .eq(VisualProduceJobExtendPO::getProduceJobId, produceJob.getJobId())
                            .eq(VisualProduceJobExtendPO::getIsDeleted, Const.ZERO));

            PreviewDTO previewDTO = new PreviewDTO();
            previewDTO.setTemplateId(visualTemplateDTO.getTemplateId() + "");
            previewDTO.setBgMusic(visualTemplateDTO.getBgMusic());
            previewDTO.setName(visualTemplateDTO.getName());
            previewDTO.setCoverUrl(visualTemplateDTO.getCoverUrl());
            previewDTO.setResolution(jobExtendPO.getResolution());
            previewDTO.setResolutionType(visualTemplateDTO.getResolutionType());
            previewDTO.setTtsParam(visualTemplateDTO.getTtsParam());
            previewDTO.setReplaceData(StringUtils.isNotBlank(produceJob.getReplaceData()) ?
                    produceJob.getReplaceData() :
                    visualTemplateDTO.getReplaceData());
            previewDTO.setApiData(StringUtils.isNotBlank(produceJob.getApiData()) ?
                    produceJob.getApiData() :
                    visualTemplateDTO.getApiData());

            if (CollectionUtils.isEmpty(visualTemplateDTO.getCards())) {
                produceJob.setPreviewData(JSONUtil.toJsonStr(previewDTO));

                transactionProxyManager.process(() -> {
                    visualProduceJobManager.updateById(produceJob);

                    //任务分片
                    this.generateSegmentTask(produceJob);
                });
                return;
            }

            //提取开启的数字人配置列表。
            List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList = this
                    .extractDigitalManConfigDTO(visualTemplateDTO.getCards());
            log.info("数字人配置列表:dmConfigDTOList:{}", JSONUtil.toJsonStr(dmConfigDTOList));

            Integer dmProduceMode = Objects.nonNull(jobExtendPO) ? jobExtendPO.getDmProduceMode() : Const.ZERO;
            //处理合并提交的数字人任务
            if (DmProduceModeEnum.MERGE_GEN_DM_THEN_ASR.getMode().equals(dmProduceMode)) {
                this.handlerMergeProduceDmJob(dmConfigDTOList, aiJobs, produceJob);
                //处理通过TTS合并驱动数字人的任务
            } else if (DmProduceModeEnum.MERGE_GEN_DM_WITH_TTS.getMode().equals(dmProduceMode)) {
                this.handlerMergeProduceDmJobWithTTS(dmConfigDTOList, aiJobs, produceJob);
                //处理分段合成的数字人任务
            } else if (DmProduceModeEnum.SEPARATE_GEN_DM.getMode().equals(dmProduceMode)) {
                this.handlerSeprateProduceDmJob(dmConfigDTOList, aiJobs, produceJob);
            }

            //找出最后完成的ai任务的完成时间
            Date lastCompleteAiJobTime = aiJobs.stream().map(VisualAiJobPO::getModifyDt)
                    .max(Comparator.comparing(Date::getTime)).orElse(null);

            Map<String, VisualAiJobPO> dmJobMap = aiJobs.stream()
                    .filter(po -> AiJobTypeE.DIGITAL_MAN.getCode().equals(po.getJobType()))
                    .collect(Collectors.toMap(VisualAiJobPO::getConfigId, Function.identity(), (v1, v2) -> v1));

            Map<String, VisualAiJobPO> ttsJobMap = aiJobs.stream()
                    .filter(po -> AiJobTypeE.TTS.getCode().equals(po.getJobType()))
                    .collect(Collectors.toMap(VisualAiJobPO::getConfigId, Function.identity(), (v1, v2) -> v1));

            List<PreviewCardDTO> cards = new ArrayList<>();
            previewDTO.setCards(cards);
            for (VisualCardDTO visualCardDTO : visualTemplateDTO.getCards()) {
                if (CollectionUtils.isEmpty(visualCardDTO.getDynamicNodes())) {
                    continue;
                }
                //不需要展示的node
                List<String> hideNodeTypeList = new ArrayList<>();
                PreviewCardDTO previewCardDTO = new PreviewCardDTO();
                previewCardDTO.setCardId(visualCardDTO.getCardId() + "");
                previewCardDTO.setTemplateId(visualCardDTO.getTemplateId() + "");
                previewCardDTO.setName(visualCardDTO.getName());
                previewCardDTO.setCoverUrl(visualCardDTO.getCoverUrl());
                previewCardDTO.setResolution(visualCardDTO.getResolution());
                previewCardDTO.setRenderData(visualCardDTO.getRenderData());
                previewCardDTO.setHideNodeTypeList(hideNodeTypeList);
                List<PreviewCardDTO.TtsDTO> ttsData = new ArrayList<>();
                List<PreviewCardDTO.DigitalManDTO> dmData = new ArrayList<>();
                List<PreviewCardDTO.VideoDTO> videoData = new ArrayList<>();
                List<PreviewCardDTO.AudioDTO> audioData = new ArrayList<>();
                List<PreviewCardDTO.DataSheetDTO> dataSheetData = new ArrayList<>();
                List<PreviewCardDTO.DynamicNodeDTO> nodes = new ArrayList<>();
                previewCardDTO.setTtsData(ttsData);
                previewCardDTO.setDmData(dmData);
                previewCardDTO.setVideoData(videoData);
                previewCardDTO.setAudioData(audioData);
                previewCardDTO.setDataSheetData(dataSheetData);
                previewCardDTO.setNodes(nodes);
                previewCardDTO.setLightEditConfigs(visualCardDTO.getLightEditConfigs());
                cards.add(previewCardDTO);
                //创建时间轴偏移量计算对象
                ClipDurationDTO clipDurationDTO = new ClipDurationDTO();
                for (DynamicNodeDTO dynamicNodeDTO : visualCardDTO.getDynamicNodes()) {
                    if (Objects.equals(dynamicNodeDTO.getIsEnabled(), Const.ZERO)) {
                        hideNodeTypeList.add(dynamicNodeDTO.getType());
                        continue;
                    }

                    //先过滤掉隐藏的配置
                    List<TtsConfigDTO> ttsList = (List<TtsConfigDTO>) filterHideConfig(dynamicNodeDTO.getTtsList());
                    List<DigitalManConfigDTO> dmList = (List<DigitalManConfigDTO>) filterHideConfig(
                            dynamicNodeDTO.getDmList());
                    List<VideoConfigDTO> videoList = (List<VideoConfigDTO>) filterHideConfig(
                            dynamicNodeDTO.getVideoList());
                    List<AudioConfigDTO> audioList = (List<AudioConfigDTO>) filterHideConfig(
                            dynamicNodeDTO.getAudioList());
                    List<DataSheetDTO> dataSheetList = (List<DataSheetDTO>) filterHideConfig(
                            dynamicNodeDTO.getDataSheetList());

                    PreviewCardDTO.DynamicNodeDTO node = new PreviewCardDTO.DynamicNodeDTO();
                    long nodeStartTime = clipDurationDTO.getNodeDurationOffset();
                    node.setNodeId(dynamicNodeDTO.getNodeId());
                    nodes.add(node);
                    //特别注意！！，当存在关键时长时，这个clipDuration为0
                    long clipDuration = dynamicNodeDTO.getDuration();
                    //节点拓展信息对象，包含数字人是否隐藏信息等
                    DynamicExtendDTO dynamicExtendDTO = new DynamicExtendDTO();
                    //查询出关键时长片段
                    if (clipDuration == 0L) {
                        TtsConfigDTO ttsKeyTime = filterKeyTimeAndFindFirstConfig(dynamicNodeDTO.getTtsList());
                        DigitalManConfigDTO dmKeyTime = filterKeyTimeAndFindFirstConfig(dynamicNodeDTO.getDmList());
                        VideoConfigDTO videoKeyTime = filterKeyTimeAndFindFirstConfig(dynamicNodeDTO.getVideoList());
                        AudioConfigDTO audioKeyTIme = filterKeyTimeAndFindFirstConfig(dynamicNodeDTO.getAudioList());
                        DataSheetDTO dataSheetKeyTime = filterKeyTimeAndFindFirstConfig(dynamicNodeDTO.getDataSheetList());
                        handleTtsKeyTime(dynamicExtendDTO, ttsKeyTime, ttsJobMap);
                        handleDmKeyTime(dynamicExtendDTO, dmKeyTime, dmJobMap);
                        handleVideoKeyTime(dynamicExtendDTO, videoKeyTime);
                        handleAudioKeyTime(dynamicExtendDTO, audioKeyTIme);
                        handleDataSheetKeyTime(dynamicExtendDTO, dataSheetKeyTime);
                    }
                    //如果TTS、数字人、视频都没有的话，则跳过
                    if (CollectionUtils.isEmpty(ttsList) && CollectionUtils.isEmpty(dmList) && CollectionUtils
                            .isEmpty(videoList) && CollectionUtils.isEmpty(audioList) && CollectionUtils.isEmpty(dataSheetList)) {
                        clipDurationDTO
                                .setTtsClipDurationOffset(clipDurationDTO.getTtsClipDurationOffset() + clipDuration);
                        clipDurationDTO
                                .setDmClipDurationOffset(clipDurationDTO.getDmClipDurationOffset() + clipDuration);
                        clipDurationDTO.setVideoClipDurationOffset(
                                clipDurationDTO.getVideoClipDurationOffset() + clipDuration);
                        clipDurationDTO.setAudioClipDurationOffset(
                                clipDurationDTO.getAudioClipDurationOffset() + clipDuration);
                        clipDurationDTO.setNodeDurationOffset(clipDurationDTO.getNodeDurationOffset() + clipDuration);
                        node.setStartTime(nodeStartTime + Double.valueOf(clipDuration * 0.8).longValue());
                        continue;
                    }

                    dynamicExtendDTO.setClipDuration(clipDuration);

                    //TTS相关（可设置为关键时长组件）
                    long ttsClipRealDuration = handleTtsClip(clipDurationDTO, dynamicExtendDTO, ttsList, ttsJobMap,
                            ttsData);
                    //数字人相关（可设置为关键时长组件）
                    long dmClipRealDuration = handleDmClip(clipDurationDTO, dynamicExtendDTO, dmList, dmJobMap, dmData,
                            dmProduceMode);
                    //视频相关（可设置为关键时长组件）
                    long videoClipDuration = handleVideoClip(clipDurationDTO, dynamicExtendDTO, videoList, videoData);
                    //数据图表相关（设置为关键时长组件）
                    long dataSheetDuartion = handleDataSheetClip(clipDurationDTO, dynamicExtendDTO, dataSheetList, dataSheetData);

                    //算出三种元素混合的情况，实际的片段时长
                    long[] clipRealDurationStr = { ttsClipRealDuration, dmClipRealDuration, videoClipDuration, dataSheetDuartion };
                    long clipRealDuration = Arrays.stream(clipRealDurationStr).max().orElse(Long.MIN_VALUE);
                    dynamicExtendDTO.setClipDuration(clipRealDuration);
                    //音效相关（不可设置为关键时长组件）,一定要在片段时长算出来后做处理
                    handleAudioClip(clipDurationDTO, dynamicExtendDTO, audioList, audioData);

                    if (dynamicExtendDTO.isHasNotHide()) {
                        clipDurationDTO.setTtsClipDurationOffset(
                                clipDurationDTO.getTtsClipDurationOffset() + clipRealDuration);
                        clipDurationDTO.setDmClipDurationOffset(clipDurationDTO.getDmClipDurationOffset() + clipRealDuration);
                        clipDurationDTO.setVideoClipDurationOffset(clipDurationDTO.getVideoClipDurationOffset() + clipRealDuration);
                        clipDurationDTO.setAudioClipDurationOffset(clipDurationDTO.getAudioClipDurationOffset() + clipRealDuration);
                        clipDurationDTO.setNodeDurationOffset(clipDurationDTO.getNodeDurationOffset() + clipRealDuration);
                        node.setStartTime(nodeStartTime + Double.valueOf(dynamicExtendDTO.getClipDuration() * 0.8).longValue());
                        continue;
                    }
                    node.setStartTime(nodeStartTime + Double.valueOf(dynamicExtendDTO.getClipDuration() * 0.8).longValue());
                }
            }

            produceJob.setPreviewData(JSONUtil.toJsonStr(previewDTO));
            if (Objects.nonNull(lastCompleteAiJobTime)) {
                produceJob.setAiCompleteDt(lastCompleteAiJobTime);
            }

            transactionProxyManager.process(() -> {
                //更新任务表
                visualProduceJobManager.lambdaUpdate().eq(VisualProduceJobPO::getJobId, produceJob.getJobId())
                        .eq(VisualProduceJobPO::getStatus, JobStatusE.READY.getCode())
                        .set(VisualProduceJobPO::getPreviewData, produceJob.getPreviewData())
                        .set(VisualProduceJobPO::getAiCompleteDt, produceJob.getAiCompleteDt())
                        .set(VisualProduceJobPO::getModifyDt, new Date()).update();
                log.info("预览数据生成完成，jobId={}", produceJob.getJobId());

                //将preview数据放入缓存
                PreviewCacheDTO previewCacheDTO = JSONUtil.toBean(produceJob.getPreviewData(), PreviewCacheDTO.class);
                previewCacheDTO.setSource(produceJob.getSource());
                previewCacheDTO.setComponentVersion(visualTemplateDTO.getComponentVersion());
                String previewCacheKey = Const.JOB_PREVIEW_KEY_PREFIX + produceJob.getJobId();
                redisUtil.set(previewCacheKey, JSONUtil.toJsonStr(previewCacheDTO), Const.JOB_PREVIEW_CACHE_SECONDS);
                log.info("将preview数据放入缓存成功。jobId:{}", produceJob.getJobId());

                //任务分片
                this.generateSegmentTask(produceJob);
                log.info("任务分片完成, jobId={}", produceJob.getJobId());
            });

        } catch (Exception e) {
            log.error("generatePreviewDataJobHandler异常，jobId:{},e:{}", produceJob.getJobId(), e.getMessage(), e);
            this.failProduceJobAndBatch(produceJob, "生成预览数据发生异常:" + e.getMessage());
        }
    }

    private static List<? extends BaseConfigDTO> filterHideConfig(List<? extends BaseConfigDTO> inputList) {
        return CollectionUtils.isEmpty(inputList) ?
                Collections.emptyList() :
                inputList.stream().filter(config -> BooleanUtils.isFalse(config.isHide())).collect(Collectors.toList());
    }

    /**
     * 获取关键时长元素
     * @param inputList
     * @return
     * @param <T>
     */
    private static <T extends BaseConfigDTO> T filterKeyTimeAndFindFirstConfig(List<T> inputList) {
        return inputList == null || inputList.isEmpty() ?
                null :
                inputList.stream()
                        .filter(config -> !config.isHide())
                        .filter(config-> Objects.equals(config.getKeyTime(), 1))
                        .findFirst()
                        .orElse(null);
    }

    /**
     * 计算片段里的TTS时间轴信息
     * 返回该关键时长下最长的时间
     */
    private long handleTtsClip(ClipDurationDTO clipDurationDTO, DynamicExtendDTO dynamicExtendDTO,
            List<DynamicNodeDTO.TtsConfigDTO> ttsList, Map<String, VisualAiJobPO> ttsJobMap,
            List<PreviewCardDTO.TtsDTO> ttsData) {
        //tts关键时长下的最大片段时长
        long clipRealDurationMax = dynamicExtendDTO.getClipDuration();
        if (CollectionUtils.isEmpty(ttsList)) {
            return clipRealDurationMax;
        }
        for (DynamicNodeDTO.TtsConfigDTO ttsConfigDTO : ttsList) {
            dynamicExtendDTO.setHasNotHide(true);
            VisualAiJobPO ttsJob = ttsJobMap.get(ttsConfigDTO.getTtsId());
            Assert.notNull(ttsJob, "未找到tts任务, ttsId=" + ttsConfigDTO.getTtsId());
            long start = ttsConfigDTO.getStart();
            long endDelay = ttsConfigDTO.getEndDelay();
            long ttsDuration = ttsJob.getDuration();
            long ttsRealDuration;
            long clipRealDuration = dynamicExtendDTO.getClipDuration();
            if (dynamicExtendDTO.getClipDuration() == 0L) {
                //该片段存在关键时长组件，以tts语音时长为准
                ttsRealDuration = ttsDuration;
                //关键时长
                if (dynamicExtendDTO.getKeyTimeClipDuration() > 0L){
                    if (start > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果入场延迟大于关键时长片段时长，则这个片段不做截取,跳过这个循环
                        continue;
                    } else if (start + ttsDuration + endDelay > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果整体市场大于 关键时长，则以关键时长为准
                        ttsRealDuration = dynamicExtendDTO.getKeyTimeClipDuration() - start;
                    }
                    clipRealDuration = dynamicExtendDTO.getKeyTimeClipDuration();
                } else {
                    clipRealDuration = start + ttsDuration + endDelay;
                }
            } else if (dynamicExtendDTO.getClipDuration() < ttsDuration + start + endDelay) {
                //以片段时长为准
                ttsRealDuration = ttsDuration;
                clipRealDuration = start + ttsDuration + endDelay;
            } else {
                //以tts语音时长为准
                ttsRealDuration = ttsDuration;
            }
            long realStart = clipDurationDTO.getTtsClipDurationOffset() + start;
            //如果该片段时长大于最大时长，则更新最大时长
            if (clipRealDuration > clipRealDurationMax){
                clipRealDurationMax = clipRealDuration;
            }

            TtsJobBO ttsJobBO = JSONUtil.toBean(ttsJob.getRequestInfo(),TtsJobBO.class);

            PreviewCardDTO.TtsDTO ttsDTO = new PreviewCardDTO.TtsDTO();
            ttsDTO.setId(ttsConfigDTO.getTtsId());
            ttsDTO.setText(ttsConfigDTO.getContent());
            ttsDTO.setType(0);
            ttsDTO.setChannel(ttsJobBO.getChannel());
            ttsDTO.setStart(ttsConfigDTO.getStart());
            ttsDTO.setRealStart(realStart);
            ttsDTO.setDuration(ttsRealDuration);
            ttsDTO.setUrl(ttsJob.getMediaInfo());
            if(StringUtils.isNotBlank(ttsJob.getSubtitleInfo())) {
                ttsDTO.setSubtitles(JSONUtil.toBean(ttsJob.getSubtitleInfo(), new TypeReference<List<PreviewCardDTO.SubtitleDTO>>() {
                }, false));
            }
            ttsData.add(ttsDTO);
        }
        return clipRealDurationMax;
    }

    /**
     * 计算片段里的数字人时间轴信息
     * 返回该关键时长下最长的时间
     */
    private long handleDmClip(ClipDurationDTO clipDurationDTO, DynamicExtendDTO dynamicExtendDTO,
            List<DynamicNodeDTO.DigitalManConfigDTO> dmList, Map<String, VisualAiJobPO> dmJobMap,
            List<PreviewCardDTO.DigitalManDTO> dmData, Integer dmProduceMode){
        //数字人关键时长下的最大片段时长
        long clipRealDurationMax = dynamicExtendDTO.getClipDuration();
        if (CollectionUtils.isEmpty(dmList)){
            return clipRealDurationMax;
        }
        for (DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO : dmList) {
            dynamicExtendDTO.setHasNotHide(true);
            VisualAiJobPO dmJob = dmJobMap.get(dmConfigDTO.getDmId());
            Assert.notNull(dmJob, "未找到数字人任务, dmId=" + dmConfigDTO.getDmId());
            long start = dmConfigDTO.getStart();
            long endDelay = Objects.nonNull(dmConfigDTO.getEndDelay()) ?
                    dmConfigDTO.getEndDelay() :
                    Const.ZERO_LONG;
            //处理数字人片段时长
            long dmDuration = dmJob.getDuration();

            long dmRealDuration;
            long clipRealDuration = dynamicExtendDTO.getClipDuration();

            if (dynamicExtendDTO.getClipDuration() == 0L) {
                //以数字人语音时长为准
                dmRealDuration = dmDuration;
                //关键时长
                if (dynamicExtendDTO.getKeyTimeClipDuration() > 0L){
                    if (start > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果入场延迟大于关键时长片段时长，则这个片段不做截取,跳过这个循环
                        continue;
                    } else if (start + dmDuration + endDelay > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果整体市场大于 关键时长，则以关键时长为准
                        dmRealDuration = dynamicExtendDTO.getKeyTimeClipDuration() - start;
                    }
                    clipRealDuration = dynamicExtendDTO.getKeyTimeClipDuration();
                } else {
                    clipRealDuration = start + dmRealDuration + endDelay;
                }

            } else if (dynamicExtendDTO.getClipDuration() < dmDuration + start + endDelay) {
                //以片段时长为准
                dmRealDuration = dmDuration;
                clipRealDuration = start + dmRealDuration + endDelay;
            } else {
                //以tts语音时长为准
                dmRealDuration = dmDuration;
            }
            long realStart = clipDurationDTO.getDmClipDurationOffset() + start;
            if (clipRealDuration > clipRealDurationMax){
                clipRealDurationMax = clipRealDuration;
            }

            PreviewCardDTO.DigitalManDTO dmDTO = new PreviewCardDTO.DigitalManDTO();
            dmDTO.setId(dmConfigDTO.getDmId());
            dmDTO.setText(dmConfigDTO.getContent());
            dmDTO.setType(0);
            dmDTO.setStart(dmConfigDTO.getStart());
            dmDTO.setRealStart(realStart);
            dmDTO.setDuration(dmRealDuration);
            dmDTO.setUrl(dmJob.getMediaInfo());
            //处理预览数据中的数字人字幕
            this.handlerDmSubtitleAndStartInDmVideo(dmProduceMode, dmJob, dmDTO, dmConfigDTO);
            dmData.add(dmDTO);
        }
        return clipRealDurationMax;
    }

    /**
     * 计算片段里的Video时间轴信息
     * 返回该关键时长下最长的时间
     */
    private long handleVideoClip(ClipDurationDTO clipDurationDTO, DynamicExtendDTO dynamicExtendDTO,
            List<DynamicNodeDTO.VideoConfigDTO> videoList, List<PreviewCardDTO.VideoDTO> videoData){
        //视频下的最大片段时长
        long clipRealDurationMax = dynamicExtendDTO.getClipDuration();
        if (CollectionUtils.isEmpty(videoList)){
            return clipRealDurationMax;
        }
        for (DynamicNodeDTO.VideoConfigDTO videoConfigDTO : videoList) {
            dynamicExtendDTO.setHasNotHide(true);
            //hasNotHide = true;
            long start = videoConfigDTO.getStart();
            long endDelay = Objects.nonNull(videoConfigDTO.getEndDelay()) ? videoConfigDTO.getEndDelay() : 0L;
            long videoDuration = videoConfigDTO.getDuration();
            //视频头部裁剪的时长
            long croppedDuration = Objects.nonNull(videoConfigDTO.getCroppedDuration()) ? videoConfigDTO.getCroppedDuration() : 0L;
            long videoRealDuration;
            long clipRealDuration = dynamicExtendDTO.getClipDuration();
            if (dynamicExtendDTO.getClipDuration() == 0L) {
                //以video时长为准
                videoRealDuration = videoDuration;
                //关键时长
                if (dynamicExtendDTO.getKeyTimeClipDuration() > 0L){
                    if (start > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果入场延迟大于关键时长片段时长，则这个片段不做截取,跳过这个循环
                        continue;
                    } else if (start + videoDuration + endDelay > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果整体市场大于 关键时长，则以关键时长为准
                        videoRealDuration = dynamicExtendDTO.getKeyTimeClipDuration() - start;
                    }
                    clipRealDuration = dynamicExtendDTO.getKeyTimeClipDuration();
                } else {
                    clipRealDuration = start +  videoDuration + endDelay;
                }

            } else if (dynamicExtendDTO.getClipDuration() < videoDuration + start + endDelay) {
                //以片段时长为准
                videoRealDuration = videoDuration;
                clipRealDuration = start + videoDuration + endDelay;
            } else {
                //以video时长为准
                videoRealDuration = videoDuration;
            }
            long realStart = clipDurationDTO.getVideoClipDurationOffset() + start;
            if (clipRealDuration > clipRealDurationMax){
                clipRealDurationMax = clipRealDuration;
            }
            PreviewCardDTO.VideoDTO videoDTO = new PreviewCardDTO.VideoDTO();
            videoDTO.setId(videoConfigDTO.getVideoId());
            videoDTO.setUrl(videoConfigDTO.getUrl());
            videoDTO.setType(0);
            videoDTO.setStart(videoConfigDTO.getStart());
            videoDTO.setRealStart(realStart);
            videoDTO.setDuration(videoRealDuration);
            videoDTO.setCroppedDuration(croppedDuration);
            videoDTO.setVolume(videoConfigDTO.getVolume());
            videoDTO.setStartDelay(videoConfigDTO.getStartDelay());
            videoDTO.setActiveRotationMode(videoConfigDTO.getActiveRotationMode());
            videoData.add(videoDTO);
        }
        return clipRealDurationMax;
    }

    /**
     * 计算片段里的Video时间轴信息
     * 返回该关键时长下最长的时间
     */
    private long handleDataSheetClip(ClipDurationDTO clipDurationDTO, DynamicExtendDTO dynamicExtendDTO,
                                 List<DynamicNodeDTO.DataSheetDTO> dataSheetList, List<PreviewCardDTO.DataSheetDTO> dataSheetData){
        //音频下的最大片段时长
        long clipRealDurationMax = dynamicExtendDTO.getClipDuration();
        if (CollectionUtils.isEmpty(dataSheetList)){
            return clipRealDurationMax;
        }
        for (DynamicNodeDTO.DataSheetDTO audioConfigDTO : dataSheetList) {
            dynamicExtendDTO.setHasNotHide(true);
            //hasNotHide = true;
            long start = audioConfigDTO.getStart();
            long audioDuration = audioConfigDTO.getDuration();
            long audioRealDuration;
            long clipRealDuration = dynamicExtendDTO.getClipDuration();
            if (dynamicExtendDTO.getClipDuration() == 0L) {
                //以video时长为准
                audioRealDuration = audioDuration;
                //关键时长
                if (dynamicExtendDTO.getKeyTimeClipDuration() > 0L){
                    if (start > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果入场延迟大于关键时长片段时长，则这个片段不做截取,跳过这个循环
                        continue;
                    } else if (start + audioDuration > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果整体市场大于 关键时长，则以关键时长为准
                        audioRealDuration = dynamicExtendDTO.getKeyTimeClipDuration() - start;
                    }
                    clipRealDuration = dynamicExtendDTO.getKeyTimeClipDuration();
                } else {
                    clipRealDuration = start +  audioDuration;
                }

            } else if (dynamicExtendDTO.getClipDuration() < audioDuration + start) {
                //以片段时长为准
                audioRealDuration = audioDuration;
                clipRealDuration = start + audioDuration;
            } else {
                //以video时长为准
                audioRealDuration = audioDuration;
            }
            long realStart = clipDurationDTO.getAudioClipDurationOffset() + start;
            if (clipRealDuration > clipRealDurationMax){
                clipRealDurationMax = clipRealDuration;
            }
            PreviewCardDTO.DataSheetDTO dataSheetDTO = new PreviewCardDTO.DataSheetDTO();
            dataSheetDTO.setDataSheetId(audioConfigDTO.getDataSheetId());
            dataSheetDTO.setStart(audioConfigDTO.getStart());
            dataSheetDTO.setEndDelay(audioConfigDTO.getEndDelay());
            dataSheetDTO.setDuration(audioRealDuration);
            dataSheetDTO.setRealStart(realStart);
            dataSheetData.add(dataSheetDTO);
        }
        return clipRealDurationMax;
    }

    /**
     * 计算片段里的Audio时间轴信息
     * 返回该关键时长下最长的时间
     * clipRealDuration 片段时长
     */
    private void handleAudioClip(ClipDurationDTO clipDurationDTO, DynamicExtendDTO dynamicExtendDTO,
                                 List<DynamicNodeDTO.AudioConfigDTO> videoList, List<PreviewCardDTO.AudioDTO> videoData){
        //音频下的最大片段时长
        long clipRealDurationMax = dynamicExtendDTO.getClipDuration();
        if (CollectionUtils.isEmpty(videoList)){
            return;
        }
        for (DynamicNodeDTO.AudioConfigDTO audioConfigDTO : videoList) {
            dynamicExtendDTO.setHasNotHide(true);
            //hasNotHide = true;
            long start = audioConfigDTO.getStart();
            long audioDuration = audioConfigDTO.getDuration();
            //视频头部裁剪的时长
            long croppedDuration = Objects.nonNull(audioConfigDTO.getCroppedDuration()) ? audioConfigDTO.getCroppedDuration() : 0L;
            long audioRealDuration;
            long clipRealDuration = dynamicExtendDTO.getClipDuration();
            if (dynamicExtendDTO.getClipDuration() == 0L) {
                //以video时长为准
                audioRealDuration = audioDuration;
                //关键时长
                if (dynamicExtendDTO.getKeyTimeClipDuration() > 0L){
                    if (start > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果入场延迟大于关键时长片段时长，则这个片段不做截取,跳过这个循环
                        continue;
                    } else if (start + audioDuration > dynamicExtendDTO.getKeyTimeClipDuration()){
                        //如果整体市场大于 关键时长，则以关键时长为准
                        audioRealDuration = dynamicExtendDTO.getKeyTimeClipDuration() - start;
                    }
                    clipRealDuration = dynamicExtendDTO.getKeyTimeClipDuration();
                } else {
                    clipRealDuration = start +  audioDuration;
                }

            } else if (dynamicExtendDTO.getClipDuration() < audioDuration + start) {
                //以片段时长为准
                audioRealDuration = audioDuration;
                clipRealDuration = start + audioDuration;
            } else {
                //以video时长为准
                audioRealDuration = audioDuration;
            }
            long realStart = clipDurationDTO.getAudioClipDurationOffset() + start;
            if (clipRealDuration > clipRealDurationMax){
                clipRealDurationMax = clipRealDuration;
            }
            PreviewCardDTO.AudioDTO audioDTO = new PreviewCardDTO.AudioDTO();
            audioDTO.setId(audioConfigDTO.getAudioId());
            audioDTO.setUrl(audioConfigDTO.getUrl());
            audioDTO.setType(0);
            audioDTO.setStart(audioConfigDTO.getStart());
            audioDTO.setRealStart(realStart);
            audioDTO.setDuration(audioRealDuration);
            audioDTO.setCroppedDuration(croppedDuration);
            audioDTO.setVolume(audioConfigDTO.getVolume());
            audioDTO.setStartDelay(audioConfigDTO.getStartDelay());
            audioDTO.setActiveRotationMode(audioConfigDTO.getActiveRotationMode());
            BigDecimal subtract = new BigDecimal(audioDuration).divide(new BigDecimal(1000)).subtract(new BigDecimal(audioConfigDTO.getFadeInTime())).subtract(new BigDecimal(audioConfigDTO.getFadeOutTime()));
            if (subtract.compareTo(new BigDecimal(0)) < 0) {
                //淡入淡出时间大于音频时间，则没有淡入淡出
                audioDTO.setFadeInTime("0");
                audioDTO.setFadeOutTime("0");
            } else {
                audioDTO.setFadeInTime(audioConfigDTO.getFadeInTime());
                audioDTO.setFadeOutTime(audioConfigDTO.getFadeOutTime());
            }
            videoData.add(audioDTO);
        }
    }

    /**
     * 判断ai任务是否成功
     *
     * @param produceJob
     * @param aiJobs
     * @return
     */
    private boolean judgeAiJobIsSuccess(VisualProduceJobPO produceJob, List<VisualAiJobPO> aiJobs) {
        if (CollectionUtils.isEmpty(aiJobs)) {
            return true;
        }

        Optional<VisualAiJobPO> failedAiJobOpt = aiJobs.stream()
                .filter(e -> Objects.equals(e.getJobStatus(), JobStatusE.FAILED.getCode())).findFirst();
        if (failedAiJobOpt.isPresent()) {
            log.warn("存在失败的ai任务，produceJobId:{},aiJobId:{}", produceJob.getJobId(), failedAiJobOpt.get().getAiJobId());
            if (failedAiJobOpt.get().getJobType().equals(AiJobTypeE.DIGITAL_MAN.getCode())) {
                this.failProduceJobAndBatch(produceJob, DIGITAL_MAN_FAIL + ":" + failedAiJobOpt.get().getFailReason());
            } else {
                this.failProduceJobAndBatch(produceJob, TTS_FAIL);
            }
            return false;
        }

        //主动查询合成中的数字人任务状态
        List<VisualAiJobPO> processingDmJobs = aiJobs.stream()
                .filter(e -> Objects.equals(e.getJobType(), Const.ONE) && Objects
                        .equals(e.getJobStatus(), JobStatusE.PROCESSING.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(processingDmJobs)) {
            if (this.queryDmJobsProcess(processingDmJobs)) {
                log.warn("存在失败的任务,produceJobId:{}", produceJob.getJobId());
                this.failProduceJobAndBatch(produceJob, DIGITAL_MAN_FAIL);
                return false;
            }
        }

        //过滤出非成功状态的ai任务， 判断tts和数字人是否全部合成完毕
        VisualAiJobPO unSuccessAiJob = aiJobs.stream()
                .filter(e -> !Objects.equals(e.getJobStatus(), JobStatusE.SUCCESS.getCode())).findAny().orElse(null);
        if (Objects.nonNull(unSuccessAiJob)) {
            log.warn("数字人或tts未合成。produceJobId:{},unSuccessAiJob:{}", produceJob.getJobId(),
                    JSONUtil.toJsonStr(unSuccessAiJob));
            return false;
        }
        return true;
    }

    /**
     * 查询数字人合成进度
     *
     * @param dmJobs
     * @return 是否失败
     */
    private boolean queryDmJobsProcess(List<VisualAiJobPO> dmJobs) {
        MediaProduceJobRequestDTO requestDTO = new MediaProduceJobRequestDTO();
        requestDTO.setMediaJobIds(dmJobs.stream().map(VisualAiJobPO::getAiJobId).collect(Collectors.toList()));
        ResultModel<List<MediaProduceJobResponseDTO>> jobResult = aiServiceClient.getJobDetail(requestDTO);
        List<MediaProduceJobResponseDTO> mediaJobs = jobResult.getDataResult();
        if (CollectionUtils.isEmpty(mediaJobs)) {
            return false;
        }
        Map<Long, MediaProduceJobResponseDTO> mediaJobMap = mediaJobs.stream()
                .collect(Collectors.toMap(MediaProduceJobResponseDTO::getMediaJobId, Function.identity()));
        boolean hasFailed = false;
        for (VisualAiJobPO dmJob : dmJobs) {
            MediaProduceJobResponseDTO mediaJob = mediaJobMap.get(dmJob.getAiJobId());
            if (Objects.isNull(mediaJob)) {
                continue;
            }
            if (Objects.equals(mediaJob.getStatus(), -1)) {
                dmJob.setJobStatus(JobStatusE.FAILED.getCode());
                dmJob.setModifyDt(new Date());
                log.info("主动查询数字人合成状态，发现该数字人任务合成失败:aiJobId:{}", dmJob.getJobId());
                hasFailed = true;
            }
            if (Objects.equals(mediaJob.getStatus(), 0)) {
                dmJob.setJobStatus(JobStatusE.SUCCESS.getCode());
                dmJob.setMediaInfo(mediaJob.getMediaUrl());
                if (Objects.nonNull(mediaJob.getDuration())) {
                    dmJob.setDuration(Double.valueOf(mediaJob.getDuration() * 1000).longValue());
                }
                dmJob.setModifyDt(new Date());
            }
        }

        visualAiJobManager.updateBatchById(dmJobs);
        return hasFailed;
    }

    private void failProduceJobAndBatch(VisualProduceJobPO produceJob, String failReason) {
        produceJob.setStatus(JobStatusE.FAILED.getCode());
        visualProduceJobManager.updateById(produceJob);

        //更新批次
        visualProduceBatchManager.update(Wrappers.lambdaUpdate(VisualProduceBatchPO.class)
                .eq(VisualProduceBatchPO::getBatchId, produceJob.getBatchId())
                .ne(VisualProduceBatchPO::getStatus, BatchStatusE.FAILED.getCode())
                .set(VisualProduceBatchPO::getStatus, BatchStatusE.FAILED.getCode()));
        //判断是否返回试用账户预扣额度
        handleTenantTrialAccount(produceJob.getTenantCode());

        //发送视频合成失败的mq
        produceFailProducer.sendProduceFailMsg(produceJob.getJobId(), produceJob.getTenantCode(), failReason);
    }

    private void handleTenantTrialAccount(String tenantCode){
        try {
            AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(tenantCode);
            //非试用租户，不需要对预试用额度进行扣除
            if (Objects.isNull(tenantInfo) || !Const.ONE.equals(tenantInfo.getIsTrial())) {
                return;
            }
            log.info("处理试用租户预扣额度返还");
            AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager
                    .getOne(Wrappers.lambdaQuery(AccountTenantTrialPO.class)
                            .eq(AccountTenantTrialPO::getTenantCode, tenantCode)
                            .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
            if (Objects.isNull(tenantTrialAccount)) {
                log.error("未找到试用租户账户 tenant = {}", tenantCode);
            }

            tenantTrialAccount.setWithhold(tenantTrialAccount.getWithhold() - 1);
            accountTenantTrialManager.updateById(tenantTrialAccount);
            log.info("返还试用账户预扣成功");
        } catch (Exception e) {
            log.error("处理返还预扣失败");
            return;
        }
    }

    /**
     * 从数字人视频中分离出音频
     *
     * @param dmVideoUrl
     * @param fileName
     * @return
     * @throws Exception
     */
    private File fetchAudioFromVideo(String dmVideoUrl, String fileName) throws Exception {
        String ffmpeg = Loader.load(org.bytedeco.ffmpeg.ffmpeg.class);
        List<String> command = new ArrayList<>();
        command.add("sudo");
        command.add(ffmpeg);
        command.add("-i");
        command.add(dmVideoUrl);
        command.add("-f");
        command.add("mp3");
        command.add("-vn");
        command.add(fileName);
        log.info("数字人音视频分离处理完整命令：{}", String.join(StringUtils.SPACE, command));
        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true);
        Process process = pb.start();
        process.waitFor();

        //打印文件内容，如果发生异常就会打印异常。
        BufferedReader br1;
        br1 = new BufferedReader(new InputStreamReader(process.getInputStream(), "utf-8"));
        String line1;
        while ((line1 = br1.readLine()) != null) {
            log.info(line1);
        }

        // 关闭Process
        if (process.isAlive()) {
            process.destroy();
        }

        return new File(fileName);
    }

    /**
     * 处理合并生成的数字人任务
     *
     * @param dmConfigDTOList
     * @param aiJobs
     * @param produceJob
     */
    private void handlerMergeProduceDmJob(List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList,
            List<VisualAiJobPO> aiJobs, VisualProduceJobPO produceJob) {
        if (CollectionUtils.isEmpty(dmConfigDTOList)) {
            return;
        }
        VisualAiJobPO dmJob = aiJobs.stream().filter(e -> Objects.equals(e.getJobType(), Const.ONE)).findFirst()
                .orElse(null);
        if (Objects.isNull(dmJob)) {
            return;
        }

        //数字人合成了一个大的视频，提取数字人大视频中的音频，调用asr和字幕校验接口
        log.info("开始提取数字人视频中的音频文件，produceJobId:{}", produceJob.getJobId());
        String audioFileName = localPathPrefix + dmJob.getJobId().toString() + DateUtil
                .format(new Date(), DateUtil.YMDHMS) + ".mp3";
        ResultModel<List<AsrSubtitleDTO>> asrResult = null;
        try {
            //从数字人视频中分离出音频
            File audioFile = fetchAudioFromVideo(dmJob.getMediaInfo(), audioFileName);
            //音频识别 todo:数字人字幕长度当前是每个片段单独控制，后续要从产品上推进全局设置。后续这里要取全局设置的句子长度。
            asrResult = aiServiceClient.asrFile(asrChannel, audioFile, dmConfigDTOList.get(0).getMaxLength());
        } catch (Exception e) {
            log.error("音频识别音频文件发生异常，jobId:{},,,dmJobId:{},,,e:{}", dmJob.getProduceJobId(), dmJob.getJobId(), e);
            throw BusinessServiceException.getInstance("数字人的音频文件asr异常!");
        } finally {
            // 删除临时音频文件
            FileUtil.del(audioFileName);
        }
        if (!asrResult.isSuccess()) {
            log.error("数字人的音频文件asr失败!,dmJob:{}, audioFileName:{}, asrResult:{}", JSONUtil.toJsonStr(dmJob),
                    audioFileName, JSONUtil.toJsonStr(asrResult));
            throw BusinessServiceException.getInstance("数字人的音频文件asr失败!");
        }
        log.info("jobId:{},,,音频识别后的字幕:List<AsrSubtitleDTO>:{}", dmJob.getProduceJobId(),
                JSONUtil.toJsonStr(asrResult.getDataResult()));

        //构建字幕词组临时map，key-wordsIndex
        Map<Integer, TempSubtitleWordsBO> tempSubtitleWordsBOMap = buildTempSubtitleWordsBOMap(
                asrResult.getDataResult());

        //字幕校验
        ReviseAsrRequestDTO reviseAsrRequestDTO = buildReviseAsrRequestDTO(produceJob, dmConfigDTOList,
                asrResult.getDataResult());
        ResultModel<RevisedAsrResponseDTO> revisedResult = aiServiceClient.asrRevise(reviseAsrRequestDTO);
        if (!revisedResult.isSuccess()) {
            log.error("数字人的字幕校验失败!,dmJob:{}, reviseAsrRequestDTO:{}, revisedResult:{}", JSONUtil.toJsonStr(dmJob),
                    JSONUtil.toJsonStr(reviseAsrRequestDTO), JSONUtil.toJsonStr(revisedResult));
            this.failProduceJobAndBatch(produceJob, "数字人的音频文件ASR后的字幕校验失败");
            return;
        }
        //校验后的字幕词组
        List<RevisedAsrSubtitleDTO> revisedAsrWords = revisedResult.getDataResult().getRevisedAsrSubtitles();
        log.info("jobId:{},,,校验后的字幕词组:revisedAsrWords:{}", dmJob.getProduceJobId(),
                JSONUtil.toJsonStr(revisedAsrWords));

        //填充校验后的字幕词组
        List<TempSubtitleWordsBO> tempSubtitleWordsBOList = fillTempSubtitleWordsBO(tempSubtitleWordsBOMap,
                revisedAsrWords);

        //构建伪数字人合成任务列表
        List<VisualAiJobPO> fakeDmJobList = buildFakeDmJobList(produceJob, dmJob, dmConfigDTOList,
                tempSubtitleWordsBOList);
        log.info("jobId:{},,,伪数字人合成任务列表:fakeDmJobList:{}", dmJob.getProduceJobId(), JSONUtil.toJsonStr(fakeDmJobList));
        //将伪数字人合成任务列表添加到aiJobs中。
        aiJobs.addAll(fakeDmJobList);
    }

    private void handlerMergeProduceDmJobWithTTS(List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList,
            List<VisualAiJobPO> aiJobs, VisualProduceJobPO produceJob) {
        if (CollectionUtils.isEmpty(dmConfigDTOList)) {
            return;
        }

        List<VisualAiJobPO> dmTtsJobs = new ArrayList<>();
        Iterator<VisualAiJobPO> iterator = aiJobs.iterator();
        while (iterator.hasNext()) {
            VisualAiJobPO element = iterator.next();
            if (Objects.equals(element.getJobType(), AiJobTypeE.DM_TTS.getCode())) {
                dmTtsJobs.add(element);
                iterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(dmTtsJobs)) {
            log.error("数字人的tts任务列表为空!,jobId:{}", produceJob.getJobId());
            return;
        }

        VisualAiJobPO dmJob = aiJobs.stream()
                .filter(e -> Objects.equals(e.getJobType(), AiJobTypeE.DIGITAL_MAN.getCode())).findFirst().orElse(null);
        if (Objects.isNull(dmJob)) {
            log.error("数字人的任务为空!,jobId:{}", produceJob.getJobId());
            return;
        }

        //将dmTtsJobs转为dmTtsJobMap，key-configId（dmId）
        Map<String, VisualAiJobPO> dmTtsJobMap = dmTtsJobs.stream()
                .collect(Collectors.toMap(VisualAiJobPO::getConfigId, Function.identity()));

        //遍历dmConfigDTOList，再从dmTtsJobMap中取dmTtsJob。
        //构建伪数字人合成任务列表
        List<VisualAiJobPO> fakeDmJobList = new ArrayList<>();
        //在大的数字人视频中的开始时间
        Long startInDmVideo = 0L;

        //计算压缩率  数字人视频的时长会比拼接的音频时长短一些，因此需要计算压缩率，并乘以(原时长+延迟) 得到压缩后的时长。
        Long dmTtsTotalDuration = 0L;
        for (VisualAiJobPO dmTtsJob : dmTtsJobs) {
            dmTtsTotalDuration += dmTtsJob.getDuration();
            //加上音频前加的100ms静音
            dmTtsTotalDuration += 100;
        }
        Long dmDuration = dmJob.getDuration();
        BigDecimal rate = new BigDecimal(dmDuration).divide(new BigDecimal(dmTtsTotalDuration),3,BigDecimal.ROUND_HALF_DOWN);

        //com.dl.magicvideo.biz.manager.visual.impl.VisualProduceJobManagerImpl.mergeSmallAudio中拼接音频时，在每个音频后面拼接了100ms静音片段
        //在下面处理时，逻辑上把这100ms拆开，0~50ms归属前一个片段末尾，50~100ms归属后一个片段开头。
        //延时100ms 一半就是50ms
        //技术文档：http://doc.dingli-inc.com/pages/viewpage.action?pageId=36897908
        long halfDelay = 50;

        for (int i = 0; i < dmConfigDTOList.size(); i++) {
            DynamicNodeDTO.DigitalManConfigDTO dmConfig = dmConfigDTOList.get(i);
            VisualAiJobPO dmTtsJob = dmTtsJobMap.get(dmConfig.getDmId());

            VisualMergeDmJobExtTempBO fakeDmJob = new VisualMergeDmJobExtTempBO();
            fakeDmJob.setAiJobId(dmTtsJob.getAiJobId());
            fakeDmJob.setJobStatus(Const.TWO);
            fakeDmJob.setJobType(AiJobTypeE.DIGITAL_MAN.getCode());
            fakeDmJob.setProduceJobId(produceJob.getJobId());
            //添加的halfDelay的次数
            int addHalfDelayTimes;
            //第一个片段，开头有2个halfDelay，末尾有1个halfDelay
            if (i == 0) {
                addHalfDelayTimes = 3;
                //最后一个片段，开头有一个halfDelay
            } else if (i == dmConfigDTOList.size() - 1) {
                addHalfDelayTimes = 1;
                //其余片段，开头和末尾各一个halfDelay
            } else {
                addHalfDelayTimes = 2;
            }
            fakeDmJob.setDuration(new BigDecimal(dmTtsJob.getDuration() + halfDelay * addHalfDelayTimes).multiply(rate)
                    .setScale(0, BigDecimal.ROUND_HALF_DOWN).longValue());

            fakeDmJob.setMediaInfo(dmJob.getMediaInfo());
            fakeDmJob.setConfigId(dmConfig.getDmId());
            fakeDmJob.setModifyDt(dmJob.getModifyDt());
            fakeDmJob.setTemplateId(produceJob.getTemplateId());
            fakeDmJob.setStartInDmVideo(startInDmVideo);

            //进行asr生成字幕
            List<PreviewCardDTO.SubtitleDTO> subtitleDTOList = this.asrAndGetSubtitle(dmTtsJob, dmConfig);
            fakeDmJob.setSubtitleInfo(
                    CollectionUtils.isNotEmpty(subtitleDTOList) ? JSONUtil.toJsonStr(subtitleDTOList) : null);

            fakeDmJobList.add(fakeDmJob);

            startInDmVideo += fakeDmJob.getDuration();
        }
        log.info("dmProduceMode:2,,,jobId:{},,,生成的fakeDmJobList:{}", produceJob.getJobId(),
                JSONUtil.toJsonStr(fakeDmJobList));

        //将伪数字人合成任务列表添加到aiJobs中。
        aiJobs.addAll(fakeDmJobList);
    }

    private void handlerSeprateProduceDmJob(List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList,
            List<VisualAiJobPO> aiJobs, VisualProduceJobPO produceJob) {
        if (CollectionUtils.isEmpty(dmConfigDTOList)) {
            return;
        }

        List<VisualAiJobPO> dmJobs = aiJobs.stream()
                .filter(e -> Objects.equals(e.getJobType(), AiJobTypeE.DIGITAL_MAN.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dmJobs)) {
            log.error("数字人的任务为空!,jobId:{}", produceJob.getJobId());
            return;
        }

        //将dmJobs转为dmJobMap，key-configId（dmId）
        Map<String, VisualAiJobPO> dmJobMap = dmJobs.stream()
                .collect(Collectors.toMap(VisualAiJobPO::getConfigId, Function.identity()));

        for (DynamicNodeDTO.DigitalManConfigDTO dmConfig : dmConfigDTOList) {
            VisualAiJobPO dmJob = dmJobMap.get(dmConfig.getDmId());
            //处理字幕
            List<PreviewCardDTO.SubtitleDTO> subtitleDTOList = this.asrFileAndGetSubtitle(dmJob, dmConfig);
            dmJob.setSubtitleInfo(
                    CollectionUtils.isNotEmpty(subtitleDTOList) ? JSONUtil.toJsonStr(subtitleDTOList) : null);
        }
        log.info("dmProduceMode:1,,,jobId:{},,,dmJobs:{}", produceJob.getJobId(), JSONUtil.toJsonStr(dmJobs));

    }

    /**
     * 提取纯文本
     *
     * @param
     * @return
     */
    private String extractPureScript(String originalScript) {
        //移除html标签（移除ssml标签）
        String rmHtmlScript = VisualProduceJobUtil.rmHtml(originalScript);
        //移除所有空格
        String script = rmHtmlScript.replaceAll(" ", "");
        //移除所有\n
        script = script.replaceAll("\n", "");
        //移除所有\r
        script = script.replaceAll("\r", "");
        return script;
    }

    private List<PreviewCardDTO.SubtitleDTO> asrAndGetSubtitle(VisualAiJobPO dmTtsJob,
            DynamicNodeDTO.DigitalManConfigDTO dmConfig) {
        if (Const.ZERO.equals(dmConfig.getEnableSubtitle())) {
            return Collections.emptyList();
        }

        //从dmTtsJob.getRequestInfo()中提取文本
        TtsJobBO ttsJobBO = JSONUtil.toBean(dmTtsJob.getRequestInfo(), TtsJobBO.class);
        String script = this.extractPureScript(ttsJobBO.getText());

        AsrSubtitleAndReviseRequestDTO requestDTO = new AsrSubtitleAndReviseRequestDTO();
        requestDTO.setAudioUrl(dmTtsJob.getMediaInfo());
        requestDTO.setChannel(asrChannel);
        requestDTO.setOriginalScript(script);
        requestDTO.setSentenceMaxLength(dmConfig.getMaxLength());

        ResultModel<RevisedAsrResponseDTO> asrResult = null;
        try {
            //音频识别并字幕校验
            asrResult = aiServiceClient.asrAndRevise(requestDTO);
        } catch (Exception e) {
            log.error("asr发生异常，jobId:{},,,aiJobId:{},,,requestDTO:{},,,e:{}", dmTtsJob.getProduceJobId(),
                    dmTtsJob.getJobId(), JSONUtil.toJsonStr(requestDTO), e.getMessage(), e);
            //吃掉异常，返回空列表，不阻塞主流程
            return Collections.emptyList();
        }
        if (!asrResult.isSuccess()) {
            log.error("数字人的音频文件asr失败!,dmTtsJob:{},,,,asrResult:{}", JSONUtil.toJsonStr(dmTtsJob),
                    JSONUtil.toJsonStr(asrResult));
            return Collections.emptyList();
        }
        log.info("音频识别后的字幕:List<AsrSubtitleDTO>:{}", JSONUtil.toJsonStr(asrResult.getDataResult()));

        return cnv2SubtitleDTOList(asrResult);
    }

    private List<PreviewCardDTO.SubtitleDTO> asrFileAndGetSubtitle(VisualAiJobPO dmJob,
            DynamicNodeDTO.DigitalManConfigDTO dmConfig) {
        if (Const.ZERO.equals(dmConfig.getEnableSubtitle())) {
            return Collections.emptyList();
        }

        //从dmJob.getRequestInfo()中提取文本
        DigitalManJobBO digitalManJobBO = JSONUtil.toBean(dmJob.getRequestInfo(), DigitalManJobBO.class);
        String script = this.extractPureScript(digitalManJobBO.getText());

        String audioFileName =
                localPathPrefix + dmJob.getJobId().toString() + DateUtil.format(new Date(), DateUtil.YMDHMS) + ".mp3";
        ResultModel<RevisedAsrResponseDTO> asrResult = null;
        try {
            //将脚本url编码
            String urlEncodeScript = URLEncoder.encode(script, "UTF-8");
            urlEncodeScript = urlEncodeScript.replaceAll("\\+", "%20");

            //从数字人视频中分离出音频
            File audioFile = fetchAudioFromVideo(dmJob.getMediaInfo(), audioFileName);
            //音频识别音频文件并字幕校验
            asrResult = aiServiceClient
                    .asrFileAndRevise(asrChannel, audioFile, dmConfig.getMaxLength(), urlEncodeScript);
        } catch (Exception e) {
            log.error("音频识别音频文件并字幕校验发生异常，jobId:{},,,dmJobId:{}", dmJob.getProduceJobId(), dmJob.getJobId(), e);
            //吃掉异常，返回空列表，不阻塞主流程
            return Collections.emptyList();
        } finally {
            // 删除临时音频文件
            FileUtil.del(audioFileName);
        }
        //若失败则返回空列表，不阻塞主流程
        if (!asrResult.isSuccess()) {
            log.error("音频识别音频文件并字幕校验失败!,dmJob:{},,,,asrResult:{}", JSONUtil.toJsonStr(dmJob),
                    JSONUtil.toJsonStr(asrResult));
            return Collections.emptyList();
        }
        log.info("jobId:{},,,dmJobId:{},,,音频识别后的字幕:List<AsrSubtitleDTO>:{}", dmJob.getProduceJobId(), dmJob.getJobId(),
                JSONUtil.toJsonStr(asrResult.getDataResult()));
        return cnv2SubtitleDTOList(asrResult);
    }

    private List<PreviewCardDTO.SubtitleDTO> cnv2SubtitleDTOList(ResultModel<RevisedAsrResponseDTO> asrResult) {
        List<PreviewCardDTO.SubtitleDTO> subtitleDTOList = new ArrayList<>();
        for (RevisedAsrSubtitleDTO revisedAsrSubtitleDTO : asrResult.getDataResult().getRevisedAsrSubtitles()) {
            PreviewCardDTO.SubtitleDTO subtitleDTO = new PreviewCardDTO.SubtitleDTO();
            subtitleDTO.setText(revisedAsrSubtitleDTO.getRevisedSubtitle());
            subtitleDTO.setBeginTime(revisedAsrSubtitleDTO.getTimePointStart().longValue());
            subtitleDTO.setEndTime(revisedAsrSubtitleDTO.getTimePointEnd().longValue());
            subtitleDTOList.add(subtitleDTO);
        }
        return subtitleDTOList;
    }

    /**
     * 构建字幕词组临时map，key-wordsIndex
     *
     * @param inputSubtitleList
     * @return
     */
    private static Map<Integer, TempSubtitleWordsBO> buildTempSubtitleWordsBOMap(
            List<AsrSubtitleDTO> inputSubtitleList) {
        Map<Integer, TempSubtitleWordsBO> tempSubtitleWordsBOMap = new HashMap<>();
        int wordsIndex = 0;
        for (int sentenceIndex = 0; sentenceIndex < inputSubtitleList.size(); sentenceIndex++) {
            AsrSubtitleDTO sentenceDTO = inputSubtitleList.get(sentenceIndex);
            for (AsrSubtitleWordsDTO wordsDTO : sentenceDTO.getWords()) {
                TempSubtitleWordsBO tempSubtitleWordsBO = new TempSubtitleWordsBO();
                tempSubtitleWordsBO.setIndex(wordsIndex);
                tempSubtitleWordsBO.setAsrText(wordsDTO.getWord());
                tempSubtitleWordsBO.setSentenceIndex(sentenceIndex);
                tempSubtitleWordsBO.setTimePointStart(wordsDTO.getTimePointStart());
                tempSubtitleWordsBO.setTimePointEnd(wordsDTO.getTimePointEnd());
                tempSubtitleWordsBOMap.put(wordsIndex, tempSubtitleWordsBO);

                wordsIndex++;
            }

        }
        return tempSubtitleWordsBOMap;
    }

    private static List<TempSubtitleWordsBO> fillTempSubtitleWordsBO(
            Map<Integer, TempSubtitleWordsBO> tempSubtitleWordsBOMap, List<RevisedAsrSubtitleDTO> revisedAsrWords) {

        //将字幕校验后的结果根据uniqId（即wordsIndex）转为map，key-wordsIndex
        Map<Integer, RevisedAsrSubtitleDTO> revisedAsrWordsMap = revisedAsrWords.stream()
                .collect(Collectors.toMap(RevisedAsrSubtitleDTO::getUniqId, Function.identity()));
        Iterator<Map.Entry<Integer, RevisedAsrSubtitleDTO>> iterator = revisedAsrWordsMap.entrySet().iterator();
        List<TempSubtitleWordsBO> resultList = new ArrayList<>();
        //因为ASR的结果可能存在多了一些词组，字幕校验后会把多余的词组给去掉。所以这里要以字幕校验后的结果为准，去tempSubtitleWordsBOMap中找asr结果。
        while (iterator.hasNext()) {
            Map.Entry<Integer, RevisedAsrSubtitleDTO> entry = iterator.next();
            Integer wordsIndex = entry.getKey();
            RevisedAsrSubtitleDTO revisedAsrSubtitleDTO = entry.getValue();

            //取TempSubtitleWordsBO
            TempSubtitleWordsBO tempSubtitleWordsBO = tempSubtitleWordsBOMap.get(wordsIndex);
            tempSubtitleWordsBO.setRevisedText(revisedAsrSubtitleDTO.getRevisedSubtitle());
            resultList.add(tempSubtitleWordsBO);
        }
        return resultList;
    }

    private static ReviseAsrRequestDTO buildReviseAsrRequestDTO(VisualProduceJobPO produceJob,
            List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList, List<AsrSubtitleDTO> inputSubtitleList) {
        //拼接原始数字人脚本
        String originalScript = jointOrignalDmScript(produceJob, dmConfigDTOList);
        //移除html标签（移除ssml标签）
        String rmHtmlScript = VisualProduceJobUtil.rmHtml(originalScript);
        //移除所有空格
        String script = rmHtmlScript.replaceAll(" ", "");
        //移除所有\n
        script = script.replaceAll("\n", "");
        //移除所有\r
        script = script.replaceAll("\r", "");

        ReviseAsrRequestDTO result = new ReviseAsrRequestDTO();
        result.setOriginalScript(script);
        List<AsrSubtitleDTO> resultSubtitleList = new ArrayList<>();
        //wordsIndex
        AtomicInteger index = new AtomicInteger();
        //取得是句子中的词组
        inputSubtitleList.stream().forEach(inputSubtitle -> {
            inputSubtitle.getWords().stream().forEach(word -> {
                AsrSubtitleDTO resultSubtitle = new AsrSubtitleDTO();
                resultSubtitle.setUniqId(index.getAndIncrement());
                resultSubtitle.setSubtitle(word.getWord());
                resultSubtitle.setTimePointStart(word.getTimePointStart());
                resultSubtitle.setTimePointEnd(word.getTimePointEnd());
                resultSubtitleList.add(resultSubtitle);
            });
        });
        result.setSubtitles(resultSubtitleList);
        return result;
    }

    /**
     * 拼接原始数字人脚本
     *
     * @param produceJob
     * @param dmConfigDTOList
     * @return
     */
    private static String jointOrignalDmScript(VisualProduceJobPO produceJob,
            List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList) {
        //提取数字人中的字幕，拼接成串。
        StringBuffer sbf = new StringBuffer();
        dmConfigDTOList.forEach(dmConfigDTO -> {
            Map<String, Object> ttsContentMap = JsonUtils.getMap4Json(dmConfigDTO.getContent());
            sbf.append(VisualProduceJobUtil.getText(ttsContentMap, produceJob.getReplaceData(), produceJob.getApiData()));
        });
        String originalScript = sbf.toString();
        return originalScript;
    }

    /**
     * 提取数字人配置列表
     *
     * @param cards
     * @return
     */
    private List<DynamicNodeDTO.DigitalManConfigDTO> extractDigitalManConfigDTO(List<VisualCardDTO> cards) {
        List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList = new ArrayList<>();
        for (VisualCardDTO visualCardDTO : cards) {
            if (CollectionUtils.isEmpty(visualCardDTO.getDynamicNodes())) {
                continue;
            }

            for (DynamicNodeDTO dynamicNodeDTO : visualCardDTO.getDynamicNodes()) {
                if (Objects.equals(dynamicNodeDTO.getIsEnabled(), Const.ZERO)) {
                    continue;
                }
                //提取数字人列表
                if (CollectionUtils.isNotEmpty(dynamicNodeDTO.getDmList())) {
                    dmConfigDTOList.addAll(dynamicNodeDTO.getDmList().stream()
                            .filter(digitalManConfigDTO -> BooleanUtils.isFalse(digitalManConfigDTO.isHide()))
                            .collect(Collectors.toList()));
                }
            }
        }
        return dmConfigDTOList;
    }

    /**
     * 构建伪数字人合成任务列表
     *
     * @param dmConfigDTOList
     * @param tempSubtitleWordsBOList
     * @return
     */
    private static List<VisualAiJobPO> buildFakeDmJobList(VisualProduceJobPO produceJob, VisualAiJobPO realDmJobPO,
            List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList,
            List<TempSubtitleWordsBO> tempSubtitleWordsBOList) {
        List<VisualMergeDmJobAndAsrExtTempBO> fakeDmJobList = new ArrayList<>();

        //字幕去除html文本和标点符号
        tempSubtitleWordsBOList.forEach(bo -> bo.setRmHtmlAndPunctuationText(
                VisualProduceJobUtil.rmPunctuation(VisualProduceJobUtil.rmHtml(bo.getRevisedText()))));

        log.info("开始进行字幕与数字人配置匹配，tempSubtitleWordsBOList:{}", JSONUtil.toJsonStr(tempSubtitleWordsBOList));

        //去除html文本和标点符号的字幕字符串，用来与数字人配置中的文本进行比较
        StringBuilder compareStr = new StringBuilder();
        //存放当前数字人配置对应的字幕
        List<TempSubtitleWordsBO> currentDmJobSubtitleList = new ArrayList<>();
        //左右指针
        int leftIndex = 0;
        int rightIndex;

        boolean matchFlag = false;
        //外层遍历数字人配置
        for (int i = 0; i < dmConfigDTOList.size(); i++) {
            DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO = dmConfigDTOList.get(i);
            Map<String, Object> ttsContentMap = JsonUtils.getMap4Json(dmConfigDTO.getContent());
            String configText = VisualProduceJobUtil.getText(ttsContentMap, produceJob.getReplaceData(), produceJob.getApiData());
            String replacedText = configText;
            //移除html标签
            configText = VisualProduceJobUtil.rmHtml(configText);
            //移除标点符号
            configText = VisualProduceJobUtil.rmPunctuation(configText);
            //移除所有空格
            configText = configText.replaceAll(" ", "");
            //移除所有\n
            configText = configText.replaceAll("\n", "");
            //移除所有\r
            configText = configText.replaceAll("\r", "");

            log.info("外层数字人配置中移除html标签和标点符号后的文本:{}", configText);

            //内层遍历字幕
            for (rightIndex = leftIndex; rightIndex < tempSubtitleWordsBOList.size(); rightIndex++) {
                TempSubtitleWordsBO rightSubtitleBO = tempSubtitleWordsBOList.get(rightIndex);
                //拼接
                compareStr.append(rightSubtitleBO.getRmHtmlAndPunctuationText());
                currentDmJobSubtitleList.add(rightSubtitleBO);
                //未匹配上
                if (!configText.equals(compareStr.toString())) {
                    continue;
                }
                //匹配上
                matchFlag = true;
                break;
            }

            //未匹配上，则抛异常
            if (!matchFlag) {
                log.error(
                        "当前数字人配置未匹配上对应的字幕，produceJob.jobId:{},,templateId:{},,dmConfigDTO:{},,tempSubtitleWordsBOList:{}",
                        produceJob.getJobId(), produceJob.getTemplateId(), JSONUtil.toJsonStr(dmConfigDTO),
                        JSONUtil.toJsonStr(tempSubtitleWordsBOList));
                throw BusinessServiceException.getInstance("数字人视频字幕匹配有误");
            }

            //匹配上，取左字幕和右字幕，并构建伪数字人合成任务
            TempSubtitleWordsBO leftSubtitleBO = tempSubtitleWordsBOList.get(leftIndex);
            TempSubtitleWordsBO rightSubtitleBO = tempSubtitleWordsBOList.get(rightIndex);
            log.info("字幕匹配成功,leftIndex:{},rightIndex:{}", leftIndex, rightIndex);

            //判断当前数字人原脚本的头尾是否含有停顿，若有则提取并补充到字幕中
            Long startBreakTime = getStartBreakTime(replacedText);
            Long endBreakTime = getEndBreakTime(replacedText);

            VisualMergeDmJobAndAsrExtTempBO fakeDmJobPO = new VisualMergeDmJobAndAsrExtTempBO();
            fakeDmJobPO.setConfigId(dmConfigDTO.getDmId());
            fakeDmJobPO.setTemplateId(produceJob.getTemplateId());
            fakeDmJobPO.setMediaInfo(realDmJobPO.getMediaInfo());
            fakeDmJobPO.setScriptStartBreakTime(startBreakTime);
            fakeDmJobPO.setScriptEndBreakTime(endBreakTime);
            fakeDmJobPO.setAsrStartTime(leftSubtitleBO.getTimePointStart());
            fakeDmJobPO.setAsrEndTime(rightSubtitleBO.getTimePointEnd());
            fakeDmJobPO.setModifyDt(realDmJobPO.getModifyDt());
            fakeDmJobPO.setJobType(AiJobTypeE.DIGITAL_MAN.getCode());
            //构建显示的字幕
            List<PreviewCardDTO.SubtitleDTO> subtitleDTOList = buildSubtitleList(currentDmJobSubtitleList);
            fakeDmJobPO.setSubtitleDTOList(subtitleDTOList);

            //处理数字人片段在整个音频中的开始时间
            if(i == 0){
                //第一个片段从0开始
                fakeDmJobPO.setStartInDmVideo(0L);
            }else{
                //在整个数字人视频中的开始时间，取字幕列表中开始时间的最小值
                Long startInDmVideo = subtitleDTOList.stream().map(SubtitleDTO::getBeginTime).min(Long::compare).orElse(0L);
                fakeDmJobPO.setStartInDmVideo(startInDmVideo);
            }

            fakeDmJobList.add(fakeDmJobPO);
            //修改左指针
            leftIndex = rightIndex + 1;
            //重置
            currentDmJobSubtitleList.clear();
            matchFlag = false;
            compareStr = new StringBuilder();
        }

        //***阅前说明：数字人视频中停顿时长与脚本停顿时长不一致，比如脚本中是500ms，但实际只有200ms停顿。
        //故此处根据上下两个片段中脚本的停顿时间比例与实际停顿时间进行计算，得到上下两个片段的起止时间。
        //两个片段之间的asr停顿时间
        Integer twoJobAsrBreakTime = 0;
        //两个片段之间的脚本停顿时间
        Long twoJobScriptBreakTime = 0L;
        //重新计算时长和字幕的开始时间和结束时间
        for (int i = 0; i < fakeDmJobList.size(); i++) {
            VisualMergeDmJobAndAsrExtTempBO currentDmJob = fakeDmJobList.get(i);

            //计算出的当前片段的开始时间和结束时间
            Long calculatedStartTime;
            Long calculatedEndTime;

            //计算当前片段的开始时间
            if (i == 0) {
                //因为ASR是开启了时间戳校验的，首片段的首字不一定是从0开始，为防止比较突兀，此处特殊处理从0开始。
                calculatedStartTime = 0L;
            } else if (twoJobScriptBreakTime == 0L) {
                //此处twoJobScriptBreakTime是当前片段和上一个片段的脚本停顿时间。为0表示两个片段之间无停顿。
                calculatedStartTime = Long.valueOf(currentDmJob.getAsrStartTime());
            } else if (twoJobAsrBreakTime == 0) {
                //此处twoJobAsrBreakTime是当前片段和上一个片段的asr停顿时间。为0表示两个片段之间无停顿。即：脚本的停顿语法没生效！
                calculatedStartTime = Long.valueOf(currentDmJob.getAsrStartTime());
            } else {
                //此处twoJobAsrBreakTime、twoJobScriptBreakTime是当前片段和上一个片段的停顿时间
                //走到这表明有停顿。计算开始时间=当前片段开始时间——两个片段asr停顿时间 *（当前片段脚本开头停顿时间/当前片段和上一片段的脚本停顿时间）
                BigDecimal calculatedStartTimeDecimal = BigDecimal.valueOf(currentDmJob.getAsrStartTime()).subtract(
                        BigDecimal.valueOf(twoJobAsrBreakTime).multiply(
                                BigDecimal.valueOf(currentDmJob.getScriptStartBreakTime())
                                        .divide(BigDecimal.valueOf(twoJobScriptBreakTime), 2,
                                                BigDecimal.ROUND_HALF_UP)));
                calculatedStartTime = calculatedStartTimeDecimal.longValue();

                log.info(
                        "当前片段开头有停顿，currentDmJob:{},,,,twoJobAsrBreakTime:{},,,,twoJobScriptBreakTime:{},,,计算后的开始时间:calculatedStartTime:{}",
                        JSONUtil.toJsonStr(currentDmJob), twoJobAsrBreakTime, twoJobScriptBreakTime,
                        calculatedStartTime);
            }

            //当前是最后一个数字人片段
            if (i == fakeDmJobList.size() - 1) {
                calculatedEndTime = Long.valueOf(currentDmJob.getAsrEndTime()) + currentDmJob.getScriptEndBreakTime();
            } else {
                //取下一个数字人片段
                VisualMergeDmJobAndAsrExtTempBO nextDmJob = fakeDmJobList.get(i + 1);
                //两个片段之间的asr停顿时间=下一个片段开始时间——当前片段结束时间
                twoJobAsrBreakTime = nextDmJob.getAsrStartTime() - currentDmJob.getAsrEndTime();
                //两个片段之间的脚本停顿时间=当前片段脚本中的结尾停顿时间+下一个片段脚本中的开头停顿时间
                twoJobScriptBreakTime = currentDmJob.getScriptEndBreakTime() + nextDmJob.getScriptStartBreakTime();
                if (twoJobScriptBreakTime == 0L) {
                    //此处twoJobScriptBreakTime是当前片段和下一个片段的脚本停顿时间。为0表示两个片段之间无停顿。
                    calculatedEndTime = Long.valueOf(currentDmJob.getAsrEndTime());
                } else if (twoJobAsrBreakTime == 0) {
                    //此处twoJobAsrBreakTime是当前片段和下一个片段的asr停顿时间。为0表示两个片段之间无停顿。即：脚本的停顿语法没生效！
                    calculatedEndTime = Long.valueOf(currentDmJob.getAsrEndTime());
                } else {
                    //走到这表明有停顿。计算结束时间=当前片段结束时间+两个片段asr停顿时间 *（当前片段脚本末尾停顿时间/当前片段和下一片段的脚本停顿时间）
                    BigDecimal calculatedEndTimeDecimal = BigDecimal.valueOf(currentDmJob.getAsrEndTime())
                            .add(BigDecimal.valueOf(twoJobAsrBreakTime).multiply(
                                    BigDecimal.valueOf(currentDmJob.getScriptEndBreakTime())
                                            .divide(BigDecimal.valueOf(twoJobScriptBreakTime), 2,
                                                    BigDecimal.ROUND_HALF_UP)));
                    calculatedEndTime = calculatedEndTimeDecimal.longValue();
                    log.info(
                            "当前片段结尾有停顿，currentDmJob:{},,,,twoJobAsrBreakTime:{},,,,twoJobScriptBreakTime:{},,,,计算后的结尾时间:calculatedEndTime:{}",
                            JSONUtil.toJsonStr(currentDmJob), twoJobAsrBreakTime, twoJobScriptBreakTime,
                            calculatedEndTime);
                }
            }
            currentDmJob.setDuration(calculatedEndTime - calculatedStartTime);

            //修改当前片段开头字幕的开始时间
            SubtitleDTO firstSubtitleDTO = currentDmJob.getSubtitleDTOList().get(0);
            firstSubtitleDTO.setBeginTime(calculatedStartTime);

            //修改当前片段结尾字幕的结束时间
            SubtitleDTO finalSubtitleDTO = currentDmJob.getSubtitleDTOList()
                    .get(currentDmJob.getSubtitleDTOList().size() - 1);
            finalSubtitleDTO.setEndTime(calculatedEndTime);

            //设置字幕
            currentDmJob.setSubtitleInfo(JSONUtil.toJsonStr(currentDmJob.getSubtitleDTOList()));
        }

        return new ArrayList<>(fakeDmJobList);
    }

    /**
     * 根据词组列表，构建显示的字幕列表
     *
     * @param tempSubtitleWordsBOList
     * @return
     */
    private static List<PreviewCardDTO.SubtitleDTO> buildSubtitleList(
            List<TempSubtitleWordsBO> tempSubtitleWordsBOList) {
        List<PreviewCardDTO.SubtitleDTO> subtitleDTOS = new ArrayList<>();
        //根据所属句子索引分组
        Map<Integer, List<TempSubtitleWordsBO>> groupBySentenceIndexMap = tempSubtitleWordsBOList.stream()
                .collect(Collectors.groupingBy(TempSubtitleWordsBO::getSentenceIndex));
        //句子索引排序
        List<Integer> sentenceIndexSet = groupBySentenceIndexMap.keySet().stream().sorted()
                .collect(Collectors.toList());
        for (Integer sentenceIndex : sentenceIndexSet) {
            List<TempSubtitleWordsBO> wordsBOList = groupBySentenceIndexMap.get(sentenceIndex);
            //先根据词组索引排个序
            wordsBOList = wordsBOList.stream().sorted(Comparator.comparing(TempSubtitleWordsBO::getIndex))
                    .collect(Collectors.toList());
            //根据词组列表 拼接为句子，句子的开始时间为第一个词组的开始时间，句子的结束时间为最后一个词组的结束时间。
            PreviewCardDTO.SubtitleDTO subtitleDTO = new SubtitleDTO();
            StringBuffer sbf = new StringBuffer();
            for (int i = 0; i < wordsBOList.size(); i++) {
                TempSubtitleWordsBO tempSubtitleWordsBO = wordsBOList.get(i);
                //拼接的是字幕校验后的文本
                sbf.append(tempSubtitleWordsBO.getRevisedText());
                if (i == 0) {
                    subtitleDTO.setBeginTime(Long.valueOf(tempSubtitleWordsBO.getTimePointStart()));
                }
                if (i == wordsBOList.size() - 1) {
                    subtitleDTO.setEndTime(Long.valueOf(tempSubtitleWordsBO.getTimePointEnd()));
                }
            }
            subtitleDTO.setText(sbf.toString());
            subtitleDTOS.add(subtitleDTO);
        }
        return subtitleDTOS;
    }

    private static List<PreviewCardDTO.SubtitleDTO> cnvSubtitleBO2DTO(List<RevisedAsrSubtitleBO> inputList) {
        List<PreviewCardDTO.SubtitleDTO> resultList = inputList.stream().map(input -> {
            PreviewCardDTO.SubtitleDTO result = new SubtitleDTO();
            result.setText(input.getRevisedSubtitle());
            result.setBeginTime(Long.valueOf(input.getTimePointStart()));
            result.setEndTime(Long.valueOf(input.getTimePointEnd()));
            return result;
        }).collect(Collectors.toList());

        return resultList;
    }

    private static String getTextFromContent(String content) {
        if (StringUtils.isBlank(content)) {
            return SymbolE.BLANK.getValue();
        }
        Map<String, Object> textConfigMap = JsonUtils.getMap4Json(content);
        return getText(textConfigMap);
    }

    /**
     * 获取末尾停顿的时间
     *
     * @param text
     * @return
     */
    private static Long getEndBreakTime(String text) {
        long breakTime = 0L;
        if (StringUtils.isBlank(text)) {
            return breakTime;
        }
        try {
            Pattern pattern = Pattern.compile(END_BREAK_TIME_PATTERN);
            Matcher matcher = pattern.matcher(text);
            if (matcher.find() && matcher.groupCount() > 0) {
                //获取连续多个的break
                String breakGroup = matcher.group(0);
                //然后从breakGroup获取所有的time
                if (StringUtils.isNotBlank(breakGroup)) {
                    Pattern timePattern = Pattern.compile(TIME_PATTERN);
                    Matcher timeMatcher = timePattern.matcher(breakGroup);
                    while (timeMatcher.find() && timeMatcher.groupCount() > 0) {
                        breakTime += Long.parseLong(timeMatcher.group(1));
                    }
                    return breakTime;
                }
            }

            Pattern patternMS = Pattern.compile(END_BREAK_TIME_PATTERN_MS);
            Matcher matcherMS = patternMS.matcher(text);
            if (matcherMS.find() && matcherMS.groupCount() > 0) {
                //获取连续多个的break
                String breakGroup = matcherMS.group(0);
                //然后从breakGroup获取所有的time
                if (StringUtils.isNotBlank(breakGroup)) {
                    Pattern timePattern = Pattern.compile(TIME_PATTERN_MS);
                    Matcher timeMatcher = timePattern.matcher(breakGroup);
                    while (timeMatcher.find() && timeMatcher.groupCount() > 0) {
                        String currentBreakTime = timeMatcher.group(1);
                        currentBreakTime = currentBreakTime.replace("ms", "");
                        breakTime += Long.parseLong(currentBreakTime);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取数字人文本停顿时间异常,text={}", text, e);
        }
        return breakTime;
    }

    /**
     * 获取开始的停顿的时间
     *
     * @param text
     * @return
     */
    private static Long getStartBreakTime(String text) {
        long breakTime = 0L;
        if (StringUtils.isBlank(text)) {
            return breakTime;
        }
        try {
            Pattern pattern = Pattern.compile(START_BREAK_TIME_PATTERN);
            Matcher matcher = pattern.matcher(text);
            if (matcher.find() && matcher.groupCount() > 0) {
                //获取连续多个的break
                String breakGroup = matcher.group(0);
                //然后从breakGroup获取所有的time
                if (StringUtils.isNotBlank(breakGroup)) {
                    Pattern timePattern = Pattern.compile(TIME_PATTERN);
                    Matcher timeMatcher = timePattern.matcher(breakGroup);
                    while (timeMatcher.find() && timeMatcher.groupCount() > 0) {
                        breakTime += Long.parseLong(timeMatcher.group(1));
                    }
                    return breakTime;
                }
            }

            Pattern patternMS = Pattern.compile(START_BREAK_TIME_PATTERN_MS);
            Matcher matcherMS = patternMS.matcher(text);
            if (matcherMS.find() && matcherMS.groupCount() > 0) {
                //获取连续多个的break
                String breakGroup = matcherMS.group(0);
                //然后从breakGroup获取所有的time
                if (StringUtils.isNotBlank(breakGroup)) {
                    Pattern timePattern = Pattern.compile(TIME_PATTERN_MS);
                    Matcher timeMatcher = timePattern.matcher(breakGroup);
                    while (timeMatcher.find() && timeMatcher.groupCount() > 0) {
                        String currentBreakTime = timeMatcher.group(1);
                        currentBreakTime = currentBreakTime.replace("ms", "");
                        breakTime += Long.parseLong(currentBreakTime);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取数字人文本停顿时间异常,text={}", text, e);
        }
        return breakTime;
    }

    private static String getText(Map<String, Object> textConfigMap) {
        if (MapUtils.isNotEmpty(textConfigMap) && Objects.nonNull(textConfigMap.get("text"))) {
            return textConfigMap.get("text").toString();
        }
        return StringUtils.EMPTY;
    }

    private void handlerDmSubtitleAndStartInDmVideo(Integer dmProduceMode, VisualAiJobPO dmJob,
            PreviewCardDTO.DigitalManDTO dmDTO, DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO) {
        //数字人视频是分段合成的
        if (DmProduceModeEnum.SEPARATE_GEN_DM.getMode().equals(dmProduceMode)) {
            if (StringUtils.isBlank(dmJob.getSubtitleInfo())) {
                return;
            }

            dmDTO.setSubtitles(
                    JSONUtil.toBean(dmJob.getSubtitleInfo(), new TypeReference<List<PreviewCardDTO.SubtitleDTO>>() {
                    }, false));
            return;
        }

        //数字人视频是合并合成的
        if (DmProduceModeEnum.MERGE_GEN_DM_THEN_ASR.getMode().equals(dmProduceMode)) {
            //理论上伪数字人合成任务中的字幕不应该为空
            if (StringUtils.isBlank(dmJob.getSubtitleInfo())) {
                return;
            }

            List<PreviewCardDTO.SubtitleDTO> subtitleList = JSONUtil
                    .toBean(dmJob.getSubtitleInfo(), new TypeReference<List<PreviewCardDTO.SubtitleDTO>>() {
                    }, false);

            Long startInDmVideo = ((VisualMergeDmJobExtTempBO) dmJob).getStartInDmVideo();
            dmDTO.setStartInDmVideo(startInDmVideo);
            //只有当数字人配置中需要字幕时，才设置字幕
            if (Const.ONE.equals(dmConfigDTO.getEnableSubtitle())) {
                //预览信息里字幕的时间是在该数字人片段的时间，所以需要减去startInDmVideo
                dmDTO.setSubtitles(subtitleList.stream().peek(subtitleDTO -> {
                    subtitleDTO.setBeginTime(subtitleDTO.getBeginTime() - startInDmVideo);
                    subtitleDTO.setEndTime(subtitleDTO.getEndTime() - startInDmVideo);
                }).collect(Collectors.toList()));
            }
            return;
        }

        //通过TTS合并驱动数字人
        if (DmProduceModeEnum.MERGE_GEN_DM_WITH_TTS.getMode().equals(dmProduceMode)) {
            dmDTO.setStartInDmVideo(((VisualMergeDmJobExtTempBO) dmJob).getStartInDmVideo());
            if(Const.ONE.equals(dmConfigDTO.getEnableSubtitle())){
                dmDTO.setSubtitles(
                        JSONUtil.toBean(dmJob.getSubtitleInfo(), new TypeReference<List<PreviewCardDTO.SubtitleDTO>>() {
                        }, false));
            }
            return;
        }

    }

    /**
     * 生成分片任务
     *
     * @param produceJob
     */
    private void generateSegmentTask(VisualProduceJobPO produceJob) {
        List<VisualProduceJobSegmentPO> segments = new ArrayList<>();
        for (int i = 0; i < segmentNum; i++) {
            VisualProduceJobSegmentPO segment = new VisualProduceJobSegmentPO();
            segment.setSegmentId(hostTimeIdg.generateId().longValue());
            segment.setProduceJobId(produceJob.getJobId());
            segment.setStatus(JobStatusE.READY.getCode());
            segment.setCreateDt(new Date());
            segment.setModifyDt(new Date());
            segment.setSort(i + 1);
            segments.add(segment);
        }
        segmentManager.saveBatch(segments);
        visualProduceJobManager.lambdaUpdate().eq(VisualProduceJobPO::getJobId, produceJob.getJobId())
                .set(VisualProduceJobPO::getSegmentStatus, Const.ONE).update();
        for (VisualProduceJobSegmentPO segment : segments) {
            redisUtil.leftPush(VISUAL_SEGMENT_QUEUE_KEY, segment.getSegmentId());
        }
    }

    /**
     * 计算片段里的TTS时间轴信息
     * 返回该关键时长下最长的时间
     */
    private void handleTtsKeyTime(DynamicExtendDTO dynamicExtendDTO, DynamicNodeDTO.TtsConfigDTO ttsConfigDTO, Map<String, VisualAiJobPO> ttsJobMap) {
        if (Objects.isNull(ttsConfigDTO)){
            return;
        }
        VisualAiJobPO ttsJob = ttsJobMap.get(ttsConfigDTO.getTtsId());
        Assert.notNull(ttsJob, "关键时长-未找到tts任务, ttsId=" + ttsConfigDTO.getTtsId());
        long start = ttsConfigDTO.getStart();
        long endDelay = ttsConfigDTO.getEndDelay();
        long ttsDuration = ttsJob.getDuration();
        dynamicExtendDTO.setKeyTimeClipDuration(start + ttsDuration + endDelay);
    }

    /**
     * 计算片段里的数字人时间轴信息
     * 返回该关键时长下最长的时间
     */
    private void handleDmKeyTime(DynamicExtendDTO dynamicExtendDTO, DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO, Map<String, VisualAiJobPO> dmJobMap) {
        if (Objects.isNull(dmConfigDTO)){
            return;
        }
        VisualAiJobPO dmJob = dmJobMap.get(dmConfigDTO.getDmId());
        Assert.notNull(dmJob, "关键时长-未找到数字人任务, dmId=" + dmConfigDTO.getDmId());
        long start = dmConfigDTO.getStart();
        long endDelay = Objects.nonNull(dmConfigDTO.getEndDelay()) ?
                dmConfigDTO.getEndDelay() :
                Const.ZERO_LONG;
        //处理数字人片段时长
        long dmDuration = dmJob.getDuration();
        dynamicExtendDTO.setKeyTimeClipDuration(start + dmDuration + endDelay);
    }

    /**
     * 计算片段里的Video时间轴信息
     * 返回该关键时长下最长的时间
     */
    private void handleVideoKeyTime(DynamicExtendDTO dynamicExtendDTO, DynamicNodeDTO.VideoConfigDTO videoConfigDTO) {
        if (Objects.isNull(videoConfigDTO)) {
            return;
        }
        long start = videoConfigDTO.getStart();
        long endDelay = Objects.nonNull(videoConfigDTO.getEndDelay()) ? videoConfigDTO.getEndDelay() : 0L;
        long videoDuration = videoConfigDTO.getDuration();
        dynamicExtendDTO.setKeyTimeClipDuration(start + videoDuration + endDelay);
    }

    /**
     * 计算片段里的Audio时间轴信息
     * 返回该关键时长下最长的时间
     * clipRealDuration 片段时长
     */
    private void handleAudioKeyTime(DynamicExtendDTO dynamicExtendDTO, DynamicNodeDTO.AudioConfigDTO audioConfigDTO){
        if (Objects.isNull(audioConfigDTO)) {
            return;
        }
        long start = audioConfigDTO.getStart();
        long endDelay = Objects.nonNull(audioConfigDTO.getEndDelay()) ? audioConfigDTO.getEndDelay() : 0L;
        long videoDuration = audioConfigDTO.getDuration();
        dynamicExtendDTO.setKeyTimeClipDuration(start + videoDuration + endDelay);
    }

    /**
     * 计算片段里的Audio时间轴信息
     * 返回该关键时长下最长的时间
     * clipRealDuration 片段时长
     */
    private void handleDataSheetKeyTime(DynamicExtendDTO dynamicExtendDTO, DynamicNodeDTO.DataSheetDTO dataSheetDTO){
        if (Objects.isNull(dataSheetDTO)) {
            return;
        }
        long start = dataSheetDTO.getStart();
        long endDelay = Objects.nonNull(dataSheetDTO.getEndDelay()) ? dataSheetDTO.getEndDelay() : 0L;
        long videoDuration = dataSheetDTO.getDuration();
        dynamicExtendDTO.setKeyTimeClipDuration(start + videoDuration + endDelay);
    }

    public static void main(String[] args) {
        DynamicNodeDTO dynamicNodeDTO = new DynamicNodeDTO();
        List<TtsConfigDTO> inputTtsList = new ArrayList<>();
        TtsConfigDTO tts1 = new TtsConfigDTO();
        tts1.setHide(true);
        tts1.setTtsId("tts1");
        inputTtsList.add(tts1);
        TtsConfigDTO tts2 = new TtsConfigDTO();
        tts2.setHide(false);
        tts2.setTtsId("tts2");
        inputTtsList.add(tts2);
        dynamicNodeDTO.setTtsList(inputTtsList);

        List<DigitalManConfigDTO> inputDmList = new ArrayList<>();
        DigitalManConfigDTO dm1 = new DigitalManConfigDTO();
        dm1.setHide(true);
        dm1.setDmId("dm1");
        inputDmList.add(dm1);
        DigitalManConfigDTO dm2 = new DigitalManConfigDTO();
        dm2.setHide(false);
        dm2.setDmId("dm2");
        inputDmList.add(dm2);
        dynamicNodeDTO.setDmList(inputDmList);

        List<TtsConfigDTO> ttsList = (List<TtsConfigDTO>) filterHideConfig(dynamicNodeDTO.getTtsList());
        List<DigitalManConfigDTO> dmList = (List<DigitalManConfigDTO>) filterHideConfig(dynamicNodeDTO.getDmList());
        System.out.println(JSONUtil.toJsonStr(ttsList));
        System.out.println(JSONUtil.toJsonStr(dmList));
    }

}
