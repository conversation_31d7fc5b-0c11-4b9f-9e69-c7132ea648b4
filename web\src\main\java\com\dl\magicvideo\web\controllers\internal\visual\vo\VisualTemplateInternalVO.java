package com.dl.magicvideo.web.controllers.internal.visual.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dl.magicvideo.biz.manager.visual.dto.VisualCardDTO;
import com.dl.magicvideo.web.controllers.template.vo.CardVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
public class VisualTemplateInternalVO implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板状态 0-启用 1-禁用
     */
    private Integer status;

    /**
     * 模板封面
     */
    private String coverUrl;

    /**
     * 尺寸
     */
    private String resolution;

    /**
     * 横版/竖版
     */
    private String resolutionType;

    /**
     * tts配置
     */
    private String ttsParam;

    /**
     * 背景音乐
     */
    private String bgMusic;

    /**
     * 背景音乐配置
     */
    private String bgMusicParam;

    /**
     * 替换变量
     */
    private String replaceData;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 是否系统模板 0-否 1-是
     */
    private Integer isSys;

    private String creatorName;

    private Long duration;

    private String shortVideoUrl;

    private String previewVideoUrl;

    public Date createDt;

    public Long createBy;

    public Date modifyDt;

    public Long modifyBy;

    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     * @see: ShareConfStateEnum
     */
    private Integer shareConfState;



    /**
     * 是否包含理财经理信息 0-未包含 1-包含
     */
    private Integer isManager;

    /**
     * 一级菜单
     */
    private Integer firstCategory;

    /**
     * 二级菜单
     */
    private Integer secondCategory;

    @ApiModelProperty("卡片列表")
    private List<CardVO> cards;

    private Integer isPPT;

    /**
     * 组件版本号，默认2.0.0
     */
    private String componentVersion;

    @ApiModelProperty("卡片列表")
    private List<TagVO> tagList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}