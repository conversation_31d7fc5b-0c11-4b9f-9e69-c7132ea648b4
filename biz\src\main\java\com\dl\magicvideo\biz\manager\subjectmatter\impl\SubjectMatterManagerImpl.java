package com.dl.magicvideo.biz.manager.subjectmatter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.subjectmatter.SubjectMatterMapper;
import com.dl.magicvideo.biz.dal.subjectmatter.param.SubjectMatterJoinPageParam;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMatterManager;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectAddBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterJoinPageBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterPageBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterSaveBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.XMindBO;
import com.dl.magicvideo.biz.manager.subjectmatter.convert.SubjectMatterConvert;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.MatchResultDTO;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.NodeEffectDTO;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.SubjectTreeDTO;
import com.dl.magicvideo.biz.manager.subjectmatter.util.CustomNameComparator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:29
 */
@Slf4j
@Component
public class SubjectMatterManagerImpl extends ServiceImpl<SubjectMatterMapper, SubjectMatterPO>
        implements SubjectMatterManager {

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Long save(SubjectMatterSaveBO saveBO) {
        if (Const.TWO.equals(saveBO.getLevel())) {
            Assert.notNull(saveBO.getParentId(), "父级id不能为空");
        }

        //校验题材库名称  查列表是因为没有对题材库的名称做强唯一限制。
        List<SubjectMatterPO> existNamePOs = this
                .list(Wrappers.lambdaQuery(SubjectMatterPO.class)
                        .eq(SubjectMatterPO::getName, saveBO.getName())
                        .eq(SubjectMatterPO::getType,Const.ONE)
                        .eq(SubjectMatterPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isNotEmpty(existNamePOs)) {
            //saveBO.getBizId()表示新增
            if (Objects.isNull(saveBO.getBizId())) {
                throw BusinessServiceException.getInstance("题材库名称[" + saveBO.getName() + "]已存在");
                //修改
            } else if (!existNamePOs.stream().map(SubjectMatterPO::getBizId).collect(Collectors.toList())
                    .contains(saveBO.getBizId())) {
                throw BusinessServiceException.getInstance("题材库名称[" + saveBO.getName() + "]已存在");
            }
        }

        SubjectMatterPO subjectMatterPO = SubjectMatterConvert.cvnSubjectMatterSaveBO2PO(saveBO);

        //修改
        if (Objects.nonNull(subjectMatterPO.getBizId())) {
            //更新
            this.update(subjectMatterPO, Wrappers.lambdaUpdate(SubjectMatterPO.class)
                    .eq(SubjectMatterPO::getBizId, subjectMatterPO.getBizId()));

            return subjectMatterPO.getBizId();
        }

        //新增
        subjectMatterPO.setBizId(hostTimeIdg.generateId().longValue());
        subjectMatterPO.setCreateDt(new Date());
        subjectMatterPO.setCreateBy(saveBO.getOperatorId());
        subjectMatterPO.setCreatorName(saveBO.getOperatorName());
        subjectMatterPO.setIsHaveChild(Const.ZERO);
        subjectMatterPO.setRootId(subjectMatterPO.getBizId());
        this.save(subjectMatterPO);

        if (Const.ONE.equals(subjectMatterPO.getLevel())) {
            return subjectMatterPO.getBizId();
        }

        //若为二级，则要维护父级的IsHaveChild字段
        SubjectMatterPO fatherPO = this.getOne(Wrappers.lambdaQuery(SubjectMatterPO.class)
                .eq(SubjectMatterPO::getBizId, subjectMatterPO.getParentId())
                .eq(SubjectMatterPO::getIsDeleted, Const.ZERO));
        Assert.notNull(fatherPO, "父级题材不存在!");
        if (Const.ZERO.equals(fatherPO.getIsHaveChild())) {
            fatherPO.setIsHaveChild(Const.ONE);
            this.updateById(fatherPO);
        }
        return subjectMatterPO.getBizId();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Long addAll(SubjectAddBO subjectAddBO) {

        //校验题材库标题  查列表是因为没有对题材库的标题做强唯一限制。
        List<SubjectMatterPO> existNamePOs = this
                .list(Wrappers.lambdaQuery(SubjectMatterPO.class)
                        .eq(SubjectMatterPO::getTitle, subjectAddBO.getTitle())
                        .eq(SubjectMatterPO::getType,Const.TWO)
                        .eq(SubjectMatterPO::getIsDeleted, Const.ZERO));

        if (CollectionUtils.isNotEmpty(existNamePOs)) {
            //subjectAddBO.getBizId()表示新增
            if (Objects.isNull(subjectAddBO.getBizId())) {
                throw BusinessServiceException.getInstance("题材库名称[" + subjectAddBO.getTitle() + "]已存在");
                //修改
            } else if (!existNamePOs.stream().map(SubjectMatterPO::getBizId).collect(Collectors.toList())
                    .contains(subjectAddBO.getBizId())) {
                throw BusinessServiceException.getInstance("题材库名称[" + subjectAddBO.getTitle() + "]已存在");
            }
        }

        //最外层BO转PO
        SubjectMatterPO subjectMatterPO = SubjectMatterConvert.cvnSubjectAddBO2PO(subjectAddBO);

        XMindBO xmindBO = subjectAddBO.getXmindBO();
        List<SubjectMatterPO> resultList = new ArrayList<>();

        //修改
        if (Objects.nonNull(subjectMatterPO.getBizId())) {
            subjectMatterPO.setRootId(subjectMatterPO.getBizId());
            subjectMatterPO.setSubjectPath(subjectMatterPO.getBizId().toString());
            //递归XMindBO，把XMindBO转换为多个SubjectMatterPO对象,同时设置需要的属性
            List<SubjectMatterPO> subjectMatterPOList = convertXMindBOToSubjectMatterPO(xmindBO, subjectMatterPO.getRootId(),
                    subjectMatterPO.getBizId(), xmindBO.getLevel(), resultList, subjectMatterPO.getSubjectPath(), subjectAddBO);

            //删除一级题材下的所有题材
            update(Wrappers.lambdaUpdate(SubjectMatterPO.class)
                    .eq(SubjectMatterPO::getRootId, subjectMatterPO.getRootId())
                    .ne(SubjectMatterPO::getLevel, Const.ONE)
                    .set(SubjectMatterPO::getIsDeleted, Const.ONE));

            //更新一级题材信息update
            update(subjectMatterPO, Wrappers.lambdaUpdate(SubjectMatterPO.class)
                    .eq(SubjectMatterPO::getBizId, subjectMatterPO.getBizId()));
            //批量保存一级题材下的所有题材
            saveBatch(subjectMatterPOList);
            return subjectMatterPO.getRootId();
        }

        //新增
        //设置其他属性
        subjectMatterPO.setBizId(hostTimeIdg.generateId().longValue());
        subjectMatterPO.setLevel(Const.ONE);
        subjectMatterPO.setIsHaveChild(Const.ONE);
        subjectMatterPO.setType(Const.TWO);
        subjectMatterPO.setSubjectPath(subjectMatterPO.getBizId().toString());
        subjectMatterPO.setRootId(subjectMatterPO.getBizId());

        subjectMatterPO.setCreateBy(subjectAddBO.getOperatorId());
        subjectMatterPO.setCreateDt(new Date());
        subjectMatterPO.setCreatorName(subjectAddBO.getOperatorName());

        //递归XMindBO，把XMindBO转换为多个SubjectMatterPO对象,同时设置需要的属性
        List<SubjectMatterPO> subjectMatterPOList = convertXMindBOToSubjectMatterPO(xmindBO, subjectMatterPO.getRootId(),
                subjectMatterPO.getBizId(), xmindBO.getLevel(), resultList, subjectMatterPO.getSubjectPath(), subjectAddBO);

        //最外层的PO对象也放入List中
        subjectMatterPOList.add(subjectMatterPO);
        //添加到数据库
        saveBatch(subjectMatterPOList);
        return subjectMatterPO.getRootId();
    }

    private List<SubjectMatterPO> convertXMindBOToSubjectMatterPO(XMindBO xmindBO, Long rootId, Long parentId, Integer level, List<SubjectMatterPO> resultList, String parentSubjectPath, SubjectAddBO subjectAddBO) {
        List<SubjectMatterPO> currentLevelList = new ArrayList<>(); // 创建一个新列表来保存当前层级的数据

        if (xmindBO != null) {
            SubjectMatterPO subjectMatterPO = null;
            if (level != 1) { // 不需要在第一层级添加数据到结果列表
                subjectMatterPO = new SubjectMatterPO();
                subjectMatterPO.setBizId(hostTimeIdg.generateId().longValue());
                subjectMatterPO.setName(xmindBO.getName());
                subjectMatterPO.setImgUrl(subjectAddBO.getImgUrl());
                subjectMatterPO.setLevel(xmindBO.getLevel());
                subjectMatterPO.setParentId(parentId);
                subjectMatterPO.setRootId(rootId);
                subjectMatterPO.setSubjectPath(parentSubjectPath + Const.VERTICAL_LINE + subjectMatterPO.getBizId());
                // 是否有子节点 1（有子节点）0（无子节点）
                subjectMatterPO.setIsHaveChild(xmindBO.getChildren().isEmpty() ? 0 : 1);
                subjectMatterPO.setType(Const.TWO);

                subjectMatterPO.setCreateDt(new Date());
                subjectMatterPO.setCreatorName(subjectAddBO.getOperatorName());
                subjectMatterPO.setCreateBy(subjectAddBO.getOperatorId());
                subjectMatterPO.setModifyName(subjectAddBO.getOperatorName());
                subjectMatterPO.setModifyBy(subjectAddBO.getOperatorId());
                subjectMatterPO.setModifyDt(new Date());

                currentLevelList.add(subjectMatterPO); // 将当前项添加到当前层级的列表中
            }
            for (XMindBO childBO : xmindBO.getChildren()) {
                Long currentParentId = level != 1 ? subjectMatterPO.getBizId() : parentId;
                String currentParentSubjectPath = level != 1 ? subjectMatterPO.getSubjectPath() : parentSubjectPath;
                // 递归调用，注意这里传递的是当前层级的列表currentLevelList，而不是最终结果列表resultList
                List<SubjectMatterPO> childLevelList = convertXMindBOToSubjectMatterPO(childBO, rootId, currentParentId, level + 1, currentLevelList, currentParentSubjectPath, subjectAddBO);
                currentLevelList.addAll(childLevelList); // 将子层级的数据合并到当前层级的列表中
            }
        }
        // 只在最外层调用时合并到最终结果列表，这样可以避免在递归过程中重复添加数据
        if (level == 1) {
            resultList.addAll(currentLevelList);
        }
        return currentLevelList; // 返回当前层级列表，供上一层递归调用合并数据
    }

    @Override
    public IPage<SubjectMatterPO> page(SubjectMatterPageBO pageBO) {
        LambdaQueryWrapper<SubjectMatterPO> wrapper = null;
        if (Objects.equals(pageBO.getType(), Const.ONE)) {
            wrapper = Wrappers.lambdaQuery(SubjectMatterPO.class)
                    .like(StringUtils.isNotBlank(pageBO.getName()), SubjectMatterPO::getName, pageBO.getName())
                    .eq(Objects.nonNull(pageBO.getLevel()), SubjectMatterPO::getLevel, pageBO.getLevel())
                    .eq(SubjectMatterPO::getType, Const.ONE)
                    .eq(SubjectMatterPO::getIsDeleted, Const.ZERO)
                    .orderByDesc(SubjectMatterPO::getModifyDt, SubjectMatterPO::getId);
        } else {
            wrapper = Wrappers.lambdaQuery(SubjectMatterPO.class)
                    .likeRight(StringUtils.isNotBlank(pageBO.getTitle()), SubjectMatterPO::getTitle, pageBO.getTitle())
                    .eq(SubjectMatterPO::getType, Const.TWO)
                    .eq(SubjectMatterPO::getLevel, Const.ONE)
                    .eq(SubjectMatterPO::getIsDeleted, Const.ZERO)
                    .orderByDesc(SubjectMatterPO::getModifyDt, SubjectMatterPO::getId);
        }
        return this.page(new Page<>(pageBO.getPageIndex(), pageBO.getPageSize()), wrapper);
    }

    @Override
    public ResponsePageQueryDO<List<SubjectMatterPO>> joinPage(SubjectMatterJoinPageBO pageBO) {
        SubjectMatterJoinPageParam param = new SubjectMatterJoinPageParam();
        param.setStockCode(pageBO.getStockCode());
        param.setPageIndex(pageBO.getPageIndex());
        param.setPageSize(pageBO.getPageSize());
        param.setLevel(pageBO.getLevel());
        param.setName(pageBO.getName());
        param.setType(pageBO.getType());
        Long count = baseMapper.joinPageCount(param);

        ResponsePageQueryDO<List<SubjectMatterPO>> result = new ResponsePageQueryDO<>();
        result.setPageIndex(pageBO.getPageIndex());
        result.setPageSize(pageBO.getPageSize());
        result.setTotal(count);

        if (count == 0L) {
            result.setDataResult(Collections.emptyList());
            return result;
        }

        List<SubjectMatterPO> resultList = baseMapper.joinPageList(param);
        result.setDataResult(resultList);
        return result;
    }

    @Override
    public SubjectTreeDTO getTreeByText(Long matterId, String text) {
        MatchResultDTO matchResultDTO = matchText(text);
        if (matchResultDTO == null || CollectionUtils.isEmpty(matchResultDTO.getEndNodeList())) {
            return null;
        }
        List<SubjectMatterPO> list = this.lambdaQuery().eq(SubjectMatterPO::getRootId, matterId).eq(SubjectMatterPO::getIsDeleted, Const.ZERO).in(SubjectMatterPO::getName, matchResultDTO.getEndNodeList()).list();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        //搜索出绝对路径，将路径里的内容拉取出来
        List<String> subjectPathList = list.stream().map(SubjectMatterPO::getSubjectPath).collect(Collectors.toList());

        Set<String> subjectPathSet = new HashSet<>();

        for (String subjectPath : subjectPathList) {
            String[] split = subjectPath.split("\\|");
            if (split.length > 0) {
                subjectPathSet.addAll(Arrays.asList(split));
            }
        }
        //获取关联的节点内容
        List<SubjectMatterPO> finalList = this.lambdaQuery().eq(SubjectMatterPO::getRootId, matterId).eq(SubjectMatterPO::getIsDeleted, Const.ZERO).in(SubjectMatterPO::getBizId, subjectPathSet).list();

        SubjectTreeDTO subject = SubjectMatterConvert.buildTree(finalList, matchResultDTO.getNodeEffectDTOMap());

        CustomNameComparator comparator = new CustomNameComparator(matchResultDTO.getSecordSortList());

        Collections.sort(subject.getChildren(), comparator);

        return subject;
    }


    /**
     * 根据文案匹配节点
     * 事件例子 "消费品以旧换新(事件)一\n下游:线上渠道--电商平台 补贴增加 (直接利好)、线上渠道--品牌官网 优惠加大(直接利好)、线下渠道--超市 活动增多(直接利好)、线下渠道--品牌专卖店 促销增大(直接利好)\n中游:白电 产品升级(直接利好)、黑电 库存优化(直接利好)、厨电 智能转型(直接利好)、小家电 需求增加(直接利好)\n上游:原材料--金属材料 需求增加(间接利好)、原材料--塑料 技术升级(间接利好)、核心零部件--压缩机 小型高效需求(间接利好)、核心零部件--电机 需求增加(间接利好)、核心零部件--芯片 技术升级驱动(间接利好)、核心零部件--传感器 需求增加(间接利好)"
     * @param text
     * @return
     */
    private static MatchResultDTO matchText(String text){
        MatchResultDTO matchResultDTO = new MatchResultDTO();
        //末尾节点
        List<String> matchList = new ArrayList<>();
        //二级节点顺序
        List<String> secordSortList = new ArrayList<>();
        //节点影响因子Map
        Map<String, NodeEffectDTO> nodeEffectDTOMap = new HashMap<>();
        String[] split1 = text.split("\n");
        if (split1.length < 2) {
            return null;
        }
        try {
            //根据换行符过滤(第一个片段为事件，过滤掉)
            for (int i = 1; i < split1.length; i++) {
                //            String[] test1 = split1[i].split("、");
                String[] test1 = split1[i].split("[,，、]");
                //根据、过滤
                for (String s1 : test1) {
                    //根据空格过滤
//                    String[] test2 = s1.split(" ");
                    String[] test2 = s1.split("\\s+");
                    //空格前位置为节点相关信息,类似下游：纯电车或下游：纯电车--黑色电车
                    String s2 = test2[0];
                    //节点名
                    String nodeName = StringUtils.EMPTY;
                    if (s2.contains("--")) {
                        String[] split = s2.split("--");
                        nodeName = split[split.length - 1];
                        if (split[0].contains(":")) {
                            String[] splitMao = s2.split(":");
                            //nodeName = splitMao[split.length - 1];
                            secordSortList.add(splitMao[0]);
                        } else if (s2.contains("：")) {
                            String[] splitMao = s2.split("：");
                            //nodeName = splitMao[split.length - 1];
                            secordSortList.add(splitMao[0]);
                        }
                    } else if (s2.contains(":")) {
                        String[] split = s2.split(":");
                        nodeName = split[split.length - 1];
                        secordSortList.add(split[0]);
                    } else if (s2.contains("：")) {
                        String[] split = s2.split("：");
                        nodeName = split[split.length - 1];
                        secordSortList.add(split[0]);
                    } else {
                        nodeName = s2;
                    }

                    if (test2.length > 1) {
                        //空格前位置为节点影响因子，如海外补贴限制（利空）
                        String s3 = test2[1];
                        if (test2.length > 2) {
                            s3 += test2[2];
                        }
                        //如果没有（(说明只有理由
                        NodeEffectDTO effectDTO = new NodeEffectDTO();
                        if (s3.contains("（") || s3.contains("(")) {
                            //如果第一个为（或者(
                            if (s3.indexOf("（") == 0 || s3.indexOf("(") == 0) {
                                effectDTO.setEffectResult(s3.substring(1, s3.length() - 1));
                            } else {
                                //影响因子数组
                                String[] effectStr = s3.split("[（(]");
                                effectDTO.setEffectReason(effectStr[0]);
                                effectDTO.setEffectResult(effectStr[1].substring(0, effectStr[1].length() - 1));
                            }
                        }  else {
                           //只有理由，没有结果
                            effectDTO.setEffectReason(s3);
                        }
                        nodeEffectDTOMap.put(nodeName, effectDTO);
                    }
                    matchList.add(nodeName);
                }
            }
            matchResultDTO.setSecordSortList(secordSortList);
            matchResultDTO.setEndNodeList(matchList);
            matchResultDTO.setNodeEffectDTOMap(nodeEffectDTOMap);
            return matchResultDTO;
        } catch (Exception e) {
            log.error("提取题材信息异常", e);
            return null;
        }
    }

    public static void main(String[] args) {
        //String text = "消费品以旧换新\n下游：纯电车 海外补贴限制（利空）\n上游：锂 关键原材料（利空）、稀土 关键原材料（利空）\n中游：电池 重点限制对象（利空） ，正负极重点限制对象（利空），电解液 重点限制对象（利空）";
        String text = "央行购金（事件）→\n" + "上游：地质勘探 加大勘探投资       （直接利好）、开采 企业采矿积极性提高（直接利好）、矿石处理 黄金提取需求（直接利好）\n"
                + "下游：金币和金条 需求增多（直接利好）、黄金交易所交易基金（ETF）投资旺盛（直接利好）、黄金衍生品和期货 投机需求（直接利好）\n"
                + "中游：冶炼 影响偏弱、精炼 （中性）";
        MatchResultDTO matchResultDTO = matchText(text);
        System.out.println("strings = " + JsonUtils.toJSON(matchResultDTO));

        String s3 = "(sdfsdf";
        if(s3.indexOf("(") == 0){
            System.out.println("111 ");
        }else {
            System.out.println("222 ");
        }
//        String a=  "1001|1002";
//        String[] split = a.split("\\|");
//        System.out.println("split = " + Arrays.toString(split));
    }
}
