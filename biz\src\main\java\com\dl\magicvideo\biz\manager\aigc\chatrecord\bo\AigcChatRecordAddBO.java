package com.dl.magicvideo.biz.manager.aigc.chatrecord.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 16:21
 */
@Data
public class AigcChatRecordAddBO {

    private Long userId;

    private String tenantCode;

    /**
     * aigc聊天记录类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum
     */
    private Integer type;

    /**
     * 聊天内容
     * 当内容类型是1:文本时，存的是字符串
     * 当内容类型是2~7:文件时，存的是AigcChatRecordContentFileBO对应的json串
     * 当内容类型是8:视频合成成功时，存的是AigcChatRecordProduceSuccessBO对应的json串
     * 当内容类型是9:视频合成失败时，存的是AigcChatRecordProduceFailBO对应的json串
     * 当内容类型是10:视频合成中时，存的是AigcChatRecordProduceIngBO对应的json串
     * 当内容类型是11:热点事件题材提问时，存的是AigcChatRecordHotEventSubjectMatterAskBO对应的json串
     * 当内容类型是12:热点事件题材回答时，存的是AigcChatRecordHotEventSubjectMatterAnswerBO对应的json串
     */
    private String content;

    /**
     * 聊天内容类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum
     */
    private Integer contentType;

    /**
     * 消息发送时间
     */
    private Date sendDt;

    /**
     * 是否能生产视频 0-否，1-是 默认为0
     */
    private Integer canProduce = 0;

    /**
     * 聊天记录的来源是哪个工具
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum
     */
    private Integer fromTool;

}
