package com.dl.magicvideo.web.controllers.aigc.chat;

import cn.easyes.core.biz.EsPageInfo;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.aiservice.share.aichat.consts.AiChatKimiConst;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.CommonCode;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.PlaceHolderUtils;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.po.EsIndexAigcChatRecord;
import com.dl.magicvideo.biz.manager.aigc.chat.AigcChatManager;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AiFileContentAndTitleRespBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcMultiChatMessageBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcMultiChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatResponseBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.AigcChatRecordManager;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordHotEventSubjectMatterAnswerBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordHotEventSubjectMatterAskBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordHotEventSubjectMatterAskContentBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordSearchBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordUpdateBO;
import com.dl.magicvideo.biz.manager.aigc.properties.AigcPropertites;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMatterManager;
import com.dl.magicvideo.biz.manager.subjectmatter.dto.SubjectTreeDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.aigc.chat.convert.AiChatConvert;
import com.dl.magicvideo.web.controllers.aigc.chat.convert.AigcChatRecordConvert;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.enums.AigcScriptStyleEnum;
import com.dl.magicvideo.web.controllers.aigc.chat.param.*;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AiFileContentAndTitleRespVO;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AigcChatRecordVO;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 14:33
 */
@Component
public class AigcChatProcess extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AigcChatProcess.class);

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private AigcChatRecordManager aigcChatRecordManager;

    @Resource
    private AigcChatManager aigcChatManager;

    @Resource
    private SubjectMatterManager subjectMatterManager;

    @Resource
    private AigcPropertites aigcPropertites;

    @Value("${visual.fileTempPath}")
    public String localPathPrefix;

    /**
     * aigc单条对话预设文本
     */
    @Value("${dl.aigc.single-chat.presuppose-text}")
    private String aigcSingleChatPresupposeText;

    /**
     * aigc提取文件标题的预设文本
     */
    @Value("${dl.aigc.extract-file.contentAndTitle.presuppose-text}")
    private String aigcExtractFileContentAndTitlePresupposeText;

    public ResultPageModel<AigcChatRecordVO> pageChatRecord(AigcChatRecordPageParam param) {
        AigcChatRecordSearchBO searchBO = new AigcChatRecordSearchBO();
        searchBO.setAfterRecordId(param.getAfterRecordId());
        searchBO.setAfterSendDt(param.getAfterSendDt());
        searchBO.setTenantCode(operatorUtil.getTenantCode());
        searchBO.setUserId(operatorUtil.getOperator());
        searchBO.setPageIndex(param.getPageIndex());
        searchBO.setPageSize(param.getPageSize());
        EsPageInfo<EsIndexAigcChatRecord> esPageInfo = aigcChatRecordManager.pageSearch(searchBO);

        ResultPageModel<AigcChatRecordVO> resultPageModel = new ResultPageModel<>();
        resultPageModel.setTotalPage((long) esPageInfo.getPages());
        resultPageModel.setPageSize((long) esPageInfo.getPageSize());
        resultPageModel.setPageIndex((long) esPageInfo.getPageNum());
        resultPageModel.setTotal(esPageInfo.getTotal());
        resultPageModel.setDataResult(
                esPageInfo.getList().stream().map(AigcChatRecordConvert::cnvEsIndexAigcChatRecord2AigcChatRecordVO)
                        .collect(Collectors.toList()));
        return resultPageModel;
    }

    public ResultModel<AigcChatRecordVO> askAndSyncAnswer(List<AigcChatSendMessageParam> params) {
        LOGGER.info("调用[提问并同步返回ai回答]接口入参:{}", JSONUtil.toJsonStr(params));
        //1.保存消息到聊天记录
        List<AigcChatRecordAddBO> sendRecordBOList = AigcChatRecordConvert
                .buildAigcChatRecordAddBOList(params, operatorUtil.getOperator(), operatorUtil.getTenantCode(),
                        AigcChatRecordTypeEnum.SEND.getType(), AigcChatToolEnum.COMMON_TOOL.getCode());
        aigcChatRecordManager.batchAdd(sendRecordBOList);

        //2.调用ai对话服务传入用户消息，接口要返回ai的响应结果
        AigcSingleChatRequestBO singleChatRequestBO = AiChatConvert
                .buildAigcSingleChatRequestBO(aigcPropertites.getAigcSingleChatPresupposeText(), params,
                        operatorUtil.getOperator());
        AigcSingleChatResponseBO singleChatResponseBO = aigcChatManager
                .singleChat(operatorUtil.getTenantCode(), singleChatRequestBO);

        //3.保存ai的响应结果
        AigcChatRecordAddBO receiveRecordBO = new AigcChatRecordAddBO();
        receiveRecordBO.setSendDt(new Date());
        receiveRecordBO.setUserId(operatorUtil.getOperator());
        receiveRecordBO.setTenantCode(operatorUtil.getTenantCode());
        receiveRecordBO.setContent(singleChatResponseBO.getContent());
        receiveRecordBO.setContentType(AigcChatRecordContentTypeEnum.TEXT.getType());
        receiveRecordBO.setType(AigcChatRecordTypeEnum.RECEIVE.getType());
        receiveRecordBO.setFromTool(AigcChatToolEnum.COMMON_TOOL.getCode());
        receiveRecordBO.setCanProduce(Const.ONE);
        LOGGER.info("准备将ai返回的内容插入聊天记录表，receiveRecordBO:{}", JSONUtil.toJsonStr(receiveRecordBO));
        EsIndexAigcChatRecord receiveRecord = aigcChatRecordManager.add(receiveRecordBO);

        return ResultModel.success(AigcChatRecordConvert.cnvEsIndexAigcChatRecord2AigcChatRecordVO(receiveRecord));
    }

    public void askAndStreamAnswer(HttpServletResponse resp, List<AigcChatSendMessageParam> params)
            throws IOException {
        LOGGER.info("调用[提问并流式返回ai回答]接口入参:{}", JSONUtil.toJsonStr(params));
        OutputStream out = resp.getOutputStream();
        // 设置响应类型为流式
        this.setRespEventStream(resp);

        //1.保存消息到聊天记录
        List<AigcChatRecordAddBO> sendRecordBOList = AigcChatRecordConvert
                .buildAigcChatRecordAddBOList(params, operatorUtil.getOperator(), operatorUtil.getTenantCode(),
                        AigcChatRecordTypeEnum.SEND.getType(), AigcChatToolEnum.COMMON_TOOL.getCode());
        aigcChatRecordManager.batchAdd(sendRecordBOList);

        //2.调用ai对话服务传入用户消息
        AigcSingleChatRequestBO singleChatRequestBO = AiChatConvert
                .buildAigcSingleChatRequestBO(aigcPropertites.getAigcSingleChatPresupposeText(), params,
                        operatorUtil.getOperator());
        StringBuffer answerSbf = new StringBuffer();
        boolean hasError = false;

        try (InputStream inputStream = aigcChatManager
                .singleChatStream(operatorUtil.getTenantCode(), singleChatRequestBO);
                BufferedReader bufferedReader = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            while (true) {
                String line = bufferedReader.readLine();
                if (line == null) {
                    break;
                }

                // 忽略空白行
                if (StrUtil.isBlank(line)) {
                    continue;
                }

                //替换每行前的'data: '
                line = StrUtil.replace(line, "data: ", StrUtil.EMPTY);

                if (JSONUtil.isTypeJSON(line)) {
                    JSONObject jsonObject = JSONUtil.parseObj(line);
                    String event = (String) jsonObject.get("event");
                    if ("cmpl".equals(event)) {
                        answerSbf.append((String) jsonObject.get("text"));
                        this.writeSseEvent(out, line);
                    }
                    if ("error".equals(event)) {
                        answerSbf.append((String) jsonObject.get("text"));
                        hasError = true;
                        this.wrapperErrorWriteSseEvent(out, (String) jsonObject.get("text"));
                    }
                } else {
                    LOGGER.warn("该值非json字符串，打印出来！:{}", line);
                }
            }

        } catch (IOException e) {
            LOGGER.error("读取或写入流时发生异常", e);
            answerSbf.append("服务器开小差了，请稍后再试");
            hasError = true;
            this.wrapperErrorWriteSseEvent(out, "服务器开小差了，请稍后再试");
        }

        //4.保存ai的响应结果
        AigcChatRecordAddBO receiveRecordBO = new AigcChatRecordAddBO();
        receiveRecordBO.setSendDt(new Date());
        receiveRecordBO.setCanProduce(hasError ? Const.ZERO : Const.ONE);
        receiveRecordBO.setUserId(operatorUtil.getOperator());
        receiveRecordBO.setTenantCode(operatorUtil.getTenantCode());
        receiveRecordBO.setContent(answerSbf.toString());
        receiveRecordBO.setContentType(AigcChatRecordContentTypeEnum.TEXT.getType());
        receiveRecordBO.setFromTool(AigcChatToolEnum.COMMON_TOOL.getCode());
        receiveRecordBO
                .setType(hasError ? AigcChatRecordTypeEnum.SYSTEM.getType() : AigcChatRecordTypeEnum.RECEIVE.getType());
        LOGGER.info("准备将ai返回的内容插入聊天记录表，receiveRecordBO:{}", JSONUtil.toJsonStr(receiveRecordBO));
        EsIndexAigcChatRecord receiveRecord = aigcChatRecordManager.add(receiveRecordBO);

        //将记录id写入流
        this.wrapperRecordIdWriteSseEvent(out, receiveRecord.getRecordId());

    }

    public void hotEventSubjectMatter(HttpServletResponse resp, AigcChatSendMessageParam param, Integer style) throws IOException {
        LOGGER.info("调用[热点事件题材对话]接口入参:{}", JSONUtil.toJsonStr(param));

        //反序列化请求参数中的提问对象
        AigcChatRecordHotEventSubjectMatterAskBO askBO = JSONUtil
                .toBean(param.getContent(), AigcChatRecordHotEventSubjectMatterAskBO.class);
        Assert.notNull(askBO, "提问对象不能为空");
        Assert.notNull(askBO.getSubjectMatterBizId(), "题材bizId不能为空");
        Assert.notNull(askBO.getAskContent(),"提问内容不能为空");
        Assert.isTrue(StringUtils.isNotBlank(askBO.getAskContent().getHotEventDesc()),"热点事件描述不能为空");
        Assert.isTrue(StringUtils.isNotBlank(askBO.getAskContent().getEventDriveLogic()),"事件驱动逻辑不能为空");

        OutputStream out = resp.getOutputStream();
        // 设置响应类型为流式
        this.setRespEventStream(resp);

        //1.保存消息到聊天记录
        AigcChatRecordAddBO sendRecordBO = AigcChatRecordConvert
                .buildAigcChatRecordAddBO(operatorUtil.getOperator(), operatorUtil.getTenantCode(),
                        AigcChatRecordTypeEnum.SEND.getType(), new Date(), param,
                        AigcChatToolEnum.HOT_EVENT_SUBJECT_MATTER_TOOL.getCode());
        aigcChatRecordManager.add(sendRecordBO);

        //题材bizId
        Long subjectMatterBizId = askBO.getSubjectMatterBizId();

        //2.查询题材树以及其配置的prompt
        SubjectMatterPO subjectMatterPO = subjectMatterManager
                .getOne(Wrappers.lambdaQuery(SubjectMatterPO.class).eq(SubjectMatterPO::getBizId, subjectMatterBizId)
                        .eq(SubjectMatterPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(subjectMatterPO)) {
            LOGGER.error("未查询到该题材!subjectMatterBizId:{}", subjectMatterBizId);
            this.wrapperErrorWriteSseEvent(out, "未查询到该题材");
            return;
        }

        //3.匹配题材树
        SubjectTreeDTO subjectTreeDTO = subjectMatterManager
                .getTreeByText(askBO.getSubjectMatterBizId(), askBO.getAskContent().getEventDriveLogic());
        if (Objects.isNull(subjectTreeDTO)) {
            LOGGER.error("未匹配上题材树!subjectMatterBizId:{},,,eventDriveLogic:{}", subjectMatterBizId,
                    askBO.getAskContent().getEventDriveLogic());
            this.wrapperErrorWriteSseEvent(out, "未匹配上题材树，请检查[事件驱动逻辑]");
            return;
        }

        //4.构建入参AiMultiChatRequestDTO
        AigcMultiChatRequestBO requestBO = this
                .buildHotEventAigcMultiChatRequestBO(askBO, subjectTreeDTO, subjectMatterPO, style);

        StringBuffer answerSbf = new StringBuffer();
        boolean hasError = false;

        //5.调用ai对话服务传入用户消息
        try (InputStream inputStream = aigcChatManager.multiChatFinalStream(operatorUtil.getTenantCode(), requestBO);
                BufferedReader bufferedReader = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            while (true) {
                String line = bufferedReader.readLine();
                if (line == null) {
                    break;
                }

                // 忽略空白行
                if (StrUtil.isBlank(line)) {
                    continue;
                }

                //替换每行前的'data: '
                line = StrUtil.replace(line, "data: ", StrUtil.EMPTY);

                if (JSONUtil.isTypeJSON(line)) {
                    JSONObject jsonObject = JSONUtil.parseObj(line);
                    String event = (String) jsonObject.get("event");
                    if ("cmpl".equals(event)) {
                        answerSbf.append((String) jsonObject.get("text"));
                        this.writeSseEvent(out, line);
                    }
                    if ("error".equals(event)) {
                        answerSbf.append((String) jsonObject.get("text"));
                        hasError = true;
                        this.wrapperErrorWriteSseEvent(out, (String) jsonObject.get("text"));
                    }
                } else {
                    LOGGER.warn("该值非json字符串，打印出来！:{}", line);
                }
            }

        } catch (IOException e) {
            LOGGER.error("读取或写入流时发生异常", e);
            answerSbf.append("服务器开小差了，请稍后再试");
            hasError = true;
            this.wrapperErrorWriteSseEvent(out, "服务器开小差了，请稍后再试");
        }

        //4.保存ai的响应结果
        AigcChatRecordHotEventSubjectMatterAnswerBO answerBO = new AigcChatRecordHotEventSubjectMatterAnswerBO();
        answerBO.setAnswerText(answerSbf.toString());
        answerBO.setMatchedSubjectMatterTree(subjectTreeDTO);
        answerBO.setSubjectMatterBizId(subjectMatterBizId);
        answerBO.setEventTitle(this.extractEventTitle(askBO.getAskContent().getEventDriveLogic()));

        AigcChatRecordAddBO receiveRecordBO = new AigcChatRecordAddBO();
        receiveRecordBO.setSendDt(new Date());
        receiveRecordBO.setCanProduce(hasError ? Const.ZERO : Const.ONE);
        receiveRecordBO.setUserId(operatorUtil.getOperator());
        receiveRecordBO.setTenantCode(operatorUtil.getTenantCode());
        receiveRecordBO.setContent(JSONUtil.toJsonStr(answerBO));
        receiveRecordBO.setContentType(AigcChatRecordContentTypeEnum.HOT_EVENT_SUBJECT_MATTER_ANSWER.getType());
        receiveRecordBO.setFromTool(AigcChatToolEnum.HOT_EVENT_SUBJECT_MATTER_TOOL.getCode());
        receiveRecordBO
                .setType(hasError ? AigcChatRecordTypeEnum.SYSTEM.getType() : AigcChatRecordTypeEnum.RECEIVE.getType());
        LOGGER.info("准备将ai返回的内容插入聊天记录表，receiveRecordBO:{}", JSONUtil.toJsonStr(receiveRecordBO));
        LOGGER.info("answerSbf.toString():{}", answerSbf.toString());
        EsIndexAigcChatRecord receiveRecord = aigcChatRecordManager.add(receiveRecordBO);

        //将subjectMatterId写入流
        this.wrapperSubjectMatterBizIdWriteSseEvent(out, subjectMatterBizId);

        //将记录id写入流
        this.wrapperRecordIdWriteSseEvent(out, receiveRecord.getRecordId());
    }

    public ResultModel<Void> updateContent(AigcChatUpdateContentParam param) {
        //查询已有记录
        EsIndexAigcChatRecord existRecord = aigcChatRecordManager.getOne(param.getRecordId());
        if (Objects.isNull(existRecord)) {
            throw BusinessServiceException.getInstance("聊天记录不存在或已删除");
        }
        if (!Objects.equals(operatorUtil.getOperator(), existRecord.getUserId())) {
            throw BusinessServiceException.getInstance(CommonCode.INVALID_AUTH.getMessage());
        }

        //更新记录
        AigcChatRecordUpdateBO updateBO = new AigcChatRecordUpdateBO();
        updateBO.setRecordId(param.getRecordId());
        updateBO.setContent(param.getContent());
        aigcChatRecordManager.updateContent(updateBO);

        return ResultModel.success(null);
    }

    public ResultModel<AiFileContentAndTitleRespVO> extractFileContentAndTitle(MultipartFile multipartFile) {
        // 创建一个 File 对象，指定文件的保存路径
        File file = null;
        String filePath = localPathPrefix + System.currentTimeMillis() + "-" + multipartFile.getOriginalFilename();
        try {
            file = new File(filePath);
            multipartFile.transferTo(file);
        } catch (Exception e) {
            LOGGER.error("文件转换发生异常！fileName:{},,,,filePath:{}", multipartFile.getOriginalFilename(), filePath);
            FileUtils.deleteQuietly(file);
            throw BusinessServiceException.getInstance("服务器开小差了，请稍后再试");
        }

        AiFileContentAndTitleRespBO respBO = null;
        try {
            respBO = aigcChatManager
                    .extractFileContentAndTitle(operatorUtil.getTenantCode(), operatorUtil.getOperator(),
                            aigcPropertites.getAigcExtractFileContentAndTitlePresupposeText(), file);
        } finally {
            FileUtils.deleteQuietly(file);
        }

        AiFileContentAndTitleRespVO vo = new AiFileContentAndTitleRespVO();
        vo.setTitle(respBO.getTitle());
        vo.setContent(respBO.getContent());
        return ResultModel.success(vo);
    }

    public ResultModel<AigcChatRecordVO> addRecord(AigcAddChatRecordParam param) {
        //保存消息到聊天记录
        AigcChatRecordAddBO sendRecordBO = AigcChatRecordConvert
                .cnvAigcAddChatRecordParam2BO(param, operatorUtil.getOperator(), operatorUtil.getTenantCode(),
                        param.getFromTool());
        EsIndexAigcChatRecord record = aigcChatRecordManager.add(sendRecordBO);

        return ResultModel.success(AigcChatRecordConvert.cnvEsIndexAigcChatRecord2AigcChatRecordVO(record));
    }

    public ResultModel<AigcChatRecordVO> recordInfo(AigcChatRecordQueryInfoParam param) {
        EsIndexAigcChatRecord record = aigcChatRecordManager.getOne(param.getRecordId());
        if (Objects.isNull(record)) {
            return null;
        }
        if (!operatorUtil.getOperator().equals(record.getUserId())) {
            return ResultModel.error("-1", "该记录并非您所有，无权查看");
        }

        return ResultModel.success(AigcChatRecordConvert.cnvEsIndexAigcChatRecord2AigcChatRecordVO(record));
    }

    public void hotEventStyleRewrite(HttpServletResponse resp, AigcChatHotEventStyleRewriteParam param)
            throws IOException {
        Assert.notNull(param.getStyle(), "风格不能为空");
        Assert.notNull(param.getRecordId(), "聊天记录id不能为空");

        //1.查询聊天记录
        EsIndexAigcChatRecord existRecord = aigcChatRecordManager.getOne(param.getRecordId());
        if (Objects.isNull(existRecord)) {
            throw BusinessServiceException.getInstance("聊天记录不存在或已删除");
        }
        if (!Objects.equals(operatorUtil.getOperator(), existRecord.getUserId())) {
            throw BusinessServiceException.getInstance(CommonCode.INVALID_AUTH.getMessage());
        }

        OutputStream out = resp.getOutputStream();
        this.setRespEventStream(resp);

        AigcChatRecordHotEventSubjectMatterAnswerBO answerBO = JSONUtil
                .toBean(existRecord.getContent(), AigcChatRecordHotEventSubjectMatterAnswerBO.class);

        //通用短视频风格，无需请求ai
        if(AigcScriptStyleEnum.GENERAL_SHORT_VIDEO.getStyle().equals(param.getStyle())){
            JSONObject object = new JSONObject();
            object.put("event", "cmpl");
            object.put("text", answerBO.getAnswerText());
            this.writeSseEvent(out, object.toString());
            //修改聊天记录ai的响应结果
            answerBO.setStyle(param.getStyle());
            answerBO.setStyleRewritenAnswerText(answerBO.getAnswerText());
            AigcChatRecordUpdateBO updateBO = new AigcChatRecordUpdateBO();
            updateBO.setContent(JSONUtil.toJsonStr(answerBO));
            updateBO.setRecordId(param.getRecordId());
            aigcChatRecordManager.updateContent(updateBO);

            //将记录id写入流
            this.wrapperRecordIdWriteSseEvent(out, param.getRecordId());
            return;
        }

        //2.构建请求参数
        AigcMultiChatRequestBO requestBO = new AigcMultiChatRequestBO();
        requestBO.setUserId(operatorUtil.getOperator());
        requestBO.setModel(AiChatKimiConst.MOONSHOT_8K);
        requestBO.setRespMaxToken(Const.ONE_FIVE_ZERO_ZERO);
        List<AigcMultiChatMessageBO> messages = new ArrayList<>();
        requestBO.setMessages(messages);
        //训练文案
        String trainText = aigcPropertites.getAigcHotEventDouyinStyleRewriteTrainText();
        //训练消息
        AigcMultiChatMessageBO presupposseMsgBO = new AigcMultiChatMessageBO(trainText);
        messages.add(presupposseMsgBO);
        //原始文案的消息
        AigcMultiChatMessageBO originalTextMsgBO = new AigcMultiChatMessageBO(answerBO.getAnswerText());
        messages.add(originalTextMsgBO);

        StringBuffer answerSbf = new StringBuffer();
        boolean hasError = false;

        //3.调用ai对话服务传入用户消息
        try (InputStream inputStream = aigcChatManager.multiChatFinalStream(operatorUtil.getTenantCode(), requestBO);
                BufferedReader bufferedReader = new BufferedReader(
                        new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            while (true) {
                String line = bufferedReader.readLine();
                if (line == null) {
                    break;
                }

                // 忽略空白行
                if (StrUtil.isBlank(line)) {
                    continue;
                }

                //替换每行前的'data: '
                line = StrUtil.replace(line, "data: ", StrUtil.EMPTY);

                if (JSONUtil.isTypeJSON(line)) {
                    JSONObject jsonObject = JSONUtil.parseObj(line);
                    String event = (String) jsonObject.get("event");
                    if ("cmpl".equals(event)) {
                        answerSbf.append((String) jsonObject.get("text"));
                        this.writeSseEvent(out, line);
                    }
                    if ("error".equals(event)) {
                        answerSbf.append((String) jsonObject.get("text"));
                        hasError = true;
                        this.wrapperErrorWriteSseEvent(out, (String) jsonObject.get("text"));
                    }
                } else {
                    LOGGER.warn("该值非json字符串，打印出来！:{}", line);
                }
            }

        } catch (IOException e) {
            LOGGER.error("读取或写入流时发生异常", e);
            answerSbf.append("服务器开小差了，请稍后再试");
            hasError = true;
            this.wrapperErrorWriteSseEvent(out, "服务器开小差了，请稍后再试");
        }

        if (hasError) {
            LOGGER.error("发生错误");
            return;
        }

        //4.修改聊天记录ai的响应结果
        answerBO.setStyle(param.getStyle());
        answerBO.setStyleRewritenAnswerText(answerSbf.toString());
        AigcChatRecordUpdateBO updateBO = new AigcChatRecordUpdateBO();
        updateBO.setContent(JSONUtil.toJsonStr(answerBO));
        updateBO.setRecordId(param.getRecordId());
        aigcChatRecordManager.updateContent(updateBO);

        LOGGER.info("answerSbf.toString():{}", answerSbf.toString());

        //将记录id写入流
        this.wrapperRecordIdWriteSseEvent(out, param.getRecordId());
    }

    private AigcMultiChatRequestBO buildHotEventAigcMultiChatRequestBO(AigcChatRecordHotEventSubjectMatterAskBO askBO,
            SubjectTreeDTO subjectTreeDTO, SubjectMatterPO subjectMatterPO, Integer style) {
        AigcMultiChatRequestBO requestBO = new AigcMultiChatRequestBO();
        requestBO.setUserId(operatorUtil.getOperator());
        requestBO.setModel(AiChatKimiConst.MOONSHOT_8K);
        requestBO.setRespMaxToken(Const.ONE_FIVE_ZERO_ZERO);
        List<AigcMultiChatMessageBO> messages = new ArrayList<>();
        requestBO.setMessages(messages);

        String subjectMatterPrompt = subjectMatterPO.getPrompt();
        Properties properties = new Properties();
        properties.put(AigcPropertites.SUBJECT_MATTER_PROMPT, subjectMatterPrompt);
        String wholePresupposeText = StringUtils.EMPTY;
        if (AigcScriptStyleEnum.GENERAL_SHORT_VIDEO.getStyle().equals(style)){
            //完整预设文案
            wholePresupposeText = PlaceHolderUtils
                    .resolveValue(aigcPropertites.getAigcHotEventSubjectMatterPresupposeText(), properties);
        } else {
            wholePresupposeText = PlaceHolderUtils
                    .resolveValue(aigcPropertites.getAigcHotEventDouyinStyleRewriteTrainText(), properties);
        }

        //预设消息
        AigcMultiChatMessageBO presupposseMsgBO = new AigcMultiChatMessageBO(wholePresupposeText);
        messages.add(presupposseMsgBO);
        //用户消息
        String userMsgText = this.buildHotEventSubjectMatterUserMsgText(askBO.getAskContent(), subjectTreeDTO);
        AigcMultiChatMessageBO userMsgBO = new AigcMultiChatMessageBO(userMsgText);
        messages.add(userMsgBO);
        return requestBO;
    }

    /**
     * 设置响应类型为流式
     *
     * @param resp
     */
    private void setRespEventStream(HttpServletResponse resp) {
        // 设置响应类型为流式
        resp.setContentType("text/event-stream");
        resp.setCharacterEncoding("UTF-8");
        resp.setHeader("Transfer-Encoding", "chunked");
        resp.setHeader("X-Accel-Buffering", "no");
    }

    /**
     * 包装chatRecordId并写入SSE事件
     *
     * @param out
     * @param chatRecordId
     */
    private void wrapperRecordIdWriteSseEvent(OutputStream out, Long chatRecordId) {
        JSONObject object = new JSONObject();
        object.put("event", "chat_record_id");
        object.put("text", String.valueOf(chatRecordId));
        writeSseEvent(out, object.toString());
    }

    /**
     * 包装subjectMatterBizId并写入SSE事件
     *
     * @param out
     * @param subjectMatterBizId
     */
    private void wrapperSubjectMatterBizIdWriteSseEvent(OutputStream out, Long subjectMatterBizId) {
        JSONObject object = new JSONObject();
        object.put("event", "subject_matter_biz_id");
        object.put("text", String.valueOf(subjectMatterBizId));
        writeSseEvent(out, object.toString());
    }

    /**
     * 包装错误并写入SSE事件
     *
     * @param out
     * @param errMsg
     */
    private void wrapperErrorWriteSseEvent(OutputStream out, String errMsg) {
        JSONObject object = new JSONObject();
        object.put("event", "error");
        object.put("text", errMsg);
        writeSseEvent(out, object.toString());
    }

    /**
     * 写入SSE事件
     * <p>
     * 内容结构体：
     * {
     * "text": "",
     * "event": ""
     * }
     * <p>
     * event值：
     * cmpl -表示ai回答   text为回答的内容
     * error -表示错误   text为错误信息
     * chat_record_id - 表示聊天记录id  text为聊天记录id
     * all_done - 表示全部ai回答完成  无text
     */
    private void writeSseEvent(OutputStream out, String data) {
        if (StringUtils.isBlank(data)) {
            LOGGER.info("data为空，不写入流");
            return;
        }
        try {
            out.write("data: ".getBytes(StandardCharsets.UTF_8));
            out.write(data.getBytes(StandardCharsets.UTF_8));
            out.write("\n".getBytes(StandardCharsets.UTF_8));
            out.flush();
        } catch (IOException e) {
            LOGGER.error("写入sse事件流，发生异常！，data:{},,,e:", data, e);
        }
    }

    // 安静关闭输出流
    private void closeQuietly(Closeable closeable) {
        try {
            if (closeable != null) {
                closeable.close();
            }
        } catch (IOException e) {
            LOGGER.error("关闭资源时发生异常", e);
        }
    }

    /**
     * 构建热点事件题材的用户消息文本
     *
     * @param askContent
     * @return
     */
    private String buildHotEventSubjectMatterUserMsgText(AigcChatRecordHotEventSubjectMatterAskContentBO askContent,
            SubjectTreeDTO subjectTreeDTO) {
        Properties properties = new Properties();
        properties.put(AigcPropertites.HOT_EVENT_DESC, askContent.getHotEventDesc());
        properties.put(AigcPropertites.REL_SUBJECT_MATTER_TREE, this.buildSubjectMatterTreeStr(subjectTreeDTO));
        properties.put(AigcPropertites.EVENT_DRIVE_LOGIC, askContent.getEventDriveLogic());
        //完整预设文案
        String wholePresupposeText = PlaceHolderUtils
                .resolveValue(aigcPropertites.getAigcHotEventSubjectMatterUserMsgPresupposeText(), properties);
        LOGGER.info("热点事件题材的用户消息文本:{}", wholePresupposeText);
        return wholePresupposeText;

        /*StringBuffer userMsgText = new StringBuffer();
        userMsgText.append("基于上面你学习的创作方法，为我创作一篇口播文案，基于下面提供的信息创作。\n");
        userMsgText.append("热点事件:\n").append(askContent.getHotEventDesc()).append("\n");
        userMsgText.append("关联产业链：").append(this.buildSubjectMatterTreeStr(subjectTreeDTO));
        userMsgText.append("事件驱动逻辑:\n").append(askContent.getEventDriveLogic()).append("\n");
        userMsgText.append("注意：\n 1.文案表达的顺序，要严格按照 信息中的 事件驱动逻辑 章节中 表述的顺序。\n");
        userMsgText.append("2.产业链每个环节介绍的开头需要加上一句过度转折的话术，让表达连贯顺畅。\n");

        return userMsgText.toString();*/
    }

    /**
     * 构建题材树字符串
     * <p>
     * 示例：
     * 家电产业链
     * 上游（原材料、零部件）：
     * - 原材料：金属材料、塑料
     * - 核心零部件：压缩机、电机、芯片、传感器、电子膨胀阀
     * 中游（家电制造）：
     * - 白电：空调、冰箱、洗衣机
     * - 黑电：电视、音箱
     * - 厨电：电饭煲、吸油烟机、燃气灶
     * - 小家电：吸尘器、扫地机器人、电烤箱（小家电）
     * 下游（销售渠道）：
     * - 线上渠道：电商平台、品牌官网
     * - 线下渠道：超市品牌专卖店
     *
     * @param subjectTreeDTO
     * @return
     */
    private String buildSubjectMatterTreeStr(SubjectTreeDTO subjectTreeDTO) {
        StringBuffer sbf = new StringBuffer();
        sbf.append(subjectTreeDTO.getName().replaceAll("\n", "").replaceAll("\r", "").replaceAll("\t", ""))
                .append("\n");
        for (SubjectTreeDTO level2 : subjectTreeDTO.getChildren()) {
            sbf.append(level2.getName().replaceAll("\n", "").replaceAll("\r", "").replaceAll("\t", "")).append("：\n");
            for (SubjectTreeDTO level3 : level2.getChildren()) {
                sbf.append("- ")
                        .append(level3.getName().replaceAll("\n", "").replaceAll("\r", "").replaceAll("\t", ""));
                if (CollectionUtils.isEmpty(level3.getChildren())) {
                    sbf.append("\n");
                    continue;
                }
                sbf.append("：");
                for (int i = 0; i < level3.getChildren().size(); i++) {
                    sbf.append(level3.getChildren().get(i).getName().replaceAll("\n", "").replaceAll("\r", "")
                            .replaceAll("\t", ""));
                    if (i != level3.getChildren().size() - 1) {
                        sbf.append("、");
                    } else {
                        sbf.append("\n");
                    }
                }
            }
        }
        return sbf.toString();
    }

    /**
     * 提取事件标题
     * 事件例子 "消费品以旧换新(事件)一\n下游:线上渠道--电商平台 补贴增加 (直接利好)、线上渠道--品牌官网 优惠加大(直接利好)、线下渠道--超市 活动增多(直接利好)、线下渠道--品牌专卖店 促销增大(直接利好)\n中游:白电 产品升级(直接利好)、黑电 库存优化(直接利好)、厨电 智能转型(直接利好)、小家电 需求增加(直接利好)\n上游:原材料--金属材料 需求增加(间接利好)、原材料--塑料 技术升级(间接利好)、核心零部件--压缩机 小型高效需求(间接利好)、核心零部件--电机 需求增加(间接利好)、核心零部件--芯片 技术升级驱动(间接利好)、核心零部件--传感器 需求增加(间接利好)"
     *
     * @param eventDriveLogic
     * @return
     */
    private String extractEventTitle(String eventDriveLogic) {
        String[] split1 = eventDriveLogic.split("\n");
        String[] test1 = split1[0].split("[（(]");
        LOGGER.info("提取到的事件标题:{}", test1[0]);
        return test1[0];
    }

    public static void main(String[] args) {
        List<AigcChatSendMessageParam> params = new ArrayList<>();
        AigcChatSendMessageParam param = new AigcChatSendMessageParam();
        param.setContentType(11);
        param.setCanProduce(0);

        AigcChatRecordHotEventSubjectMatterAskBO askBO = new AigcChatRecordHotEventSubjectMatterAskBO();
        askBO.setSubjectMatterBizId(1163728422591019279L);

        AigcChatRecordHotEventSubjectMatterAskContentBO askContent = new AigcChatRecordHotEventSubjectMatterAskContentBO();
        askContent.setHotEventDesc(
                "世界黄金协会4月4日的报告显示，2月份全球央行黄金储备增加了19吨，这是连续第九个月的增长。此外，中国央行在3月再度增持了黄金4.98吨，这是自2022年11月以来连续第17个月的购金行为，显示出黄金作为货币属性的重要性在当前的国际金融环境中仍然被重视。\n"
                        + "央行增持黄金可增强国家货币信誉，保护经济免受通胀和全球不稳定的影响，同时减少对单一货币的依赖，提高经济的自主性和稳定性。");
        askContent.setEventDriveLogic("央行购金（事件）→\n" + "上游：地质勘探 加大勘探投资（直接利好）、开采 企业采矿积极性提高（直接利好）、矿石处理 黄金提取需求（直接利好）\n"
                + "下游：金币和金条 需求增多（直接利好）、黄金交易所交易基金（ETF）投资旺盛（直接利好）、黄金衍生品和期货 投机需求（直接利好）\n" + "中游：冶炼 影响偏弱（中性）、精炼 影响较小（中性）");
        askBO.setAskContent(askContent);
        param.setContent(JSONUtil.toJsonStr(askBO));
        params.add(param);
        System.out.println(JSONUtil.toJsonStr(params));
    }

    public ResultModel<Void> delRecord(AigcChatRecordDelParam param) {
        EsIndexAigcChatRecord record = aigcChatRecordManager.getOne(param.getRecordId());
        if (Objects.isNull(record)) {
            return ResultModel.error("-1", "不存在该记录");
        }
        if (!operatorUtil.getOperator().equals(record.getUserId())) {
            return ResultModel.error("-1", "该记录并非您所有，无权查看");
        }
        record.setIsDeleted(Const.ONE);
        aigcChatRecordManager.deleteRecord(record);
        return ResultModel.success(null);
    }
}
