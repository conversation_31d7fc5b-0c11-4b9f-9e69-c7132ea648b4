package com.dl.magicvideo.openapi.controller.produce.vo;

import com.dl.magicvideo.biz.manager.visual.dto.CrossClipsDTO;
import com.dl.magicvideo.biz.manager.visual.dto.LightEditConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-16 19:21
 */
@Data
public class OpenVisualProduceJobDetailVO extends OpenVisualProduceJobInfoVO{

    @ApiModelProperty("数字人片段向上取整分钟总数")
    private Long dmTotalCeilingMinutes;

    @ApiModelProperty("数字人片段总时长，单位:毫秒")
    private Long dmTotalDuration;

    @ApiModelProperty("轻编辑配置")
    private List<LightEditConfigDTO> lightEditConfigs;

    @ApiModelProperty("跨片段组建")
    private List<CrossClipsDTO> crossClips;

    @ApiModelProperty("数字人名称")
    private String dmName;

    @ApiModelProperty("渠道名称")
    private String channelName;

    @ApiModelProperty("性别：1 男; 2 女")
    private Integer gender;

    @ApiModelProperty("数字人场景名称")
    private String sceneName;

    @ApiModelProperty(value = "场景id")
    private String sceneId;

    @ApiModelProperty(value = "失败原因")
    private String failReason;
}
