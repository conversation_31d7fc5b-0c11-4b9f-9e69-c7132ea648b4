package com.dl.magicvideo.biz.manager.font;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.font.po.PatternFontPO;
import com.dl.magicvideo.biz.manager.font.bo.PatternFontListBO;
import com.dl.magicvideo.biz.manager.font.bo.PatternFontPageBO;
import com.dl.magicvideo.biz.manager.font.dto.PatternFontDTO;

import java.util.List;

public interface PatternFontManager extends IService<PatternFontPO> {

    /**
     * 花字列表
     * @param bo
     * @return
     */
    List<PatternFontDTO> fontList(PatternFontListBO bo);

    /**
     * 花字详情
     * @param bizId
     * @return
     */
    PatternFontDTO detail(Long bizId);

    /**
     * 分页查询花字
     * @param bo
     * @return
     */
    IPage<PatternFontDTO> fontPage(PatternFontPageBO bo);
}
