package com.dl.magicvideo.openapi.controller.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class OpenDigitalManVO {

    @ApiModelProperty(value = "数字人形象代码")
    private String vmCode;

    @ApiModelProperty(value = "数字人名称")
    private String vmName;

    @ApiModelProperty(value = "数字人声音代码")
    private String vmVoiceKey;

    @ApiModelProperty(value = "性别：1 男; 2 女")
    private Integer gender;

    @ApiModelProperty(value = "渠道：1 硅基")
    private Integer channel;

    @ApiModelProperty(value = "数字人头像地址url")
    private String headImg;

    @ApiModelProperty(value = "数字人场景列表")
    private List<OpenDigitalManSceneVO> sceneList;
}