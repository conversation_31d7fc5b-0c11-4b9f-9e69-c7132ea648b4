package com.dl.magicvideo.biz.manager.statistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.dal.statistics.StatisticsEfficiencyMapper;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsEfficiencyPO;
import com.dl.magicvideo.biz.manager.statistics.StatisticsEfficiencyManager;
import com.dl.magicvideo.biz.manager.statistics.bo.EfficiencyBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsEfficiencyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【visual_ai_job】的数据库操作Service实现
 * @createDate 2023-06-08 16:23:52
 */
@Service
@Slf4j
public class StatisticsEfficiencyManagerImpl extends ServiceImpl<StatisticsEfficiencyMapper, StatisticsEfficiencyPO>
        implements StatisticsEfficiencyManager {

    @Override
    public ResponsePageQueryDO<List<StatisticsEfficiencyDTO>> pageQuery(EfficiencyBO pageQueryDO) {
        ResponsePageQueryDO<List<StatisticsEfficiencyDTO>> response = new ResponsePageQueryDO<>();
        LambdaQueryWrapper<StatisticsEfficiencyPO> queryWrapper = Wrappers.lambdaQuery(StatisticsEfficiencyPO.class);
        queryWrapper.ge(StatisticsEfficiencyPO::getStatisticsTime, pageQueryDO.getStartTime());
        queryWrapper.le(StatisticsEfficiencyPO::getStatisticsTime, pageQueryDO.getEndTime());
        queryWrapper.eq(StatisticsEfficiencyPO::getType, pageQueryDO.getType());
        queryWrapper.orderByDesc(StatisticsEfficiencyPO::getId);

        IPage<StatisticsEfficiencyPO> pageResult = baseMapper.selectPage(convert(pageQueryDO), queryWrapper);
        List<StatisticsEfficiencyPO> data = pageResult.getRecords();
        log.debug("StatisticsCountManager.baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        List<StatisticsEfficiencyDTO> dtos = new ArrayList<>(data.stream().map(po -> {
            StatisticsEfficiencyDTO dto = new StatisticsEfficiencyDTO();
            dto.setId(po.getId());
            dto.setStatisticsTime(po.getStatisticsTime());
            dto.setStatisticsValue(po.getStatisticsValue());
            return dto;
        }).collect(Collectors.toList()));
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        response.setDataResult(dtos);
        return response;
    }
}




