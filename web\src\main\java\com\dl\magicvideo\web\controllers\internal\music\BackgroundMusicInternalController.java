package com.dl.magicvideo.web.controllers.internal.music;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.music.po.BackgroundMusicPO;
import com.dl.magicvideo.biz.manager.cos.CosFileUploadManager;
import com.dl.magicvideo.biz.manager.cos.dto.MediaInfo;
import com.dl.magicvideo.biz.manager.music.BackgroundMusicManager;
import com.dl.magicvideo.biz.manager.music.dto.BackgroundMusicDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.music.convert.BackgroundMusicInternalConvert;
import com.dl.magicvideo.web.controllers.internal.music.param.BackgroundMusicPageParam;
import com.dl.magicvideo.web.controllers.internal.music.param.BackgroundMusicParam;
import com.dl.magicvideo.web.controllers.internal.music.vo.BackgroundMusicInternalVO;
import com.dl.magicvideo.web.controllers.material.process.MaterialProcess;
import com.qcloud.cos.model.ciModel.mediaInfo.MediaFormat;
import com.qcloud.cos.model.ciModel.mediaInfo.MediaInfoResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/visual/internal/background/music")
public class BackgroundMusicInternalController extends AbstractController {

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Resource
    private CosFileUploadManager cosFileUploadManager;

    @Autowired
    private BackgroundMusicManager backgroundMusicManager;

    @Autowired
    private MaterialProcess materialProcess;

    @Autowired
    private OperatorUtil operatorUtil;

    @PostMapping("/list")
    @ApiOperation("背景音列表")
    public ResultModel<List<BackgroundMusicInternalVO>> list(){
        List<BackgroundMusicDTO> backgroundMusicDTOList = backgroundMusicManager.backgroundMusicList(null);
        return ResultModel.success(backgroundMusicDTOList.stream().map(
                BackgroundMusicInternalConvert::cnvBackgroundMusicDTO2InternalVO).collect(Collectors.toList()));
    }

    @PostMapping("/add")
    @ApiOperation("新增背景音")
    public ResultModel<String> add(@RequestBody BackgroundMusicParam param){
        log.info("开始新增背景音乐 param = {}", JSONUtil.toJsonStr(param));
        operatorUtil.init(param.getUserId(), null,null,null);
        BackgroundMusicPO backgroundMusic = new BackgroundMusicPO();
        backgroundMusic.setName(param.getName());
        backgroundMusic.setType(param.getType());
        backgroundMusic.setUrl(param.getUrl());
        backgroundMusic.setBizId(hostTimeIdg.generateId().longValue());
        backgroundMusic.setDuration(getDuration(param.getUrl()));
        backgroundMusicManager.save(backgroundMusic);
        return ResultModel.success(backgroundMusic.getBizId().toString());
    }

    @PostMapping("/page")
    @ApiOperation("分页查询背景音")
    public ResultPageModel<BackgroundMusicInternalVO> page(@RequestBody BackgroundMusicPageParam param){
        log.info("开始查询背景音乐 param = {}", JSONUtil.toJsonStr(param));
        LambdaQueryWrapper<BackgroundMusicPO> wrapper = Wrappers.lambdaQuery(
                BackgroundMusicPO.class);
        wrapper.eq(Objects.nonNull(param.getType()),BackgroundMusicPO::getType,param.getType())
                .like(StringUtils.isNotBlank(param.getName()),BackgroundMusicPO::getName,param.getName())
                .eq(BackgroundMusicPO::getIsDeleted,Const.ZERO)
                .orderByDesc(BackgroundMusicPO::getCreateDt);

        Page<BackgroundMusicPO> musicPage = backgroundMusicManager.page(
                new Page<>(param.getPageIndex(), param.getPageSize()), wrapper);

        return pageQueryModel(musicPage,musicPage.getRecords().stream().map(BackgroundMusicInternalConvert::cnvBackgroundMusicPO2InternalVO).collect(
                Collectors.toList()));
    }

    @PostMapping("/update")
    @ApiOperation("修改背景音乐")
    public ResultModel<Boolean> update(@RequestBody BackgroundMusicParam param){
        log.info("开始修改背景音乐 param = {}", JSONUtil.toJsonStr(param));
        operatorUtil.init(param.getUserId(), null,null,null);
        BackgroundMusicPO po = backgroundMusicManager.getOne(
                Wrappers.lambdaQuery(BackgroundMusicPO.class).eq(BackgroundMusicPO::getBizId, param.getBizId()).eq(BackgroundMusicPO::getIsDeleted,
                        Const.ZERO));
        if (Objects.isNull(po)){
            return ResultModel.error("9999","未找到该音乐");
        }
        po.setType(param.getType());
        po.setName(param.getName());
        po.setUrl(param.getUrl());
        po.setDuration(getDuration(param.getUrl()));
        return ResultModel.success(backgroundMusicManager.updateById(po));
    }


    @PostMapping("/delete")
    @ApiOperation("删除背景音乐")
    public ResultModel<Boolean> del(@RequestBody BackgroundMusicParam param){
        log.info("开始查询删除音乐 param = {}", JSONUtil.toJsonStr(param));
        operatorUtil.init(param.getUserId(), null,null,null);
        BackgroundMusicPO po = backgroundMusicManager.getOne(
                Wrappers.lambdaQuery(BackgroundMusicPO.class).eq(BackgroundMusicPO::getBizId, param.getBizId()).eq(BackgroundMusicPO::getIsDeleted,
                        Const.ZERO));
        if (Objects.isNull(po)){
            return ResultModel.error("9999","未找到该音乐");
        }
        po.setIsDeleted(Const.ONE);
        return ResultModel.success(backgroundMusicManager.updateById(po));
    }

    public Long getDuration(String url){
        try {
            String decodeUrl = URLDecoder.decode(url, "UTF-8");
            String path = MaterialProcess.getCosPathFromUrlString(decodeUrl);
            log.info("path = {}",path);
            MediaInfo generateMediainfo = cosFileUploadManager.generateMediainfo(path);
            Long duration = null;
            //视频时长
            BigDecimal bigDecimal = BigDecimal.valueOf(generateMediainfo.getDuration());
            duration = (bigDecimal.longValue() * 1000);
            return duration;
        }catch (Exception e){
            throw BusinessServiceException.getInstance("获取音频时长失败");
        }
    }
}
