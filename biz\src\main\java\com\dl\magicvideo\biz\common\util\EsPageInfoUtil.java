package com.dl.magicvideo.biz.common.util;

import cn.easyes.core.biz.EsPageInfo;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2024/12/26 14:31
 */
public class EsPageInfoUtil {
    public static <T> EsPageInfo<T> fromSearchResponse(SearchResponse response, Class<T> clazz, int pageNumber, int pageSize) {
        EsPageInfo esPageInfo = new EsPageInfo();
        long totalHits = response.getHits().getTotalHits().value;
        List<T> results = new ArrayList<>();

        for (SearchHit hit : response.getHits().getHits()) {
            // Assuming you have a method to convert SearchHit to your desired type T
            T item = convertHitToObject(hit, clazz);
            results.add(item);
        }
        esPageInfo.setTotal(totalHits);
        esPageInfo.setList(results);
        esPageInfo.setPages(pageNumber);
        esPageInfo.setPageSize(pageSize);
        return esPageInfo;
    }

    private static <T> T convertHitToObject(SearchHit hit, Class<T> clazz) {
        ObjectMapper objectMapper = new ObjectMapper();
        // Implement your logic to convert SearchHit to an object of type T
        // For example, using Jackson or another JSON deserialization library
        // This is a placeholder example
        try {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            // Assuming a simple constructor or method to create T from Map
            // This would need to be replaced with actual deserialization logic
            return objectMapper.convertValue(sourceAsMap, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Error converting SearchHit to object", e);
        }
    }
}
