package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 组件版本详情(ComponentVersionDetail)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 20:23:33
 */
@TableName(value ="component_version_detail")
@Data
public class ComponentVersionDetailPO implements Serializable {
    private static final long serialVersionUID = -13929755300993875L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 业务id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long bizId;
    /**
     * 组件地址
     */
    private String url;
    /**
     * 创建人userId
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private Date createDt;
    /**
     * 修改人userId
     */
    private Long modifyBy;
    /**
     * 修改时间
     */
    private Date modifyDt;
    /**
     * 是否删除 0否 1是
     */
    private Integer isDeleted;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @Version
    private String version;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDt() {
        return createDt;
    }

    public void setCreateDt(Date createDt) {
        this.createDt = createDt;
    }

    public Long getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(Long modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyDt() {
        return modifyDt;
    }

    public void setModifyDt(Date modifyDt) {
        this.modifyDt = modifyDt;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

}

