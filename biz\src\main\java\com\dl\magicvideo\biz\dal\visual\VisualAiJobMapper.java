package com.dl.magicvideo.biz.dal.visual;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobDurationBO;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【visual_ai_job】的数据库操作Mapper
 * @createDate 2023-06-08 16:23:52
 * @Entity generator.domain.VisualAiJob
 */
@BaseDao
public interface VisualAiJobMapper extends BaseMapper<VisualAiJobPO> {

    Integer countExt(@Param("query") VisualAiJobPageBO query);

    List<VisualAiJobExtPO> pageExt(@Param("query") VisualAiJobPageBO query);

    Long countDuration(@Param("query") VisualAiJobDurationBO query);
}




