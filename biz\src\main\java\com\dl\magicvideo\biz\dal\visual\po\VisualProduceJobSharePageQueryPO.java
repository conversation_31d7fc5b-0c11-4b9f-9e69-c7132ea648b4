package com.dl.magicvideo.biz.dal.visual.po;

import com.dl.framework.common.bo.PageQueryDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class VisualProduceJobSharePageQueryPO extends PageQueryDO {

    private String name;

    private Integer resolutionType;

    @ApiModelProperty("排序类型 0-创建时间倒序 1-名称升序，时间倒序 2 修改时间倒序")
    private Integer sortType;

    private String tenantCode;

    private Date startTime;

    private Date endTime;
}
