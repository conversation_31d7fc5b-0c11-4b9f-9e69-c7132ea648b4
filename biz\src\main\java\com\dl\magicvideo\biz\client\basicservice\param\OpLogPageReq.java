package com.dl.magicvideo.biz.client.basicservice.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:11
 */
@Data
public class OpLogPageReq extends AbstractPageParam {

    @NotBlank(message = "接入业务的编码不能为空")
    @ApiModelProperty(value = "接入业务的编码", required = true)
    private String bizCode;

    @NotBlank(message = "租户编码不能为空")
    @ApiModelProperty(value = "租户编码", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "操作对象")
    private String opObject;

    @ApiModelProperty(value = "操作对象主键")
    private String opKey;

    @ApiModelProperty(value = "操作类型 自定义（add、update、delete）")
    private String opType;

    @ApiModelProperty("操作开始时间")
    private Date opSince;

    @ApiModelProperty("操作结束时间")
    private Date opUntil;

    @ApiModelProperty("操作人id")
    private String opUserId;

}
