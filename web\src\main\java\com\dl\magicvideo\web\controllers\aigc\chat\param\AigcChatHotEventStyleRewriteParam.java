package com.dl.magicvideo.web.controllers.aigc.chat.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-07-01 10:21
 */
@Data
@ApiModel("aigc-聊天-热点事件-文本风格改写参数")
public class AigcChatHotEventStyleRewriteParam {

    /**
     * @see com.dl.magicvideo.biz.manager.aigc.chatrecord.enums.AigcScriptStyleEnum
     */
    @NotNull(message = "风格不能为空")
    @ApiModelProperty("风格，0-通用短视频 1-抖音")
    private Integer style;

    @NotNull(message = "聊天记录id不能为空")
    @ApiModelProperty("聊天记录id")
    private Long recordId;

}
