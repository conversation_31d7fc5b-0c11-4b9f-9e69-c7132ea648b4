package com.dl.magicvideo.biz.dal.subjectmatter.param;

import com.dl.framework.common.bo.PageQueryDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 17:29
 */
@Data
public class SubjectMatterJoinPageParam extends PageQueryDO {

    /**
     * 题材名称
     */
    private String name;
    /**
     * 股票编码
     */
    private String stockCode;
    /**
     * 题材级别，1-一级题材，2-二级题材
     * 为空则查询所有级别
     */
    private Integer level;
    /**
     * 题材类型，1-个股题材库，2-题材库
     */
    private Integer type;
}
