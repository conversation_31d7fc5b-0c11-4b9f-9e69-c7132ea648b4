package com.dl.magicvideo.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsEfficiencyPO;
import com.dl.magicvideo.biz.dal.visual.po.StatisticsJobCountPO;
import com.dl.magicvideo.biz.dal.visual.po.StatisticsJobEfficiencyPO;
import com.dl.magicvideo.biz.manager.statistics.StatisticsCountManager;
import com.dl.magicvideo.biz.manager.statistics.StatisticsEfficiencyManager;
import com.dl.magicvideo.biz.manager.transaction.TransactionProxyManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.StatisticsJobCountBO;
import com.dl.magicvideo.biz.manager.visual.bo.StatisticsJobEfficiencyBO;
import com.dl.magicvideo.biz.manager.visual.enums.EfficiencyTypeEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @describe: GeneratePreviewDataTask
 * @author: zhousx
 * @date: 2023/6/15 9:19
 */
@Slf4j
@Component
public class StatisticsTask {

    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    @Resource
    private StatisticsCountManager statisticsCountManager;

    @Resource
    private StatisticsEfficiencyManager statisticsEfficiencyManager;

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Resource
    private TransactionProxyManager transactionProxyManager;

//    @XxlJob("statisticsJobHandler")
    @Transactional(rollbackFor = Throwable.class)
    public void statistics() {
        int dayAmount = -1;
        String dayAmountStr = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(dayAmountStr)) {
            dayAmount = Integer.parseInt(dayAmountStr);
        }

        //获取昨天时间
        Date dayStartTime = DateUtil.getDayStartTime(dayAmount);
        Date dayLastTime = DateUtil.getDayLastTime(dayAmount);
        String lastDay = DateUtil.format(dayLastTime, DateUtil.Y_M_D);
        log.info("{}统计任务开始运行",lastDay);

        //处理指定某一天的视频生产统计数据
        this.handleSpecificDayJobStatistics(dayStartTime, dayLastTime);

        //生产效率统计 视频总时长/生产所花时间=倍数
        StatisticsJobEfficiencyBO efficiencyBO = new StatisticsJobEfficiencyBO();
        efficiencyBO.setStartTime(dayStartTime);
        efficiencyBO.setEndTime(dayLastTime);
        StatisticsJobEfficiencyPO efficiency = visualProduceJobManager.efficiency(efficiencyBO);
        if (Objects.nonNull(efficiency) && Objects.nonNull(efficiency.getDurationSecond())) {
            if (Objects.nonNull(efficiency.getAllCompleteSecond())) {
                StatisticsEfficiencyPO sf1 = new StatisticsEfficiencyPO();
                sf1.setStatisticsTime(lastDay);
                double ef1 = efficiency.getAllCompleteSecond() / efficiency.getDurationSecond();
                sf1.setStatisticsValue(String.format("%.2f", ef1));
                sf1.setEfficiencyId(hostTimeIdg.generateId().longValue());
                sf1.setCreateBy(0L);
                sf1.setModifyBy(0L);
                sf1.setType(EfficiencyTypeEnum.ALL.getCode());
                statisticsEfficiencyManager.save(sf1);
            }

            if (Objects.nonNull(efficiency.getCompleteSecond())) {
                StatisticsEfficiencyPO sf4 = new StatisticsEfficiencyPO();
                sf4.setStatisticsTime(lastDay);
                double ef4 = efficiency.getCompleteSecond() / efficiency.getDurationSecond();
                if (Objects.nonNull(efficiency.getAiCompleteSecond()) && efficiency.getAiCompleteSecond() > 0f) {
                    ef4 = (efficiency.getCompleteSecond() + efficiency.getAiCompleteSecond()) / efficiency.getDurationSecond();
                }
                sf4.setStatisticsValue(String.format("%.2f", ef4));
                sf4.setEfficiencyId(hostTimeIdg.generateId().longValue());
                sf4.setCreateBy(0L);
                sf4.setModifyBy(0L);
                sf4.setType(EfficiencyTypeEnum.SINGLE.getCode());
                statisticsEfficiencyManager.save(sf4);
            }
        }

        //生产效率统计 视频总时长/生产所花时间=倍数
        StatisticsJobEfficiencyBO efficiencyBO2 = new StatisticsJobEfficiencyBO();
        efficiencyBO2.setStartTime(dayStartTime);
        efficiencyBO2.setEndTime(dayLastTime);
        efficiencyBO2.setType(Const.ONE);
        StatisticsJobEfficiencyPO efficiency2 = visualProduceJobManager.efficiency(efficiencyBO2);
        if (Objects.nonNull(efficiency2) && Objects.nonNull(efficiency2.getDurationSecond())) {
            if (Objects.nonNull(efficiency2.getAllCompleteSecond())) {
                StatisticsEfficiencyPO sf2 = new StatisticsEfficiencyPO();
                sf2.setStatisticsTime(lastDay);
                double ef2 = efficiency2.getCompleteSecond() / efficiency2.getDurationSecond();
                if (Objects.nonNull(efficiency2.getAiCompleteSecond()) && efficiency2.getAiCompleteSecond() > 0f) {
                    ef2 = (efficiency2.getCompleteSecond() + efficiency2.getAiCompleteSecond()) / efficiency2.getDurationSecond();
                }
                sf2.setStatisticsValue(String.format("%.2f", ef2));
                sf2.setEfficiencyId(hostTimeIdg.generateId().longValue());
                sf2.setCreateBy(0L);
                sf2.setModifyBy(0L);
                sf2.setType(EfficiencyTypeEnum.DIGITAL_MAN_TYPE.getCode());
                statisticsEfficiencyManager.save(sf2);
            }
        }

        //生产效率统计 视频总时长/生产所花时间=倍数
        StatisticsJobEfficiencyBO efficiencyBO3 = new StatisticsJobEfficiencyBO();
        efficiencyBO3.setStartTime(dayStartTime);
        efficiencyBO3.setEndTime(dayLastTime);
        efficiencyBO3.setType(Const.ZERO);
        StatisticsJobEfficiencyPO efficiency3 = visualProduceJobManager.efficiency(efficiencyBO3);
        if (Objects.nonNull(efficiency3) && Objects.nonNull(efficiency3.getDurationSecond())) {
            if (Objects.nonNull(efficiency3.getAllCompleteSecond())) {
                StatisticsEfficiencyPO sf3 = new StatisticsEfficiencyPO();
                sf3.setStatisticsTime(lastDay);
                double ef3 = efficiency3.getCompleteSecond() / efficiency3.getDurationSecond();
                if (Objects.nonNull(efficiency3.getAiCompleteSecond()) && efficiency3.getAiCompleteSecond() > 0f) {
                    ef3 = (efficiency3.getCompleteSecond() + efficiency3.getAiCompleteSecond()) / efficiency3.getDurationSecond();
                }
                sf3.setStatisticsValue(String.format("%.2f", ef3));
                sf3.setEfficiencyId(hostTimeIdg.generateId().longValue());
                sf3.setCreateBy(0L);
                sf3.setModifyBy(0L);
                sf3.setType(EfficiencyTypeEnum.PICTURE.getCode());
                statisticsEfficiencyManager.save(sf3);
            }
        }
        log.info("{}统计任务结束", lastDay);
    }

    /**
     * 历史作品数据统计定时任务
     * 历史数据计算的逻辑
     * <p>
     * 需要在xxl-job后台配置起止日期。该定时任务在单个环境下只执行一次！
     */
//    @XxlJob("historyStatisticsJobHandler")
    public void historyStatisticsJobHandler() {
        String beginEndDateStr = XxlJobHelper.getJobParam();
        Assert.isTrue(StringUtils.isNotBlank(beginEndDateStr), "起止日期不能为空");
        String[] dateArr = beginEndDateStr.split(SymbolE.MINUS.getValue());
        String beginDateBoundaryStr = dateArr[0];
        String endDateBoundaryStr = dateArr[1];
        Date beginDateBoundary = DateUtil.parse(beginDateBoundaryStr, DateUtil.YMD);
        Date endDateBoundary = DateUtil.parse(endDateBoundaryStr, DateUtil.YMD);

        //计算两个日期边界之差
        int betweenDays = (int) DateUtil.between(beginDateBoundary, endDateBoundary, Calendar.DATE);
        Assert.isTrue(betweenDays >= 0L, "起止日期输入错误，开始日期不应晚于结束日期");
        log.info("开始处理历史的任务数据的定时任务，开始日期:{},结束日期:{}", beginDateBoundaryStr, endDateBoundaryStr);

        for (int i = 0; i <= betweenDays; i++) {
            Date beginDate = DateUtil.addDay(i, beginDateBoundary);
            Date endDate = DateUtil.getMaxDate(beginDate);
            transactionProxyManager.process(() -> this.handleSpecificDayJobStatistics(beginDate, endDate));
        }
        log.info("处理历史的任务数据的定时任务完成，开始日期:{},结束日期:{}", beginDateBoundaryStr, endDateBoundaryStr);
    }

    private void handleSpecificDayJobStatistics(Date beginDate, Date endDate) {
        String statisticsTime = DateUtil.format(beginDate, DateUtil.Y_M_D);
        //先移除该日的数据
        statisticsCountManager.remove(Wrappers.lambdaQuery(StatisticsCountPO.class)
                .eq(StatisticsCountPO::getStatisticsTime, statisticsTime));

        //获取统计数据
        StatisticsJobCountBO statisticsJobCountBO = new StatisticsJobCountBO();
        statisticsJobCountBO.setStartTime(beginDate);
        statisticsJobCountBO.setEndTime(endDate);
        List<StatisticsJobCountPO> statistics = visualProduceJobManager.statistics(statisticsJobCountBO);
        if (CollectionUtils.isEmpty(statistics)) {
            return;
        }

        statistics.forEach(e -> {
            StatisticsCountPO statisticsCountPO = new StatisticsCountPO();
            statisticsCountPO.setStatisticsTime(statisticsTime);
            statisticsCountPO.setStatisticsValue(e.getCompleteNum().toString());
            statisticsCountPO.setFailNum(e.getFailNum());
            statisticsCountPO.setCountId(hostTimeIdg.generateId().longValue());
            statisticsCountPO.setTenantCode(e.getTenantCode());
            statisticsCountPO.setTenantName(e.getTenantName());
            statisticsCountPO.setTotalDuration(e.getTotalDuration());
            statisticsCountPO.setCreateBy(0L);
            statisticsCountPO.setModifyBy(0L);
            statisticsCountManager.save(statisticsCountPO);
        });
    }

    public static void main(String[] args) {
        double ef = 5.215501629844259d;
        String format = String.format("%.2f", ef);
        System.out.println(format);
    }
}
