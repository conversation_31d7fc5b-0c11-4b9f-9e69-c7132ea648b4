package com.dl.magicvideo.biz.manager.aigc.chatrecord.bo;

import com.dl.magicvideo.biz.manager.subjectmatter.dto.SubjectTreeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-15 15:42
 */
@Data
public class AigcChatRecordHotEventSubjectMatterAnswerBO {

    @ApiModelProperty(value = "题材bizId")
    private Long subjectMatterBizId;

    @ApiModelProperty(value = "匹配到的题材树")
    private SubjectTreeDTO matchedSubjectMatterTree;

    @ApiModelProperty(value = "回答文本")
    private String answerText;

    @ApiModelProperty(value = "事件标题")
    private String eventTitle;

    /**
     * @see com.dl.magicvideo.biz.manager.aigc.chatrecord.enums.AigcScriptStyleEnum
     */
    @ApiModelProperty(value = "风格，0-通用短视频 1-抖音")
    private Integer style;

    @ApiModelProperty(value = "风格改写后的回答文本")
    private String styleRewritenAnswerText;

}
