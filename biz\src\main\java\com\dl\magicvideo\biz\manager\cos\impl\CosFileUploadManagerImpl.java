package com.dl.magicvideo.biz.manager.cos.impl;


import cn.hutool.core.io.FileUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.properties.cos.CosProperties;
import com.dl.magicvideo.biz.manager.cos.CosFileUploadManager;
import com.dl.magicvideo.biz.manager.cos.dto.MediaInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * @ClassName CosFileUploadManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/11 17:58
 * @Version 1.0
 **/
@Slf4j
@Service
public class CosFileUploadManagerImpl implements CosFileUploadManager {

    private final static String FILE_DIR_TEMPLATE = "visual/videos/";
    private final static String FILE_URL = "https://%s.cos.%s.myqcloud.com/%s";

    private final static String BUCKET_NAME = "pelotavatar";

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Resource
    private CosProperties cosProperties;

    @Override
    public String uploadFile(File file, String type, String path, Boolean random, Boolean deleteFile) {
        if (Objects.isNull(file)) {
            return null;
        }

        String fileName = file.getName();
        //扩展名
        String extName = FileUtil.extName(fileName);
        //主要名称
        String mainName = FileUtil.mainName(fileName);
        //处理文件类型
        if (StringUtils.hasLength(type) && !StringUtils.hasLength(extName)) {
            extName = type;
        }
        if(random){
            mainName = mainName + "-" + hostTimeIdg.generateId().longValue();
        }
        if (StringUtils.hasLength(path)) {
            fileName = path + Const.SLASH + mainName + SymbolE.DOT.getValue() + extName;
        } else {
            fileName = FILE_DIR_TEMPLATE + mainName + SymbolE.DOT.getValue() + extName;
        }


        OSS ossClient = createCli();
        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME, fileName, file);
            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            return result.getResponse().getUri();
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } catch (Exception e) {
            log.error("阿里云上传异常", e);
        }  finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    @Override
    public MediaInfo generateMediainfo(String path) {
        if (StringUtils.isEmpty(path)) {
            return null;
        }
        OSS cosClient = createCli();
        MediaInfo mediaInfo = null;
        try {
            // 构建视频信息提取的处理指令。
            GetObjectRequest getObjectRequest = new GetObjectRequest(cosProperties.getBucketId(), path);
            getObjectRequest.setProcess("video/info");

            // 使用getObject方法，并通过process参数传入处理指令。
            OSSObject ossObject = cosClient.getObject(getObjectRequest);

            // 读取并打印视频信息。
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = ossObject.getObjectContent().read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            String videoInfo = baos.toString("UTF-8");
            mediaInfo = JsonUtils.fromJSON(videoInfo, MediaInfo.class);
        } catch (IOException e) {
            log.error("读取视频信息异常", e);
        } finally {
            // 关闭OSSClient。
            cosClient.shutdown();
        }
        return mediaInfo;
    }

    private OSS createCli() {
        String endpoint = "https://oss-cn-shanghai.aliyuncs.com";

        // 填写Endpoint对应的Region信息，例如cn-hangzhou。
        String region = "cn-shanghai";
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        DefaultCredentialProvider credentialsProvider = null;
        try {
            credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(cosProperties.getAccessKeyId(), cosProperties.getAccessKeySecret());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 创建OSSClient实例。
        // 当OSSClient实例不再使用时，调用shutdown方法以释放资源。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();
        return ossClient;
    }

}
