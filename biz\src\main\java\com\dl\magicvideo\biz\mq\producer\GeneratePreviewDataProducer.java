package com.dl.magicvideo.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobMsgDTO;
import com.dl.magicvideo.biz.mq.DlChannels;
import com.dl.magicvideo.biz.mq.dto.GenenratePreviewDataDTO;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-17 09:46
 */
@Component
public class GeneratePreviewDataProducer {
    private static final Logger LOGGER = LoggerFactory.getLogger(GeneratePreviewDataProducer.class);

    @Autowired
    private DlChannels dlChannels;

    public void sendGeneratePreviewDataMsg(Long jobId) {
        LOGGER.info("准备发送生成预览数据的消息 jobId = {}", jobId);
        GenenratePreviewDataDTO msgDTO = new GenenratePreviewDataDTO();
        msgDTO.setJobId(jobId);
        // Kafka不支持延时消息，直接发送
        Message message = MessageBuilder.withPayload(msgDTO).build();
        try {
            boolean resp = dlChannels.generatePreviewDataProducer().send(message, 2000L);
            LOGGER.info("发送生成预览数据的消息,message:{},sendResult:{}", JSONUtil.toJsonStr(message), resp);
        } catch (Exception e) {
            LOGGER.error("发送生成预览数据的消息发生异常,message:{},e:{}", JSONUtil.toJsonStr(message), e);
        }
    }

}
