package com.dl.magicvideo.biz.manager.aigc.chatrecord.bo;

import lombok.Data;

/**
 * aigc聊天记录内容中文件对象
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 16:23
 */
@Data
public class AigcChatRecordContentFileBO {

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件url地址
     */
    private String url;

    /**
     * 文件类型（粗的类型，如图片、doc）
     *
     * @see: com.dl.magicvideo.biz.manager.aigc.prompt.enums.AigcFileTypeEnum
     */
    private Integer type;

    /**
     * 文件格式（精确的格式，如jpg、png、doc、docx）
     */
    private String typeFormat;

    /**
     * 文件大小，单位：字节
     */
    private Long size;
}
