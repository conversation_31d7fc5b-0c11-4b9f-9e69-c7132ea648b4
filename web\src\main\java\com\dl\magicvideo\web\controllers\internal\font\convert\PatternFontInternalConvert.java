package com.dl.magicvideo.web.controllers.internal.font.convert;

import com.dl.magicvideo.biz.manager.font.dto.PatternFontDTO;
import com.dl.magicvideo.web.controllers.internal.font.vo.PatternFontInternalPageVO;
import com.dl.magicvideo.web.controllers.internal.font.vo.PatternFontInternalVO;

import java.util.Objects;

public class PatternFontInternalConvert {

    public static PatternFontInternalVO cnvPatternFontDTO2InternalVO(PatternFontDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        PatternFontInternalVO result = new PatternFontInternalVO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setFontType(input.getFontType());
        result.setStyles(input.getStyles());
        result.setCoverImg(input.getCoverImg());
        return result;
    }

    public static PatternFontInternalPageVO cnvPatternFontDTO2InternalPageVO(PatternFontDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        PatternFontInternalPageVO result = new PatternFontInternalPageVO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setFontType(input.getFontType());
        result.setCoverImg(input.getCoverImg());
        result.setUserId(input.getUserId());
        result.setCreateDt(input.getCreateDt());
        result.setStyles(input.getStyles());
        return result;
    }
}
