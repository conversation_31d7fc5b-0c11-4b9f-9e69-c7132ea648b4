package com.dl.magicvideo.web.controllers.aigc.chat.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 14:58
 */
@Data
@ApiModel("aigc-聊天记录分页参数")
public class AigcChatRecordPageParam extends AbstractPageParam {

    /**
     * 查询在此记录id之后的记录
     */
    private Long afterRecordId;

    /**
     * 查询发送时间在此之后的记录
     */
    private Date afterSendDt;

}
