package com.dl.magicvideo.web.controllers.pinyin;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.common.util.PinyinUtil;
import com.dl.magicvideo.web.controllers.pinyin.param.PolyphonicParam;
import com.dl.magicvideo.web.controllers.pinyin.vo.PolyphonicVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @describe: 拼音相关controller
 * @author: hongcj
 * @date: 2023/6/17 14:43
 */
@Slf4j
@RestController
@RequestMapping("/visual/pinyin")
@Api("拼音信息")
public class PinyinController {

    @PostMapping("/polyphonic")
    @ApiOperation("多音字内容")
    public ResultModel<List<PolyphonicVO>> polyphonic(@RequestBody @Validated PolyphonicParam param) {
        List<PolyphonicVO> list = new ArrayList<>();
        List<String> pinyinWithToneMarkList = PinyinUtil.polyphonicWithToneMark(param.getPolyphonic());
        List<String> pinyinWithToneNumberList = PinyinUtil.polyphonicWithToneNumber(param.getPolyphonic());
        if (CollectionUtils.isNotEmpty(pinyinWithToneMarkList) && CollectionUtils.isNotEmpty(
                pinyinWithToneNumberList)) {
            pinyinWithToneMarkList.forEach(e -> {
                PolyphonicVO vo = new PolyphonicVO();
                vo.setWithToneMark(e);
                list.add(vo);
            });
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setWithToneNumber(pinyinWithToneNumberList.get(i));
            }
        }
        return ResultModel.success(list);
    }
}
