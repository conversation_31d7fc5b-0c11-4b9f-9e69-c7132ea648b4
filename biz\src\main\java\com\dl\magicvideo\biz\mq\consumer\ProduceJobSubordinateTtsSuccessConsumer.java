package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.dal.visual.po.VisualCardPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.VisualCardManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.dto.preview.PreviewCardDTO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.biz.mq.dto.ProduceJobSubordinateTtsSuccessDTO;
import com.dl.magicvideo.biz.mq.dto.TtsJobDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频任务下属的tts合成成功的消息消费者
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-03 15:38
 */
@Component
public class ProduceJobSubordinateTtsSuccessConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProduceJobSubordinateTtsSuccessConsumer.class);

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    @Resource
    private VisualTemplateManager visualTemplateManager;

    @Resource
    private VisualCardManager visualCardManager;

    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    @StreamListener("producejobsubordinatettssuccessconsumer")
    public void consume(@Payload ProduceJobSubordinateTtsSuccessDTO msgDTO) {
        LOGGER.info("收到视频任务下属的tts合成成功的消息，templateId:{},,,jobId:{}", msgDTO.getTemplateId(), msgDTO.getJobId());

        //1.查询作品信息
        VisualProduceJobPO jobPO = visualProduceJobManager.getOne(Wrappers.lambdaQuery(VisualProduceJobPO.class)
                .eq(VisualProduceJobPO::getJobId, msgDTO.getJobId()).select(VisualProduceJobPO.class,
                        po -> !"replace_data".equals(po.getColumn()) && !"preview_data".equals(po.getColumn())
                                && !"template_data".equals(po.getColumn()) && !"api_data".equals(po.getColumn())));

        //处理aigc作品对应的模板
        this.handleAigcJobTemplate(msgDTO, jobPO);

        LOGGER.info("视频任务下属的tts合成成功的消息处理完毕，templateId:{},,,jobId:{}", msgDTO.getTemplateId(), msgDTO.getJobId());
    }

    /**
     * 处理aigc作品对应的模板
     *
     * @param msgDTO
     */
    private void handleAigcJobTemplate(ProduceJobSubordinateTtsSuccessDTO msgDTO, VisualProduceJobPO jobPO) {
        //查询作品扩展信息
        VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                        .eq(VisualProduceJobExtendPO::getProduceJobId, msgDTO.getJobId()));
        if (!JobTypeEnum.AIGC.getType().equals(jobExtendPO.getType())) {
            LOGGER.info("该作品不是AIGC作品，不处理。jobId:{},jobType:{}", msgDTO.getJobId(), jobExtendPO.getType());
            return;
        }
        if (VisualProduceJobSourceEnum.AIGC_PRODUCE_HOT_EVENT_SUBJECT_MATTER_TOOL.getCode().equals(jobPO.getSource())) {
            LOGGER.info("该作品是通过aigc-热点事件题材工具触发的aigc作品，不处理。jobId:{}", msgDTO.getJobId());
            return;
        }

        //过滤出数字人的tts任务列表
        List<TtsJobDTO> dmTtsJobDTOList = msgDTO.getTtsJobList().stream()
                .filter(job -> AiJobTypeE.DM_TTS.getCode().equals(job.getJobType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dmTtsJobDTOList)) {
            LOGGER.error("不存在数字人的tts任务！！,templateId:{},jobId:{},ttsJobList:{}", msgDTO.getTemplateId(),
                    msgDTO.getJobId(), JSONUtil.toJsonStr(msgDTO.getTtsJobList()));
            return;
        }

        List<VisualCardPO> cardPOList = visualCardManager
                .list(Wrappers.lambdaQuery(VisualCardPO.class).eq(VisualCardPO::getTemplateId, msgDTO.getTemplateId())
                        .eq(VisualCardPO::getIsDeleted, Const.ZERO));
        if (CollectionUtils.isEmpty(cardPOList)) {
            LOGGER.error("该模板下不存在card，配置有误！！,templateId:{},jobId:{}", msgDTO.getTemplateId(), msgDTO.getJobId());
            return;
        }
        //暂时只考虑单个card和单个片段（即单个动态节点）的情况
        VisualCardPO cardPO = cardPOList.get(0);
        TtsJobDTO dmTtsJobDTO = dmTtsJobDTOList.get(0);

        String renderData = cardPO.getRenderData().replaceAll("\n", "").replaceAll("\r", "");

        ObjectMapper objectMapper = new ObjectMapper();
        //将renderData解析为JsonNode
        JsonNode jsonNode = null;
        try {
            jsonNode = objectMapper.readTree(renderData);
        } catch (JsonProcessingException e) {
            LOGGER.error("解析renderData发生异常!templateId:{},jobId:{},,,", msgDTO.getTemplateId(), msgDTO.getJobId(), e);
            return;
        }

        List<PreviewCardDTO.SubtitleDTO> subtitleDTOList = JSONUtil
                .toBean(dmTtsJobDTO.getSubtitleInfo(), new TypeReference<List<PreviewCardDTO.SubtitleDTO>>() {
                }, false);
        ArrayNode subtitleArrayNode = null;
        try {
            // 将字幕对象数组转换为JsonNode（ArrayNode）
            subtitleArrayNode = objectMapper.valueToTree(subtitleDTOList);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println(new Date());
        //替换renderData中的时长、tts音频、数字人字幕
        replaceValue(jsonNode, dmTtsJobDTO.getDuration(), dmTtsJobDTO.getMediaInfo(), subtitleArrayNode);

        //更新后的renderData
        String updatedJsonString;
        try {
            // 将更新后的JsonNode转回字符串
            updatedJsonString = objectMapper.writeValueAsString(jsonNode);
        } catch (Exception e) {
            LOGGER.error("转换更新后的renderData发生异常!templateId:{},jobId:{},,,", msgDTO.getTemplateId(), msgDTO.getJobId(),
                    e);
            return;
        }

        //更新renderData
        visualCardManager.update(Wrappers.lambdaUpdate(VisualCardPO.class).eq(VisualCardPO::getId, cardPO.getId())
                .set(VisualCardPO::getRenderData, updatedJsonString).set(VisualCardPO::getModifyDt, new Date()));
        LOGGER.info("更新模板中的renderData成功！,templateId:{},,,jobId:{}", msgDTO.getTemplateId(), msgDTO.getJobId());

        //更新模板的is_show字段为1、模板时长
        visualTemplateManager.update(Wrappers.lambdaUpdate(VisualTemplatePO.class)
                .eq(VisualTemplatePO::getTemplateId, msgDTO.getTemplateId()).set(VisualTemplatePO::getIsShow, Const.ONE)
                .set(VisualTemplatePO::getDuration, dmTtsJobDTO.getDuration())
                .set(VisualTemplatePO::getModifyDt, new Date()));
        LOGGER.info("更新模板中的is_show字段为1成功！,templateId:{},,,jobId:{}", msgDTO.getTemplateId(), msgDTO.getJobId());

    }

    public static void main(String[] args) throws Exception {
        String filPath = "/Users/<USER>/work/定力/aigc小助手/renderdata替换/1163723983999552034的renderData.txt";
        String content = "";
        try {
            byte[] encodeBytes = Files.readAllBytes(Paths.get(filPath));
            content = new String(encodeBytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            System.out.println("发生异常，e:" + e);
            return;
        }

        String newRenderData = content.replaceAll("\n", "").replaceAll("\r", "");

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = null;
        try {
            jsonNode = objectMapper.readTree(newRenderData);
        } catch (JsonProcessingException e) {
            return;
        }

        List<PreviewCardDTO.SubtitleDTO> subtitleDTOList = JSONUtil
                .toBean("[{\"text\":\"在我们订阅神奇九转之后\",\"beginTime\":0,\"endTime\":2540},{\"text\":\"可以在我们的K线和分时图上\",\"beginTime\":3060,\"endTime\":5655},{\"text\":\"查看九转信号的指标\",\"beginTime\":5655,\"endTime\":7640},{\"text\":\"这是查看的股票类型\",\"beginTime\":8140,\"endTime\":10080},{\"text\":\"包括沪深A股\",\"beginTime\":10400,\"endTime\":11935},{\"text\":\"B股\",\"beginTime\":11935,\"endTime\":12600},{\"text\":\"沪深指数、\",\"beginTime\":13100,\"endTime\":14270},{\"text\":\"场内基金以及可转债\",\"beginTime\":14270,\"endTime\":16839}]",
                        new TypeReference<List<PreviewCardDTO.SubtitleDTO>>() {
                        }, false);
        ArrayNode subtitleArrayNode = null;
        try {
            // 将字幕对象数组转换为JsonNode（ArrayNode）
            subtitleArrayNode = objectMapper.valueToTree(subtitleDTOList);
        } catch (Exception e) {
            e.printStackTrace();
        }

        System.out.println(new Date());
        replaceValue(jsonNode, 123456L,
                "https://videomaker-resources.ivh.qq.com/broadcast/Basic/audio/3344ff6bd03842698e0467242af0b498.mp3",
                subtitleArrayNode);

        // 将更新后的JsonNode转回字符串
        String updatedJsonString = objectMapper.writeValueAsString(jsonNode);
        System.out.println(updatedJsonString);
        System.out.println(new Date());
    }

    private static void replaceValue(JsonNode node, Long realDuration, String audioSrc, ArrayNode subtitleArrayNode) {
        // 如果是对象节点
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            // 遍历对象的字段
            Iterator<String> fieldNames = objectNode.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode fieldNode = objectNode.get(fieldName);
                // 如果字段名是"duration"，则替换值
                if ("duration".equals(fieldName)) {
                    objectNode.put(fieldName, realDuration);
                } else if ("realDuration".equals(fieldName)) {
                    objectNode.put(fieldName, realDuration);
                } else if ("audioSrc".equals(fieldName)) {
                    objectNode.put(fieldName, audioSrc);
                } else if ("subtitles".equals(fieldName)) {
                    objectNode.set(fieldName, subtitleArrayNode);
                } else {
                    // 递归处理对象或数组节点
                    replaceValue(fieldNode, realDuration, audioSrc, subtitleArrayNode);
                }
            }
        }
        // 如果是数组节点
        else if (node.isArray()) {
            ArrayNode arrayNode = (ArrayNode) node;
            for (JsonNode element : arrayNode) {
                replaceValue(element, realDuration, audioSrc, subtitleArrayNode);
            }
        }
    }

}
