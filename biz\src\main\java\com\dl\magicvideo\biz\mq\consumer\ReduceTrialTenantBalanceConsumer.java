package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.bo.MagicVideoJobBO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class ReduceTrialTenantBalanceConsumer {

    @Autowired
    private TenantInfoManager tenantInfoManager;

    @Autowired
    private AccountTenantTrialManager accountTenantTrialManager;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    private static final String REDUCE_BALANCE_KEY = "reduce_balance_key:";

    /**
     * 1天
     */
    private static final Long LOCK_TIME = 24 * 60 * 60L;

    private String genKey(Long jobId) {
        return REDUCE_BALANCE_KEY + jobId;
    }

    @StreamListener("reducebalanceconsumer")
    public void consume(@Payload MagicVideoJobBO input) {
        try {
            log.info("收到Magic合成成功消息，处理预扣额度返还以及次数扣除，input:{}", JSONUtil.toJsonStr(input));
            String lockKey = genKey(input.getJobId());
            if (!redisUtil.tryLockAndSetTimeout(lockKey, LOCK_TIME)) {
                log.warn("已处理过该作品的预扣额度返还以及次数扣除！jobId:{}", input.getJobId());
                return;
            }

            //判断作品类型
            VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                    .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                            .eq(VisualProduceJobExtendPO::getProduceJobId, input.getJobId()));
            //若是数据图表作品，则不涉及试用账户余额
            if (Objects.nonNull(jobExtendPO) && JobTypeEnum.DATA_CHART.getType().equals(jobExtendPO.getType())) {
                return;
            }

            AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfoFromCache(input.getTenantCode());
            //非试用租户，不需要对预试用额度进行扣除
            if (!Const.ONE.equals(tenantInfo.getIsTrial())) {
                return;
            }
            AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager
                    .getOne(Wrappers.lambdaQuery(AccountTenantTrialPO.class)
                            .eq(AccountTenantTrialPO::getTenantCode, input.getTenantCode())
                            .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
            if (Objects.isNull(tenantTrialAccount)) {
                log.error("未找到试用账户,input:{}", JSONUtil.toJsonStr(input));
                return;
            }

            tenantTrialAccount.setWithhold(tenantTrialAccount.getWithhold() - 1);
            tenantTrialAccount.setBalance(tenantTrialAccount.getBalance() - 1);
            accountTenantTrialManager.updateById(tenantTrialAccount);
            log.info("试用账户返还预扣以及扣除成功 jobId:{}", input.getJobId());
        }catch (Exception e){
            log.error("试用账户返还预扣以及扣除失败 jobId:{},,e:{}", input.getJobId(), e);
        }
    }

}
