package com.dl.magicvideo.web.controllers.digitalman.convert;

import com.beust.jcommander.internal.Lists;
import com.dl.aiservice.share.digitalman.DigitalManAggregationInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManBaseInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManSceneBaseInfoDTO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DigitalManSceneVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.DmAggregationVO;
import com.dl.magicvideo.web.controllers.digitalman.vo.GenericDigitalmanVO;
import com.dl.magicvideo.web.controllers.voice.vo.GenericVoiceVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-14 19:12
 */
public class VisualDigitalmanConvert {

    public static GenericDigitalmanVO cnvDigitalManBaseInfoDTO2VO(DigitalManBaseInfoDTO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        GenericDigitalmanVO result = new GenericDigitalmanVO();
        fillGenericDigitalmanVO(input, result);
        return result;
    }

    public static void fillGenericDigitalmanVO(DigitalManBaseInfoDTO input, GenericDigitalmanVO result) {
        result.setDigitalManId(String.valueOf(input.getDigitalManId()));
        result.setChannel(input.getChannel());
        result.setGender(input.getGender());
        result.setHeadImg(input.getHeadImg());
        result.setVmCode(input.getDgAvatar());
        result.setVmName(input.getDgName());
        result.setVmType(input.getVmType());
        result.setHeadImg(input.getHeadImg());
    }

    public static DmAggregationVO cnvDmAggregationDTO2VO(DigitalManAggregationInfoDTO input, String sceneId) {
        if (Objects.isNull(input)) {
            return null;
        }
        DmAggregationVO result = new DmAggregationVO();
        fillGenericDigitalmanVO(input, result);

        if (Objects.nonNull(input.getVoiceInfo())) {
            GenericVoiceVO voiceInfo = GenericVoiceVO.builder().voiceGender(input.getVoiceInfo().getGender())
                    .voiceKey(input.getVoiceInfo().getVoiceKey()).voiceName(input.getVoiceInfo().getVoiceName())
                    .voiceCategory(input.getVoiceInfo().getVoiceCategory()).channel(input.getVoiceInfo().getChannel())
                    .sampleLink(input.getVoiceInfo().getSampleLink()).volume(input.getVoiceInfo().getVolume())
                    .speed(input.getVoiceInfo().getSpeed()).duration(input.getVoiceInfo().getDuration()).build();
            result.setVoiceInfo(voiceInfo);
        }

        if (CollectionUtils.isNotEmpty(input.getSceneList())) {
            if (StringUtils.isBlank(sceneId)) {
                List<DigitalManSceneVO> sceneVOList = input.getSceneList().stream()
                        .map(sceneDTO -> cnvDigitalManSceneDTO2VO(sceneDTO, result.getDigitalManId()))
                        .collect(Collectors.toList());
                result.setSceneList(sceneVOList);
            } else {
                Optional<DigitalManSceneBaseInfoDTO> opt = input.getSceneList().stream()
                        .filter(sceneDTO -> sceneId.equals(sceneDTO.getSceneId())).findAny();
                if (opt.isPresent()) {
                    result.setSceneList(
                            Lists.newArrayList(cnvDigitalManSceneDTO2VO(opt.get(), result.getDigitalManId())));
                } else {
                    result.setSceneList(Collections.emptyList());
                }
            }
        }

        return result;
    }

    public static DigitalManSceneVO cnvDigitalManSceneDTO2VO(DigitalManSceneBaseInfoDTO sceneDTO, String vmBizId) {
        DigitalManSceneVO sceneVO = new DigitalManSceneVO();
        sceneVO.setBizId(vmBizId);
        sceneVO.setSceneId(sceneDTO.getSceneId());
        sceneVO.setSceneName(sceneDTO.getSceneName());
        sceneVO.setCloth(sceneDTO.getCloth());
        sceneVO.setCoverUrl(sceneDTO.getCoverUrl());
        sceneVO.setExampleDuration(sceneDTO.getExampleDuration());
        sceneVO.setExampleText(sceneDTO.getExampleText());
        sceneVO.setExampleUrl(sceneDTO.getExampleUrl());
        sceneVO.setPose(sceneDTO.getPose());
        sceneVO.setResolution(sceneDTO.getResolution());
        return sceneVO;
    }
}
