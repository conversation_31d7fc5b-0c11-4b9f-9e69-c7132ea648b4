package com.dl.magicvideo.web.controllers.aigc.chat.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 15:14
 */
@Data
@ApiModel("aigc-聊天-发送消息")
public class AigcChatSendMessageParam {

    /**
     * 聊天内容
     * 当内容类型是1:文本时，存的是字符串
     * 当内容类型是2~7:文件时，存的是AigcChatRecordContentFileBO对应的json串
     * 当内容类型是8:视频合成成功时，存的是AigcChatRecordProduceSuccessBO对应的json串
     * 当内容类型是9:视频合成失败时，存的是AigcChatRecordProduceFailBO对应的json串
     * 当内容类型是10:视频合成中时，存的是AigcChatRecordProduceIngBO对应的json串
     * 当内容类型是11:热点事件题材提问时，存的是AigcChatRecordHotEventSubjectMatterAskBO对应的json串
     * 当内容类型是12:热点事件题材回答时，存的是AigcChatRecordHotEventSubjectMatterAnswerBO对应的json串
     */
    @NotEmpty(message = "聊天内容不能为空")
    @ApiModelProperty("聊天内容")
    private String content;

    /**
     * 聊天内容类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum
     */
    @NotNull(message = "聊天内容类型为空")
    @ApiModelProperty(
            "聊天内容类型,1-文本 2-pdf 3-doc 4-xlsx 5-ppt 6-txt 7-图片 8-视频合成成功 9-视频合成失败 10-视频合成中 11-热点事件题材提问 12-热点事件题材回答")
    private Integer contentType;

    @ApiModelProperty("是否能生产视频 0-否，1-是。默认为0")
    private Integer canProduce = 0;

}
