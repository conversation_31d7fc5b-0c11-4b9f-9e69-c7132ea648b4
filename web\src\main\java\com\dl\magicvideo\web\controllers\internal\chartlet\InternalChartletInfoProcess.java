package com.dl.magicvideo.web.controllers.internal.chartlet;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.chartlet.po.ChartletInfoPO;
import com.dl.magicvideo.biz.manager.chartlet.ChartletInfoManager;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.chartlet.convert.InternalChartletInfoConvert;
import com.dl.magicvideo.web.controllers.internal.chartlet.dto.InternalChartletInfoDTO;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChareletDeleteParam;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChartletInfoAddParam;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChartletInfoUptParam;
import com.dl.magicvideo.web.controllers.internal.chartlet.param.InternalChartletPageParam;
import io.jsonwebtoken.lang.Assert;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-12 19:01
 */
@Component
public class InternalChartletInfoProcess extends AbstractController {

    @Resource
    private ChartletInfoManager chartletInfoManager;

    @Resource
    private HostTimeIdg hostTimeIdg;

    public ResultModel<Long> add(InternalChartletInfoAddParam param) {
        ChartletInfoPO insertPO = new ChartletInfoPO();
        insertPO.setBizId(hostTimeIdg.generateId().longValue());
        insertPO.setName(param.getName());
        insertPO.setContentUrl(param.getContentUrl());
        insertPO.setResolutionRatio(param.getResolutionRatio());
        insertPO.setDuration(param.getDuration());
        insertPO.setCoverImg(param.getCoverImg());
        insertPO.setCreatorName(param.getCreatorName());
        insertPO.setModifyName(param.getCreatorName());
        insertPO.setCreateBy(param.getCreateBy());
        insertPO.setModifyBy(param.getCreateBy());
        insertPO.setIsDeleted(Const.ZERO);
        insertPO.setCategory(param.getCategory());
        chartletInfoManager.save(insertPO);

        return ResultModel.success(insertPO.getBizId());
    }

    public ResultModel<Void> update(InternalChartletInfoUptParam param) {
        ChartletInfoPO exisitPO = chartletInfoManager
                .getOne(Wrappers.lambdaQuery(ChartletInfoPO.class).eq(ChartletInfoPO::getBizId, param.getBizId())
                        .eq(ChartletInfoPO::getIsDeleted, Const.ZERO));
        Assert.notNull(exisitPO, "贴图不存在");

        chartletInfoManager
                .update(Wrappers.lambdaUpdate(ChartletInfoPO.class).set(ChartletInfoPO::getName, param.getName())
                        .set(ChartletInfoPO::getContentUrl, param.getContentUrl())
                        .set(ChartletInfoPO::getResolutionRatio, param.getResolutionRatio())
                        .set(ChartletInfoPO::getDuration, param.getDuration())
                        .set(ChartletInfoPO::getCoverImg, param.getCoverImg())
                        .set(ChartletInfoPO::getCategory, param.getCategory())
                        .set(ChartletInfoPO::getModifyName, param.getModifyName())
                        .set(ChartletInfoPO::getModifyBy, param.getModifyBy())
                        .set(ChartletInfoPO::getModifyDt, new Date()).eq(ChartletInfoPO::getBizId, param.getBizId()));

        return ResultModel.success(null);
    }

    public ResultPageModel<InternalChartletInfoDTO> page(InternalChartletPageParam param) {
        LambdaQueryWrapper<ChartletInfoPO> wrapper = Wrappers.lambdaQuery(ChartletInfoPO.class)
                .like(StringUtils.isNotBlank(param.getName()), ChartletInfoPO::getName, param.getName())
                .eq(ChartletInfoPO::getIsDeleted, Const.ZERO).orderByDesc(ChartletInfoPO::getModifyDt);

        Page<ChartletInfoPO> resp = chartletInfoManager
                .page(new Page<>(param.getPageIndex(), param.getPageSize()), wrapper);
        return pageQueryModel(resp, resp.getRecords().stream().map(InternalChartletInfoConvert::cnvChartletInfoPO2DTO)
                .collect(Collectors.toList()));
    }

    public ResultModel<Void> delete(InternalChareletDeleteParam param) {
        ChartletInfoPO exisitPO = chartletInfoManager
                .getOne(Wrappers.lambdaQuery(ChartletInfoPO.class).eq(ChartletInfoPO::getBizId, param.getBizId())
                        .eq(ChartletInfoPO::getIsDeleted, Const.ZERO));
        Assert.notNull(exisitPO, "贴图不存在");

        chartletInfoManager
                .update(Wrappers.lambdaUpdate(ChartletInfoPO.class).set(ChartletInfoPO::getIsDeleted, Const.ONE)
                        .set(ChartletInfoPO::getModifyName, param.getModifyName())
                        .set(ChartletInfoPO::getModifyBy, param.getModifyBy())
                        .set(ChartletInfoPO::getModifyDt, new Date()).eq(ChartletInfoPO::getBizId, param.getBizId()));
        return ResultModel.success(null);
    }

}
