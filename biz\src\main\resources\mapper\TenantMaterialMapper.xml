<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.TenantMaterialMapper">

    <resultMap id="ResultMap" type="com.dl.magicvideo.biz.dal.visual.po.LatestTenantMaterialPO">
        <result property="id" column="id"/>
        <result property="bizId" column="biz_id"/>
        <result property="folderId" column="folder_id"/>
        <result property="title" column="title"/>
        <result property="logoImg" column="logo_img"/>
        <result property="duration" column="duration"/>
        <result property="materialType" column="material_type"/>
        <result property="createDt" column="create_dt"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="createBy" column="create_by"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="url" column="url"/>
        <result property="webmUrl" column="webm_url"/>
        <result property="size" column="size"/>
        <result property="resolutionRatio" column="resolution_ratio"/>
        <result property="rowNumber" column="row_number"/>
    </resultMap>

    <sql id="Column_List">
        id,biz_id,folder_id,title,logo_img,duration,material_type,create_dt,tenant_code,create_by,is_deleted,url,webm_url,resolution_ratio,size
    </sql>

    <select id="latestMaterialByFolderIds"
            resultMap="ResultMap">
        select
        <include refid="Column_List"/>,rn
        from
        (
        select
        <include refid="Column_List"/>,
        ROW_NUMBER() OVER (PARTITION BY folder_id ORDER BY create_dt DESC) AS rn
        from tenant_material
        where
        tenant_code = #{tenantCode}
        <if test="folderIds!=null and folderIds.size() > 0">
            and folder_id in
            <foreach collection="folderIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="materialType != null">
            and material_type = #{materialType}
        </if>
        and is_deleted = 0
        ) AS ranked
        WHERE
        rn &lt;= #{rowNumber};
    </select>

</mapper>
