package com.dl.magicvideo.biz.client.basicservice.intercepter;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.ApplicationUtil;
import com.dl.magicvideo.biz.config.WorkBenchConfig;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.interceptor.Interceptor;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-15 20:40
 */
@Slf4j
public class BasicServiceInterceptor implements Interceptor {

    @Override
    public boolean beforeExecute(ForestRequest request) {
        WorkBenchConfig bean = ApplicationUtil.getBean(WorkBenchConfig.class);
//        request.setUrl("https://test.dinglitec.com/basicservice" + request.getMethod().getMetaRequest().getUrl());
        request.setUrl(bean.getBasicServiceBaseUrl() + request.getMethod().getMetaRequest().getUrl());
        request.addHeader(Const.SYSTEM_CODE, Const.DL_MAGIC_VIDEO);
        return Boolean.TRUE;
    }

}
