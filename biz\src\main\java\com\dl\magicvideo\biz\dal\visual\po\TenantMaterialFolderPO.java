package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName tenant_material_folder
 */
@TableName(value ="tenant_material_folder")
@Data
public class TenantMaterialFolderPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 文件夹ID
     */
    private Long folderId;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 素材类型：3视频，6图片
     */
    private Integer materialType;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}