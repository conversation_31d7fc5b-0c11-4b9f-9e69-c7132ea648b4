package com.dl.magicvideo.web.controllers.aigc.chat.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-21 16:48
 */
@Data
@ApiModel("aigc-聊天-修改聊天内容")
public class AigcChatUpdateContentParam {

    @NotNull(message = "记录id不能为空")
    @ApiModelProperty("记录id")
    private Long recordId;

    /**
     * 聊天内容
     * 当内容类型是1:文本时，存的是字符串
     * 当内容类型是2~7:文件时，存的是AigcChatRecordContentFileBO对应的json串
     * 当内容类型是8:视频合成成功时，存的是AigcChatRecordProduceSuccessBO对应的json串
     * 当内容类型是9:视频合成失败时，存的是AigcChatRecordProduceFailBO对应的json串
     * 当内容类型是10:视频合成中时，存的是AigcChatRecordProduceIngBO对应的json串
     */
    @NotEmpty(message = "聊天内容不能为空")
    @ApiModelProperty("聊天内容")
    private String content;

}
