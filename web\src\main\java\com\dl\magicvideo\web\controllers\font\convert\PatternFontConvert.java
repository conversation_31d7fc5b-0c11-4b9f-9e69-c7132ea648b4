package com.dl.magicvideo.web.controllers.font.convert;

import com.dl.magicvideo.biz.manager.font.dto.PatternFontDTO;
import com.dl.magicvideo.web.controllers.font.vo.PatternFontPageVO;
import com.dl.magicvideo.web.controllers.font.vo.PatternFontVO;

import java.util.Objects;

public class PatternFontConvert {
    public static PatternFontVO cnvPatternFontDTO2VO(PatternFontDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        PatternFontVO result = new PatternFontVO();
        result.setBizId(input.getBizId()+"");
        result.setName(input.getName());
        result.setFontType(input.getFontType());
        result.setStyles(input.getStyles());
        result.setCoverImg(input.getCoverImg());
        return result;
    }

    public static PatternFontPageVO cnvPatternFontDTO2PageVO(PatternFontDTO input){
        if (Objects.isNull(input)){
            return null;
        }
        PatternFontPageVO result = new PatternFontPageVO();
        result.setBizId(input.getBizId()+"");
        result.setName(input.getName());
        result.setFontType(input.getFontType());
        result.setCoverImg(input.getCoverImg());
        result.setUserId(input.getUserId());
        result.setStyles(input.getStyles());
        return result;
    }
}
