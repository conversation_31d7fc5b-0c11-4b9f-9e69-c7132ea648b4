package com.dl.magicvideo.web.controllers.internal.visual.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-16 20:26
 */
@Data
public class VisualProduceJobInternalDetailVO extends VisualProduceJobInternalVO {
    private static final long serialVersionUID = 5150881095854119030L;

    @ApiModelProperty(value = "导航栏标题")
    private String navigationBarTitle;

    @ApiModelProperty(value = "推荐转发文案")
    private String recommendShareCnt;

    @ApiModelProperty(value = "小程序转发封面图")
    private String mpShareCoverImg;

    @ApiModelProperty(value = "H5转发封面图")
    private String h5ShareCoverImg;

    @ApiModelProperty(value = "转发标题")
    private String shareTitle;

    @ApiModelProperty(value = "转发摘要")
    private String shareRemark;

    @ApiModelProperty(value = "使用场景 0-朋友圈/群发/视频号 1-私聊")
    private Integer useCase;

    @ApiModelProperty("视频方向 1-竖版 2-横版")
    private Integer resolutionType;

}
