package com.dl.magicvideo.openapi.controller.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OpenDigitalManSceneVO {

    /**
     * 场景id
     */
    @ApiModelProperty(value = "场景id")
    private String sceneId;

    /**
     * 场景名称
     */
    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    /**
     * 场景封面地址
     */
    @ApiModelProperty(value = "场景封面地址")
    private String coverUrl;

    /**
     * 场景样例视频地址
     */
    @ApiModelProperty(value = "场景样例视频地址")
    private String exampleUrl;
}
