package com.dl.magicvideo.web.controllers.internal.subjectmatter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterStockPO;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMaterialManager;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMatterManager;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMatterStockManager;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.*;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.helper.InternalSubjectHelper;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.helper.InternalSubjectMatterHelper;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.param.*;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.vo.InternalSubjectMatterDetailVO;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.vo.InternalSubjectMatterEditVO;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.vo.InternalSubjectMatterVO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 14:26
 */
@Component
public class InternalSubjectMatterProcess extends AbstractController {

    @Resource
    private SubjectMatterManager subjectMatterManager;
    @Resource
    private SubjectMatterStockManager subjectMatterStockManager;
    @Resource
    private SubjectMaterialManager subjectMaterialManager;

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<Long> save(InternalSubjectMatterSaveParam param) {
        SubjectMatterSaveBO subjectMatterSaveBO = InternalSubjectMatterHelper.cnvSubjectMatterSaveParam2BO(param);
        Long smBizId = subjectMatterManager.save(subjectMatterSaveBO);

        List<SubjectMatterStockSaveBO> stockSaveBOList = InternalSubjectMatterHelper
                .cnvSubjectMatterStockSaveParams2BOs(param.getStockParams());
        subjectMatterStockManager.batchSave(smBizId, stockSaveBOList);

        return ResultModel.success(smBizId);
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<Long> add(InternalSubjectAddParam param) {
        //param转为BO对象
        SubjectAddBO subjectAddBO = InternalSubjectHelper.cnvSubjectAddParam2BO(param);
        //题材表保存数据
        Long rootId = subjectMatterManager.addAll(subjectAddBO);

        //param集合转为BO集合
        List<MaterialAddParam> materialList = param.getMaterialList();
        //为集合中的对象设置属性
        materialList.forEach(materialAddParam -> {
            materialAddParam.setOperatorName(param.getOperatorName());
            materialAddParam.setOperatorId(param.getOperatorId());
        });
        List<SubjectMaterialAddBO> materialAddBOS = InternalSubjectHelper.cnvSubjectMaterialAddParams2BOs(materialList);
        //题材素材关联表保存数据
        subjectMaterialManager.batchAdd(rootId, materialAddBOS);
        return ResultModel.success(rootId);
    }

    public ResultModel<InternalSubjectMatterDetailVO> detail(Long bizId) {
        SubjectMatterPO subjectMatterPO = subjectMatterManager
                .getOne(Wrappers.lambdaQuery(SubjectMatterPO.class).eq(SubjectMatterPO::getBizId, bizId)
                        .eq(SubjectMatterPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(subjectMatterPO)) {
            return ResultModel.success(null);
        }

        List<SubjectMatterStockPO> stockRelPOList = subjectMatterStockManager
                .list(Wrappers.lambdaQuery(SubjectMatterStockPO.class)
                        .eq(SubjectMatterStockPO::getSmBizId, bizId));

        return ResultModel
                .success(InternalSubjectMatterHelper.buildSubjectMatterDetailVO(subjectMatterPO, stockRelPOList));
    }

    public ResultModel<InternalSubjectMatterEditVO> edit(Long bizId) {
        SubjectMatterPO subjectMatterPO = subjectMatterManager.getOne(Wrappers.lambdaQuery(SubjectMatterPO.class).eq(SubjectMatterPO::getBizId, bizId)
                .eq(SubjectMatterPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(subjectMatterPO)){
            return ResultModel.success(null);
        }

        List<SubjectMaterialPO> materialPOList = subjectMaterialManager.list(Wrappers.lambdaQuery(SubjectMaterialPO.class)
                .eq(SubjectMaterialPO::getMatterId, bizId));

        return ResultModel.success(InternalSubjectMatterHelper.buildSubjectMatterEditVO(subjectMatterPO, materialPOList));

    }

    public ResultPageModel<InternalSubjectMatterVO> page(InternalSubjectMatterPageParam pageParam) {

        if (StringUtils.isNotBlank(pageParam.getStockCode())) {
            SubjectMatterJoinPageBO pageBO = new SubjectMatterJoinPageBO();
            pageBO.setName(pageParam.getName());
            pageBO.setStockCode(pageParam.getStockCode());
            pageBO.setPageIndex(pageParam.getPageIndex());
            pageBO.setPageSize(pageParam.getPageSize());
            pageBO.setLevel(pageParam.getLevel());
            ResponsePageQueryDO<List<SubjectMatterPO>> resultDO = subjectMatterManager.joinPage(pageBO);

            return pageQueryModel(resultDO,
                    resultDO.getDataResult().stream().map(InternalSubjectMatterHelper::cnvSubjectMatterPO2VO)
                            .collect(Collectors.toList()));
        }

        SubjectMatterPageBO pageBO = new SubjectMatterPageBO();
        pageBO.setName(pageParam.getName());
        pageBO.setTitle(pageParam.getTitle());
        pageBO.setType(pageParam.getType());
        pageBO.setPageIndex(pageParam.getPageIndex());
        pageBO.setPageSize(pageParam.getPageSize());
        pageBO.setLevel(pageParam.getLevel());
        IPage<SubjectMatterPO> resultPO = subjectMatterManager.page(pageBO);
        return pageQueryModel(resultPO,
                resultPO.getRecords().stream().map(InternalSubjectMatterHelper::cnvSubjectMatterPO2VO)
                        .collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<Void> delete(InternalSubjectMatterDeleteParam param) {
        Long bizId = param.getBizId();
        SubjectMatterPO currentPO = subjectMatterManager
                .getOne(Wrappers.lambdaQuery(SubjectMatterPO.class).eq(SubjectMatterPO::getBizId, bizId)
                        .eq(SubjectMatterPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(currentPO)) {
            return ResultModel.success(null);
        }
        subjectMatterManager.update(Wrappers.lambdaUpdate(SubjectMatterPO.class).eq(SubjectMatterPO::getBizId, bizId)
                .set(SubjectMatterPO::getIsDeleted, Const.ONE).set(SubjectMatterPO::getModifyDt, new Date())
                .set(SubjectMatterPO::getModifyBy, param.getOperatorId())
                .set(SubjectMatterPO::getModifyName, param.getOperatorName()));

        //如果是一级，则需要删除其下所有子级
        if (Const.ONE.equals(currentPO.getLevel())) {
            subjectMatterManager
                    .update(Wrappers.lambdaUpdate(SubjectMatterPO.class).eq(SubjectMatterPO::getParentId, bizId)
                            .set(SubjectMatterPO::getIsDeleted, Const.ONE).set(SubjectMatterPO::getModifyDt, new Date())
                            .set(SubjectMatterPO::getModifyBy, param.getOperatorId())
                            .set(SubjectMatterPO::getModifyName, param.getOperatorName()));

            return ResultModel.success(null);
        }

        //如果是二级,需要查询其父级是否仍有其他子级
        List<SubjectMatterPO> otherSons = subjectMatterManager.list(Wrappers.lambdaQuery(SubjectMatterPO.class)
                .eq(SubjectMatterPO::getParentId, currentPO.getParentId()).eq(SubjectMatterPO::getIsDeleted, Const.ZERO)
                .ne(SubjectMatterPO::getBizId, bizId));
        if (CollectionUtils.isNotEmpty(otherSons)) {
            return ResultModel.success(null);
        }

        //父级无子级，更新父级的isHaveChild字段
        subjectMatterManager.update(Wrappers.lambdaUpdate(SubjectMatterPO.class)
                .eq(SubjectMatterPO::getBizId, currentPO.getParentId()).set(SubjectMatterPO::getIsHaveChild, Const.ZERO)
                .set(SubjectMatterPO::getModifyDt, new Date()).set(SubjectMatterPO::getModifyBy, param.getOperatorId())
                .set(SubjectMatterPO::getModifyName, param.getOperatorName()));

        return ResultModel.success(null);
    }

    @Transactional(rollbackFor = Throwable.class)
    public ResultModel<Void> deleteSubject(InternalSubjectDeleteParam param) {
        Long rootId = param.getBizId();
        //删除题材表中的一级题材及其下所有子级
        subjectMatterManager.update(Wrappers.lambdaUpdate(SubjectMatterPO.class)
                .eq(SubjectMatterPO::getRootId, rootId)
                .set(SubjectMatterPO::getIsDeleted, Const.ONE)
                .set(SubjectMatterPO::getModifyDt, new Date())
                .set(SubjectMatterPO::getModifyBy, param.getOperatorId())
                .set(SubjectMatterPO::getModifyName, param.getOperatorName()));
        //删除题材素材关联表中的题材下的素材
        subjectMaterialManager.remove(Wrappers.lambdaQuery(SubjectMaterialPO.class).eq(SubjectMaterialPO::getMatterId,rootId));
        return ResultModel.success(null);
    }

    public ResultModel<List<InternalSubjectMatterVO>> listSons(Long parentId) {
        List<SubjectMatterPO> sonPOs = subjectMatterManager
                .list(Wrappers.lambdaQuery(SubjectMatterPO.class).eq(SubjectMatterPO::getParentId, parentId)
                        .eq(SubjectMatterPO::getIsDeleted, Const.ZERO).orderByDesc(SubjectMatterPO::getCreateDt));
        if (CollectionUtils.isEmpty(sonPOs)) {
            return ResultModel.success(Collections.emptyList());
        }

        return ResultModel.success(
                sonPOs.stream().map(InternalSubjectMatterHelper::cnvSubjectMatterPO2VO).collect(Collectors.toList()));
    }
}
