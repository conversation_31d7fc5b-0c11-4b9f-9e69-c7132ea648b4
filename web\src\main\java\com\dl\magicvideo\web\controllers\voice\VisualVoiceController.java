package com.dl.magicvideo.web.controllers.voice;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.web.controllers.voice.param.QueryVmVoiceParam;
import com.dl.magicvideo.web.controllers.voice.param.TtsParam;
import com.dl.magicvideo.web.controllers.voice.param.TtsSubtitleMarkParam;
import com.dl.magicvideo.web.controllers.voice.vo.DmVoiceVO;
import com.dl.magicvideo.web.controllers.voice.vo.GenericVoiceVO;
import com.dl.magicvideo.web.controllers.voice.vo.TtsSubtitleMarkResultVO;
import com.dl.magicvideo.web.controllers.voice.vo.TtsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @describe: 语音接口
 * @author: zhousx
 * @date: 2023/5/6 11:47
 */
@Slf4j
@RestController
@RequestMapping("/visual/voice")
@Api("智能语音")
public class VisualVoiceController {
    @Autowired
    private VisualVoiceProcess visualVoiceProcess;

    @PostMapping("/producetts")
    @ApiOperation("语音合成")
    public ResultModel<TtsVO> produceTts(@RequestBody @Validated TtsParam param) {
        return visualVoiceProcess.produceTts(param);
    }

    @PostMapping("/genericvoicelist")
    @ApiOperation("TTS音色列表")
    public ResultModel<List<GenericVoiceVO>> genericVoiceList() {
        return visualVoiceProcess.genericVoiceList();
    }

    @PostMapping("/digitalmanvoicelist")
    @ApiOperation("数字人音色列表")
    public ResultModel<List<DmVoiceVO>> digitalManVoiceList(@RequestBody QueryVmVoiceParam param) {
        return visualVoiceProcess.digitalManVoiceList(param);
    }

    @PostMapping("/subtitlemark")
    @ApiOperation("字幕标记")
    public ResultModel<TtsSubtitleMarkResultVO> subtitleMark(@RequestBody @Validated TtsSubtitleMarkParam param){
        return visualVoiceProcess.subtitleMark(param);
    }

}
