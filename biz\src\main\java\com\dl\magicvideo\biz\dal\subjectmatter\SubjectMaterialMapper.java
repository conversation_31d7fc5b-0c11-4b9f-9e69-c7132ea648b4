package com.dl.magicvideo.biz.dal.subjectmatter;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.subjectmatter.param.RandSubjectMaterialParam;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:27
 */
@BaseDao
public interface SubjectMaterialMapper extends BaseMapper<SubjectMaterialPO> {
    /**
     * 题材Id关联的随机素材内容
     * @param param
     * @return
     */
    List<SubjectMaterialPO> randomMaterial(@Param("param") RandSubjectMaterialParam param);
}
