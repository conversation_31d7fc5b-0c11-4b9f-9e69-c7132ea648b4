package com.dl.magicvideo.biz.manager.visual.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: TemplateAddBO
 * @author: zhousx
 * @date: 2023/2/1 15:03
 */
@Data
public class VisualTemplateBO {
    private Long id;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板状态 0-启用 1-禁用
     */
    private Integer status;

    /**
     * 模板封面
     */
    private String coverUrl;

    /**
     * 尺寸
     */
    private String resolution;

    /**
     * 横版/竖版
     */
    private String resolutionType;

    /**
     * tts配置
     */
    private String ttsParam;

    /**
     * 背景音乐
     */
    private String bgMusic;

    /**
     * 背景音乐配置
     */
    private String bgMusicParam;

    /**
     * 替换变量
     */
    private String replaceData;

    /**
     * 模板时长 毫秒
     */
    private Long duration;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 用户名
     */
    private String creatorName;

    /**
     * 卡片
     */
    private List<CardBO> cards;

    /**
     * 是否包含理财经理信息 1 包含 0 不包含
     */
    private Integer isManager;

    /**
     * 一级分类 1.银行，2.证券，3.基金，4.理财子
     */
    private Integer firstCategory;

    /**
     * 二级分类
     */
    private Integer secondCategory;

    /**
     * 接口信息列表
     */
    @ApiModelProperty("数据网关替换变量")
    private String apiData;

    /**
     * 组件版本号，默认2.0.0
     */
    private String componentVersion;

    /**
     * 是否展示 0-否,1-是
     */
    private Integer isShow;
}
