package com.dl.magicvideo.web.controllers.material.vo;

import lombok.Data;

@Data
public class MaterialDetailVO {
    /**
     * 名称
     */
    private String title;

    /**
     * 素材类型：3-视频，6-图片，7-音频
     */
    private Integer materialType;

    /**
     * 素材Id
     */
    private String bizId;

    /**
     * 素材大小
     */
    private Long size;

    /**
     * 分辨率,1920*1680
     */
    private String resolutionRatio;

    /**
     * 创建时间
     */
    public Long createDt;

    /**
     * 创建者Id
     */
    public Long createBy;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 封面图，素材类型为视频时有值
     */
    private String logoImg;

    /**
     * 时长，单位秒，素材类型为视频或音频时有值
     */
    private Long duration;

    /**
     * 素材地址
     */
    private String url;

    /**
     * 对应的webmUrl
     */
    private String webmUrl;
}
