package com.dl.magicvideo.biz.manager.aigc.chatrecord.helper;

import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.es.aigc.chatrecord.po.EsIndexAigcChatRecord;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 16:52
 */
public class AigcChatRecordHelper {

    public static EsIndexAigcChatRecord cnvAigcChatRecordAddBO2EsIndexAigcChatRecord(AigcChatRecordAddBO input) {
        EsIndexAigcChatRecord result = new EsIndexAigcChatRecord();
        result.setUserId(input.getUserId());
        result.setTenantCode(input.getTenantCode());
        result.setType(input.getType());
        result.setContent(input.getContent());
        result.setContentType(input.getContentType());
        result.setIsDeleted(Const.ZERO);
        result.setSendDt(input.getSendDt());
        result.setCreateDt(new Date());
        result.setModifyDt(new Date());
        result.setUserId(input.getUserId());
        result.setCanProduce(input.getCanProduce());
        result.setFromTool(input.getFromTool());
        return result;
    }
}
