<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.VisualTemplateMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO">
        <result property="id" column="id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="templateId" column="template_id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="resolution" column="resolution"/>
        <result property="resolutionType" column="resolution_type"/>
        <result property="ttsParam" column="tts_param"/>
        <result property="bgMusic" column="bg_music"/>
        <result property="bgMusicParam" column="bg_music_param"/>
        <result property="replaceData" column="replace_data"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="isSys" column="is_sys"/>
        <result property="creatorName" column="creator_name"/>
        <result property="duration" column="duration"/>
        <result property="previewVideoUrl" column="preview_video_url"/>
        <result property="shortVideoUrl" column="short_video_url"/>
        <result property="shareConfState" column="share_conf_state"/>
        <result property="isManager" column="is_manager"/>
    </resultMap>

    <select id="listAuthedTemplates" resultMap="BaseResultMap">
        SELECT distinct vt.template_id, vt.id, vta.id AS vtaId, vt.tenant_code, vt.name, vt.status, vt.cover_url,
        vt.resolution,
        vt.resolution_type, vt.tts_param, vt.bg_music, vt.bg_music_param, vt.replace_data, vt.is_deleted, vt.is_sys,
        vt.creator_name, vt.duration, vta.modify_dt, vt.short_video_url, vt.preview_video_url ,vt.share_conf_state,
        vt.first_category,vt.second_category,vt.is_manager, vta.create_dt
        FROM visual_template vt JOIN visual_template_auth vta ON vt.template_id = vta.template_id
            LEFT JOIN template_tag_link ttl ON vt.template_id = ttl.template_id
        WHERE vt.is_deleted = 0
        <if test="param.templateId != null">
            AND vt.template_id = #{param.templateId}
        </if>
        <if test="param.templateIds != null and param.templateIds.size() > 0">
            AND vt.template_id in
            <foreach collection="param.templateIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.tenantCode != null">
            AND vta.tenant_code = #{param.tenantCode}
        </if>
        <if test="param.status != null">
            AND vt.status = #{param.status}
        </if>
        <if test="param.name != null and param.name != ''">
            AND vt.name like concat('%', #{param.name}, '%')
        </if>
        <if test="param.resolutionType != null and param.resolutionType != ''">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.firstCategory != null and param.firstCategory != ''">
            AND vt.first_category = #{param.firstCategory}
        </if>
        <if test="param.secondCategory != null and param.secondCategory != ''">
            AND vt.second_category = #{param.secondCategory}
        </if>
        <if test="param.isSys != null">
            AND vt.is_sys = #{param.isSys}
        </if>
        <if test="param.isPPT != null">
            AND vt.is_ppt = #{param.isPPT}
        </if>
        <if test="param.tagIds != null and param.tagIds.size() > 0">
            AND ttl.tag_id in
            <foreach collection="param.tagIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.isSync != null">
            ORDER BY vta.modify_dt DESC
        </if>
        limit #{param.offset}, #{param.pageSize}
    </select>

    <select id="countAuthedTemplates" resultType="java.lang.Integer">
        SELECT count(distinct vt.template_id)
        FROM visual_template vt JOIN visual_template_auth vta ON vt.template_id = vta.template_id
        LEFT JOIN template_tag_link ttl ON vt.template_id = ttl.template_id
        WHERE vt.is_deleted = 0
        <if test="param.templateId != null">
            AND vt.template_id = #{param.templateId}
        </if>
        <if test="param.tenantCode != null">
            AND vta.tenant_code = #{param.tenantCode}
        </if>
        <if test="param.status != null">
            AND vt.status = #{param.status}
        </if>
        <if test="param.name != null and param.name != ''">
            AND vt.name like concat('%', #{param.name}, '%')
        </if>
        <if test="param.resolutionType != null and param.resolutionType != ''">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.firstCategory != null and param.firstCategory != ''">
            AND vt.first_category = #{param.firstCategory}
        </if>
        <if test="param.secondCategory != null and param.secondCategory != ''">
            AND vt.second_category = #{param.secondCategory}
        </if>
        <if test="param.isSys != null">
            AND vt.is_sys = #{param.isSys}
        </if>
        <if test="param.isPPT != null">
            AND vt.is_ppt = #{param.isPPT}
        </if>
        <if test="param.tagIds != null and param.tagIds.size() > 0">
            AND ttl.tag_id in
            <foreach collection="param.tagIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTemplateNameMaxIndex" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(CAST(SUBSTRING_INDEX(`name`, #{sourceTemplateName}, -1) AS UNSIGNED)), 0) AS max_suffix
        FROM visual_template
        WHERE `name` REGEXP concat(#{sourceTemplateName}, '[0-9]+$');
    </select>

    <select id="listWorkingTemplates" resultMap="BaseResultMap">
        SELECT vt.*
        FROM visual_template vt LEFT JOIN template_collect tc ON vt.template_id = tc.template_id
        WHERE vt.is_deleted = 0
        <if test="param.name != null and param.name != ''">
            AND vt.name like concat('%', #{param.name}, '%')
        </if>
        <if test="param.status != null">
            AND vt.status = #{param.status}
        </if>
        <if test="param.tenantCode != null">
            AND vt.tenant_code = #{param.tenantCode}
        </if>
        <if test="param.createBy != null">
            AND vta.create_by = #{param.createBy}
        </if>
        <if test="param.isSys != null">
            AND vt.is_sys = #{param.isSys}
        </if>
        <if test="param.resolutionType != null and param.resolutionType != ''">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.shareConfState != null and param.shareConfState != ''">
            AND vt.share_conf_state = #{param.shareConfState}
        </if>
        <if test="param.firstCategory != null and param.firstCategory != ''">
            AND vt.first_category = #{param.firstCategory}
        </if>
        <if test="param.secondCategory != null and param.secondCategory != ''">
            AND vt.second_category = #{param.secondCategory}
        </if>
        <if test="param.isManager != null">
            AND vt.is_manager = #{param.isManager}
        </if>
        <if test="param.isPPTType != null">
            AND vt.is_ppt = #{param.isPPTType}
        </if>
        <if test="param.templateId != null">
            AND vt.template_id = #{param.templateId}
        </if>
        <if test="param.templateIds != null and param.templateIds.size() > 0">
            AND vt.template_id in
            <foreach collection="param.templateIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.type != null">
            AND vt.type = #{param.type}
        </if>
        <if test="param.isShow != null">
            AND vt.is_show = #{param.isShow}
        </if>
        <if test="param.collectUserId != null">
            AND tc.user_id = #{param.collectUserId}
        </if>
        <if test="param.sortType == 1">
            ORDER BY vt.name ASC, vt.create_dt DESC
        </if>
        <if test="param.sortType == 2">
            ORDER BY vt.modify_dt DESC
        </if>
        <if test="param.sortType != 1 and param.sortType != 2">
            ORDER BY vt.create_dt DESC
        </if>
        limit #{param.offset}, #{param.pageSize}
    </select>

    <select id="countWorkingTemplates" resultType="java.lang.Integer">
        SELECT count(1)
        FROM visual_template vt LEFT JOIN template_collect tc ON vt.template_id = tc.template_id
        WHERE vt.is_deleted = 0
        <if test="param.name != null and param.name != ''">
            AND vt.name like concat('%', #{param.name}, '%')
        </if>
        <if test="param.status != null">
            AND vt.status = #{param.status}
        </if>
        <if test="param.tenantCode != null">
            AND vt.tenant_code = #{param.tenantCode}
        </if>
        <if test="param.createBy != null">
            AND vta.create_by = #{param.createBy}
        </if>
        <if test="param.isSys != null">
            AND vt.is_sys = #{param.isSys}
        </if>
        <if test="param.resolutionType != null and param.resolutionType != ''">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.shareConfState != null and param.shareConfState != ''">
            AND vt.share_conf_state = #{param.shareConfState}
        </if>
        <if test="param.firstCategory != null and param.firstCategory != ''">
            AND vt.first_category = #{param.firstCategory}
        </if>
        <if test="param.secondCategory != null and param.secondCategory != ''">
            AND vt.second_category = #{param.secondCategory}
        </if>
        <if test="param.isManager != null">
            AND vt.is_manager = #{param.isManager}
        </if>
        <if test="param.isPPTType != null">
            AND vt.is_ppt = #{param.isPPTType}
        </if>
        <if test="param.templateId != null">
            AND vt.template_id = #{param.templateId}
        </if>
        <if test="param.templateIds != null and param.templateIds.size() > 0">
            AND vt.template_id in
            <foreach collection="param.templateIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.type != null">
            AND vt.type = #{param.type}
        </if>
        <if test="param.isShow != null">
            AND vt.is_show = #{param.isShow}
        </if>
        <if test="param.collectUserId != null">
            AND tc.user_id = #{param.collectUserId}
        </if>
        <if test="param.sortType == 1">
            ORDER BY vt.name ASC, vt.create_dt DESC
        </if>
        <if test="param.sortType == 2">
            ORDER BY vt.modify_dt DESC
        </if>
        <if test="param.sortType != 1 and param.sortType != 2">
            ORDER BY vt.create_dt DESC
        </if>
    </select>
</mapper>
