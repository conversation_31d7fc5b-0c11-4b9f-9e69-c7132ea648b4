package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.common.util.PlaceHolderUtils;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.AigcChatRecordManager;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordProduceFailBO;
import com.dl.magicvideo.biz.manager.sse.dto.SSEProduceEvent;
import com.dl.magicvideo.biz.manager.sse.enums.SSEEventEnum;
import com.dl.magicvideo.biz.manager.util.ProduceFailUtil;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobCallbackManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.VisualProduceJobExtendBO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.biz.mq.dto.ProduceFailDTO;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-07 19:35
 */
@Component
public class ProduceFailConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProduceFailConsumer.class);

    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;

    @Resource
    private VisualProduceJobCallbackManager visualProduceJobCallbackManager;

    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    @Resource
    private AigcChatRecordManager aigcChatRecordManager;
    @Resource
    private RedisUtil redisUtil;

    private OkHttpClient httpClient = new OkHttpClient();

    private static final String FAIL_ALARM_MSG =
            "{\n" + "\t\"msg_type\": \"post\",\n" + "\t\"content\": {\n" + "\t\t\"post\": {\n" + "\t\t\t\"zh_cn\": {\n"
                    + "\t\t\t\t\"title\": \"视频合成失败报警\",\n" + "\t\t\t\t\"content\": [\n" + "\t\t\t\t\t[{\n"
                    + "\t\t\t\t\t\t\t\"tag\": \"text\",\n"
                    + "\t\t\t\t\t\t\t\"text\": \"租户:【${tenantName}${tenantCode}】, jobId:【${jobId}】, jobName:【${jobName}】, 发起人:【${creatorName}】, 【${createDt}】发起视频合成, 【${failDt}】合成失败, 失败原因:【${failReason}】 \"\n"
                    + "\t\t\t\t\t\t}\n" + "\t\t\t\t\t]\n" + "\t\t\t\t]\n" + "\t\t\t}\n" + "\t\t}\n" + "\t}\n" + "}";

    @StreamListener("producefailconsumer")
    public void consume(@Payload ProduceFailDTO input) {
        LOGGER.info("收到视频合成失败消息，input:{}", JSONUtil.toJsonStr(input));
        //1.更新任务的失败原因
        VisualProduceJobExtendBO updateBO = new VisualProduceJobExtendBO();
        updateBO.setBizId(input.getJobId());
        updateBO.setFailReason(input.getFailReason());
        visualProduceJobExtendManager.saveOrUpdateExtend(updateBO);

        //2.查询作品信息
        VisualProduceJobPO jobPO = visualProduceJobManager.getOne(Wrappers.lambdaQuery(VisualProduceJobPO.class)
                .eq(VisualProduceJobPO::getJobId, input.getJobId()).select(VisualProduceJobPO.class,
                        po -> !"replace_data".equals(po.getColumn()) && !"preview_data".equals(po.getColumn())
                                && !"template_data".equals(po.getColumn()) && !"api_data".equals(po.getColumn())));

        //3.发送告警
        this.alarm(input, jobPO);

        //4.aigc作品插入聊天记录
        this.aigcJobAddChatRecord(input,jobPO);

        //5.回调第三方
        this.callbackThird(input, jobPO);

        // 6.当接收到消息时，通过SSE发送给所有连接的客户端
        handleSSEMessage(input);

    }

    private void handleSSEMessage(ProduceFailDTO input) {
        SSEProduceEvent event = new SSEProduceEvent();
        event.setJobId(input.getJobId().toString());
        event.setStatus(3);
        event.setTenantCode(input.getTenantCode());
        StringBuilder sseMsg = new StringBuilder();
        sseMsg.append("event:").append(SSEEventEnum.PRODUCE.getDesc());
        sseMsg.append("\n");
        sseMsg.append("data:").append(JsonUtils.toJSON(event));
        sseMsg.append("\n\n");
        //推送所有的key
        Set<String> sseSet = redisUtil.getSet(Const.VISUAL_SSE_QUEUE_KEY_LIST);
        for (String key : sseSet){
            redisUtil.leftPush(key, sseMsg.toString());
        }
    }

    private void callbackThird(ProduceFailDTO input, VisualProduceJobPO jobPO) {
        try {
            //回调第三方
            visualProduceJobCallbackManager.callbackThird(input.getJobId(), input.getTenantCode(), jobPO);
        } catch (Exception e) {
            LOGGER.error("视频合成失败的消费者内，回调第三方 发生异常! input:{},e:{}", JSONUtil.toJsonStr(input), e);
        }
    }

    /**
     * 告警
     *
     * @param input
     */
    private void alarm(ProduceFailDTO input, VisualProduceJobPO jobPO) {
        Properties properties = new Properties();
        properties.put("tenantName", jobPO.getTenantName());
        properties.put("tenantCode", jobPO.getTenantCode());
        properties.put("jobId", jobPO.getJobId() + "");
        properties.put("jobName", jobPO.getName());
        properties.put("creatorName", jobPO.getCreatorName());
        properties.put("createDt", DateUtil.format(jobPO.getCreateDt(), DateUtil.Y_M_D_H_M_S));
        properties.put("failDt", DateUtil.format(new Date(), DateUtil.Y_M_D_H_M_S));
        String failReason = input.getFailReason();
        if (Objects.isNull(failReason)) {
            failReason = "";
        } else if (StringUtils.isNotBlank(failReason)) {
            failReason = failReason.replaceAll("\n", "\\n").replaceAll("\r", "\\r").replaceAll("\t", "\\t");
        }
        properties.put("failReason", failReason);

        String noticeMsg = PlaceHolderUtils.resolveValue(FAIL_ALARM_MSG, properties);

        LOGGER.info("jobId:{},,,noticeMsg:{}", jobPO.getJobId(), noticeMsg);
    }

    /**
     * aigc作品插入聊天记录
     *
     * @param jobPO
     */
    private void aigcJobAddChatRecord(ProduceFailDTO failDTO, VisualProduceJobPO jobPO) {
        VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                        .eq(VisualProduceJobExtendPO::getProduceJobId, jobPO.getJobId()));
        if (!JobTypeEnum.AIGC.getType().equals(jobExtendPO.getType())) {
            return;
        }

        AigcChatRecordAddBO recordAddBO = new AigcChatRecordAddBO();
        try {
            recordAddBO.setUserId(jobPO.getCreateBy());
            recordAddBO.setTenantCode(jobPO.getTenantCode());
            recordAddBO.setSendDt(new Date());
            recordAddBO.setType(AigcChatRecordTypeEnum.SYSTEM.getType());
            recordAddBO.setContentType(AigcChatRecordContentTypeEnum.PRODUCE_FAIL.getType());
            if (VisualProduceJobSourceEnum.AIGC_PRODUCE_HOT_EVENT_SUBJECT_MATTER_TOOL.getCode()
                    .equals(jobPO.getSource())) {
                recordAddBO.setFromTool(AigcChatToolEnum.HOT_EVENT_SUBJECT_MATTER_TOOL.getCode());
            } else {
                recordAddBO.setFromTool(AigcChatToolEnum.COMMON_TOOL.getCode());
            }

            AigcChatRecordProduceFailBO failBO = new AigcChatRecordProduceFailBO();
            failBO.setJobId(jobPO.getJobId());
            failBO.setVideoName(jobPO.getName());
            failBO.setWrapperFailReason(ProduceFailUtil.wrapperFailReason(failDTO.getFailReason()));
            recordAddBO.setContent(JSONUtil.toJsonStr(failBO));

            aigcChatRecordManager.add(recordAddBO);
        } catch (Exception e) {
            LOGGER.error("视频合成失败的消费者内，aigc作品插入聊天记录 发生异常! jobId:{},,,recordAddBO:{},e:{}", jobPO.getJobId(),
                    JSONUtil.toJsonStr(recordAddBO), e);
        }
    }

    public static void main(String[] args) {
        Properties properties = new Properties();
        properties.put("tenantName", "定力");
        properties.put("tenantCode", "DL");
        properties.put("jobId", "1163721388169401507");
        properties.put("jobName", "AS阿达阿萨德_2024-03-22");
        properties.put("createDt", DateUtil.format(new Date(), DateUtil.Y_M_D_H_M_S));
        properties.put("failDt", DateUtil.format(new Date(), DateUtil.Y_M_D_H_M_S));
        properties.put("failReason", "视频分段合成失败。错误原因:seek超时次数已超过阈值:3");

        String noticeMsg = PlaceHolderUtils.resolveValue(FAIL_ALARM_MSG, properties);
        System.out.println(noticeMsg);
    }
}
