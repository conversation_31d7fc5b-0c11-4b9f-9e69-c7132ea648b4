package com.dl.magicvideo.biz.dal.visual;

import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.visual.po.VisualCardPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【visual_card】的数据库操作Mapper
* @createDate 2023-04-24 16:16:02
* @Entity com.dl.magicvideo.biz.dal.visual.po.VisualCard
*/
@BaseDao
public interface VisualCardMapper extends BaseMapper<VisualCardPO> {
    /**
     * 功能描述: <br>
     * @Param: [list]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/4/26 10:29
     */
    void batchUpdate(@Param("list")List<VisualCardPO> list);
}




