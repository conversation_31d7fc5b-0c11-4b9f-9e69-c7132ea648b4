<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.VisualCardMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.visual.po.VisualCardPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="cardId" column="card_id" jdbcType="BIGINT"/>
            <result property="templateId" column="template_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="coverUrl" column="cover_url" jdbcType="VARCHAR"/>
            <result property="resolution" column="resolution" jdbcType="VARCHAR"/>
            <result property="duration" column="duration" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="renderData" column="render_data" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="modifyDt" column="modify_dt" jdbcType="TIMESTAMP"/>
            <result property="modifyBy" column="modify_by" jdbcType="BIGINT"/>
            <result property="isDeleted" column="is_deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,card_id,template_id,
        name,cover_url,resolution,
        duration,status,render_data,
        sort,create_dt,create_by,
        modify_dt,modify_by,is_deleted
    </sql>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update visual_card
            set
            <if test="item.name != null and item.name != '' ">
                name = #{item.name},
            </if>
            <if test="item.coverUrl != null and item.coverUrl != '' ">
                coverUrl = #{item.coverUrl},
            </if>
            <if test="item.resolution != null and item.resolution != '' ">
                resolution = #{item.resolution},
            </if>
            <if test="item.renderData != null and item.renderData != '' ">
                render_data = #{item.renderData},
            </if>
            <if test="item.lightEditConfigs != null and item.lightEditConfigs != '' ">
                light_edit_configs = #{item.lightEditConfigs},
            </if>
            <if test="item.duration != null ">
                duration = #{item.duration},
            </if>
            <if test="item.sort != null ">
                sort = #{item.sort},
            </if>
            modify_dt = now()
            where template_id = #{item.templateId} and is_deleted = 0
        </foreach>
    </update>
</mapper>
