package com.dl.magicvideo.web.controllers.template.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: TemplatePageQueryParam
 * @author: zhousx
 * @date: 2023/2/8 11:15
 */
@Data
public class AuthedTemplatePageQueryParam extends AbstractPageParam {
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("1-竖版 2-横版")
    private Integer resolutionType;

    @ApiModelProperty("一级菜单")
    private Integer firstCategory;

    @ApiModelProperty("二级菜单")
    private Integer secondCategory;

    @ApiModelProperty("模板状态 0-启用 1-禁用")
    private Integer status;

    @ApiModelProperty("是否是PPT模板，1是，0否")
    private Integer isPPTType;

    @ApiModelProperty("标签ID")
    private List<Long> tagIds;
}
