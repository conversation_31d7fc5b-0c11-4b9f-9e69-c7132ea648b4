package com.dl.magicvideo.web.controllers.internal.account;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.web.controllers.internal.account.param.CreateAccountParamDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-27 15:40
 */
@Api("租户试用账户控制器")
@RestController
@RequestMapping("/visual/internal/accounttenanttrial")
public class AccountTenantTrialInternalController {

    @Resource
    private AccountTenantTrialManager accountTenantTrialManager;

    @ApiOperation("创建试用账户")
    @PostMapping("/createaccount")
    public ResultModel<Void> createAccount(@RequestBody CreateAccountParamDTO param) {
        accountTenantTrialManager.createAccount(param.getTenantCode(),param.getInitBalance());
        return ResultModel.success(null);
    }

}
