package com.dl.magicvideo.web.controllers.subjectmatter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dl.aiservice.share.aichat.consts.AiChatKimiConst;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.PlaceHolderUtils;
import com.dl.magicvideo.biz.dal.subjectmatter.param.RandSubjectMaterialParam;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.biz.manager.aigc.chat.AigcChatManager;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatRequestBO;
import com.dl.magicvideo.biz.manager.aigc.chat.bo.AigcSingleChatResponseBO;
import com.dl.magicvideo.biz.manager.aigc.properties.AigcPropertites;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMaterialManager;
import com.dl.magicvideo.biz.manager.subjectmatter.SubjectMatterManager;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterJoinPageBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterPageBO;
import com.dl.magicvideo.biz.manager.visual.enums.MaterialTypeEnum;
import com.dl.magicvideo.biz.manager.visual.enums.MatterTypeEnum;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.subjectmatter.helper.SubjectMatterHelper;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectMaterialParam;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectMatterAiMatchParam;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectMatterQueryParam;
import com.dl.magicvideo.web.controllers.subjectmatter.param.SubjectNameParam;
import com.dl.magicvideo.web.controllers.subjectmatter.vo.SubjectMaterialVO;
import com.dl.magicvideo.web.controllers.subjectmatter.vo.SubjectMatterVO;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-01 13:55
 */
@Component
public class SubjectMatterProcess extends AbstractController {

    @Resource
    private SubjectMatterManager subjectMatterManager;

    @Resource
    private SubjectMaterialManager subjectMaterialManager;

    @Resource
    private AigcChatManager aigcChatManager;

    @Resource
    private AigcPropertites aigcPropertites;

    @Resource
    private OperatorUtil operatorUtil;

    public ResultModel<List<SubjectMatterVO>> latest(SubjectMatterQueryParam pageParam) {
        if (StringUtils.isNotBlank(pageParam.getStockCode())) {
            SubjectMatterJoinPageBO pageBO = new SubjectMatterJoinPageBO();
            pageBO.setPageIndex(Const.ONE);
            pageBO.setPageSize(pageParam.getNumber());
            pageBO.setStockCode(pageParam.getStockCode());
            pageBO.setType(pageParam.getType());
            ResponsePageQueryDO<List<SubjectMatterPO>> resultDO = subjectMatterManager.joinPage(pageBO);

            return ResultModel.success(resultDO.getDataResult().stream().map(SubjectMatterHelper::cnvSubjectMatterPO2VO)
                    .collect(Collectors.toList()));
        }

        SubjectMatterPageBO pageBO = new SubjectMatterPageBO();
        pageBO.setName(pageParam.getName());
        pageBO.setPageIndex(Const.ONE);
        pageBO.setPageSize(pageParam.getNumber());
        pageBO.setType(pageParam.getType());
        IPage<SubjectMatterPO> resultPO = subjectMatterManager.page(pageBO);
        return ResultModel.success(resultPO.getRecords().stream().map(SubjectMatterHelper::cnvSubjectMatterPO2VO)
                .collect(Collectors.toList()));
    }

    public ResultModel<List<SubjectMaterialVO>> findMaterialBySubjectId(SubjectMaterialParam param) {
        List<SubjectMaterialVO> voList = new ArrayList<>();

        //视频
        RandSubjectMaterialParam randSubjectMaterialParam = new RandSubjectMaterialParam();
        randSubjectMaterialParam.setMatterId(param.getSubjectBizId());
        randSubjectMaterialParam.setLimit(param.getVideoLimit());
        randSubjectMaterialParam.setMaterialType(MaterialTypeEnum.AUDIT.getCode());
        List<SubjectMaterialPO> videoList = subjectMaterialManager.randomMaterial(randSubjectMaterialParam);
        if (!CollectionUtils.isEmpty(videoList)){
            videoList.forEach(e->{
                SubjectMaterialVO vo = new SubjectMaterialVO();
                vo.setDuration(e.getDuration());
                vo.setUrl(e.getUrl());
                vo.setMaterialType(e.getMaterialType());
                vo.setResolutionRatio(e.getResolution());
                voList.add(vo);
            });
        }

        //图片
        RandSubjectMaterialParam imageMaterialParam = new RandSubjectMaterialParam();
        imageMaterialParam.setMatterId(param.getSubjectBizId());
        imageMaterialParam.setLimit(param.getImageLimit());
        imageMaterialParam.setMaterialType(MaterialTypeEnum.FOREVER.getCode());
        List<SubjectMaterialPO> imageList = subjectMaterialManager.randomMaterial(imageMaterialParam);
        if (!CollectionUtils.isEmpty(imageList)){
            imageList.forEach(e->{
                SubjectMaterialVO vo = new SubjectMaterialVO();
                vo.setDuration(e.getDuration());
                vo.setUrl(e.getUrl());
                vo.setMaterialType(e.getMaterialType());
                vo.setResolutionRatio(e.getResolution());
                voList.add(vo);
            });
        }
        return ResultModel.success(voList);
    }

    public ResultModel<List<SubjectMatterVO>> findSubjectMatterByName(SubjectNameParam param) {
//        题材类型，1-个股题材库，2-题材库
        List<SubjectMatterPO> list = subjectMatterManager.lambdaQuery()
                .eq(SubjectMatterPO::getType, MatterTypeEnum.TICAIKU.getCode())
                .eq(SubjectMatterPO::getLevel, Const.ONE)
                .eq(SubjectMatterPO::getIsDeleted, Const.ZERO)
                .like(StringUtils.isNotBlank(param.getName()), SubjectMatterPO::getName, param.getName())
                .list();
        Assert.isTrue(!CollectionUtils.isEmpty(list), "未匹配到结果");
        List<SubjectMatterVO> result = new ArrayList<>();
        list.forEach(e -> {
            SubjectMatterVO vo = new SubjectMatterVO();
            vo.setName(e.getName());
            vo.setLevel(e.getLevel());
            vo.setBizId(e.getBizId().toString());
            result.add(vo);
        });
        return ResultModel.success(result);
    }

    public ResultModel<SubjectMatterVO> aiMatch(SubjectMatterAiMatchParam param) {
        //1.查询所有一级题材库
        List<SubjectMatterPO> list = subjectMatterManager.lambdaQuery()
                .eq(SubjectMatterPO::getType, MatterTypeEnum.TICAIKU.getCode()).eq(SubjectMatterPO::getLevel, Const.ONE)
                .eq(SubjectMatterPO::getIsDeleted, Const.ZERO)
                .list();
        //提取所有的title
        List<String> subjectMatterTitleList = list.stream().map(SubjectMatterPO::getTitle).collect(Collectors.toList());

        //2.构建请求参数
        Properties properties = new Properties();
        properties.put(AigcPropertites.TEXT, param.getText());
        properties.put(AigcPropertites.SUBJECT_MATTER_TITLE, String.join("、", subjectMatterTitleList));
        //完整预设文案
        String wholePresupposeText = PlaceHolderUtils
                .resolveValue(aigcPropertites.getAigcSubjectMatterMatchPresupposeText(), properties);

        //3.调用ai对话服务传入消息，接口要返回ai的响应结果
        AigcSingleChatRequestBO singleChatRequestBO = new AigcSingleChatRequestBO();
        singleChatRequestBO.setUserId(operatorUtil.getOperator());
        singleChatRequestBO.setUserMessage(wholePresupposeText);
        singleChatRequestBO.setModel(AiChatKimiConst.MOONSHOT_8K);
        singleChatRequestBO.setRespMaxToken(Const.ONE_ZERO_TWO_FOUR);
        AigcSingleChatResponseBO singleChatResponseBO = aigcChatManager
                .singleChat(operatorUtil.getTenantCode(), singleChatRequestBO);

        String respTitle = singleChatResponseBO.getContent();
        if (StringUtils.isBlank(respTitle)) {
            return ResultModel.success(null);
        }

        //4.将题材库list转换为map，key-title  用ai返回的title反查
        Map<String, SubjectMatterPO> subjectMatterMap = list.stream()
                .collect(Collectors.toMap(SubjectMatterPO::getTitle, Function.identity(), (s1, s2) -> s1));
        SubjectMatterPO chosenSubjectMatterPO = subjectMatterMap.get(respTitle);
        return ResultModel.success(SubjectMatterHelper.cnvSubjectMatterPO2VO(chosenSubjectMatterPO));
    }

}
