package com.dl.magicvideo.biz.manager.visual.enums;

public enum TemplateLightEditConfigEnum {

    /**
     * 图片
     */
    IMAGE(0, "image"),
    /**
     * 视频
     */
    VIDEO(1, "video"),
    /**
     * 数字人文本
     */
    ANCHOR_CONTENT(2, "anchorContent"),
    /**
     * 图表
     */
    CHART(3, "chart"),
    /**
     * 图表
     */
    CHART_LET(4, "chartlet"),
    /**
     * 文本
     */
    TEXT(5, "text"),
    /**
     * tts文本
     */
    TTS_CONTENT(6, "ttsContent"),
    ;

    private Integer code;
    private String desc;

    TemplateLightEditConfigEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
