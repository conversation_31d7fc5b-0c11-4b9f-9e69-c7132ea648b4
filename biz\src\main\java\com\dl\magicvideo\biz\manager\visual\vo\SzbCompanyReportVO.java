package com.dl.magicvideo.biz.manager.visual.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上证报公司季报模板数据
 */
@Data
public class SzbCompanyReportVO {
    /**
     * 公司代码
     */
    @ApiModelProperty("公司代码")
    private String secucode;
    /**
     * 公司简称
     */
    @ApiModelProperty("公司简称")
    private String secuabbr;
    /**
     * 报告描述，如：2022年度财报
     */
    @ApiModelProperty("报告描述，如：2022年度财报")
    private String reportDesc;

    /**
     * 所属行业（申万），如食品饮料-白酒Ⅱ：
     */
    @ApiModelProperty("所属行业（申万），如：食品饮料-白酒Ⅱ")
    private String swIndustry;

    /**
     * 封面口播文案
     */
    @ApiModelProperty("封面口播文案")
    private String coverTts;

    /**
     * 营业总收入，亿元
     */
    @ApiModelProperty("营业总收入，亿元")
    private String operatingRevenue;

    /**
     * 营业总收入口播文案
     */
    @ApiModelProperty("营业总收入口播文案")
    private String operatingRevenueTts;

    /**
     * 营业总收入口单位
     */
    @ApiModelProperty("营业总收入单位")
    private String operatingRevenueUnit;

    /**
     * 营业总收入增长标志
     */
    @ApiModelProperty("营业总收入增长标志")
    private boolean operatingRevenueIncreaseFlag;
    /**
     * 营业总收入历史图表，指标增长时有返回
     */
    @ApiModelProperty("营业总收入历史图表，指标增长时有返回")
    private ValueFormVO<String> operatingRevenueHisForm;

    /**
     * 营业总收入增长口播文案
     */
    @ApiModelProperty("营业总收入增长口播文案")
    private String operatingRevenueFlagTts;

    /**
     * 归母净利润
     */
    @ApiModelProperty("归母净利润")
    private String netProfit;

    /**
     * 归母净利润单位
     */
    @ApiModelProperty("归母净利润单位")
    private String netProfitUnit;
    /**
     * 归母净利润口播文案
     */
    @ApiModelProperty("归母净利润口播文案")
    private String netProfitTts;
    /**
     * 归母净利润增长标志
     */
    @ApiModelProperty("归母净利润增长标志")
    private boolean netProfitIncreaseFlag;
    /**
     * 归母净利润历史图表，指标增长时有返回
     */
    @ApiModelProperty("归母净利润历史图表，指标增长时有返回")
    private ValueFormVO<String> netProfitHisForm;

    /**
     * 归母净利润增长口播文案
     */
    @ApiModelProperty("归母净利润增长口播文案")
    private String netProfitFlagTts;

    /**
     * 销售毛利率，亿元
     */
    @ApiModelProperty("销售毛利率，亿元")
    private String grossIncomeRatio;

    /**
     * 销售毛利率口播文案
     */
    @ApiModelProperty("销售毛利率口播文案")
    private String grossIncomeRatioTts;
    /**
     * 销售毛利率增长标志
     */
    @ApiModelProperty("销售毛利率增长标志")
    private boolean grossIncomeRatioIncreaseFlag;

    /**
     * 销售毛利率历史图表，指标增长时有返回
     */
    @ApiModelProperty("销售毛利率历史图表，指标增长时有返回")
    private ValueFormVO<String> grossIncomeRatioHisForm;

    /**
     * 销售毛利率增长口播文案
     */
    @ApiModelProperty("销售毛利率增长口播文案")
    private String grossIncomeRatioFlagTts;

    /**
     * 基本每股收益，元
     */
    @ApiModelProperty("基本每股收益，元")
    private String basicEPS;

    /**
     * 基本每股收益口播文案
     */
    @ApiModelProperty("基本每股收益口播文案")
    private String basicEPSTts;

    /**
     * 基本每股收益增长标志
     */
    @ApiModelProperty("基本每股收益增长标志")
    private boolean basicEPSIncreaseFlag;

    /**
     * 基本每股收益历史图表，指标增长时有返回
     */
    @ApiModelProperty("基本每股收益历史图表，指标增长时有返回")
    private ValueFormVO<String> basicEPSHisForm;

    /**
     * 基本每股收益增长口播文案
     */
    @ApiModelProperty("基本每股收益增长口播文案")
    private String basicEPSFlagTts;


    /**
     * 净资产收益率，如：32.41%
     */
    @ApiModelProperty("净资产收益率，如：32.41%")
    private String profitabilityRoe;

    /**
     * 净资产收益率口播文案
     */
    @ApiModelProperty("净资产收益率口播文案")
    private String profitabilityRoeTts;

    /**
     * 净资产收益率增长标志
     */
    @ApiModelProperty("净资产收益率增长标志")
    private boolean profitabilityRoeIncreaseFlag;

    /**
     * 净资产收益率历史图表，指标增长时有返回
     */
    @ApiModelProperty("净资产收益率历史图表，指标增长时有返回")
    private ValueFormVO<String> profitabilityRoeHisForm;

    /**
     * 净资产收益率增长口播文案
     */
    @ApiModelProperty("净资产收益率增长口播文案")
    private String profitabilityRoeFlagTts;

    /**
     * 经营活动现金流净额，亿元
     */
    @ApiModelProperty("经营活动现金流净额，亿元")
    private String jyhdxjlje;

    /**
     * 经营活动现金流净额口播文案
     */
    @ApiModelProperty("经营活动现金流净额口播文案")
    private String jyhdxjljeTts;

    /**
     * 经营活动现金流净额口播单位
     */
    @ApiModelProperty("经营活动现金流净额单位")
    private String jyhdxjljeUnit;

    /**
     * 经营活动现金流净额增长标志
     */
    @ApiModelProperty("经营活动现金流净额增长标志")
    private boolean jyhdxjljeIncreaseFlag;

    /**
     * 经营活动现金流净额历史图表，指标增长时有返回
     */
    @ApiModelProperty("经营活动现金流净额历史图表，指标增长时有返回")
    private ValueFormVO<String> jyhdxjljeHisForm;

    /**
     * 经营活动现金流净额增长口播文案
     */
    @ApiModelProperty("经营活动现金流净额增长口播文案")
    private String jyhdxjljeFlagTts;

    /**
     * 经营活动现金流净额/营业利润增长标志
     */
    @ApiModelProperty("经营活动现金流净额/营业利润增长标志")
    private boolean operatingProfitIncreaseFlag;

    /**
     * 经营活动现金流净额/营业利润历史图表，指标增长时有返回
     */
    @ApiModelProperty("经营活动现金流净额/营业利润历史图表，指标增长时有返回")
    private ValueFormVO<String> operatingProfitFlowHisForm;

    /**
     * 经营活动现金流净额/营业利润增长口播文案
     */
    @ApiModelProperty("经营活动现金流净额/营业利润增长口播文案")
    private String operatingProfitFlagTts;

    /**
     * 资产负债率，如：19.42%
     */
    @ApiModelProperty("资产负债率，如：19.42%")
    private String debtAssetsRatio;

    /**
     * 资产负债率图表
     */
    @ApiModelProperty("资产负债率图表")
    private ValueFormVO<String> debtAssetsRatioForm;

    /**
     * 资产负债率口播文案
     */
    @ApiModelProperty("资产负债率口播文案")
    private String debtAssetsRatioTts;

    /**
     * 速动比率，如：3.62
     */
    @ApiModelProperty("速动比率，如：3.62")
    private String quickRatio;

    /**
     * 速动比率口播文案
     */
    @ApiModelProperty("速动比率口播文案")
    private String quickRatioTts;

    /**
     * 速动比率增长标志
     */
    @ApiModelProperty("速动比率增长标志")
    private boolean quickRatioIncreaseFlag;

    /**
     * 速动比率历史图表，指标增长时有返回
     */
    @ApiModelProperty("速动比率历史图表，指标增长时有返回")
    private ValueFormVO<String> quickRatioHisForm;

    /**
     * 速动比率增长口播文案
     */
    @ApiModelProperty("速动比率增长口播文案")
    private String quickRatioFlagTts;

    /**
     * 总资产周转率，如：0.5
     */
    @ApiModelProperty("总资产周转率，如：0.5")
    private String totalAssetTRate;

    /**
     * 总资产周转率口播文案
     */
    @ApiModelProperty("总资产周转率口播文案")
    private String totalAssetTRateTts;

    /**
     * 总资产周转率增长标志
     */
    @ApiModelProperty("总资产周转率增长标志")
    private boolean totalAssetTIncreaseFlag;

    /**
     * 总资产周转率历史图表，指标增长时有返回
     */
    @ApiModelProperty("总资产周转率历史图表，指标增长时有返回")
    private ValueFormVO<String> totalAssetTHisForm;

    /**
     * 总资产周转率增长口播文案
     */
    @ApiModelProperty("总资产周转率增长口播文案")
    private String totalAssetTFlagTts;


    /**
     * 应收账款周转率，如：35927.26次
     */
    @ApiModelProperty("应收账款周转率，如：35927.26次")
    private String aRTRate;

    /**
     * 应收账款周转率口播文案
     */
    @ApiModelProperty("应收账款周转率口播文案")
    private String aRTRateTts;

    /**
     * 应收账款周转率增长标志
     */
    @ApiModelProperty("应收账款周转率增长标志")
    private boolean aRTRateIncreaseFlag;

    /**
     * 应收账款周转率历史图表，指标增长时有返回
     */
    @ApiModelProperty("应收账款周转率历史图表，指标增长时有返回")
    private ValueFormVO<String> aRTRateHisForm;

    /**
     * 应收账款周转率增长口播文案
     */
    @ApiModelProperty("应收账款周转率增长口播文案")
    private String aRTRateFlagTts;

    /**
     * 研发投入模块是否展示标志
     */
    @ApiModelProperty("研发投入模块是否展示标志")
    private boolean totalRDInputFlag;

    /**
     * 研发投入 单位：亿元
     */
    @ApiModelProperty("研发投入 单位：亿元")
    private String totalRDInput;

    /**
     * 研发投入口播文案
     */
    @ApiModelProperty("研发投入口播文案")
    private String totalRDInputTts;

    /**
     * 研发投入单位
     */
    @ApiModelProperty("研发投入单位")
    private String totalRDInputUnit;

    /**
     * 研发投入占比标志
     */
    @ApiModelProperty("研发投入占比标志")
    private boolean rDInputRatioHisFormFlag;

    /**
     * 研发投入占比,如：0.45%
     */
    @ApiModelProperty("研发投入占比,如：0.45%")
    private String rDInputRatio;

    /**
     * 研发投入占比图表
     */
    @ApiModelProperty("研发投入占比图表")
    private ValueFormVO<String> rDInputRatioForm;

    /**
     * 研发投入占比口播文案
     */
    @ApiModelProperty("研发投入占比口播文案")
    private String rDInputRatioTts;

    /**
     * 研发投入占比历史图表，指标增长时有返回
     */
    @ApiModelProperty("研发投入占比历史图表，指标增长时有返回")
    private ValueFormVO<String> totalRDInputRatioHisForm;

    /**
     * 研发投入占比增长口播文案
     */
    @ApiModelProperty("研发投入占比增长口播文案")
    private String rDInputRatioFlagTts;

    /**
     * 主营业务占比情况（按产品）
     */
    @ApiModelProperty("主营业务占比情况（按产品）")
    private ValueFormVO<String> mainBusinessProHisForm;

    /**
     * 主营业务占比情况（按产品）口播文案
     */
    @ApiModelProperty("主营业务占比情况（按产品）口播文案")
    private String mainBusinessProTts;

    /**
     * 主营业务收入历史图表
     */
    @ApiModelProperty("主营业务收入历史图表")
    private ValueFormVO<String> mainBusinessIncomeHisForm;

    /**
     * 主营业务收入口播文案
     */
    @ApiModelProperty("主营业务收入口播文案")
    private String mainBusinessIncomeTts;

    /**
     * 股东增加户数
     */
    @ApiModelProperty("股东增加户数")
    private String shareholderIncreaseNum;

    /**
     * 股东增加户数口播文案
     */
    @ApiModelProperty("股东增加户数口播文案")
    private String shareholderIncreaseNumTts;

    /**
     * 股东户数历史图表
     */
    @ApiModelProperty("股东户数历史图表")
    private ValueFormVO<String> shareholderNumHisForm;

    /**
     * 前十大股东占比合计
     */
    @ApiModelProperty("前十大股东占比合计")
    private String shareholder10Sum;

    /**
     * 前十大股东占比合计口播文案
     */
    @ApiModelProperty("前十大股东占比合计口播文案")
    private String shareholder10SumTts;


    /**
     * 前十大股东整体比例变化
     */
    @ApiModelProperty("前十大股东整体比例变化")
    private String shareholder10Change;

    /**
     * 1-5大股东图表
     */
    @ApiModelProperty("1-5大股东图表")
    private ValueFormVO<String> shareholder10HisForm;

    /**
     * 6-10大股东图表
     */
    @ApiModelProperty("6-10大股东图表")
    private ValueFormVO<String> shareholder10HisForm2;

    /**
     * 报告期季度
     */
    @ApiModelProperty("报告期季度")
    private Integer reportQuarter;



    // 针对银行企业的相关字段 begin

    /**
     * 银行企业标志
     */
    @ApiModelProperty("银行企业标志")
    private boolean bankCorpFlag;

    /**
     * 净息差，如：2.41%
     */
    @ApiModelProperty("净息差，如：2.41%")
    private String interestAvAcAssetRatio;

    /**
     * 净息差口播文案
     */
    @ApiModelProperty("净息差口播文案")
    private String interestAvAcAssetRatioTts;

    /**
     * 净息差增长标志
     */
    @ApiModelProperty("净息差增长标志")
    private boolean interestAvAcAssetRatioIncreaseFlag;

    /**
     * 净息差历史图表，指标增长时有返回
     */
    @ApiModelProperty("净息差历史图表，指标增长时有返回")
    private ValueFormVO<String> interestAvAcAssetRatioHisForm;

    /**
     * 净息差增长口播文案
     */
    @ApiModelProperty("净息差增长口播文案")
    private String interestAvAcAssetRatioFlagTts;

    /**
     * 不良贷款率，如：2.41%
     */
    @ApiModelProperty("不良贷款率，如：2.41%")
    private String nonPerformingRatio;

    /**
     * 不良贷款率口播文案
     */
    @ApiModelProperty("不良贷款率口播文案")
    private String nonPerformingRatioTts;

    /**
     * 不良贷款率增长标志
     */
    @ApiModelProperty("不良贷款率增长标志")
    private boolean nonPerformingRatioDecreaseFlag;

    /**
     * 不良贷款率历史图表，指标下降时有返回
     */
    @ApiModelProperty("不良贷款率历史图表，指标下降时有返回")
    private ValueFormVO<String> nonPerformingRatioHisForm;

    /**
     * 不良贷款率下降口播文案
     */
    @ApiModelProperty("不良贷款率下降口播文案")
    private String nonPerformingRatioFlagTts;

    /**
     * 核心一级资本充足率，如：12.41%
     */
    @ApiModelProperty("核心一级资本充足率，如：12.41%")
    private String cet1car;

    /**
     * 核心一级资本充足率口播文案
     */
    @ApiModelProperty("核心一级资本充足率口播文案")
    private String cet1carTts;

    /**
     * 核心一级资本充足率增长标志
     */
    @ApiModelProperty("核心一级资本充足率增长标志")
    private boolean cet1carIncreaseFlag;

    /**
     * 核心一级资本充足率历史图表，指标增长时有返回
     */
    @ApiModelProperty("核心一级资本充足率历史图表，指标增长时有返回")
    private ValueFormVO<String> cet1carHisForm;

    /**
     * 核心一级资本充足率增长口播文案
     */
    @ApiModelProperty("核心一级资本充足率增长口播文案")
    private String cet1carFlagTts;

    // 针对银行企业的相关字段 end




}
