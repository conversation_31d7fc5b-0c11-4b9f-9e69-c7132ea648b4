package com.dl.magicvideo.biz.manager.basicservice;

import cn.hutool.json.JSONUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.TenantUserBindClient;
import com.dl.magicvideo.biz.client.basicservice.param.InternalTenantUserBindQueryParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 10:40
 */
@Component
public class TenantUserBindManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantUserBindManager.class);

    @Resource
    private TenantUserBindClient tenantUserBindClient;

    /**
     * 根据用户id查询外部用户id
     *
     * @param tenantCode
     * @param userId
     * @return
     */
    public String queryExtUserIdByUserId(String tenantCode, Long userId) {
        InternalTenantUserBindQueryParam param = new InternalTenantUserBindQueryParam();
        param.setUserId(userId);
        param.setTenantCode(tenantCode);
        ResultModel<String> resultModel = tenantUserBindClient.queryExtUserIdByUserId(param);
        if (!resultModel.isSuccess()) {
            LOGGER.error("根据用户id查询外部用户id失败!,param:{},resultModel:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance("根据用户id查询外部用户id失败");
        }
        return resultModel.getDataResult();
    }

}
