package com.dl.magicvideo.biz.manager.statistics;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.PageQueryDO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsEfficiencyPO;
import com.dl.magicvideo.biz.manager.statistics.bo.EfficiencyBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsEfficiencyDTO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthSearchBO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【visual_template_auth】的数据库操作Service
* @createDate 2023-06-18 10:34:13
*/
public interface StatisticsEfficiencyManager extends IService<StatisticsEfficiencyPO>, CommonService {

    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List<com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO>>
     * @Author: zhousx
     * @Date: 2023/6/18 23:48
     */
    ResponsePageQueryDO<List<StatisticsEfficiencyDTO>> pageQuery(EfficiencyBO queryDO);
}
