package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.visual.VisualProduceJobExtendMapper;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobExtendPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobExtendManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualProduceJobExtendBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.biz.manager.visual.enums.ShareConfStateEnum;
import com.dl.magicvideo.biz.manager.visual.helper.ProduceJobHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class VisualProduceJobExtendManagerImpl extends ServiceImpl<VisualProduceJobExtendMapper, VisualProduceJobExtendPO> implements VisualProduceJobExtendManager {

    @Resource
    private VisualProduceJobManager visualProduceJobManager;

    @Resource
    private VisualProduceJobExtendMapper visualProduceJobExtendMapper;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveOrUpdateExtend(VisualProduceJobExtendBO bo) {
        VisualProduceJobPO produceJobPO = visualProduceJobManager
                .getOne(Wrappers.<VisualProduceJobPO>lambdaQuery().eq(VisualProduceJobPO::getJobId, bo.getBizId())
                        .eq(VisualProduceJobPO::getIsDeleted, Const.ZERO));
        if (Objects.isNull(produceJobPO)) {
            return;
        }
        VisualProduceJobExtendPO extendPO = this.lambdaQuery()
                .eq(VisualProduceJobExtendPO::getProduceJobId, bo.getBizId())
                .eq(VisualProduceJobExtendPO::getIsDeleted, Const.ZERO).one();
        if (Objects.isNull(extendPO)) {
            // 新增
            VisualProduceJobExtendPO insertExtendPO = new VisualProduceJobExtendPO();
            insertExtendPO.setProduceJobId(produceJobPO.getJobId());
            insertExtendPO.setTenantCode(produceJobPO.getTenantCode());
            insertExtendPO.setIsDeleted(Const.ZERO);
            buildParam(bo, insertExtendPO);
            this.save(insertExtendPO);
            return;
        }
        // 更新
        buildParam(bo, extendPO);
        this.updateById(extendPO);
    }

    private static void buildParam(VisualProduceJobExtendBO bo, VisualProduceJobExtendPO po) {
        if (Objects.nonNull(bo.getShareConfState())) {
            if (Objects.isNull(po.getShareConfState()) || !po.getShareConfState()
                    .equals(ShareConfStateEnum.INTERACTIVE_SHARE.getCode())) {
                po.setShareConfState(bo.getShareConfState());
            }
        }
        if (Objects.nonNull(bo.getRecommendState())) {
            po.setRecommendState(bo.getRecommendState());
        }
        if (Objects.nonNull(bo.getRecommendEnableDt())) {
            po.setRecommendEnableDt(bo.getRecommendEnableDt());
        }
        if (Objects.nonNull(bo.getDmProduceMode())) {
            po.setDmProduceMode(bo.getDmProduceMode());
        }
        if (StringUtils.isNotBlank(bo.getFailReason())) {
            //失败原因，截取前200个字符
            po.setFailReason(StringUtils
                    .substring(bo.getFailReason(), 0, Math.min(StringUtils.length(bo.getFailReason()), 200)));
        }
        if (StringUtils.isNotBlank(bo.getExtUserId())) {
            po.setExtUserId(bo.getExtUserId());
        }
        if (Objects.nonNull(bo.getType())) {
            po.setType(bo.getType());
        }
        if (StringUtils.isNotBlank(bo.getResolution())) {
            po.setResolution(bo.getResolution());
        }

    }

    @Override
    public IPage<VisualProduceJobDTO> pageRecommend(ProduceJobSearchBO bo) {
        //1.查询已经配置启用的视频
        List<VisualProduceJobPO> visualProduceJobPOList = visualProduceJobExtendMapper.recommendJobPage(bo);
        if (CollectionUtils.isEmpty(visualProduceJobPOList)){
            return new Page<>(bo.getPageIndex(),bo.getPageSize());
        }

        //2.查询总数量
        Long total = visualProduceJobExtendMapper.recommendJobTotal(bo);

        //3.类型转化
        List<VisualProduceJobDTO> produceJobDTOList = visualProduceJobPOList.stream()
                .map(ProduceJobHelper::cnvVisualProduceJobPO2DTO).collect(Collectors.toList());

        //4.构建分页对象
        Long totalPages = (total + bo.getPageSize() - 1) / bo.getPageSize();
        IPage<VisualProduceJobDTO> result = new Page<>(bo.getPageIndex(),bo.getPageSize(),total);
        result.setRecords(produceJobDTOList);
        result.setPages(totalPages);
        return result;

    }
}




