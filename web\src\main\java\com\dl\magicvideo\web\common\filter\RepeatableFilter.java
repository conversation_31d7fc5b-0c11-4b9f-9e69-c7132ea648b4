package com.dl.magicvideo.web.common.filter;

import com.dl.magicvideo.web.common.util.RequestBodyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Repeatable 过滤器
 */
@Slf4j
public class RepeatableFilter implements Filter {
    private RequestBodyUtil requestBodyUtil;
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        //在 RepeatableFilter 中添加对multipart请求的判断，避免对文件上传请求进行包装：
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String contentType = httpRequest.getContentType();

        // 如果是multipart请求，不进行包装直接放行
        if (contentType != null && contentType.toLowerCase().contains("multipart/form-data")) {
            chain.doFilter(request, response);
            return;
        }


        ServletRequest requestWrapper = null;
        if (request instanceof HttpServletRequest && (
                StringUtils.equalsIgnoreCase(request.getContentType(), MediaType.APPLICATION_JSON_VALUE)
                        || StringUtils.equalsIgnoreCase(request.getContentType(),
                        MediaType.APPLICATION_JSON_UTF8_VALUE))) {
            requestWrapper = new RepeatedlyRequestWrapper((HttpServletRequest) request, response);
        }
        long a = System.currentTimeMillis();
        if (null == requestWrapper) {
            chain.doFilter(request, response);
        } else {
            requestBodyUtil.init(((RepeatedlyRequestWrapper) requestWrapper).getBodyString());
            chain.doFilter(requestWrapper, response);
        }
        long b = System.currentTimeMillis();
        if (request instanceof HttpServletRequest) {
            log.info("接口:{}, 耗时:{} 毫秒", ((HttpServletRequest) request).getRequestURI(), b - a);

        }
    }

    public void setRequestBodyUtil(RequestBodyUtil requestBodyUtil) {
        this.requestBodyUtil = requestBodyUtil;
    }
}
