package com.dl.magicvideo.biz.manager.subjectmatter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.subjectmatter.param.RandSubjectMaterialParam;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMaterialAddBO;

import java.util.List;

public interface SubjectMaterialManager extends IService<SubjectMaterialPO> {
    void batchAdd(Long rootId, List<SubjectMaterialAddBO> materialAddBOS);

    List<SubjectMaterialPO> randomMaterial(RandSubjectMaterialParam param);
}
