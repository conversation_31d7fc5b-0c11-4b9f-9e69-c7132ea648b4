package com.dl.magicvideo.web.controllers.font;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.manager.font.PatternFontManager;
import com.dl.magicvideo.biz.manager.font.bo.PatternFontPageBO;
import com.dl.magicvideo.biz.manager.font.dto.PatternFontDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.font.convert.PatternFontConvert;
import com.dl.magicvideo.web.controllers.font.param.PatternFontDetailParam;
import com.dl.magicvideo.web.controllers.font.param.PatternFontPageParam;
import com.dl.magicvideo.web.controllers.font.vo.PatternFontPageVO;
import com.dl.magicvideo.web.controllers.font.vo.PatternFontVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class PatternProcess extends AbstractController {

    @Autowired
    private PatternFontManager patternFontManager;

    public ResultModel<List<PatternFontPageVO>> list(){
        List<PatternFontDTO> dtoList = patternFontManager.fontList(null);
        return ResultModel.success(dtoList.stream().map(PatternFontConvert::cnvPatternFontDTO2PageVO).collect(Collectors.toList()));
    }

    public ResultModel<PatternFontVO> detail(PatternFontDetailParam param){
        PatternFontDTO dto = patternFontManager.detail(Long.valueOf(param.getBizId()));
        return ResultModel.success(PatternFontConvert.cnvPatternFontDTO2VO(dto));
    }

    public ResultPageModel<PatternFontPageVO> page(PatternFontPageParam param){
        PatternFontPageBO bo = new PatternFontPageBO();
        bo.setName(param.getName());
        bo.setType(param.getType());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        IPage<PatternFontDTO> resp = patternFontManager.fontPage(bo);
        return pageQueryModel(resp,resp.getRecords().stream().map(PatternFontConvert::cnvPatternFontDTO2PageVO).collect(
                Collectors.toList()));
    }
}
