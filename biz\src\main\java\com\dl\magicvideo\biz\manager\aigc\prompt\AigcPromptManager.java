package com.dl.magicvideo.biz.manager.aigc.prompt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.aigc.po.AigcPromptPO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptAddBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptQueryBO;
import com.dl.magicvideo.biz.manager.aigc.prompt.bo.AigcPromptUptBO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 11:39
 */
public interface AigcPromptManager extends IService<AigcPromptPO> {

    IPage<AigcPromptPO> pageQuey(AigcPromptQueryBO queryBO);

    Long add(AigcPromptAddBO addBO);

    void update(AigcPromptUptBO uptBO);

}
