package com.dl.magicvideo.web.controllers.digitalman.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @describe: GenericDigitalmanVO
 * @author: zhousx
 * @date: 2023/6/5 14:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenericDigitalmanVO {
    @ApiModelProperty("数字人id")
    private String digitalManId;

    @ApiModelProperty("渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云")
    private Integer channel;

    @ApiModelProperty("外部厂商数字人唯一标识")
    private String vmCode;

    @ApiModelProperty("数字人名称")
    private String vmName;

    @ApiModelProperty("仿真人声音代码")
    private String vmVoiceKey;

    @ApiModelProperty("数字人头像地址url")
    private String headImg;

    @ApiModelProperty("数字⼈类型：1 2d真⼈;2 3d真⼈")
    private Integer vmType;

    @ApiModelProperty("性别 1 男 2 女")
    private Integer gender;

    @ApiModelProperty("语速")
    private String speed;
}
