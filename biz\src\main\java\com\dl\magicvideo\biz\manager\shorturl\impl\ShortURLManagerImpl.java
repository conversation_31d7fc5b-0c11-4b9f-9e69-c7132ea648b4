package com.dl.magicvideo.biz.manager.shorturl.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.shorturl.ShortURLMapper;
import com.dl.magicvideo.biz.dal.shorturl.po.ShortURLPO;
import com.dl.magicvideo.biz.manager.shorturl.ShortURLManager;
import com.dl.magicvideo.biz.manager.shorturl.dto.ShortURLDTO;
import com.dl.magicvideo.biz.manager.shorturl.util.ShortUrlGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-15 16:59
 */
@Component
public class ShortURLManagerImpl extends ServiceImpl<ShortURLMapper, ShortURLPO> implements ShortURLManager {

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private ShortUrlGenerator shortUrlGenerator;

    @Value("${short.url.domain:https://test.dinglitec.com}")
    private String domain;

    /**
     * 短链锁前缀
     */
    private static final String SHORT_URL_LOCK_KEY_PREFIX = "magic_short_url_lock_key:";

    /**
     * 短链的缓存失效时间
     */
    private static final Long SHORT_URL_LOCK_TIMEOUT = 24 * 60L;

    /**
     * NULL字符串
     */
    public static final String NULL = "NULL";

    /**
     * 短链路径
     */
    public static final String SHORT_URL_PATH = "/ms/";

    @Override
    public ShortURLDTO createShortURL(String targetURL, int expireTime, String tenantCode) {
        Assert.isTrue(StringUtils.isNotBlank(targetURL), "目标地址不能为空");
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户编号不能为空");

        String code = shortUrlGenerator.createShortUrl(targetURL);
        String shortUrl = domain + SHORT_URL_PATH + code;

        ShortURLPO insertURLPO = new ShortURLPO();
        insertURLPO.setCode(code);
        insertURLPO.setShortURL(shortUrl);
        insertURLPO.setTargetURL(targetURL);
        insertURLPO.setTenantCode(tenantCode);
        if (expireTime > 0) {
            insertURLPO.setExpireDate(DateUtil.addMinute(expireTime, new Date()));
        }
        baseMapper.insert(insertURLPO);

        return cnvShortURLPO2DTO(insertURLPO);
    }

    @Override
    public ShortURLDTO getTargetURL(String code) {
        Assert.isTrue(StringUtils.isNotBlank(code), "短链编码不能为空");
        Assert.isTrue(code.length() == 6, "短链编码非法");
        Assert.isTrue(ShortUrlGenerator.isLetterDigit(code), "短链编码非法");
        //先从缓存中查找
        String key = generateKey(code);
        String value = redisUtil.get(key);
        if (NULL.equals(value)) {
            return null;
        }
        if (StringUtils.isNotBlank(value)) {
            ShortURLDTO shortURLDTO = JSONUtil.toBean(value, ShortURLDTO.class);
            return shortURLDTO;
        }
        //从db中找
        ShortURLPO shortURLPO = baseMapper.selectOne(
                Wrappers.lambdaQuery(ShortURLPO.class).eq(ShortURLPO::getCode, code)
                        .eq(ShortURLPO::getIsDeleted, Const.ZERO));
        ShortURLDTO shortURLDTO = cnvShortURLPO2DTO(shortURLPO);
        if (Objects.isNull(shortURLPO)) {
            //若不存在，则设值为"NULL",防止缓存穿透
            value = NULL;
        } else {
            value = JSONUtil.toJsonStr(shortURLDTO);
        }
        //放入缓存中
        redisUtil.set(key, value, SHORT_URL_LOCK_TIMEOUT);
        return shortURLDTO;
    }

    private static ShortURLDTO cnvShortURLPO2DTO(ShortURLPO input) {
        if (Objects.isNull(input)) {
            return null;
        }
        ShortURLDTO result = new ShortURLDTO();
        result.setShortURL(input.getShortURL());
        result.setCode(input.getCode());
        result.setTargetURL(input.getTargetURL());
        result.setExpireDate(input.getExpireDate());
        return result;
    }

    private String generateKey(String code) {
        return SHORT_URL_LOCK_KEY_PREFIX + code;
    }
}
