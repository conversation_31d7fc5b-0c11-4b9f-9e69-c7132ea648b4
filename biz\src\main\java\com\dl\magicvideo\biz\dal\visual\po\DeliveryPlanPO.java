package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

/**
 * 
 * @TableName delivery_plan
 */
@TableName(value ="delivery_plan")
@Data
public class DeliveryPlanPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 交付计划id
     */
    private Long planId;

    /**
     * 名称
     */
    private String name;

    /**
     * 简介
     */
    private String description;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 关联模板id
     */
    private Long templateId;

    /**
     * 负责人
     */
    private String director;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 触发次数，默认-1 代表无限制
     */
    private Integer maxTimes;

    /**
     * 周期，-1-无，0-天，1-周，2-月
     */
    private Integer period;

    private Integer status;

    private Integer isNotify;

    private String notifyUrl;

    private String callbackUrl;

    private Integer produceWay;

    private Integer hasPeriod;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}