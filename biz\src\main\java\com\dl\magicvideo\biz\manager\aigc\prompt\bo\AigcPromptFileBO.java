package com.dl.magicvideo.biz.manager.aigc.prompt.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-25 09:32
 */
@Data
public class AigcPromptFileBO {

    @ApiModelProperty("文件名称")
    private String name;

    @ApiModelProperty("文件url地址")
    private String url;

    /**
     * @see: com.dl.magicvideo.biz.manager.aigc.prompt.enums.AigcFileTypeEnum
     */
    @ApiModelProperty("文件类型（粗的类型，如图片、doc）")
    private Integer type;

    @ApiModelProperty("文件格式（精确的格式，如jpg、png、doc、docx）")
    private String typeFormat;

    @ApiModelProperty("文件大小，单位：字节")
    private Long size;

}
