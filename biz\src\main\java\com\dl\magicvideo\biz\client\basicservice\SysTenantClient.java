package com.dl.magicvideo.biz.client.basicservice;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.client.basicservice.intercepter.BasicServiceInterceptor;
import com.dl.magicvideo.biz.client.basicservice.param.TenantInfoParam;
import com.dl.magicvideo.biz.client.basicservice.param.TenantInfoQueryListParamDTO;
import com.dl.magicvideo.biz.client.basicservice.param.TenantReduceTrialCountParam;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-28 10:25
 */
@BaseRequest(interceptor = BasicServiceInterceptor.class)
public interface SysTenantClient {

    /**
     * 获取租户信息
     *
     * @param param
     * @return
     */
    @Post("/internal/tenant/info")
    ResultModel<AdmTenantInfoDTO> info(@JSONBody TenantInfoParam param);

    /**
     * 获取租户信息
     *
     * @param param
     * @return
     */
    @Post("/internal/tenant/list")
    ResultModel<List<AdmTenantInfoDTO>> list(@JSONBody TenantInfoQueryListParamDTO param);
}
