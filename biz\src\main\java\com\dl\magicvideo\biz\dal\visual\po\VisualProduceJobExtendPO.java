package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 作品扩展信息表
 * @TableName visual_produce_job_extend
 */
@TableName(value ="visual_produce_job_extend")
@Data
public class VisualProduceJobExtendPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    private Long produceJobId;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     * @see: ShareConfStateEnum
     */
    private Integer shareConfState;

    /**
     * 推荐使用状态 0-未启用 1-已启用
     */
    private Integer recommendState;

    /**
     * 推荐启用的时间
     */
    private Date recommendEnableDt;

    /**
     * 数字人视频合成方式。 0-模板每个卡片合成1次请求数字人合成视频，并通过ASR识别时间戳。1-模板每个卡片都请求数字人合成视频方式。
     */
    @TableField("dm_produce_mode")
    private Integer dmProduceMode;

    /**
     * 是否删除，0-否，1-是
     */
    private Integer isDeleted;

    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    @TableField("ext_user_id")
    private String extUserId;

    /**
     * 作品类型，1-常规作品，2-数据图表作品
     * @see: com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum
     */
    private Integer type;

    /**
     * 尺寸
     */
    private String resolution;

    /**
     * 其他格式的视频地址
     */
    @TableField("other_format_video_url")
    private String otherFormatVideoUrl;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}