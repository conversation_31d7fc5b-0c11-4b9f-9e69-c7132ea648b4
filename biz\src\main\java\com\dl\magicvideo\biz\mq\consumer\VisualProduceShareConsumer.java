package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.visual.po.VisualShareConfPO;
import com.dl.magicvideo.biz.manager.visual.VisualShareConfManager;
import com.dl.magicvideo.biz.manager.visual.bo.MagicVideoJobBO;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfCopyBO;
import com.dl.magicvideo.biz.manager.visual.bo.ShareConfQueryBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualShareConfDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Component
@Slf4j
public class VisualProduceShareConsumer {

    @Resource
    private VisualShareConfManager visualShareConfManager;

    @StreamListener("produceshareconfconsumer")
    public void consume(@Payload MagicVideoJobBO input) {
        log.info("收到Magic合成成功消息，保存基本转发设置，input:{}", JSONUtil.toJsonStr(input));
        shareProcess(input);
    }

    private void shareProcess(MagicVideoJobBO input) {
        //幂等判断
        List<VisualShareConfPO> shareConfPOList = visualShareConfManager.lambdaQuery()
                .eq(VisualShareConfPO::getIsDeleted, Const.ZERO)
                .eq(VisualShareConfPO::getBizType, Const.TWO)
                .eq(VisualShareConfPO::getBizId, input.getJobId())
                .list();
        if (CollectionUtils.isNotEmpty(shareConfPOList)){
            log.info("收到Magic合成成功消息，该作品已配置，重复消费，input:{}", JSONUtil.toJsonStr(input));
            return;
        }

        //1.查询是否配置基础转发设置
        ShareConfQueryBO shareConfQueryBO = new ShareConfQueryBO();
        shareConfQueryBO.setBizId(input.getTemplateId());
        shareConfQueryBO.setBizType(Const.ONE);
        VisualShareConfDTO sourceShareConf = visualShareConfManager.info(shareConfQueryBO);
        if (Objects.isNull(sourceShareConf)) {
            return;
        }

        ShareConfCopyBO shareConfCopyBO = new ShareConfCopyBO();
        shareConfCopyBO.setSourceBizId(sourceShareConf.getBizId());
        shareConfCopyBO.setSourceBizType(sourceShareConf.getBizType());
        shareConfCopyBO.setTargetBizId(input.getJobId());
        shareConfCopyBO.setTargetBizType(Const.TWO);
        shareConfCopyBO.setName(input.getName());
        //生成作品来源与目标租户编码相同
        shareConfCopyBO.setSourceTenantCode(input.getTenantCode());
        shareConfCopyBO.setTargetTenantCode(input.getTenantCode());
        visualShareConfManager.copyShareConf(shareConfCopyBO);
    }

}
