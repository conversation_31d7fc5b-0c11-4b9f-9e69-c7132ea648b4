package com.dl.magicvideo.web.controllers.aigc.prompt.vo;

import com.dl.magicvideo.web.controllers.aigc.prompt.param.AigcPromptContentParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 11:46
 */
@Data
public class AigcPromptVO {

    @ApiModelProperty("提示id")
    private String bizId;

    @ApiModelProperty("提示名称")
    private String name;

    @ApiModelProperty("提示排序")
    private Integer sort;

    @ApiModelProperty("关联文件")
    private AigcPromptFileVO relFile;

    @ApiModelProperty("内容")
    private AigcPromptContentVO content;
}
