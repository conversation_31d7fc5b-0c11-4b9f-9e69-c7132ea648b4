<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.statistics.StatisticsCountMapper">

    <resultMap id="BaseResultMap" type="com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="countId" column="count_id" jdbcType="BIGINT"/>
            <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
            <result property="statisticsValue" column="statistics_value" jdbcType="VARCHAR"/>
            <result property="statisticsTime" column="statistics_time" jdbcType="VARCHAR"/>
            <result property="createDt" column="create_dt" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="modifyDt" column="modify_dt" jdbcType="TIMESTAMP"/>
            <result property="modifyBy" column="modify_by" jdbcType="BIGINT"/>
            <result property="failNum" column="fail_num" jdbcType="INTEGER"/>
        <result property="totalDuration" column="total_duration" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,count_id,tenant_code,
        statistics_value,statistics_time,create_dt,create_by,
        modify_dt,modify_by,fail_num,total_duration
    </sql>

    <select id="topMaxTenantCode" resultType="com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO">
        SELECT
        SUM(statistics_value) AS sumValue,tenant_code AS tenant_code
        FROM
        statistics_count
        WHERE
        <![CDATA[
            statistics_time >= #{param.startTime}
            AND statistics_time <= #{param.endTime}
        ]]>
        GROUP BY
        tenant_code
        ORDER BY
        sumValue DESC
        LIMIT #{param.count}
    </select>

    <select id="totalCount" resultType="com.dl.magicvideo.biz.dal.statistics.po.StatisticsTotalCountPO">
        SELECT
            SUM(statistics_value) AS sumValue,
            SUM(fail_num) as failCount,
            statistics_time AS statisticsTime
        FROM
        statistics_count
        WHERE
        <![CDATA[
            statistics_time >= #{param.startTime}
            AND statistics_time <= #{param.endTime}
        ]]>
        GROUP BY statistics_time
        ORDER BY statistics_time ASC
    </select>

    <select id="topMaxMsg" resultType="com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO">
        SELECT
        tenant_code AS tenantCode,tenant_name AS tenantName,
        statistics_time AS statisticsTime,statistics_value AS statisticsValue
        FROM
        statistics_count
        WHERE
        <![CDATA[
            statistics_time >= #{param.startTime}
            AND statistics_time <= #{param.endTime}
        ]]>
        <if test="param.tenantCodeList != null and param.tenantCodeList.size() > 0">
            AND tenant_code in
            <foreach collection="param.tenantCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY tenant_code,tenant_name,statistics_time,statistics_value
        ORDER BY statistics_time ASC
    </select>

    <select id="queryTenantTotalDuration"
            resultType="com.dl.magicvideo.biz.dal.statistics.po.TenantStatisticsTotalDurationPO">
        SELECT
            SUM(total_tts_text_length) AS totalTextLength,
            SUM(total_duration) AS totalDuration,
            MAX(statistics_time) as latestStatisticsTime
        FROM
        statistics_count
        WHERE
        tenant_code = #{param.tenantCode}
        <if test="param.startTime != null">
            AND statistics_time &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND statistics_time &lt;= #{param.endTime}
        </if>
    </select>

</mapper>
