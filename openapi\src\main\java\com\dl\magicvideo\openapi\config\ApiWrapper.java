package com.dl.magicvideo.openapi.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.OpenApiConstant;
import com.dl.magicvideo.biz.common.util.ApplicationUtil;
import com.dl.magicvideo.biz.common.util.OpenApiSignUtil;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;

@Slf4j
public class ApiWrapper extends HttpServletRequestWrapper {

    private byte[] requestBody = null;//用于将流保存下来

    private String originalParam;

    public ApiWrapper(HttpServletRequest request) throws Exception {
        super(request);
        requestBody = IOUtils.toByteArray(request.getInputStream());

        this.originalParam = new String(requestBody, "UTF-8");
        JSONObject jsonObject = JSON.parseObject(originalParam);

        check(request, JSON.toJSONString(jsonObject, SerializerFeature.MapSortField));
    }

    /**
     * 过期时间：5分钟
     */
    private static final Long AUTH_TIME_EXPIRED = 5 * 60 * 1000L;

    /**
     * 检验
     *
     * @throws Exception 鉴权错误信息
     */
    public static void check(HttpServletRequest request, String originalParam) throws Exception {

        TenantInfoManager tenantInfoManager = ApplicationUtil.getBean(TenantInfoManager.class);

        //accessKey ak（⽤户传递--这里对应tenantCode）
        String accessKey = request.getHeader(OpenApiConstant.ACCESS_KEY);

        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfoFromCache(accessKey);
        if (tenantInfo == null) {
            throw BusinessServiceException.getInstance("accessKey不正确");
        }
        //timestamp 时间戳（⽤户传递）
        String timestamp = request.getHeader(OpenApiConstant.TIMESTAMP);
        //signature 签名串（⽤户传递）
        String signature = request.getHeader(OpenApiConstant.SIGNATURE);

        if (StringUtils.isBlank(timestamp)) {
            throw BusinessServiceException.getInstance("鉴权参数不可为空, timestamp=" + timestamp);
        }
        long time = 0;
        try {
            time = Long.parseLong(timestamp);
        } catch (NumberFormatException e) {
            throw BusinessServiceException.getInstance("鉴权参数异常！");
        }

        if (System.currentTimeMillis() - time > AUTH_TIME_EXPIRED) {
            throw BusinessServiceException.getInstance("鉴权时间过期！");
        }
        String sign = OpenApiSignUtil.getSignature(accessKey, tenantInfo.getId().toString(), timestamp, originalParam);
        if (!StringUtils.equals(sign, signature)) {
            throw BusinessServiceException.getInstance("签名不正确");
        }
    }

    @Override
    public ServletInputStream getInputStream() {
        // 返回的是我们处理之后的数据
        final ByteArrayInputStream bais = new ByteArrayInputStream(requestBody);
        return new ServletInputStream() {
            @Override
            public int read() {
                return bais.read();  // 读取 requestBody 中的数据
            }

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }
        };
    }

}