package com.dl.magicvideo.web.controllers.template.param;

import com.dl.magicvideo.biz.manager.visual.dto.CrossClipsDTO;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO;
import com.dl.magicvideo.biz.manager.visual.dto.LightEditConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @describe: ProduceParam
 * @author: zhousx
 * @date: 2023/6/25 10:07
 */
@Data
public class ProduceParam {
    @ApiModelProperty(value = "模板id", required = true)
    @NotBlank
    private String templateId;

    @ApiModelProperty("动态变量")
    private String replaceData;

    private Integer source;

    @ApiModelProperty("轻编辑配置")
    private List<LightEditConfigDTO> lightEditConfigs;

    @ApiModelProperty("跨片段组建")
    private List<CrossClipsDTO> crossClips;

    @ApiModelProperty("渲染数据")
    private String renderData;

    @ApiModelProperty("作品名称")
    private String produceJobName;

    @ApiModelProperty("动态节点")
    private List<DynamicNodeDTO> dynamicNodes;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("作品封面，支持前端传入，传入则取该封面，不传则在合成时生成封面")
    private String jobCoverUrl;

}
