package com.dl.magicvideo.biz.manager.visual.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: DynamicNodeBO
 * @author: zhousx
 * @date: 2023/4/25 17:38
 */
@Data
public class DynamicNodeBO {
    private Long nodeId;

    private String type;

    private Long duration;

    private String coverUrl;

    private Integer isEnabled;

    private List<TtsConfigBO> ttsList;

    private List<DigitalManConfigBO> dmList;

    private List<VideoConfigBO> videoList;

    private List<AudioConfigBO> audioList;

    private List<DataSheetBO> dataSheetList;


    /**
     * 节点程序选择表达式
     */
    private String expression;

    /**
     * 是否启用程序选择
     */
    private Integer expressionFlag;

    @Data
    public static class TtsConfigBO {
        private String ttsId;

        private Long start;

        private String content;

        private Integer enableSubtitle;

        private Integer maxLength;

        private Long endDelay;

        private boolean hide;

        /**
         * 是否需要字幕中关键词高亮，0-否，1-是
         */
        private Integer subtitleKeyWordsHighlight;

        /**
         * 0 非关键时长组件 1 关键时长组件
         */
        private Integer keyTime;
    }

    @Data
    public static class DigitalManConfigBO {
        private String dmId;

        private Long start;

        private String content;

        private Integer enableSubtitle;

        private Integer maxLength;

        private boolean hide;

        /**
         * 出场延迟
         */
        private Long endDelay;

        /**
         * 0 非关键时长组件 1 关键时长组件
         */
        private Integer keyTime;
    }

    @Data
    public static class VideoConfigBO {
        /**
         * id
         */
        private String videoId;

        /**
         * 入场延迟
         */
        private Long start;

        /**
         * 视频链接
         */
        private String url;

        /**
         * 出场延迟
         */
        private Long endDelay;
        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 音量
         */
        private String volume;

        /**
         * 视频裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频时长
         */
        private Long duration;

        /**
         * 预留字段
         */
        private String content;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失(视频类型时才存在)
         */
        private String activeRotationMode;

        /**
         * 裁剪开始的时间
         */
        private Long startDelay;

        /**
         * 0 非关键时长组件 1 关键时长组件
         */
        private Integer keyTime;
    }

    @Data
    public static class AudioConfigBO {
        /**
         * id
         */
        private String audioId;

        /**
         * 入场延迟
         */
        private Long start;

        /**
         * 视频链接
         */
        private String url;

        /**
         * 出场延迟
         */
        private Long endDelay;
        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 音量
         */
        private String volume;

        /**
         * 视频裁剪时长
         */
        private Long croppedDuration;

        /**
         * 视频时长
         */
        private Long duration;

        /**
         * 预留字段
         */
        private String content;

        /**
         * 轮播方式:static 静帧，loop 循环，vanish 消失(视频类型时才存在)
         */
        private String activeRotationMode;

        /**
         * 裁剪开始的时间
         */
        private Long startDelay;
        /**
         * 淡入时间
         */
        private String fadeInTime;

        /**
         * 淡出时间
         */
        private String fadeOutTime;

        /**
         * 0 非关键时长组件 1 关键时长组件
         */
        private Integer keyTime;
    }

    @Data
    public static class DataSheetBO {
        private String dataSheetId;

        /**
         * 是否为关键时长组件，0-否，1-是
         */
        private Integer keyTime;

        /**
         * 内容 {"replaceKey":"替换变量key"}
         */
        private String content;

        /**
         * 是否隐藏
         */
        private boolean hide;

        /**
         * 入场延迟
         */
        private Long start;

        /**
         * 出场延迟
         */
        private Long endDelay;
    }
}
