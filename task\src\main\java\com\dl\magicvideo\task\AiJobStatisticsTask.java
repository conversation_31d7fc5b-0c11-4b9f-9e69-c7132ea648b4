package com.dl.magicvideo.task;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Lists;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.util.CeilingUtils;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.dal.aigc.po.AigcPromptPO;
import com.dl.magicvideo.biz.dal.music.po.BackgroundMusicPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsAiJobPO;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobExtPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiJobPO;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.statistics.StatisticsAiJobManager;
import com.dl.magicvideo.biz.manager.transaction.TransactionProxyManager;
import com.dl.magicvideo.biz.manager.visual.VisualAiJobManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-13 17:51
 */
@Component
public class AiJobStatisticsTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(AiJobStatisticsTask.class);

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Resource
    private StatisticsAiJobManager statisticsAiJobManager;

    @Resource
    private VisualAiJobManager visualAiJobManager;

    @Resource
    private TransactionProxyManager transactionProxyManager;

    @Resource
    private TenantInfoManager tenantInfoManager;

    /**
     * key-"tenantCode + statisticsTime + aiJobType"
     */
    private Map<String, StatisticsAiJobPO> statisticsMap = new HashMap<>();

    /**
     * ai任务数据统计定时任务
     * 处理前一天的数据
     */
//    @XxlJob("aiJobStatisticsHandler")
    public void aiJobStatisticsHandler() {
        int dayAmount = -1;

        //获取昨天时间
        Date beginDate = DateUtil.getDayStartTime(dayAmount);
        Date endDate = DateUtil.getDayLastTime(dayAmount);
        LOGGER.info("开始处理ai任务数据的定时任务，开始日期:{},结束日期:{}", beginDate, endDate);

        transactionProxyManager.process(
                () -> this.handleSpecificDayAiJobStatistics(beginDate, endDate, AiJobTypeE.DIGITAL_MAN.getCode()));

        transactionProxyManager
                .process(() -> this.handleSpecificDayAiJobStatistics(beginDate, endDate, AiJobTypeE.TTS.getCode()));

        transactionProxyManager
                .process(() -> this.handleSpecificDayAiJobStatistics(beginDate, endDate, AiJobTypeE.DM_TTS.getCode()));

        LOGGER.info("ai任务数据的定时任务处理完成，开始日期:{},结束日期:{}", beginDate, endDate);
    }

    /**
     * ai任务历史数据统计定时任务
     * 历史数据计算的逻辑
     * <p>
     * 需要在xxl-job后台配置起止日期。该定时任务在单个环境下只执行一次！
     */
//    @XxlJob("aiJobHistoryStatisticsHandler")
    public void aiJobHistoryStatisticsHandler() {
        String beginEndDateStr = XxlJobHelper.getJobParam();
        Assert.isTrue(StringUtils.isNotBlank(beginEndDateStr), "起止日期不能为空");
        String[] dateArr = beginEndDateStr.split(SymbolE.MINUS.getValue());
        String beginDateBoundaryStr = dateArr[0];
        String endDateBoundaryStr = dateArr[1];
        Date beginDateBoundary = DateUtil.parse(beginDateBoundaryStr, DateUtil.YMD);
        Date endDateBoundary = DateUtil.parse(endDateBoundaryStr, DateUtil.YMD);

        //计算两个日期边界之差
        int betweenDays = (int) DateUtil.between(beginDateBoundary, endDateBoundary, Calendar.DATE);
        Assert.isTrue(betweenDays >= 0L, "起止日期输入错误，开始日期不应晚于结束日期");
        LOGGER.info("开始处理历史的ai任务数据的定时任务，开始日期:{},结束日期:{}", beginDateBoundaryStr, endDateBoundaryStr);

        for (int i = 0; i <= betweenDays; i++) {
            Date beginDate = DateUtil.addDay(i, beginDateBoundary);
            Date endDate = DateUtil.getMaxDate(beginDate);
//            transactionProxyManager.process(
//                    () -> this.handleSpecificDayAiJobStatistics(beginDate, endDate, AiJobTypeE.DIGITAL_MAN.getCode()));
            transactionProxyManager
                    .process(() -> this.handleSpecificDayAiJobStatistics(beginDate, endDate, AiJobTypeE.TTS.getCode()));
            transactionProxyManager.process(
                    () -> this.handleSpecificDayAiJobStatistics(beginDate, endDate, AiJobTypeE.DM_TTS.getCode()));
        }
        LOGGER.info("处理历史的ai任务数据的定时任务完成，开始日期:{},结束日期:{}", beginDateBoundaryStr, endDateBoundaryStr);
    }

    /**
     * ai任务历史数据统计定时任务
     * 历史数据计算的逻辑
     * <p>
     * 需要在xxl-job后台配置起止日期。该定时任务在单个环境下只执行一次！
     */
//    @XxlJob("aiJobHistoryTtsHandler")
    public void aiJobHistoryTtsHandler() {
        String beginEndDateStr = XxlJobHelper.getJobParam();
        Assert.isTrue(StringUtils.isNotBlank(beginEndDateStr), "起止日期不能为空");
        String[] dateArr = beginEndDateStr.split(SymbolE.MINUS.getValue());
        String beginDateBoundaryStr = dateArr[0];
        String endDateBoundaryStr = dateArr[1];
        Date beginDateBoundary = DateUtil.parse(beginDateBoundaryStr, DateUtil.YMD);
        Date endDateBoundary = DateUtil.parse(endDateBoundaryStr, DateUtil.YMD);

        //计算两个日期边界之差
        int betweenDays = (int) DateUtil.between(beginDateBoundary, endDateBoundary, Calendar.DATE);
        Assert.isTrue(betweenDays >= 0L, "起止日期输入错误，开始日期不应晚于结束日期");
        LOGGER.info("开始处理历史的ai任务数据的定时任务，开始日期:{},结束日期:{}", beginDateBoundaryStr, endDateBoundaryStr);

        for (int i = 0; i <= betweenDays; i++) {
            processTtsTextLength(i, beginDateBoundary, AiJobTypeE.TTS.getCode());
            processTtsTextLength(i, beginDateBoundary, AiJobTypeE.DM_TTS.getCode());
        }
        LOGGER.info("处理历史的ai任务数据的定时任务完成，开始日期:{},结束日期:{}", beginDateBoundaryStr, endDateBoundaryStr);
    }

    private void processTtsTextLength(int i , Date beginDateBoundary, Integer aiJobType){
        Date beginDate = DateUtil.addDay(i, beginDateBoundary);
        Date endDate = DateUtil.getMaxDate(beginDate);
        //分页查询
        VisualAiJobPageBO pageParam = new VisualAiJobPageBO();
        pageParam.setAiJobType(aiJobType);
        pageParam.setJobStatusList(Collections.singletonList(JobStatusE.SUCCESS.getCode()));
        pageParam.setMinDt(beginDate);
        pageParam.setMaxDt(endDate);
        pageParam.setNeedQueryDeleted(Const.ONE);
        pageParam.setPageIndex(Const.ONE);
        pageParam.setPageSize(Const.ONE_HUNDRED);

        ResponsePageQueryDO<List<VisualAiJobExtPO>> pageResult = visualAiJobManager.pageExt(pageParam);

        while (pageParam.getPageIndex() <= pageResult.getTotalPage()) {
            //处理单条记录
            pageResult.getDataResult().forEach(this::handleTtsTextLength);
            if (pageParam.getPageIndex() == pageResult.getTotalPage()) {
                break;
            }
            //查下一页
            pageParam.setPageIndex(pageParam.getPageIndex() + 1);
            pageResult = visualAiJobManager.pageExt(pageParam);
        }
    }

    /**
     * 处理tts的合成字数
     * @param visualAiJobExtPO
     */
    private void handleTtsTextLength(VisualAiJobExtPO visualAiJobExtPO) {
        String requestInfo = visualAiJobExtPO.getRequestInfo();
        JSONObject jsonObject = JSONUtil.parseObj(requestInfo);
        String text = jsonObject.getStr("text");
        visualAiJobManager.lambdaUpdate().eq(VisualAiJobPO::getJobId, visualAiJobExtPO.getJobId()).set(VisualAiJobPO::getTextLength, text.length()).update();
    }

    public static void main(String[] args) {
        String requestInfo = "{\"voiceName\":\"aixia\",\"channel\":4,\"templateId\":1188950301625949448,\"produceJobId\":1190720342180402005,\"enableSubtitle\":1,\"configId\":\"__inner__tts_22_290877388\",\"text\":\"1234567890\"}";
        JSONObject jsonObject = JSONUtil.parseObj(requestInfo);
        String text = jsonObject.getStr("text");
        System.out.println(text.length());
    }


    private void handleSpecificDayAiJobStatistics(Date beginDate, Date endDate, Integer aiJobType) {
        //先移除该日的数据
        statisticsAiJobManager.remove(Wrappers.lambdaQuery(StatisticsAiJobPO.class)
                .ge(StatisticsAiJobPO::getStatisticsTime, beginDate).le(StatisticsAiJobPO::getStatisticsTime, endDate)
                .eq(StatisticsAiJobPO::getAiJobType, aiJobType));

        //分页查询
        VisualAiJobPageBO pageParam = new VisualAiJobPageBO();
        pageParam.setAiJobType(aiJobType);
        pageParam.setJobStatusList(Collections.singletonList(JobStatusE.SUCCESS.getCode()));
        pageParam.setMinDt(beginDate);
        pageParam.setMaxDt(endDate);
        pageParam.setNeedQueryDeleted(Const.ONE);
        pageParam.setPageIndex(Const.ONE);
        pageParam.setPageSize(Const.ONE_HUNDRED);

        ResponsePageQueryDO<List<VisualAiJobExtPO>> pageResult = visualAiJobManager.pageExt(pageParam);

        while (pageParam.getPageIndex() <= pageResult.getTotalPage()) {
            //处理单条记录
            pageResult.getDataResult().forEach(record -> this.handleSingleRecord(record, beginDate));
            if (pageParam.getPageIndex() == pageResult.getTotalPage()) {
                break;
            }
            //查下一页
            pageParam.setPageIndex(pageParam.getPageIndex() + 1);
            pageResult = visualAiJobManager.pageExt(pageParam);
        }

        Collection<StatisticsAiJobPO> reportPOList = statisticsMap.values();
        if (CollectionUtils.isEmpty(reportPOList)) {
            LOGGER.info("无ai任务数据统计，beginDate:{}, pageResult.pages:{}", DateUtil.format(beginDate, DateUtil.Y_M_D),
                    pageResult.getTotalPage());
            statisticsMap.clear();
            return;
        }

        //填充租户名称
        this.fillTenantName(reportPOList);

        try {
            //批量保存报表列表
            statisticsAiJobManager.saveBatch(reportPOList);
        } catch (Exception e) {
            LOGGER.error("批量保存ai任务数据统计发生异常！beginDate:{},e:{}", beginDate, e);
        } finally {
            statisticsMap.clear();
        }
    }

    private void handleSingleRecord(VisualAiJobExtPO visualAiJobExtPO, Date beginDate) {
        //1.生成key-"tenantCode + statisticsTime + aiJobType"，从statisticsMap中获取报表对象
        String key = this.generateKey(visualAiJobExtPO.getTenantCode(), beginDate, visualAiJobExtPO.getJobType());
        StatisticsAiJobPO reportPO = statisticsMap.get(key);

        Long durationCeilSeconds = CeilingUtils.millsToMinutes(visualAiJobExtPO.getDuration());

        //2.1若已存在报表对象
        if (Objects.nonNull(reportPO)) {
            reportPO.setJobCount(reportPO.getJobCount() + 1);
            reportPO.setTotalCeilingMinutes(reportPO.getTotalCeilingMinutes() + durationCeilSeconds);
            reportPO.setTotalTimeMillis(
                    reportPO.getTotalTimeMillis() + (Objects.nonNull(visualAiJobExtPO.getDuration()) ?
                            visualAiJobExtPO.getDuration() :
                            0L));
            reportPO.setTotalTextLength(
                    reportPO.getTotalTextLength() + (Objects.nonNull(visualAiJobExtPO.getTextLength()) ?
                            visualAiJobExtPO.getTextLength() :
                            0L));

            return;
        }
        //2.2若不存在报表对象，则生成报表对象
        reportPO = new StatisticsAiJobPO();
        reportPO.setStatisticsTime(beginDate);
        reportPO.setAiJobType(visualAiJobExtPO.getJobType());
        reportPO.setTenantCode(visualAiJobExtPO.getTenantCode());
        reportPO.setJobCount(Const.ONE);
        reportPO.setRecordId(hostTimeIdg.generateId().longValue());
        reportPO.setTotalCeilingMinutes(durationCeilSeconds);
        reportPO.setTotalTimeMillis(
                (Objects.nonNull(visualAiJobExtPO.getDuration()) ? visualAiJobExtPO.getDuration() : 0L));
        reportPO.setTotalTextLength((Objects.nonNull(visualAiJobExtPO.getTextLength()) ? visualAiJobExtPO.getTextLength() : 0L));
        reportPO.setCreateDt(new Date());
        reportPO.setModifyDt(new Date());
        statisticsMap.put(key, reportPO);
    }

    private void fillTenantName(Collection<StatisticsAiJobPO> reportPOList) {
        Set<String> tenantCodeSet = reportPOList.stream().map(StatisticsAiJobPO::getTenantCode)
                .collect(Collectors.toSet());

        List<AdmTenantInfoDTO> tenantInfoDTOList = tenantInfoManager.listTenantInfo(Lists.newArrayList(tenantCodeSet));
        Map<String, String> tenantCodeNameMap = tenantInfoDTOList.stream()
                .collect(Collectors.toMap(AdmTenantInfoDTO::getTenantCode, AdmTenantInfoDTO::getName, (t1, t2) -> t1));

        reportPOList.forEach(reportPO -> {
            reportPO.setTenantName(tenantCodeNameMap.get(reportPO.getTenantCode()));
        });
    }

    /**
     * 生成key-"tenantCode + statisticsTime + aiJobType"
     *
     * @return
     */
    private String generateKey(String tenantCode, Date beginDt, Integer aiJobType) {
        StringBuffer sbf = new StringBuffer();
        sbf.append(tenantCode).append("-").append(beginDt).append("-").append(aiJobType);
        return sbf.toString();
    }

}
