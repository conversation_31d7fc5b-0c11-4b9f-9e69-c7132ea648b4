package com.dl.magicvideo.biz.manager.font.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.font.PatternFontMapper;
import com.dl.magicvideo.biz.dal.font.po.PatternFontPO;
import com.dl.magicvideo.biz.manager.font.PatternFontManager;
import com.dl.magicvideo.biz.manager.font.bo.PatternFontListBO;
import com.dl.magicvideo.biz.manager.font.bo.PatternFontPageBO;
import com.dl.magicvideo.biz.manager.font.convert.PatternFontConvert;
import com.dl.magicvideo.biz.manager.font.dto.PatternFontDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PatternFontManagerImpl extends ServiceImpl<PatternFontMapper, PatternFontPO> implements
        PatternFontManager {

    @Override
    public List<PatternFontDTO> fontList(PatternFontListBO bo) {
        List<PatternFontPO> patternFontList = this.list(
                Wrappers.lambdaQuery(PatternFontPO.class).eq(PatternFontPO::getIsDeleted, Const.ZERO)
                        .orderByDesc(PatternFontPO::getCreateDt));
        return patternFontList.stream().map(PatternFontConvert::cnvPatternFontPO2DTO).collect(Collectors.toList());
    }

    @Override
    public PatternFontDTO detail(Long bizId) {
        PatternFontPO font = this.getOne(
                Wrappers.lambdaQuery(PatternFontPO.class).eq(PatternFontPO::getBizId,bizId).eq(PatternFontPO::getIsDeleted, Const.ZERO));
        return PatternFontConvert.cnvPatternFontPO2DTO(font);
    }

    @Override
    public IPage<PatternFontDTO> fontPage(PatternFontPageBO bo) {
        LambdaQueryWrapper<PatternFontPO> wrapper = Wrappers.lambdaQuery(PatternFontPO.class);
        wrapper.like(StringUtils.isNotBlank(bo.getName()),
                        PatternFontPO::getName, bo.getName())
                .eq(Objects.nonNull(bo.getType()), PatternFontPO::getFontType, bo.getType())
                .eq(PatternFontPO::getIsDeleted, Const.ZERO)
                .orderByDesc(PatternFontPO::getCreateDt);
        Page<PatternFontPO> resp = this.page(new Page<>(bo.getPageIndex(), bo.getPageSize()), wrapper);
        IPage<PatternFontDTO> result = new Page<>(resp.getCurrent(), resp.getSize(),resp.getTotal());
        result.setPages(resp.getPages());
        List<PatternFontDTO> data = resp.getRecords().stream().map(PatternFontConvert::cnvPatternFontPO2DTO)
                .collect(Collectors.toList());
        result.setRecords(data);
        return result;
    }

}
