package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.dal.visual.po.DeliveryPlanPO;
import com.dl.magicvideo.biz.manager.visual.bo.DeliveryPlanBO;
import com.dl.magicvideo.biz.manager.visual.bo.DeliveryPlanSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.DeliveryPlanDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【delivery_plan】的数据库操作Service
* @createDate 2023-09-04 11:23:26
*/
public interface DeliveryPlanManager extends IService<DeliveryPlanPO> {
    /**
     * 功能描述: 新增交付计划
     * @Param: [bo]
     * @Return: java.lang.Long
     * @Author: zhousx
     * @Date: 2023/9/5 9:45
     */
    Long addDeliveryPlan(DeliveryPlanBO bo);

    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List<com.dl.magicvideo.biz.manager.visual.dto.DeliveryPlanDTO>>
     * @Author: zhousx
     * @Date: 2023/9/5 14:51
     */
    ResponsePageQueryDO<List<DeliveryPlanDTO>> pageQuery(DeliveryPlanSearchBO bo);

    /**
     * 功能描述: <br> 修改交付计划
     * @Param: [bo]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/9/7 11:26
     */
    void udpateDeliveryPlan(DeliveryPlanBO bo);
}
