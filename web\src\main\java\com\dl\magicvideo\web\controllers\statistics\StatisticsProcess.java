package com.dl.magicvideo.web.controllers.statistics;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.bo.PageQueryDO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsAiJobPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.manager.statistics.StatisticsAiJobManager;
import com.dl.magicvideo.biz.manager.statistics.StatisticsCountManager;
import com.dl.magicvideo.biz.manager.statistics.StatisticsEfficiencyManager;
import com.dl.magicvideo.biz.manager.statistics.bo.EfficiencyBO;
import com.dl.magicvideo.biz.manager.statistics.dto.StatisticsEfficiencyDTO;
import com.dl.magicvideo.biz.manager.visual.enums.EfficiencyTypeEnum;
import com.dl.magicvideo.web.controllers.statistics.param.CountParam;
import com.dl.magicvideo.web.controllers.statistics.param.EfficiencyParam;
import com.dl.magicvideo.web.controllers.statistics.param.StatisticsAiJobQueryParam;
import com.dl.magicvideo.web.controllers.statistics.vo.CountDTO;
import com.dl.magicvideo.web.controllers.statistics.vo.CountVO;
import com.dl.magicvideo.web.controllers.statistics.vo.EfficiencyDTO;
import com.dl.magicvideo.web.controllers.statistics.vo.EfficiencyVO;
import com.dl.magicvideo.web.controllers.statistics.vo.StatisticsAiJobVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @describe: 统计相关接口
 * @author: hongcj
 * @date: 2023/6/17 14:43
 */
@Component
public class StatisticsProcess {

    @Resource
    private StatisticsCountManager statisticsCountManager;

    @Resource
    private StatisticsEfficiencyManager statisticsEfficiencyManager;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private StatisticsAiJobManager statisticsAiJobManager;

    public static void main(String[] args) {
        Date startDate = new Date(1687795200000L);
        Date endDate = new Date(1687881599999L);
        String startTime = DateUtil.format(startDate, DateUtil.Y_M_D);
        String endTime = DateUtil.format(endDate, DateUtil.Y_M_D);
        System.out.println(startTime);
        System.out.println(endTime);
    }

    public List<CountVO> count(CountParam param) {
        Assert.isTrue(Objects.nonNull(param.getStartTime()),"开始时间不能为空");
        Assert.isTrue(Objects.nonNull(param.getEndTime()),"结束时间不能为空");
        Assert.isTrue(param.getStartTime() < param.getEndTime(),"开始时间不能大于结束时间");
        Assert.isTrue(param.getEndTime() - param.getStartTime() < 15768000000L,"查询范围不能超过半年");
        List<CountVO> countVOS = new ArrayList<>();
        Date startDate = new Date(param.getStartTime());
        Date endDate = new Date(param.getEndTime());
        String startTime = DateUtil.format(startDate, DateUtil.Y_M_D);
        String endTime = DateUtil.format(endDate, DateUtil.Y_M_D);
        //查询当前租户信息
        List<String> tenantCodeList = Collections.singletonList(operatorUtil.getTenantCode());

        TopMaxMsgParam topMaxMsgParam = new TopMaxMsgParam();
        topMaxMsgParam.setStartTime(startTime);
        topMaxMsgParam.setEndTime(endTime);
        topMaxMsgParam.setTenantCodeList(tenantCodeList);
        //top系列租户
        List<StatisticsMsgPO> statisticsMsgPOS = statisticsCountManager.topMaxMsg(topMaxMsgParam);

        //租户，租户信息分组
        Map<String, List<StatisticsMsgPO>> map = statisticsMsgPOS.stream()
                .collect(Collectors.groupingBy(StatisticsMsgPO::getTenantCode));
        map.keySet().forEach(e->{
            CountVO mapCountVO = new CountVO();
            mapCountVO.setTenantCode(e);
            List<StatisticsMsgPO> tentCountMsg = map.get(e);
            mapCountVO.setTenantName(tentCountMsg.get(0).getTenantName());
            List<CountDTO> countMsgList =  new ArrayList<>();
            tentCountMsg.forEach(a->{
                CountDTO countDTO = new CountDTO();
                countDTO.setTime(a.getStatisticsTime());
                countDTO.setValue(a.getStatisticsValue());
                countMsgList.add(countDTO);
            });

            mapCountVO.setCountMsgList(this.completeData(startDate, endDate, countMsgList));
            countVOS.add(mapCountVO);
        });


        return countVOS;
    }

    public List<EfficiencyVO> efficiency(EfficiencyParam param) {
        Assert.isTrue(Objects.nonNull(param.getStartTime()), "开始时间不能为空");
        Assert.isTrue(Objects.nonNull(param.getEndTime()), "结束时间不能为空");
        Assert.isTrue(param.getStartTime() < param.getEndTime(), "开始时间不能大于结束时间");
        Assert.isTrue(param.getEndTime() - param.getStartTime() < 15768000000L, "查询范围不能超过半年");
        Long startTimeLong = param.getStartTime();
        Long endTimeLong = param.getEndTime();
        List<EfficiencyVO> list = new ArrayList<>();
        list.add(getMsgList(startTimeLong, endTimeLong, EfficiencyTypeEnum.SINGLE));
        list.add(getMsgList(startTimeLong, endTimeLong, EfficiencyTypeEnum.DIGITAL_MAN_TYPE));
        list.add(getMsgList(startTimeLong, endTimeLong, EfficiencyTypeEnum.PICTURE));
        return list;
    }

    public List<StatisticsAiJobVO> aiJobStatistics(StatisticsAiJobQueryParam param) {
        Assert.isTrue(param.getMinDt().before(param.getMaxDt()), "开始时间不能大于结束时间");
        Assert.isTrue(DateUtil.between(param.getMinDt(), param.getMaxDt(), Calendar.DAY_OF_MONTH) < 180, "查询范围不能超过半年");
        Date minDt = DateUtil.getMinDate(param.getMinDt());
        Date maxDt = DateUtil.getMaxDate(param.getMaxDt());

        List<StatisticsAiJobPO> statisticsPOList = statisticsAiJobManager
                .list(Wrappers.lambdaQuery(StatisticsAiJobPO.class)
                        .eq(StatisticsAiJobPO::getTenantCode, operatorUtil.getTenantCode())
                        .ge(StatisticsAiJobPO::getStatisticsTime, minDt).le(StatisticsAiJobPO::getStatisticsTime, maxDt)
                        .eq(StatisticsAiJobPO::getAiJobType, param.getAiJobType())
                        .eq(StatisticsAiJobPO::getIsDeleted, Const.ZERO));

        //间隔的日期列表
        List<Date> dateList = DateUtil.getRangeDays(minDt, maxDt);
        //key-日期
        Map<Date, StatisticsAiJobPO> existDateMap = statisticsPOList.stream()
                .collect(Collectors.toMap(StatisticsAiJobPO::getStatisticsTime, Function.identity(), (v1, v2) -> v2));

        List<StatisticsAiJobVO> resultList = new ArrayList<>();
        for (Date date : dateList) {
            StatisticsAiJobVO result = new StatisticsAiJobVO();
            resultList.add(result);
            result.setTenantCode(operatorUtil.getTenantCode());
            result.setTenantName(operatorUtil.getTenantName());
            result.setAiJobType(param.getAiJobType());
            if (existDateMap.containsKey(date)) {
                StatisticsAiJobPO input = existDateMap.get(date);
                result.setJobCount(input.getJobCount());
                result.setTotalTimeMillis(input.getTotalTimeMillis());
                result.setStatisticsTime(input.getStatisticsTime());
            } else {
                result.setJobCount(Const.ONE);
                result.setTotalTimeMillis(Const.ZERO_LONG);
                result.setStatisticsTime(date);
            }
        }

        return resultList;
    }

    private EfficiencyVO getMsgList(Long startTimeLong, Long endTimeLong, EfficiencyTypeEnum efficiencyTypeEnum) {
        EfficiencyBO pageQueryDO = new EfficiencyBO();
        pageQueryDO.setPageIndex(1);
        pageQueryDO.setPageSize(PageQueryDO.MAX_PAGE_SIZE);
        Date startDate = new Date(startTimeLong);
        Date endDate = new Date(endTimeLong);
        String startTime = DateUtil.format(startDate, DateUtil.Y_M_D);
        String endTime = DateUtil.format(endDate, DateUtil.Y_M_D);
        pageQueryDO.setStartTime(startTime);
        pageQueryDO.setEndTime(endTime);
        pageQueryDO.setType(efficiencyTypeEnum.getCode());
        ResponsePageQueryDO<List<StatisticsEfficiencyDTO>> listResponsePageQueryDO = statisticsEfficiencyManager.pageQuery(
                pageQueryDO);

        if (!listResponsePageQueryDO.isSuccess()) {
            throw BusinessServiceException.getInstance(listResponsePageQueryDO.getCode(),
                    listResponsePageQueryDO.getMessage());
        }
        EfficiencyVO efficiencyVO = new EfficiencyVO();
        efficiencyVO.setTenantCode("");
        efficiencyVO.setTenantName(efficiencyTypeEnum.getName());
        List<EfficiencyDTO> countMsgList = new ArrayList<>();
        if (Objects.nonNull(listResponsePageQueryDO.getDataResult())) {
            listResponsePageQueryDO.getDataResult().forEach(e -> {
                EfficiencyDTO efficiencyDTO = new EfficiencyDTO();
                efficiencyDTO.setTime(e.getStatisticsTime());
                efficiencyDTO.setValue(e.getStatisticsValue());
                countMsgList.add(efficiencyDTO);
            });
        }
        efficiencyVO.setCountMsgList(completeEfficiencyData(startDate, endDate, countMsgList));
        return efficiencyVO;
    }

    /**
     * statisticsValue查询时会出现70.0这样的情况，只取70
     * @param statisticsValue
     * @return
     */
    private String formatValue(String statisticsValue) {
        if (StringUtils.isEmpty(statisticsValue)) {
            return statisticsValue;
        }
        int i = statisticsValue.indexOf(".");
        if (i == -1) {
            //-1表示没有. 则返回原来字符串
            return statisticsValue;
        } else {
            return statisticsValue.substring(0, i);
        }
    }

    /**
     * 补全数据
     *
     * @param startTime 开始日期
     * @param endTime 截止日期
     * @param oldList 未补全的列表
     * @return 补全后的列表
     */
    private List<CountDTO> completeData(Date startTime, Date endTime, List<CountDTO> oldList) {
        LocalDate preDate = startTime.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate endDate = endTime.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDate();

        List<CountDTO> newList = new ArrayList<>();
        Map<LocalDate, CountDTO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oldList)) {
            map = oldList.stream()
                    .collect(Collectors.toMap(e->LocalDate.parse(e.getTime()), Function.identity()));
        }
        //间隔的日期列表
        List<LocalDate> dates = getRangeDays(preDate, endDate);
        Map<LocalDate, CountDTO> finalMap = map;
        dates.forEach(c -> {
            if (finalMap.containsKey(c)) {
                newList.add(finalMap.get(c));
            } else {
                //没有这一天的数据，默认补0
                String format = c.format(DateTimeFormatter.ofPattern(DateUtil.Y_M_D));
                newList.add(new CountDTO(Const.ZERO_STR, format));
            }
        });
        return newList;
    }

    /**
     * 补全数据
     *
     * @param startTime 开始日期
     * @param endTime 截止日期
     * @param oldList 未补全的列表
     * @return 补全后的列表
     */
    private List<EfficiencyDTO> completeEfficiencyData(Date startTime, Date endTime, List<EfficiencyDTO> oldList) {
        LocalDate preDate = startTime.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDate();
        LocalDate endDate = endTime.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDate();

        List<EfficiencyDTO> newList = new ArrayList<>();
        Map<LocalDate, EfficiencyDTO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oldList)) {
            map = oldList.stream().collect(Collectors.toMap(e -> LocalDate.parse(e.getTime()), Function.identity()));
        }
        //间隔的日期列表
        List<LocalDate> dates = getRangeDays(preDate, endDate);

        Map<LocalDate, EfficiencyDTO> finalMap = map;
        dates.forEach(c -> {
            if (finalMap.containsKey(c)) {
                newList.add(finalMap.get(c));
            } else {
                //没有这一天的数据，默认补0
                String format = c.format(DateTimeFormatter.ofPattern(DateUtil.Y_M_D));
                newList.add(new EfficiencyDTO(Const.ZERO_STR, format));
            }
        });
        return newList;
    }

    /**
     * 获取间隔的日期列表
     *
     * @param preDate 开始日期
     * @param endDate 截止日期
     * @return
     */
    private List<LocalDate> getRangeDays(LocalDate preDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        //间隔的天数
        long betweenDays = ChronoUnit.DAYS.between(preDate, endDate);
        if (betweenDays < 1) {
            //开始日期<=截止日期（则取当天）
            dates.add(preDate);
            return dates;
        }
        //创建一个从开始日期、每次加一天的无限流，限制到截止日期为止
        Stream.iterate(preDate, c -> c.plusDays(1))
                .limit(betweenDays + 1)
                .forEach(dates::add);
        return dates;
    }


}
