package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.visual.po.LatestTenantMaterialPO;
import com.dl.magicvideo.biz.dal.visual.po.TenantMaterialPO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【TenantMaterialPO】的数据库操作Service
 * @createDate 2023-06-08 16:23:52
 */
public interface TenantMaterialManager extends IService<TenantMaterialPO> {

    List<LatestTenantMaterialPO> latestMaterialByFolderIds(String tenantCode, Set<Long> folderIds, Integer rowNumber,
            Integer materialType);
}
