package com.dl.magicvideo.biz.manager.aigc.chatrecord.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 15:07
 */
@Data
public class AigcChatRecordDTO {

    private Long recordId;

    private Long userId;

    private String tenantCode;

    /**
     * aigc聊天记录类型枚举
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum
     */
    private Integer type;

    /**
     * 聊天内容
     */
    private String content;

    /**
     * 聊天内容类型
     *
     * @see: com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum
     */
    private Integer contentType;

    private Integer isDeleted;

    private Date sendDt;

    private Date createDt;

    private Date modifyDt;

}
