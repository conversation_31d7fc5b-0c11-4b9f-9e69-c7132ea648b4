package com.dl.magicvideo.biz.dal.subjectmatter.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:25
 */
@Data
@TableName("subject_matter_stock")
public class SubjectMatterStockPO implements Serializable {

    private static final long serialVersionUID = -7096417768851200879L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 题材bizId
     */
    private Long smBizId;

    /**
     * 股票编码
     */
    private String stockCode;

    /**
     * 股票简称
     */
    private String stockShortName;

    /**
     * create_dt
     */
    private Date createDt;
}
