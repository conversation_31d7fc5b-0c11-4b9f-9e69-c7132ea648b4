package com.dl.magicvideo.biz.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Slf4j
public class VelocityUtil {
    private static Properties props = new Properties();
    private static VelocityEngine engine = new VelocityEngine(props);

    static {
        props.setProperty(Velocity.INPUT_ENCODING, "UTF-8");
        props.setProperty(Velocity.RESOURCE_LOADER, "class");
        props.setProperty("class.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
    }

    public static VelocityContext createContext(Map<String, ?>... vars) {
        VelocityContext velocityContext = new VelocityContext();
        if (vars != null) {
            for (Map<String, ?> map : vars) {
                if (map != null) {
                    map.entrySet().forEach(en -> velocityContext.put(en.getKey(), en.getValue()));
                }
            }
        }
        return velocityContext;
    }

    public static String render(VelocityContext context, String template) {
        StringWriter writer = new StringWriter();
        engine.evaluate(context, writer, "", template);
        return writer.toString();
    }

    public static String render(Map<String, String> map, String template) {
        StringWriter writer = new StringWriter();
        engine.evaluate(createContext(map), writer, "", template);
        return writer.toString();
    }

    public static boolean calculateBoolean(VelocityContext context, String expression) {
        StringWriter writer = new StringWriter();
        String exp = "#set ($v = (" + expression + "))$v";
        try {
            engine.evaluate(context, writer, "", exp);
            return Boolean.parseBoolean(writer.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("userName", "aaaa");
        System.out.println(VelocityUtil.render(map, "${userName}：基金首发解读"));
        //        map.put("isFemale", "false");
        //        map.put("isDigital", "false");
        //        VelocityContext context = createContext(map);
        //        //        System.out.println(calculateBoolean(context, "(${isFemale}=='true')&&!(${isDigital}=='true')"));
        //        //        System.out.println(calculateBoolean(context, "(${isFemale})&&(${isDigital})&&!(${isFemale}=='true')&&!(${isDigital}=='true')"));
        //        System.out.println(calculateBoolean(context,
        //                "(${isFemale})&&(${isDigital})&&(${isFemale}=='true')&&!(${isDigital}=='true')"));
        //        System.out.println(calculateBoolean(context,
        //                "(${isFemale})&&(${isDigital})&&!(${isFemale}=='true')&&!(${isDigital}=='true')"));

    }
}
