package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class SzbAttachmentOneDTO {
    /**
     * 数据模块指标名
     */
    private String note;
    /**
     * 数据模块画图部分指标，如["营业总收入","同比","环比"],表示画图数据中对应营收、同比、环比
     */
    private List<String> htNote;
    /**
     * 画图部分x轴数据集
     */
    private List<String> htxData;
    /**
     * 指标同比数据（都是正值）
     */
    private String tb;
    /**
     * 指标数据单位
     */
    private String unit;
    /**
     * 画图部分数据指标对应单位：["亿元","%","%"]
     */
    private List<String> htUnit;
    /**
     * 画图部分y轴数据集（包含多个指标为二维数据，一个指标时为一维数组
     */
    private List<Object> htyData;
    /**
     * 同比方向（同比为正1，同比为负值-1）
     */
    private Integer tbfx;
    /**
     * 数据是否正确（1为数据正常，如果数据模块其他值为空且dataOk为1时，表示本报告期此数据模块为空
     */
    private Integer dataOk;
    /**
     * 指标环比数据（都是正值）可为空
     */
    private String hb;
    /**
     * 环比方向（参考同比方向）环比为空时，方向也为空
     */
    private Integer hbfx;
    /**
     * 指标数据
     */
    private String value;
    /**
     * 此数据模块文字描述
     */
    private String desc;
}