package com.dl.magicvideo.biz.manager.cos.dto;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2025/8/28 11:53
 */
import java.util.List;

public class MediaInfo {
    private List<AudioStream> audioStreams;
    private long bitrate;
    private double duration;
    private String formatLongName;
    private String formatName;
    private String requestId;
    private long size;
    private double startTime;
    private int streamCount;
    private int videoHeight;
    private List<VideoStream> videoStreams;
    private int videoWidth;

    // Getters and Setters
    public List<AudioStream> getAudioStreams() { return audioStreams; }
    public void setAudioStreams(List<AudioStream> audioStreams) { this.audioStreams = audioStreams; }

    public long getBitrate() { return bitrate; }
    public void setBitrate(long bitrate) { this.bitrate = bitrate; }

    public double getDuration() { return duration; }
    public void setDuration(double duration) { this.duration = duration; }

    public String getFormatLongName() { return formatLongName; }
    public void setFormatLongName(String formatLongName) { this.formatLongName = formatLongName; }

    public String getFormatName() { return formatName; }
    public void setFormatName(String formatName) { this.formatName = formatName; }

    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }

    public long getSize() { return size; }
    public void setSize(long size) { this.size = size; }

    public double getStartTime() { return startTime; }
    public void setStartTime(double startTime) { this.startTime = startTime; }

    public int getStreamCount() { return streamCount; }
    public void setStreamCount(int streamCount) { this.streamCount = streamCount; }

    public int getVideoHeight() { return videoHeight; }
    public void setVideoHeight(int videoHeight) { this.videoHeight = videoHeight; }

    public List<VideoStream> getVideoStreams() { return videoStreams; }
    public void setVideoStreams(List<VideoStream> videoStreams) { this.videoStreams = videoStreams; }

    public int getVideoWidth() { return videoWidth; }
    public void setVideoWidth(int videoWidth) { this.videoWidth = videoWidth; }
}

class AudioStream {
    private String channelLayout;
    private int channels;
    private String codecLongName;
    private String codecName;
    private String codecTag;
    private String codecTagString;
    private int index;
    private String language;
    private String sampleFormat;
    private int sampleRate;
    private double startTime;
    private String timeBase;

    // Getters and Setters
    public String getChannelLayout() { return channelLayout; }
    public void setChannelLayout(String channelLayout) { this.channelLayout = channelLayout; }

    public int getChannels() { return channels; }
    public void setChannels(int channels) { this.channels = channels; }

    public String getCodecLongName() { return codecLongName; }
    public void setCodecLongName(String codecLongName) { this.codecLongName = codecLongName; }

    public String getCodecName() { return codecName; }
    public void setCodecName(String codecName) { this.codecName = codecName; }

    public String getCodecTag() { return codecTag; }
    public void setCodecTag(String codecTag) { this.codecTag = codecTag; }

    public String getCodecTagString() { return codecTagString; }
    public void setCodecTagString(String codecTagString) { this.codecTagString = codecTagString; }

    public int getIndex() { return index; }
    public void setIndex(int index) { this.index = index; }

    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }

    public String getSampleFormat() { return sampleFormat; }
    public void setSampleFormat(String sampleFormat) { this.sampleFormat = sampleFormat; }

    public int getSampleRate() { return sampleRate; }
    public void setSampleRate(int sampleRate) { this.sampleRate = sampleRate; }

    public double getStartTime() { return startTime; }
    public void setStartTime(double startTime) { this.startTime = startTime; }

    public String getTimeBase() { return timeBase; }
    public void setTimeBase(String timeBase) { this.timeBase = timeBase; }
}

class VideoStream {
    private String averageFrameRate;
    private String codecLongName;
    private String codecName;
    private String codecTag;
    private String codecTagString;
    private String colorPrimaries;
    private String colorRange;
    private String colorSpace;
    private String colorTransfer;
    private String displayAspectRatio;
    private String frameRate;
    private int height;
    private String language;
    private int level;
    private String pixelFormat;
    private String profile;
    private String sampleAspectRatio;
    private double startTime;
    private String timeBase;
    private int width;

    // Getters and Setters
    public String getAverageFrameRate() { return averageFrameRate; }
    public void setAverageFrameRate(String averageFrameRate) { this.averageFrameRate = averageFrameRate; }

    public String getCodecLongName() { return codecLongName; }
    public void setCodecLongName(String codecLongName) { this.codecLongName = codecLongName; }

    public String getCodecName() { return codecName; }
    public void setCodecName(String codecName) { this.codecName = codecName; }

    public String getCodecTag() { return codecTag; }
    public void setCodecTag(String codecTag) { this.codecTag = codecTag; }

    public String getCodecTagString() { return codecTagString; }
    public void setCodecTagString(String codecTagString) { this.codecTagString = codecTagString; }

    public String getColorPrimaries() { return colorPrimaries; }
    public void setColorPrimaries(String colorPrimaries) { this.colorPrimaries = colorPrimaries; }

    public String getColorRange() { return colorRange; }
    public void setColorRange(String colorRange) { this.colorRange = colorRange; }

    public String getColorSpace() { return colorSpace; }
    public void setColorSpace(String colorSpace) { this.colorSpace = colorSpace; }

    public String getColorTransfer() { return colorTransfer; }
    public void setColorTransfer(String colorTransfer) { this.colorTransfer = colorTransfer; }

    public String getDisplayAspectRatio() { return displayAspectRatio; }
    public void setDisplayAspectRatio(String displayAspectRatio) { this.displayAspectRatio = displayAspectRatio; }

    public String getFrameRate() { return frameRate; }
    public void setFrameRate(String frameRate) { this.frameRate = frameRate; }

    public int getHeight() { return height; }
    public void setHeight(int height) { this.height = height; }

    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }

    public int getLevel() { return level; }
    public void setLevel(int level) { this.level = level; }

    public String getPixelFormat() { return pixelFormat; }
    public void setPixelFormat(String pixelFormat) { this.pixelFormat = pixelFormat; }

    public String getProfile() { return profile; }
    public void setProfile(String profile) { this.profile = profile; }

    public String getSampleAspectRatio() { return sampleAspectRatio; }
    public void setSampleAspectRatio(String sampleAspectRatio) { this.sampleAspectRatio = sampleAspectRatio; }

    public double getStartTime() { return startTime; }
    public void setStartTime(double startTime) { this.startTime = startTime; }

    public String getTimeBase() { return timeBase; }
    public void setTimeBase(String timeBase) { this.timeBase = timeBase; }

    public int getWidth() { return width; }
    public void setWidth(int width) { this.width = width; }
}

