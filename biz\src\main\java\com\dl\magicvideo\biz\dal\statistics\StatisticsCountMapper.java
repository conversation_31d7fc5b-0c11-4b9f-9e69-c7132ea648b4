package com.dl.magicvideo.biz.dal.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.magicvideo.biz.common.annotation.BaseDao;
import com.dl.magicvideo.biz.dal.statistics.param.TenantStatisticsTotalDurationQueryParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxMsgParam;
import com.dl.magicvideo.biz.dal.statistics.param.TopMaxTenantCodeParam;
import com.dl.magicvideo.biz.dal.statistics.param.TotalCountParam;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsCountTopMaxPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsMsgPO;
import com.dl.magicvideo.biz.dal.statistics.po.StatisticsTotalCountPO;
import com.dl.magicvideo.biz.dal.statistics.po.TenantStatisticsTotalDurationPO;
import com.dl.magicvideo.biz.dal.visual.po.StatisticsJobCountPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
*/
@BaseDao
public interface StatisticsCountMapper extends BaseMapper<StatisticsCountPO> {

    /**
     * 一段时间内topX 租户
     * @param param
     * @return
     */
    List<StatisticsCountTopMaxPO> topMaxTenantCode(@Param("param") TopMaxTenantCodeParam param);

    /**
     * 一段时间内的总数
     * @param param
     * @return
     */
    List<StatisticsTotalCountPO> totalCount(@Param("param") TotalCountParam param);

    /**
     * 一端时间内租户的数量信息
     *
     * @param param
     * @return
     */
    List<StatisticsMsgPO> topMaxMsg(@Param("param") TopMaxMsgParam param);

    /**
     * 查询指定租户的总合成时长
     *
     * @param param
     * @return
     */
    TenantStatisticsTotalDurationPO queryTenantTotalDuration(@Param("param") TenantStatisticsTotalDurationQueryParam param);

}




