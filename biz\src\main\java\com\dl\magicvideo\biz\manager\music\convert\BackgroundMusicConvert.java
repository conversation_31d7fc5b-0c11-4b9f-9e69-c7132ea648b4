package com.dl.magicvideo.biz.manager.music.convert;

import com.dl.magicvideo.biz.dal.music.po.BackgroundMusicPO;
import com.dl.magicvideo.biz.manager.music.dto.BackgroundMusicDTO;

import java.util.Objects;

public class BackgroundMusicConvert {
    public static BackgroundMusicDTO cnvBackGroundMusicPO2DTO(BackgroundMusicPO input){
        if (Objects.isNull(input)){
            return null;
        }
        BackgroundMusicDTO result = new BackgroundMusicDTO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setType(input.getType());
        result.setDuration(input.getDuration());
        result.setUrl(input.getUrl());
        return result;
    }
}
