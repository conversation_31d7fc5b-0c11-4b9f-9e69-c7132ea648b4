package com.dl.magicvideo.openapi.aspect;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.TenantUserBindClient;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.client.basicservice.dto.TenantUserSimpleDTO;
import com.dl.magicvideo.biz.client.basicservice.param.TenantUserBindQueryDTO;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-09 11:37
 */
@Aspect
@Component
public class ExtUserIdConvertAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExtUserIdConvertAspect.class);

    @Resource
    private TenantUserBindClient tenantUserBindClient;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private TenantInfoManager tenantInfoManager;

    @Before(("@annotation(com.dl.magicvideo.openapi.aspect.annotation.ExtUserIdConvert)"))
    public Object convertExtUserId2UserId(JoinPoint joinPoint) {
        LOGGER.info("进入外部用户id转换的切面");
        Object[] args = joinPoint.getArgs();
        //请开发同学注意，extUserId请放置于一个实体类中，并置于方法入参第一位
        JSONObject jsonObject = JSONUtil.parseObj(args[0]);
        String extUserId = jsonObject.getStr("extUserId");

        //切面在拦截器之后，所以operatorUtil里可以取到租户号
        String tenantCode = operatorUtil.getTenantCode();
        LOGGER.info("外部用户id转换的切面,extUserId:{},tenantCode:{}", extUserId, tenantCode);

        //根据租户号+外部用户id查询绑定的用户信息
        TenantUserBindQueryDTO tenantUserBindQueryDTO = new TenantUserBindQueryDTO();
        tenantUserBindQueryDTO.setExtUserId(extUserId);
        tenantUserBindQueryDTO.setTenantCode(tenantCode);
        ResultModel<TenantUserSimpleDTO> resultModel = tenantUserBindClient.queryByExtUserId(tenantUserBindQueryDTO);
        if (!resultModel.isSuccess()) {
            LOGGER.error("根据外部用户id查询用户信息失败,extUserId:{},tenantCode:{},resultModel:{}", extUserId, tenantCode,
                    JSONUtil.toJsonStr(resultModel));
            throw BusinessServiceException.getInstance(resultModel.getCode(), "根据外部用户id查询用户信息失败");
        }

        if (Objects.isNull(resultModel.getDataResult())) {
            LOGGER.warn("根据外部用户id查询用户信息为空,extUserId:{},tenantCode:{}", extUserId, tenantCode);
            return joinPoint;
        }
        TenantUserSimpleDTO userSimpleDTO = resultModel.getDataResult();

        AdmTenantInfoDTO tenantInfoDTO = tenantInfoManager.getTenantInfoFromCache(tenantCode);

        operatorUtil.init(userSimpleDTO.getUserId(), userSimpleDTO.getUserName(), tenantCode, tenantInfoDTO.getName());
        return joinPoint;
    }

}
