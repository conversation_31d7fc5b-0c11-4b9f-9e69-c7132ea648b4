package com.dl.magicvideo.biz.manager.font.convert;

import com.dl.magicvideo.biz.dal.font.po.PatternFontPO;
import com.dl.magicvideo.biz.manager.font.dto.PatternFontDTO;

import java.util.Objects;

public class PatternFontConvert {

    public static PatternFontDTO cnvPatternFontPO2DTO(PatternFontPO input){
        if (Objects.isNull(input)){
            return null;
        }
        PatternFontDTO result = new PatternFontDTO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setFontType(input.getFontType());
        result.setStyles(input.getStyles());
        result.setCoverImg(input.getCoverImg());
        result.setUserId(input.getCreateBy());
        result.setCreateDt(input.getCreateDt());
        return result;
    }

}
