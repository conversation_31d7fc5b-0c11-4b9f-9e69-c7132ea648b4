package com.dl.magicvideo.web.controllers.internal.visual.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-23 10:39
 */
@Data
@ApiModel("数影作品转发配置")
public class VisualProduceJobInternalShareConfVO {

    @ApiModelProperty(value = "业务来源id")
    private Long bizId;

    @ApiModelProperty(value = "业务类型 1模板 2作品")
    private Integer bizType;

    @ApiModelProperty(value = "导航栏标题")
    private String navigationBarTitle;

    @ApiModelProperty(value = "视频封面图")
    private String coverImg;

    @ApiModelProperty(value = "推荐转发文案")
    private String recommendShareCnt;

    @ApiModelProperty(value = "小程序转发封面图")
    private String mpShareCoverImg;

    @ApiModelProperty(value = "H5转发封面图")
    private String h5ShareCoverImg;

    @ApiModelProperty(value = "转发标题")
    private String shareTitle;

    @ApiModelProperty(value = "转发摘要")
    private String shareRemark;

    @ApiModelProperty(value = "使用场景 0-朋友圈/群发/视频号 1-私聊")
    private Integer useCase;

}
