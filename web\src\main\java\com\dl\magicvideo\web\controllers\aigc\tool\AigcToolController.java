package com.dl.magicvideo.web.controllers.aigc.tool;

import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AiFileContentAndTitleRespVO;
import com.dl.magicvideo.web.controllers.aigc.tool.param.AigcExtractTitleFromTextParam;
import com.dl.magicvideo.web.controllers.aigc.tool.vo.AigcExtractTitleFromTextResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-06-06 11:32
 */
@Api("aigc-工具")
@RestController
@RequestMapping("/visual/aigc/tool")
public class AigcToolController {

    @Resource
    private AigcToolProcess aigcToolProcess;

    @ApiOperation("提取文件内容和标题")
    @PostMapping("/extractfilecontentandtitle")
    public ResultModel<AiFileContentAndTitleRespVO> extractFileContentAndTitle(MultipartFile file) {
        return aigcToolProcess.extractFileContentAndTitle(file);
    }

    @ApiOperation("从文本中提取标题")
    @PostMapping("/extracttitlefromtext")
    public ResultModel<AigcExtractTitleFromTextResultVO> extractTitleFromText(@RequestBody @Validated AigcExtractTitleFromTextParam param){
        return aigcToolProcess.extractTitleFromText(param);
    }

}
