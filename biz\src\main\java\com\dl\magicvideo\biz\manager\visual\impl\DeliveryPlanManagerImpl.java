package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.DeliveryPlanPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.DeliveryPlanManager;
import com.dl.magicvideo.biz.dal.visual.DeliveryPlanMapper;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.DeliveryPlanBO;
import com.dl.magicvideo.biz.manager.visual.bo.DeliveryPlanSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.DeliveryPlanDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【delivery_plan】的数据库操作Service实现
* @createDate 2023-09-04 11:23:26
*/
@Slf4j
@Service
public class DeliveryPlanManagerImpl extends ServiceImpl<DeliveryPlanMapper, DeliveryPlanPO>
implements DeliveryPlanManager, CommonService {
    private static final String PLAN_STATUS_CHANGE_NOTICE = "{\n" +
            "\t\"msg_type\": \"post\",\n" +
            "\t\"content\": {\n" +
            "\t\t\"post\": {\n" +
            "\t\t\t\"zh_cn\": {\n" +
            "\t\t\t\t\"title\": \"交付计划状态变更\",\n" +
            "\t\t\t\t\"content\": [\n" +
            "\t\t\t\t\t[\n" +
            "\t\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\t\"tag\": \"text\",\n" +
            "\t\t\t\t\t\t\t\"text\": \"%s\"\n" +
            "\t\t\t\t\t\t},\n" +
            "\t\t\t\t\t\t{\n" +
            "\t\t\t\t\t\t\t\"tag\": \"at\",\n" +
            "\t\t\t\t\t\t\t\"user_id\": \"all\",\n" +
            "\t\t\t\t\t\t\t\"user_name\": \"所有人\"\n" +
            "\t\t\t\t\t\t}\n" +
            "\t\t\t\t\t]\n" +
            "\t\t\t\t]\n" +
            "\t\t\t}\n" +
            "\t\t}\n" +
            "\t}\n" +
            "}";
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private VisualTemplateManager visualTemplateManager;
    @Autowired
    private OperatorUtil operatorUtil;

    private OkHttpClient httpClient = new OkHttpClient();

    @Override
    public Long addDeliveryPlan(DeliveryPlanBO bo) {
        Assert.notNull(bo, "入参不可为空");
        Assert.isTrue(StringUtils.isNotBlank(bo.getName()), "交付计划名称不可为空");

        DeliveryPlanPO deliveryPlanPO = lambdaQuery().eq(DeliveryPlanPO::getName, bo.getName()).one();
        Assert.isNull(deliveryPlanPO, "已存在相同名称的计划");

        operatorUtil.init(bo.getCreateBy(), null, null, null);
        Long planId = hostTimeIdg.generateId().longValue();
        DeliveryPlanPO po = new DeliveryPlanPO();
        po.setPlanId(planId);
        po.setDirector(bo.getDirector());
        po.setName(bo.getName());
        po.setDescription(bo.getDesc());
        po.setMaxTimes(bo.getLimit());
        po.setMobile(bo.getMobile());
        po.setPeriod(bo.getPeriod());
        po.setTemplateId(bo.getTemplateId());
        po.setTenantCode(bo.getTenantCode());
        po.setTenantName(bo.getTenantName());
        po.setNotifyUrl(bo.getNotifyUrl());
        po.setCallbackUrl(bo.getCallbackUrl());
        po.setIsNotify(bo.getIsNotify());
        po.setProduceWay(bo.getProduceWay());
        po.setHasPeriod(bo.getHasPeriod());
        save(po);
        return planId;
    }

    @Override
    public ResponsePageQueryDO<List<DeliveryPlanDTO>> pageQuery(DeliveryPlanSearchBO bo) {
        ResponsePageQueryDO<List<DeliveryPlanDTO>> response = new ResponsePageQueryDO<>();
        LambdaQueryWrapper<DeliveryPlanPO> queryWrapper = Wrappers.lambdaQuery(DeliveryPlanPO.class);
        queryWrapper.like(StringUtils.isNotBlank(bo.getName()), DeliveryPlanPO::getName, bo.getName())
                .like(StringUtils.isNotBlank(bo.getTenantName()), DeliveryPlanPO::getTenantName, bo.getTenantName())
                .orderByDesc(DeliveryPlanPO::getCreateDt);

        IPage<DeliveryPlanPO> pageResult = baseMapper.selectPage(convert(bo), queryWrapper);
        List<DeliveryPlanPO> data = pageResult.getRecords();
        log.info("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        List<VisualTemplatePO> templates = visualTemplateManager.lambdaQuery().in(VisualTemplatePO::getTemplateId, data.stream().map(DeliveryPlanPO::getTemplateId).distinct().collect(Collectors.toList())).list();
        Map<Long, VisualTemplatePO> templateMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(templates)) {
            templateMap.putAll(templates.stream().collect(Collectors.toMap(VisualTemplatePO::getTemplateId, Function.identity(), (v1, v2) -> v1)));
        }
        List<DeliveryPlanDTO> dtos = new ArrayList<>();
        dtos.addAll(data.stream().map(po -> {
            DeliveryPlanDTO dto = new DeliveryPlanDTO();
            dto.setPlanId(po.getPlanId());
            dto.setCreateDt(po.getCreateDt());
            dto.setDesc(po.getDescription());
            dto.setName(po.getName());
            dto.setDirector(po.getDirector());
            dto.setLimit(po.getMaxTimes());
            dto.setMobile(po.getMobile());
            dto.setPeriod(po.getPeriod());
            dto.setTenantCode(po.getTenantCode());
            dto.setTenantName(po.getTenantName());
            dto.setTemplateId(po.getTemplateId());
            dto.setStatus(po.getStatus());
            dto.setCallbackUrl(po.getCallbackUrl());
            dto.setIsNotify(po.getIsNotify());
            dto.setNotifyUrl(po.getNotifyUrl());
            dto.setProduceWay(po.getProduceWay());
            dto.setHasPeriod(po.getHasPeriod());
            VisualTemplatePO templatePO = templateMap.get(po.getTemplateId());
            if(Objects.nonNull(templatePO)) {
                dto.setTemplateCoverUrl(templatePO.getCoverUrl());
                dto.setTemplateName(templatePO.getName());
            }
            return dto;
        }).collect(Collectors.toList()));
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        response.setDataResult(dtos);
        return response;
    }

    @Override
    public void udpateDeliveryPlan(DeliveryPlanBO bo) {
        Assert.notNull(bo, "入参不可为空");
        Assert.notNull(bo.getPlanId(), "交付计划id不可为空");

        DeliveryPlanPO deliveryPlanPO = lambdaQuery().eq(DeliveryPlanPO::getName, bo.getName()).one();
        Assert.isTrue(Objects.isNull(deliveryPlanPO) || Objects.equals(deliveryPlanPO.getPlanId(), bo.getPlanId()), "已存在相同名称的计划");


        DeliveryPlanPO po = lambdaQuery().eq(DeliveryPlanPO::getPlanId, bo.getPlanId()).one();
        Assert.notNull(po, "交互计划不存在");

        Integer oldStatus = po.getStatus();
        Integer newStatus = bo.getStatus();

        po.setDirector(bo.getDirector());
        po.setName(bo.getName());
        po.setDescription(bo.getDesc());
        po.setMaxTimes(bo.getLimit());
        po.setMobile(bo.getMobile());
        po.setPeriod(bo.getPeriod());
        if(StringUtils.isNotBlank(bo.getNotifyUrl())) {
            po.setNotifyUrl(bo.getNotifyUrl());
        }
        po.setCallbackUrl(bo.getCallbackUrl());
        if (Objects.nonNull(bo.getIsNotify())) {
            po.setIsNotify(bo.getIsNotify());
        }
        po.setProduceWay(bo.getProduceWay());
        po.setStatus(bo.getStatus());
        po.setHasPeriod(bo.getHasPeriod());
        updateById(po);

        // 状态变更，飞书消息通知
        if(!Objects.equals(oldStatus, newStatus) && Objects.equals(po.getIsNotify(), Const.ONE)) {
            String noticeMsg = String.format(PLAN_STATUS_CHANGE_NOTICE, "【"+po.getName()+"】于"+ DateUtil.format(new Date(), DatePattern.CHINESE_DATE_TIME_PATTERN)+(Objects.equals(bo.getStatus(), Const.ONE)?"开始运行":"停止运行"));
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json;charset=utf-8"), noticeMsg);
            Request.Builder builder = new Request.Builder();
            Request request = builder
                    .addHeader("content-type", "application/json")
                    .url(po.getNotifyUrl())
                    .post(requestBody)
                    .build();
            Call call = httpClient.newCall(request);

            call.enqueue(new Callback() {
                //请求时失败时调用
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("交付计划飞书消息通知失败", e);
                }

                //请求成功时调用
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    log.info("交付计划飞书消息通知成功");
                }
            });
        }
    }
}
