package com.dl.magicvideo.web.controllers.template;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogAddReq;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.PPT2Util;
import com.dl.magicvideo.biz.common.util.PPTUtil;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.account.trial.emuns.TenantDosageEnum;
import com.dl.magicvideo.biz.dal.account.trial.po.TenantDosageConfigPO;
import com.dl.magicvideo.biz.dal.visual.po.*;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.po.EsIndexAigcChatRecord;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.biz.manager.account.trial.TenantDosageConfigManager;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.AigcChatRecordManager;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordProduceIngBO;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.cos.CosFileUploadManager;
import com.dl.magicvideo.biz.manager.oplog.OpLogManager;
import com.dl.magicvideo.biz.manager.oplog.consts.OpLogConsts;
import com.dl.magicvideo.biz.manager.oplog.enums.OpObjectEnum;
import com.dl.magicvideo.biz.manager.oplog.enums.OpTypeEnum;
import com.dl.magicvideo.biz.manager.util.ProduceFailUtil;
import com.dl.magicvideo.biz.manager.visual.*;
import com.dl.magicvideo.biz.manager.visual.bo.*;
import com.dl.magicvideo.biz.manager.visual.dto.*;
import com.dl.magicvideo.biz.manager.visual.enums.InteractiveConfBizTypeEnum;
import com.dl.magicvideo.biz.manager.visual.enums.TemplateTypeEnum;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.aigc.chat.convert.AigcChatRecordConvert;
import com.dl.magicvideo.web.controllers.aigc.chat.vo.AigcChatRecordVO;
import com.dl.magicvideo.web.controllers.internal.visual.param.TagPageQueryParam;
import com.dl.magicvideo.web.controllers.internal.visual.vo.TagVO;
import com.dl.magicvideo.web.controllers.template.convert.VisualTemplateConvert;
import com.dl.magicvideo.web.controllers.template.param.*;
import com.dl.magicvideo.web.controllers.template.vo.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dl.magicvideo.web.controllers.template.convert.VisualTemplateConvert.cnvSimpleVisualTemplateDTO2VO;

/**
 * @describe: TemplateProcess
 * @author: zhousx
 * @date: 2023/2/1 15:21
 */
@Slf4j
@Component
public class VisualTemplateProcess extends AbstractController {
    @Autowired
    private ComponentVersionDetailManager componentVersionDetailManager;
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private VisualTemplateManager visualTemplateManager;
    @Autowired
    private VisualCardManager visualCardManager;
    @Autowired
    private VisualProduceJobManager visualProduceJobManager;
    @Autowired
    private VisualDynamicNodeManager visualDynamicNodeManager;
    @Autowired
    private DataProductManager dataProductManager;
    @Autowired
    private TemplateProductManager templateProductManager;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private VisualProduceJobExtendManager visualProduceJobExtendManager;
    @Resource
    private TenantInfoManager tenantInfoManager;
    @Autowired
    private DeliveryPlanManager deliveryPlanManager;
    @Autowired
    private AccountTenantTrialManager accountTenantTrialManager;
    @Autowired
    private VisualProduceBatchManager visualProduceBatchManager;
    @Autowired
    private CosFileUploadManager cosFileUploadManager;
    @Autowired
    private OpLogManager opLogManager;
    @Resource
    private RedisUtil redisUtil;
    @Autowired
    private VisualAiConfigManager visualAiConfigManager;
    @Resource
    private AigcChatRecordManager aigcChatRecordManager;

    @Value("${visual.produce.tmpDir}")
    private String tmpDir;

    @Value("${dl.batchTemplateIds}")
    private String batchTemplateIds;

    @Resource
    private TagManager tagManager;
    @Resource
    private TemplateCollectManager templateCollectManager;

    @Resource
    private TenantDosageConfigManager tenantDosageConfigManager;

    public static final String COS_PATH_CONTENT_IMAGE = "visual/maimage";

    public static final String EXCEL_BATCH_PRODUCE_LOCK_KEY_PREFIX = "excel_batch_produce:";

    public static final Long EXCEL_BATCH_PRODUCE_LOCK_TIME = 60 * 60L;

    public static final String JOB_PREVIEW_KEY_PREFIX = "dl.job.preview.key.";

    public static final Long FIVE_MINUTES_EXPIRE_SECONDS = 5 * 60L;

    public ResultModel<VisualTemplateVO> add(VisualTemplateAddParam param) {
        VisualTemplateBO bo = new VisualTemplateBO();
        bo.setName(param.getName());
        bo.setCoverUrl(param.getCoverUrl());
        bo.setResolution(param.getResolution());
        bo.setResolutionType(param.getResolutionType());
        bo.setBgMusic(param.getBgMusic());
        bo.setBgMusicParam(param.getBgMusicParam());
        bo.setTtsParam(param.getTtsParam());
        bo.setReplaceData(param.getReplaceData());
        bo.setDuration(param.getDuration());
        bo.setIsShow(param.getIsShow());
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setCreatorName(operatorUtil.getUserName());
        bo.setComponentVersion(param.getComponentVersion());
        Long templateId = visualTemplateManager.add(bo);
        Assert.notNull(templateId, "模板保存失败。");

        //保存新增模板日志
        this.saveAddTemplateLog(templateId, getIp(), operatorUtil.getOperator(), operatorUtil.getUserName(),
                operatorUtil.getTenantCode());

        VisualTemplateVO vo = new VisualTemplateVO();
        vo.setTemplateId(templateId + "");
        return ResultModel.success(vo);
    }

    public ResultModel<VisualTemplateVO> update(VisualTemplateUpdateParam param) {
        Long templateId = Long.valueOf(param.getTemplateId());
        VisualTemplatePO exitTemplatePO = visualTemplateManager.lambdaQuery()
                .eq(VisualTemplatePO::getTemplateId, templateId).one();
        Assert.isTrue(Objects.nonNull(exitTemplatePO) && Objects.equals(exitTemplatePO.getIsDeleted(), Const.ZERO),
                "模板不存在或已删除。");

        VisualTemplateBO bo = new VisualTemplateBO();
        checkTemplateTenantCode(templateId);
        bo.setTemplateId(templateId);
        bo.setName(param.getName());
        bo.setCoverUrl(param.getCoverUrl());
        bo.setResolution(param.getResolution());
        bo.setResolutionType(param.getResolutionType());
        bo.setBgMusic(param.getBgMusic());
        bo.setBgMusicParam(param.getBgMusicParam());
        bo.setTtsParam(param.getTtsParam());
        bo.setReplaceData(param.getReplaceData());
        bo.setApiData(param.getApiDataList());
        bo.setDuration(param.getDuration());
        List<VisualDynamicNodePO> existNodes = null;
        if (CollectionUtils.isNotEmpty(param.getCards())) {
            existNodes = visualDynamicNodeManager.lambdaQuery().eq(VisualDynamicNodePO::getTemplateId, templateId)
                    .list();
            Map<Long, String> existNodeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(existNodes)) {
                existNodeMap.putAll(existNodes.stream().filter(x -> StringUtils.isNotBlank(x.getCoverUrl())).collect(
                        Collectors.toMap(VisualDynamicNodePO::getNodeId, VisualDynamicNodePO::getCoverUrl,
                                (v1, v2) -> v1)));
            }
            bo.setCards(param.getCards().stream().map(cardUpdateParam -> {
                CardBO cardBO = new CardBO();
                cardBO.setTemplateId(templateId);
                cardBO.setName(cardUpdateParam.getName());
                if (StringUtils.isNumeric(cardUpdateParam.getCardId())) {
                    cardBO.setCardId(Long.valueOf(cardUpdateParam.getCardId()));
                }
                cardBO.setCoverUrl(cardUpdateParam.getCoverUrl());
                cardBO.setResolution(cardUpdateParam.getResolution());
                cardBO.setRenderData(cardUpdateParam.getRenderData());
                cardBO.setDuration(cardBO.getDuration());
                VisualTemplateConvert.buildLightEditConfig(cardUpdateParam, cardBO);
                if (CollectionUtils.isNotEmpty(cardUpdateParam.getDynamicNodes())) {
                    cardBO.setDynamicNodes(cardUpdateParam.getDynamicNodes().stream().map(dynamicNodeParam -> {
                        DynamicNodeBO dynamicNodeBO = new DynamicNodeBO();
                        if (StringUtils.isNumeric(dynamicNodeParam.getNodeId())) {
                            Long nodeId = Long.valueOf(dynamicNodeParam.getNodeId());
                            dynamicNodeBO.setNodeId(nodeId);
                            dynamicNodeBO.setCoverUrl(existNodeMap.get(nodeId));
                        }
                        dynamicNodeBO.setType(dynamicNodeParam.getId());
                        dynamicNodeBO.setIsEnabled(dynamicNodeParam.getIsEnabled());
                        dynamicNodeBO.setDuration(dynamicNodeParam.getDuration());
                        dynamicNodeBO.setExpression(dynamicNodeParam.getExpression());
                        dynamicNodeBO.setExpressionFlag(dynamicNodeParam.getExpressionFlag());
                        if (CollectionUtils.isNotEmpty(dynamicNodeParam.getTtsList())) {
                            dynamicNodeBO.setTtsList(dynamicNodeParam.getTtsList().stream().map(ttsConfigParam -> {
                                DynamicNodeBO.TtsConfigBO ttsConfigBO = new DynamicNodeBO.TtsConfigBO();
                                ttsConfigBO.setTtsId(ttsConfigParam.getTtsId());
                                ttsConfigBO.setContent(ttsConfigParam.getContent());
                                ttsConfigBO.setStart(ttsConfigParam.getStart());
                                ttsConfigBO.setEnableSubtitle(ttsConfigParam.getEnableSubtitle());
                                ttsConfigBO.setMaxLength(ttsConfigParam.getMaxLength());
                                ttsConfigBO.setHide(ttsConfigParam.isHide());
                                ttsConfigBO.setEndDelay(ttsConfigParam.getEndDelay());
                                ttsConfigBO.setSubtitleKeyWordsHighlight(ttsConfigParam.getSubtitleKeyWordsHighlight());
                                ttsConfigBO.setKeyTime(ttsConfigParam.getKeyTime());
                                return ttsConfigBO;
                            }).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(dynamicNodeParam.getDmList())) {
                            dynamicNodeBO.setDmList(dynamicNodeParam.getDmList().stream().map(dmConfigParam -> {
                                DynamicNodeBO.DigitalManConfigBO dmConfigBO = new DynamicNodeBO.DigitalManConfigBO();
                                dmConfigBO.setDmId(dmConfigParam.getDmId());
                                dmConfigBO.setContent(dmConfigParam.getContent());
                                dmConfigBO.setStart(dmConfigParam.getStart());
                                dmConfigBO.setEnableSubtitle(dmConfigParam.getEnableSubtitle());
                                dmConfigBO.setMaxLength(dmConfigParam.getMaxLength());
                                dmConfigBO.setHide(dmConfigParam.isHide());
                                dmConfigBO.setEndDelay(dmConfigParam.getEndDelay());
                                dmConfigBO.setKeyTime(dmConfigParam.getKeyTime());
                                return dmConfigBO;
                            }).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(dynamicNodeParam.getVideoList())) {
                            dynamicNodeBO.setVideoList(dynamicNodeParam.getVideoList().stream().map(videoConfigParam -> {
                                DynamicNodeBO.VideoConfigBO videoConfigBO = new DynamicNodeBO.VideoConfigBO();
                                videoConfigBO.setContent("备注");
                                videoConfigBO.setVideoId(videoConfigParam.getVideoId());
                                videoConfigBO.setStart(videoConfigParam.getStart());
                                videoConfigBO.setHide(videoConfigParam.isHide());
                                videoConfigBO.setUrl(videoConfigParam.getUrl());
                                videoConfigBO.setVolume(videoConfigParam.getVolume());
                                videoConfigBO.setDuration(videoConfigParam.getDuration());
                                videoConfigBO.setCroppedDuration(videoConfigParam.getCroppedDuration());
                                videoConfigBO.setActiveRotationMode(videoConfigParam.getActiveRotationMode());
                                videoConfigBO.setStartDelay(videoConfigParam.getStartDelay());
                                videoConfigBO.setKeyTime(videoConfigParam.getKeyTime());
                                return videoConfigBO;
                            }).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(dynamicNodeParam.getAudioList())) {
                            dynamicNodeBO.setAudioList(dynamicNodeParam.getAudioList().stream().map(audioConfigParam -> {
                                DynamicNodeBO.AudioConfigBO videoConfigBO = new DynamicNodeBO.AudioConfigBO();
                                videoConfigBO.setContent("备注");
                                videoConfigBO.setAudioId(audioConfigParam.getAudioId());
                                videoConfigBO.setStart(audioConfigParam.getStart());
                                videoConfigBO.setHide(audioConfigParam.isHide());
                                videoConfigBO.setUrl(audioConfigParam.getUrl());
                                videoConfigBO.setVolume(audioConfigParam.getVolume());
                                videoConfigBO.setDuration(audioConfigParam.getDuration());
                                videoConfigBO.setCroppedDuration(audioConfigParam.getCroppedDuration());
                                videoConfigBO.setActiveRotationMode(audioConfigParam.getActiveRotationMode());
                                videoConfigBO.setStartDelay(audioConfigParam.getStartDelay());
                                videoConfigBO.setFadeInTime(audioConfigParam.getFadeInTime());
                                videoConfigBO.setFadeOutTime(audioConfigParam.getFadeOutTime());
                                videoConfigBO.setKeyTime(audioConfigParam.getKeyTime());
                                return videoConfigBO;
                            }).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(dynamicNodeParam.getDataSheetList())) {
                            dynamicNodeBO.setDataSheetList(dynamicNodeParam.getDataSheetList().stream().filter(DynamicNodeParam.DataSheetParam::isDynamicDuration).map(dataSheetParam -> {
                                if(dataSheetParam.getPageSize() == 0){
                                    throw BusinessServiceException.getInstance("动态图表组件，pageSize不可设置为0");
                                }
                                DynamicNodeBO.DataSheetBO dataSheetBO = new DynamicNodeBO.DataSheetBO();
                                dataSheetBO.setStart(dataSheetParam.getScrollStartDelay());
                                dataSheetBO.setEndDelay(dataSheetParam.getScrollEndDelay());
                                dataSheetBO.setDataSheetId(dataSheetParam.getId());
                                dataSheetBO.setKeyTime(dataSheetParam.getKeyTime());
                                dataSheetBO.setHide(dataSheetParam.isHide());
                                DataSheetContentDTO contentDTO = getDataSheetContentDTO(dataSheetParam);
                                dataSheetBO.setContent(JsonUtils.toJSON(contentDTO));
                                return dataSheetBO;
                            }).collect(Collectors.toList()));
                        }
                        return dynamicNodeBO;
                    }).collect(Collectors.toList()));
                }
                return cardBO;
            }).collect(Collectors.toList()));
        }
        visualTemplateManager.update(bo);

        //保存修改的操作日志
        this.saveUpdateTemplateLog(exitTemplatePO, existNodes, param, getIp(), operatorUtil.getOperator(),
                operatorUtil.getUserName());

        VisualTemplateDTO templateDTO = visualTemplateManager.detail(templateId, operatorUtil.getTenantCode());
        return ResultModel.success(VisualTemplateConvert.cnvVisualTemplateDTO2VO(templateDTO));
    }

    private static DataSheetContentDTO getDataSheetContentDTO(DynamicNodeParam.DataSheetParam dataSheetParam) {
        DataSheetContentDTO contentDTO = new DataSheetContentDTO();
        contentDTO.setDynamicDuration(dataSheetParam.isDynamicDuration());
        contentDTO.setPageSize(dataSheetParam.getPageSize());
        contentDTO.setAnimationTime(dataSheetParam.getAnimationTime());
        contentDTO.setBindKey(dataSheetParam.getBindKey());
        contentDTO.setPageScrollTime(dataSheetParam.getPageScrollTime());
        return contentDTO;
    }

    public ResultModel<VisualTemplateVO> addIntegrity(VisualTemplateAddIntegrityParam param) {
        //1.新增模板完整信息
        VisualTemplateBO insertBO = VisualTemplateConvert.cnvVisualTemplateAddIntegrityParam2BO(param);
        insertBO.setTenantCode(operatorUtil.getTenantCode());
        insertBO.setCreatorName(operatorUtil.getUserName());
        Long templateId = visualTemplateManager.addIntegrity(insertBO);

        //2.保存新增模板日志
        this.saveAddTemplateLog(templateId, getIp(), operatorUtil.getOperator(), operatorUtil.getUserName(),
                operatorUtil.getTenantCode());

        VisualTemplateDTO templateDTO = visualTemplateManager.detail(templateId, operatorUtil.getTenantCode());
        return ResultModel.success(VisualTemplateConvert.cnvVisualTemplateDTO2VO(templateDTO));
    }

    public ResultModel<VisualTemplateVO> detail(Long templateId) {
        VisualTemplateDTO templateDTO = visualTemplateManager.detail(templateId, operatorUtil.getTenantCode());
        Assert.notNull(templateDTO, "模板不存在。");
        return ResultModel.success(VisualTemplateConvert.cnvVisualTemplateDTO2VO(templateDTO));
    }

    public ResultPageModel<VisualTemplateVO> list(TemplatePageQueryParam param) {
        TemplateSearchBO bo = new TemplateSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setName(param.getName());
        bo.setStatus(param.getStatus());
        bo.setCreateBy(getUserId());
        bo.setIsSys(param.getIsSys());
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setResolutionType(param.getResolutionType());
        bo.setSortType(param.getSortType());
        bo.setShareConfState(param.getShareConfState());
        bo.setIsManager(param.getIsManager());
        bo.setTemplateId(param.getTemplateId());
        bo.setType(TemplateTypeEnum.NORMAL.getType());
        if (BooleanUtils.isTrue(param.isCollect())) {
            bo.setCollectUserId(operatorUtil.getOperator());
        }
        ResponsePageQueryDO<List<VisualTemplateDTO>> result = visualTemplateManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        Map<Long, Long> templateCollectMap;
        if (!BooleanUtils.isTrue(param.isCollect())){
            List<Long> templateIds = result.getDataResult().stream().map(VisualTemplateDTO::getTemplateId).collect(Collectors.toList());
            List<TemplateCollectPO> collectList = templateCollectManager.lambdaQuery().eq(TemplateCollectPO::getUserId, operatorUtil.getOperator())
                    .in(TemplateCollectPO::getTemplateId, templateIds).list();
            if (CollectionUtils.isNotEmpty(collectList)) {
                templateCollectMap = collectList.stream().collect(Collectors.toMap(TemplateCollectPO::getTemplateId, TemplateCollectPO::getTemplateId));
            } else {
                templateCollectMap = null;
            }
        } else {
            templateCollectMap = result.getDataResult().stream().collect(Collectors.toMap(VisualTemplateDTO::getTemplateId, VisualTemplateDTO::getTemplateId));
        }


        List<VisualTemplateVO> vos = result.getDataResult().stream().map(e-> cnvSimpleVisualTemplateDTO2VO(e, templateCollectMap)).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    public ResultPageModel<VisualTemplateVO> authList(AuthedTemplatePageQueryParam param) {
        TemplateSearchBO bo = new TemplateSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setName(param.getName());
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setResolutionType(param.getResolutionType());
        bo.setFirstCategory(param.getFirstCategory());
        bo.setSecondCategory(param.getSecondCategory());
        bo.setStatus(param.getStatus());
        bo.setIsPPTType(param.getIsPPTType());
        bo.setTagIds(param.getTagIds());
        ResponsePageQueryDO<List<VisualTemplateDTO>> result = visualTemplateManager.authedTemplatePageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<VisualTemplateVO> vos = result.getDataResult().stream().map(VisualTemplateConvert::cnvVisualTemplateDTO2VO)
                .collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    public ResultModel<Void> delete(Long templateId) {
        checkTemplateTenantCode(templateId);
        visualTemplateManager.delete(templateId);
        return ResultModel.success(null);
    }

    private void checkTemplateTenantCode(Long templateId) {
//        VisualTemplatePO po = visualTemplateManager.lambdaQuery().eq(VisualTemplatePO::getTemplateId, templateId)
//                .eq(VisualTemplatePO::getIsDeleted, Const.ZERO).one();
//        Assert.notNull(po,"模板不存在");
//        Assert.isTrue(Objects.equals(po.getTenantCode(), operatorUtil.getTenantCode()), "模板不属于当前租户，不可操作");
    }

    public ResultModel<CardVO> addCard(CardAddParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板id格式错误。");
        CardBO cardAddBO = new CardBO();
        cardAddBO.setName(param.getName());
        cardAddBO.setCoverUrl(param.getCoverUrl());
        cardAddBO.setTemplateId(Long.valueOf(param.getTemplateId()));
        cardAddBO.setResolution(param.getResolution());
        VisualCardDTO dto = visualCardManager.add(cardAddBO);

        CardVO vo = new CardVO();
        vo.setCardId(dto.getCardId() + "");
        vo.setName(dto.getName());
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setTemplateId(dto.getTemplateId() + "");
        vo.setResolution(dto.getResolution());
        vo.setRenderData(dto.getRenderData());
        return ResultModel.success(vo);
    }

    public ResultModel<Void> deleteCard(Long cardId) {
        visualCardManager.delete(cardId);
        return ResultModel.success(null);
    }

    public ResultModel<PreviewVO> preview(PreviewParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板id不可为空。");
        Assert.isTrue(StringUtils.isNumeric(param.getJobId()), "任务id不可为空。");

        Long startTimemillis = System.currentTimeMillis();
        String key = Const.JOB_PREVIEW_KEY_PREFIX + param.getJobId();
        String value = redisUtil.get(key);
        Long endTimemillis = System.currentTimeMillis();
        log.info("/preview接口 redis耗时:{}", endTimemillis - startTimemillis);
        if (StringUtils.isNotBlank(value)) {
            if (Const.NULL.equals(value)) {
                return ResultModel.success(null);
            }

            PreviewVO previewVO = JSONUtil.toBean(value, PreviewVO.class);
            if (CollectionUtils.isNotEmpty(previewVO.getCards())) {
                previewVO.getCards().forEach(card -> {
                    if (StringUtils.isNotBlank(card.getLightEditConfigs())) {
                        //坑：previewDTO中的lightEditConfigs是包含了lightEditConfigs和crossClips 所以此处需要反序列化 提取出来
                        TemplateLightEditConfigDTO templateLightEditConfigDTO = JSONUtil
                                .toBean(card.getLightEditConfigs(), TemplateLightEditConfigDTO.class);
                        card.setLightEditConfigs(JSONUtil.toJsonStr(templateLightEditConfigDTO.getLightEditConfigs()));
                        card.setCrossClips(JSONUtil.toJsonStr(templateLightEditConfigDTO.getCrossClips()));
                    }
                });
            }
            Long doneTimemillis = System.currentTimeMillis();
            log.info("/preview接口 总耗时:{}", doneTimemillis - startTimemillis);
            return ResultModel.success(previewVO);
        }

        VisualProduceJobPO visualProduceJobPO = visualProduceJobManager.lambdaQuery()
                .eq(VisualProduceJobPO::getJobId, param.getJobId()).one();
        if (Objects.isNull(visualProduceJobPO)) {
            //放置"null"到redis中，防止缓存穿透，缓存5分钟
            redisUtil.set(key, Const.NULL, FIVE_MINUTES_EXPIRE_SECONDS);
            throw BusinessServiceException.getInstance("任务不存在");
        }

        VisualTemplatePO templatePO = visualTemplateManager.getOne(Wrappers.lambdaQuery(VisualTemplatePO.class)
                .eq(VisualTemplatePO::getTemplateId, param.getTemplateId()));
        if (Objects.isNull(templatePO)) {
            //放置"null"到redis中，防止缓存穿透，缓存5分钟
            redisUtil.set(key, Const.NULL, FIVE_MINUTES_EXPIRE_SECONDS);
            throw BusinessServiceException.getInstance("模板不存在");
        }

        PreviewVO previewVO = JSONUtil.toBean(visualProduceJobPO.getPreviewData(), PreviewVO.class);
        previewVO.setSource(visualProduceJobPO.getSource());
        previewVO.setComponentVersion(templatePO.getComponentVersion());
        //坑：visualProduceJobPO.getPreviewData()中的lightEditConfigs是包含了lightEditConfigs和crossClips，所以此处先塞缓存，下面再去额外处理lightEditConfigs和crossClips
        redisUtil.set(key, JSONUtil.toJsonStr(previewVO), FIVE_MINUTES_EXPIRE_SECONDS);

        if (CollectionUtils.isNotEmpty(previewVO.getCards())) {
            VisualTemplateDTO jobTemplateData = JSONUtil
                    .toBean(visualProduceJobPO.getTemplateData(), VisualTemplateDTO.class);
            Map<Long, VisualCardDTO> cardIdMap = jobTemplateData.getCards().stream()
                    .collect(Collectors.toMap(VisualCardDTO::getCardId, Function.identity()));
            previewVO.getCards().forEach(card -> {
                VisualCardDTO visualCardDTO = cardIdMap.get(Long.valueOf(card.getCardId()));
                if (StringUtils.isNotBlank(visualCardDTO.getLightEditConfigs())) {
                    TemplateLightEditConfigDTO templateLightEditConfigDTO = JSONUtil
                            .toBean(visualCardDTO.getLightEditConfigs(), TemplateLightEditConfigDTO.class);
                    card.setLightEditConfigs(JSONUtil.toJsonStr(templateLightEditConfigDTO.getLightEditConfigs()));
                    card.setCrossClips(JSONUtil.toJsonStr(templateLightEditConfigDTO.getCrossClips()));
                }
            });
        }

        Long doneTimemillis = System.currentTimeMillis();
        log.info("/preview接口 走到了该接口刷数据到redis中，总耗时:{}", doneTimemillis - startTimemillis);
        return ResultModel.success(previewVO);
    }

    public ResultPageModel<VisualProduceJobVO> jobList(VisualProduceJobPageQueryParam param) {
        ProduceJobSearchBO bo = new ProduceJobSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setName(param.getName());
        bo.setStatusList(param.getStatusList());
        bo.setSortType(param.getSortType());
        if (Objects.nonNull(param.getResolutionType())) {
            bo.setResolutionType(String.valueOf(param.getResolutionType()));
        }
        bo.setStartTime(param.getStartTime());
        bo.setEndTime(param.getEndTime());
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setRecommendState(param.getRecommendState());
        bo.setType(param.getType());
        if (Const.ONE.equals(param.getOnlyMine())) {
            bo.setCreatorId(operatorUtil.getOperator());
        }
        ResponsePageQueryDO<List<VisualProduceJobDTO>> result = visualProduceJobManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<VisualProduceJobVO> vos = result.getDataResult().stream().map(dto -> {
            VisualProduceJobVO vo = new VisualProduceJobVO();
            vo.setJobId(dto.getJobId() + "");
            vo.setTemplateId(dto.getTemplateId() + "");
            vo.setName(dto.getName());
            vo.setCoverUrl(dto.getCoverUrl());
            vo.setStatus(dto.getStatus());
            vo.setVideoUrl(dto.getVideoUrl());
            vo.setCreateDt(dto.getCreateDt());
            vo.setDuration(dto.getDuration());
            vo.setResolution(dto.getResolution());
            vo.setResolutionType(dto.getResolutionType());
            vo.setCompleteDt(dto.getCompleteDt());
            vo.setSize(dto.getSize());
            vo.setCreatorName(dto.getCreatorName());
            vo.setShareConfState(dto.getShareConfState());
            vo.setRecommendState(dto.getRecommendState());
            vo.setTemplateName(dto.getTemplateName());
            vo.setOtherFormatVideoUrl(dto.getOtherFormatVideoUrl());
            vo.setFailReason(ProduceFailUtil.wrapperFailReason(dto.getFailReason()));
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    public ResultPageModel<VisualProduceJobVO> jogMessage() {
        ResponsePageQueryDO<List<VisualProduceJobDTO>> result =
                visualProduceJobManager.pageQuery(operatorUtil.getTenantCode());
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<VisualProduceJobVO> vos = result.getDataResult().stream().map(dto -> {
            VisualProduceJobVO vo = new VisualProduceJobVO();
            vo.setJobId(dto.getJobId() + "");
            vo.setTemplateId(dto.getTemplateId() + "");
            vo.setName(dto.getName());
            vo.setCoverUrl(dto.getCoverUrl());
            vo.setStatus(dto.getStatus());
            vo.setVideoUrl(dto.getVideoUrl());
            vo.setCreateDt(dto.getCreateDt());
            vo.setDuration(dto.getDuration());
            vo.setResolution(dto.getResolution());
            vo.setResolutionType(dto.getResolutionType());
            vo.setCompleteDt(dto.getCompleteDt());
            vo.setSize(dto.getSize());
            vo.setCreatorName(dto.getCreatorName());
            return vo;
        }).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    private void checkDosage(){
        TenantDosageConfigPO configPO = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, operatorUtil.getTenantCode()).eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.DIGITAL_MAN.getType()).one();
        if (Objects.nonNull(configPO) && !Objects.equals(configPO.getBalance(), Const.MINUS_ONE_LONG) && configPO.getBalance() * 60 * 1000 <= configPO.getUsed()) {
            throw BusinessServiceException.getInstance("数字人合成分钟数不足，请联系商务处理");
        }
        TenantDosageConfigPO configPO2 = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, operatorUtil.getTenantCode()).eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.VIDEO.getType()).one();
        if (Objects.nonNull(configPO2) && !Objects.equals(configPO2.getBalance(), Const.MINUS_ONE_LONG) && configPO2.getBalance() * 60 * 1000 <= configPO2.getUsed()) {
            throw BusinessServiceException.getInstance("视频合成分钟数不足，请联系商务处理");
        }
        TenantDosageConfigPO configPO3 = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, operatorUtil.getTenantCode()).eq(TenantDosageConfigPO::getConfigType, TenantDosageEnum.TTS.getType()).one();
        if (Objects.nonNull(configPO3) && !Objects.equals(configPO3.getBalance(), Const.MINUS_ONE_LONG) && configPO3.getBalance() * 10000 <= configPO3.getUsed()) {
            throw BusinessServiceException.getInstance("TTS合成字数不足，请联系商务处理");
        }
    }
    public ResultModel<VisualProduceCreateVO> produce(ProduceParam param) {
        log.info("调用合成接口,入参: param:{}", JSONUtil.toJsonStr(param));
        checkDosage();
        VisualTemplatePO visualTemplatePO = visualTemplateManager.lambdaQuery()
                .select(VisualTemplatePO::getTemplateId, VisualTemplatePO::getApiData, VisualTemplatePO::getName)
                .eq(VisualTemplatePO::getTemplateId, param.getTemplateId())
                .eq(VisualTemplatePO::getIsDeleted, Const.ZERO).one();
        Assert.notNull(visualTemplatePO, "不存在的模板");
        Assert.isTrue(visualTemplatePO.getName().length() <= 60, "模板名称超过60字，请删减后再提交合成");

        Integer balance = null;
        if (TemplateTypeEnum.NORMAL.getType().equals(visualTemplatePO.getType())) {
            balance = accountTenantTrialManager.checkTenantBalance(operatorUtil.getTenantCode(), Const.ONE);
        }

        TemplateLightEditConfigDTO lightEditConfigs = null;
        if (CollectionUtils.isNotEmpty(param.getLightEditConfigs()) || CollectionUtils.isNotEmpty(param.getCrossClips())) {
            lightEditConfigs = new TemplateLightEditConfigDTO();
            lightEditConfigs.setLightEditConfigs(param.getLightEditConfigs());
            lightEditConfigs.setCrossClips(param.getCrossClips());
        }

        ProduceJobBO produceJobBO = new ProduceJobBO();
        produceJobBO.setTemplateId(Long.valueOf(param.getTemplateId()));
        produceJobBO.setReplaceData(param.getReplaceData());
        produceJobBO.setSource(param.getSource());
        produceJobBO.setLightEditConfigs(lightEditConfigs);
        produceJobBO.setExtUserId(null);
        produceJobBO.setDigitalManParamBO(null);
        produceJobBO.setRenderData(param.getRenderData());
        produceJobBO.setResolution(param.getResolution());
        produceJobBO.setJobCoverUrl(param.getJobCoverUrl());
        produceJobBO.setApiData(visualTemplatePO.getApiData());
        produceJobBO.setProduceJobName(param.getProduceJobName());
        produceJobBO.setDynamicNodes(Objects.nonNull(param.getDynamicNodes()) ?
                param.getDynamicNodes() :
                convert(visualDynamicNodeManager, visualAiConfigManager, param.getTemplateId()));

        Long jobId = visualProduceJobManager.produce(produceJobBO);
        VisualProduceCreateVO result = new VisualProduceCreateVO();
        result.setBalance(String.valueOf(balance));
        result.setJobId(String.valueOf(jobId));
        log.info("调用合成接口,入参: param:{},出参:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(result));
        return ResultModel.success(result);
    }

    public ResultModel<AigcChatRecordVO> aigcProduce(AigcProduceParam param) {
        //1.合成视频
        ResultModel<VisualProduceCreateVO> produceResultModel = this.produce(param);

        //2.新增一条视频合成中的聊天记录
        AigcChatRecordProduceIngBO produceIngBO = new AigcChatRecordProduceIngBO();
        produceIngBO.setJobId(Long.valueOf(produceResultModel.getDataResult().getJobId()));
        produceIngBO.setVideoName(param.getProduceJobName());
        produceIngBO.setCoverUrl(param.getJobCoverUrl());

        AigcChatRecordAddBO receiveRecordBO = new AigcChatRecordAddBO();
        receiveRecordBO.setSendDt(new Date());
        receiveRecordBO.setUserId(operatorUtil.getOperator());
        receiveRecordBO.setTenantCode(operatorUtil.getTenantCode());
        receiveRecordBO.setContent(JSONUtil.toJsonStr(produceIngBO));
        receiveRecordBO.setContentType(AigcChatRecordContentTypeEnum.PRODUCE_ING.getType());
        receiveRecordBO.setType(AigcChatRecordTypeEnum.SYSTEM.getType());
        receiveRecordBO.setFromTool(param.getFromTool());
        EsIndexAigcChatRecord chatRecord = aigcChatRecordManager.add(receiveRecordBO);

        return ResultModel.success(AigcChatRecordConvert.cnvEsIndexAigcChatRecord2AigcChatRecordVO(chatRecord));
    }

    public static List<DynamicNodeDTO> convert(VisualDynamicNodeManager visualDynamicNodeManager,
                                               VisualAiConfigManager visualAiConfigManager, String templateId) {
        List<VisualDynamicNodePO> dynamicNodePOList = visualDynamicNodeManager.lambdaQuery()
                .eq(VisualDynamicNodePO::getTemplateId, templateId).list();
        if (CollectionUtils.isNotEmpty(dynamicNodePOList)) {
            return dynamicNodePOList.stream().map(visualDynamicNodePO -> {
                DynamicNodeDTO dynamicNodeDTO = new DynamicNodeDTO();
                dynamicNodeDTO.setNodeId(visualDynamicNodePO.getNodeId());
                dynamicNodeDTO.setType(visualDynamicNodePO.getType());
                dynamicNodeDTO.setDuration(visualDynamicNodePO.getDuration());
                dynamicNodeDTO.setCoverUrl(visualDynamicNodePO.getCoverUrl());
                dynamicNodeDTO.setIsEnabled(visualDynamicNodePO.getIsEnabled());
                dynamicNodeDTO.setExpression(visualDynamicNodePO.getExpression());
                dynamicNodeDTO.setExpressionFlag(visualDynamicNodePO.getExpressionFlag());
                List<VisualAiConfigPO> ttsConfigPOList = visualAiConfigManager.lambdaQuery()
                        .eq(VisualAiConfigPO::getTemplateId, templateId)
                        .eq(VisualAiConfigPO::getNodeId, visualDynamicNodePO.getNodeId()).list();
                if (CollectionUtils.isNotEmpty(ttsConfigPOList)) {
                    dynamicNodeDTO.setTtsList(ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 0))
                            .map(visualAiConfigPO -> {
                                DynamicNodeDTO.TtsConfigDTO ttsConfigDTO = new DynamicNodeDTO.TtsConfigDTO();
                                ttsConfigDTO.setTtsId(visualAiConfigPO.getTtsId());
                                ttsConfigDTO.setContent(visualAiConfigPO.getContent());
                                ttsConfigDTO.setStart(visualAiConfigPO.getStart());
                                ttsConfigDTO.setEnableSubtitle(visualAiConfigPO.getEnableSubtitle());
                                ttsConfigDTO.setMaxLength(visualAiConfigPO.getMaxLength());
                                ttsConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                ttsConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                ttsConfigDTO.setSubtitleKeyWordsHighlight(visualAiConfigPO.getSubtitleKeyWordsHighlight());
                                ttsConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                return ttsConfigDTO;
                            }).collect(Collectors.toList()));
                    dynamicNodeDTO.setDmList(ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 1))
                            .map(visualAiConfigPO -> {
                                DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO = new DynamicNodeDTO.DigitalManConfigDTO();
                                dmConfigDTO.setDmId(visualAiConfigPO.getTtsId());
                                dmConfigDTO.setContent(visualAiConfigPO.getContent());
                                dmConfigDTO.setStart(visualAiConfigPO.getStart());
                                dmConfigDTO.setEnableSubtitle(visualAiConfigPO.getEnableSubtitle());
                                dmConfigDTO.setMaxLength(visualAiConfigPO.getMaxLength());
                                dmConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                dmConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                dmConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                return dmConfigDTO;
                            }).collect(Collectors.toList()));
                    dynamicNodeDTO.setVideoList(
                            ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 2))
                                    .map(visualAiConfigPO -> {
                                        DynamicNodeDTO.VideoConfigDTO videoConfigDTO = new DynamicNodeDTO.VideoConfigDTO();
                                        videoConfigDTO.setVideoId(visualAiConfigPO.getTtsId());
                                        videoConfigDTO.setStart(visualAiConfigPO.getStart());
                                        videoConfigDTO.setUrl(visualAiConfigPO.getUrl());
                                        videoConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                        videoConfigDTO.setCroppedDuration(visualAiConfigPO.getCroppedDuration());
                                        videoConfigDTO.setActiveRotationMode(visualAiConfigPO.getActiveRotationMode());
                                        videoConfigDTO.setStartDelay(visualAiConfigPO.getStartDelay());
                                        videoConfigDTO.setDuration(visualAiConfigPO.getDuration());
                                        videoConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                        videoConfigDTO.setVolume(visualAiConfigPO.getVolume());
                                        videoConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                        return videoConfigDTO;
                                    }).collect(Collectors.toList()));
                    dynamicNodeDTO.setAudioList(
                            ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 3))
                                    .map(visualAiConfigPO -> {
                                        DynamicNodeDTO.AudioConfigDTO audioConfigDTO = new DynamicNodeDTO.AudioConfigDTO();
                                        audioConfigDTO.setAudioId(visualAiConfigPO.getTtsId());
                                        audioConfigDTO.setStart(visualAiConfigPO.getStart());
                                        audioConfigDTO.setUrl(visualAiConfigPO.getUrl());
                                        audioConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                        audioConfigDTO.setCroppedDuration(visualAiConfigPO.getCroppedDuration());
                                        audioConfigDTO.setActiveRotationMode(visualAiConfigPO.getActiveRotationMode());
                                        audioConfigDTO.setStartDelay(visualAiConfigPO.getStartDelay());
                                        audioConfigDTO.setDuration(visualAiConfigPO.getDuration());
                                        audioConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                        audioConfigDTO.setVolume(visualAiConfigPO.getVolume());
                                        audioConfigDTO.setFadeInTime(visualAiConfigPO.getFadeInTime());
                                        audioConfigDTO.setFadeOutTime(visualAiConfigPO.getFadeOutTime());
                                        audioConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                        return audioConfigDTO;
                                    }).collect(Collectors.toList()));
                    dynamicNodeDTO.setDataSheetList(
                            ttsConfigPOList.stream().filter(e -> Objects.equals(e.getType(), 4))
                                    .map(visualAiConfigPO -> {
                                        DynamicNodeDTO.DataSheetDTO audioConfigDTO = new DynamicNodeDTO.DataSheetDTO();
                                        audioConfigDTO.setDataSheetId(visualAiConfigPO.getTtsId());
                                        audioConfigDTO.setContent(visualAiConfigPO.getContent());
                                        audioConfigDTO.setHide(Const.ONE.equals(visualAiConfigPO.getIsHide()));
                                        audioConfigDTO.setKeyTime(visualAiConfigPO.getKeyTime());
                                        audioConfigDTO.setStart(visualAiConfigPO.getStart());
                                        audioConfigDTO.setEndDelay(visualAiConfigPO.getEndDelay());
                                        return audioConfigDTO;
                                    }).collect(Collectors.toList()));
                }
                return dynamicNodeDTO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    public ResultModel<VisualProduceCreateVO> produceWithPlan(Long planId, String replaceData, Integer source) {
        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(operatorUtil.getTenantCode());
        DeliveryPlanPO deliveryPlanPO = deliveryPlanManager.lambdaQuery().eq(DeliveryPlanPO::getPlanId, planId).one();
        Assert.notNull(deliveryPlanPO, "交付计划不存在");
        if (Objects.equals(deliveryPlanPO.getHasPeriod(), Const.ONE)) {
            Date beginDt = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
            if (Objects.equals(deliveryPlanPO.getPeriod(), Const.ONE)) {
                beginDt = cn.hutool.core.date.DateUtil.beginOfWeek(new Date(), true);
            }
            if (Objects.equals(deliveryPlanPO.getPeriod(), Const.TWO)) {
                beginDt = cn.hutool.core.date.DateUtil.beginOfMonth(new Date());
            }
            int total = visualProduceBatchManager.lambdaQuery()
                    .notIn(VisualProduceBatchPO::getStatus, Arrays.asList(Const.THREE, Const.FOUR))
                    .eq(VisualProduceBatchPO::getPlanId, deliveryPlanPO.getPlanId())
                    .ge(VisualProduceBatchPO::getCreateDt, beginDt).count();
            Assert.isTrue(total < deliveryPlanPO.getMaxTimes(), "交付计划运行次数超限");
        }

        Long batchId = hostTimeIdg.generateId().longValue();
        Long jobId = visualProduceJobManager.doProduce(ProduceJobContextBO.builder()
                .templateId(deliveryPlanPO.getTemplateId())
                .replaceData(replaceData)
                .batchId(batchId)
                .source(source)
                .dmProduceMode(tenantInfo.getDmProduceMode())
                .planId(planId)
                .build());
        VisualProduceCreateVO result = new VisualProduceCreateVO();
        result.setJobId(jobId + "");
        return ResultModel.success(result);
    }

    private TemplateCopyBO buildTemplateCopyBO(CopyTemplateParam param){
        TemplateCopyBO bo = new TemplateCopyBO();
        bo.setTemplateId(Long.valueOf(param.getSourceTemplateId()));
        bo.setSys(false);
        bo.setTenantCode(operatorUtil.getTenantCode());
        bo.setCopySource(param.getSource());
        bo.setType(param.getType());
        return bo;
    }

    public ResultModel<TemplateCopyVO> copy(CopyTemplateParam param) {
        TemplateCopyDTO newTemplate = visualTemplateManager.copy(buildTemplateCopyBO(param));
        TemplateCopyVO vo = new TemplateCopyVO();
        if (Objects.nonNull(newTemplate)) {

            //保存新增模板日志
            this.saveAddTemplateLog(newTemplate.getTemplateId(), getIp(), operatorUtil.getOperator(),
                    operatorUtil.getUserName(), operatorUtil.getTenantCode());

            vo.setTemplateId("" + newTemplate.getTemplateId());
            if (CollectionUtils.isNotEmpty(newTemplate.getDynamicNodeDTOList())) {
                vo.setNodeId(newTemplate.getDynamicNodeDTOList().stream().map(e -> String.valueOf(e.getNodeId())).collect(Collectors.toList()));
            }
            if (Objects.nonNull(newTemplate.getCardId())) {
                vo.setCardId(newTemplate.getCardId().toString());
            }
        }
        return ResultModel.success(vo);
    }

    public ResultModel<VisualProduceCreateBatchVO> batchProduce(String title, Long templateId, List<String> prodCodeList) {
        List<TemplateProductPO> list = templateProductManager.lambdaQuery()
                .eq(TemplateProductPO::getTemplateId, templateId).in(TemplateProductPO::getProdCode, prodCodeList)
                .list();
        Integer balance = accountTenantTrialManager.checkTenantBalance(operatorUtil.getTenantCode(), list.size());
        Assert.isTrue(CollectionUtils.isNotEmpty(list), "产品code与该模板未关联");
        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(operatorUtil.getTenantCode());

        List<DataProductPO> dataProductPOList = dataProductManager.lambdaQuery()
                .in(DataProductPO::getProdCode, prodCodeList).list();
        Assert.isTrue(CollectionUtils.isNotEmpty(dataProductPOList), "产品code未查询到");
        Map<String, DataProductPO> prodMap = dataProductPOList.stream()
                .collect(Collectors.toMap(DataProductPO::getProdCode, Function.identity()));
        //Long batchId = hostTimeIdg.generateId().longValue();
        for (TemplateProductPO dataTemplateProductPO : list) {
            if (Objects.nonNull(prodMap.get(dataTemplateProductPO.getProdCode()))) {
                DataProductPO dataProductPO = prodMap.get(dataTemplateProductPO.getProdCode());
                String templateName = title + dataProductPO.getProdName();
                Long batchId = hostTimeIdg.generateId().longValue();
                visualProduceJobManager.doProduce(ProduceJobContextBO.builder()
                        .templateId(templateId)
                        .replaceData(dataProductPO.getReplaceData())
                        .batchId(batchId)
                        .source(VisualProduceJobSourceEnum.BATCH_PRODUCE.getCode())
                        .templateName(templateName)
                        .ttsParam(null)
                        .dmProduceMode(tenantInfo.getDmProduceMode())
                        .build());
            }
        }
        VisualProduceCreateBatchVO result = new VisualProduceCreateBatchVO();
        result.setBalance(String.valueOf(balance));
        return ResultModel.success(result);
    }

    public ResultModel<List<TemplateProductVO>> prodlist(Long templateId) {
        List<TemplateProductVO> voList = new ArrayList<>();
        List<TemplateProductPO> list = templateProductManager.lambdaQuery()
                .eq(TemplateProductPO::getTemplateId, templateId).list();
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> prodCodeList = list.stream().map(TemplateProductPO::getProdCode).collect(Collectors.toList());
            List<DataProductPO> dataProductPO = dataProductManager.lambdaQuery()
                    .in(DataProductPO::getProdCode, prodCodeList).list();
            if (CollectionUtils.isNotEmpty(dataProductPO)) {
                dataProductPO.forEach(e -> {
                    TemplateProductVO vo = new TemplateProductVO();
                    vo.setProdCode(e.getProdCode());
                    vo.setProdName(e.getProdName());
                    voList.add(vo);
                });
            }
        }
        return ResultModel.success(voList);
    }

    public ResultModel<Boolean> updaterecommendstate(ProduceUpdateParam param) {

        //1查询作品信息
        VisualProduceJobPO visualProduceJobPO = visualProduceJobManager.lambdaQuery()
                .eq(VisualProduceJobPO::getIsDeleted, Const.ZERO)
                .eq(VisualProduceJobPO::getJobId, param.getJobId()).one();
        Assert.notNull(visualProduceJobPO, "任务不存在");
        VisualProduceJobExtendPO extendPO = visualProduceJobExtendManager.lambdaQuery()
                .eq(VisualProduceJobExtendPO::getIsDeleted, Const.ZERO)
                .eq(VisualProduceJobExtendPO::getProduceJobId, param.getJobId())
                .one();
        if (Objects.isNull(extendPO)) {
            Assert.notNull(visualProduceJobPO, "在开启推荐使用之前，请确保已完成转发设置。");
        }
        if (Objects.nonNull(param.getRecommendState())) {
            if (Objects.equals(param.getRecommendState(), Const.ONE)) {
                //2.校验转发配置是否完成
                if (!Objects.equals(extendPO.getShareConfState(), Const.TWO)) {
                    Assert.notNull(visualProduceJobPO, "在开启推荐使用之前，请确保已完成转发设置。");
                }
                extendPO.setRecommendState(param.getRecommendState());
                extendPO.setRecommendEnableDt(new Date());
            }
            if (Objects.equals(param.getRecommendState(), Const.ZERO)) {
                extendPO.setRecommendState(param.getRecommendState());
            }
            //3.更新推荐状态
            visualProduceJobExtendManager.updateById(extendPO);
        }
        return ResultModel.success(true);
    }

    public ResultModel<Boolean> updateShareConfState(ShareConfUpdateStateParam param) {
        boolean flag = false;
        if (Objects.equals(param.getBizType(), InteractiveConfBizTypeEnum.MAGIC_WORKS.getCode())) {
            //定数作品修改交互式状态
            //1查询作品信息
            VisualProduceJobPO visualProduceJobPO = visualProduceJobManager.lambdaQuery()
                    .eq(VisualProduceJobPO::getIsDeleted, Const.ZERO)
                    .eq(VisualProduceJobPO::getJobId, param.getBizId()).one();
            Assert.notNull(visualProduceJobPO, "任务不存在");
            VisualProduceJobExtendPO extendPO = visualProduceJobExtendManager.lambdaQuery()
                    .eq(VisualProduceJobExtendPO::getIsDeleted, Const.ZERO)
                    .eq(VisualProduceJobExtendPO::getProduceJobId, param.getBizId())
                    .one();
            extendPO.setShareConfState(param.getShareConfState());
            //3.更新推荐状态
            flag = visualProduceJobExtendManager.updateById(extendPO);
        }
        if (Objects.equals(param.getBizType(), InteractiveConfBizTypeEnum.MAGIC_TEMPLATE.getCode())) {
            //定数模板修改交互式状态
            VisualTemplatePO templatePO = visualTemplateManager.lambdaQuery()
                    .eq(VisualTemplatePO::getIsDeleted, Const.ZERO)
                    .eq(VisualTemplatePO::getTemplateId, param.getBizId())
                    .one();
            Assert.notNull(templatePO, "模板不存在");
            if (Objects.equals(templatePO.getShareConfState(), Const.ZERO)) {
                Assert.notNull(templatePO, "未配置基本转发配置未配置，无法修改交互式状态");
            }
            templatePO.setShareConfState(param.getShareConfState());
            flag = visualTemplateManager.updateById(templatePO);
        }
        return ResultModel.success(flag);
    }

    private String interFaceSubTilte(List<InterfaceDTO> interfaceList) {
        if (CollectionUtils.isEmpty(interfaceList)) {
            return StringUtils.EMPTY;
        }
        List<Object> paramValueList = new ArrayList<>();
        interfaceList.forEach(e -> {
            Iterator<Map.Entry<String, Object>> iterator = e.getKeyMap().entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                Object paramValue = entry.getValue();
                paramValueList.add(paramValue);
            }
        });
        return StringUtils.join(paramValueList, "_");
    }

    public ResultModel<VisualProduceCreateBatchVO> batchProduceNew(BatchProduceNewParam param) {
        Integer balance = null;
        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(operatorUtil.getTenantCode());
        if (Objects.equals(param.getType(), Const.ONE)) {
            Assert.isTrue(CollectionUtils.isNotEmpty(param.getBatchParams()), "接口参数不能为空");
            balance = accountTenantTrialManager.checkTenantBalance(operatorUtil.getTenantCode(), param.getBatchParams().size());
            param.getBatchParams().forEach(e -> {
                if (CollectionUtils.isNotEmpty(e.getList())) {
                    List<InterfaceDTO> interfaceList = e.getList();
                    String templateName = param.getTitle() + interFaceSubTilte(interfaceList);
                    Long batchId = hostTimeIdg.generateId().longValue();
                    String apiData = visualProduceJobManager.suppleApiData(interfaceList);
                    visualProduceJobManager.doProduce(ProduceJobContextBO.builder()
                            .templateId(Long.parseLong(param.getTemplateId()))
                            .batchId(batchId)
                            .source(VisualProduceJobSourceEnum.BATCH_PRODUCE.getCode())
                            .templateName(templateName)
                            .dmProduceMode(tenantInfo.getDmProduceMode())
                            .apiData(apiData)
                            .dynamicNodes(convert(visualDynamicNodeManager, visualAiConfigManager, param.getTemplateId()))
                            .build());
                }
            });
        } else {
            Assert.isTrue(CollectionUtils.isNotEmpty(param.getDigitalManParams()), "数字人信息不能为空");
            balance = accountTenantTrialManager.checkTenantBalance(operatorUtil.getTenantCode(), param.getDigitalManParams().size());
            param.getDigitalManParams().forEach(e -> {
                String templateName = param.getTitle() + "_" + System.currentTimeMillis();
                Long batchId = hostTimeIdg.generateId().longValue();
                visualProduceJobManager.doProduce(
                        ProduceJobContextBO.builder().templateId(Long.parseLong(param.getTemplateId())).batchId(batchId)
                                .source(VisualProduceJobSourceEnum.BATCH_PRODUCE.getCode()).templateName(templateName)
                                .dmProduceMode(tenantInfo.getDmProduceMode()).digitalManParamBO(converter(e)).build());
            });
        }

        VisualProduceCreateBatchVO result = new VisualProduceCreateBatchVO();
        result.setBalance(String.valueOf(balance));
        return ResultModel.success(result);
    }

    public ResultModel<ExcelBatchProducePreCheckResultVO> excelBatchProducePreChek(MultipartFile file)
            throws IOException {
        ExcelBatchProducePreCheckResultVO resultVO = new ExcelBatchProducePreCheckResultVO();
        Set<Integer> blankDataRows = new HashSet<>();
        resultVO.setBlankDataRows(blankDataRows);
        EasyExcel.read(file.getInputStream(), new ExcelBatchProducePreCheckListner(resultVO)).sheet().doRead();

        if (CollectionUtils.isNotEmpty(resultVO.getBlankDataRows())) {
            return ResultModel.success(resultVO);
        }

        //生成调用令牌
        String passToken = UUID.randomUUID().toString();
        redisUtil.set(genExcelBatchProduceLockKey(operatorUtil.getOperator(), file.getName()), passToken,
                EXCEL_BATCH_PRODUCE_LOCK_TIME);
        resultVO.setCheckPassToken(passToken);
        return ResultModel.success(resultVO);
    }

    public ResultModel<VisualProduceCreateBatchVO> excelBatchProduce(MultipartFile file, ExcelBatchProduceParam param) {
        String lockKey = genExcelBatchProduceLockKey(operatorUtil.getOperator(), file.getName());
        if (!redisUtil.hasKey(lockKey)) {
            throw BusinessServiceException.getInstance("请重新上传后再进行重试");
        }

        VisualProduceCreateBatchVO resultVO = new VisualProduceCreateBatchVO();
        try {
            EasyExcel.read(file.getInputStream(),
                    new ExcelBatchProduceListner(operatorUtil, param, accountTenantTrialManager,
                            visualProduceJobManager, hostTimeIdg, tenantInfoManager, resultVO, visualDynamicNodeManager, visualAiConfigManager)).sheet().doRead();
        } catch (Exception e) {
            log.error("解析excel数据并批量合成发生异常！,param:{},e:{}", JSONUtil.toJsonStr(param), e);
            throw BusinessServiceException.getInstance("解析excel数据并批量合成失败，错误信息:" + e.getMessage());
        }
        redisUtil.del(lockKey);

        return ResultModel.success(resultVO);
    }

    private DigitalManParamBO converter(DigitalManParam param) {
        DigitalManParamBO digitalManParamBO = new DigitalManParamBO();
        digitalManParamBO.setSpeed(param.getSpeed());
        digitalManParamBO.setChannel(param.getChannel());
        digitalManParamBO.setSceneId(param.getSceneId());
        digitalManParamBO.setVoiceCode(param.getVoiceCode());
        return digitalManParamBO;
    }

    /**
     * 合并json
     *
     * @param object
     */
    private void mergeJson(ObjectMapper objectMapper, ObjectNode mergedNode, Object object) {
        // 解析第一个 JSON
        try {
            // 将第一个 JSON 合并到 mergedObject 中
            JsonNode node1 = objectMapper.readTree(JSONUtil.toJsonStr(object));
            mergedNode.setAll((ObjectNode) node1);
        } catch (Exception e) {
            log.error("json合并异常");
        }

    }

    public ResultModel<List<PPTVO>> ppt(MultipartFile multipartFiles, HttpServletRequest request) {
        List<PPTVO> PPTVOList = new ArrayList<>();
        List<String> urlList = new ArrayList<>();
        //tmpDir = "C:\\Users\\<USER>\\Downloads\\";
        String path = tmpDir + System.currentTimeMillis() + "-" + multipartFiles.getOriginalFilename();
        File file = new File(path);

        try {
            FileOutputStream fos = new FileOutputStream(file);
            InputStream inputStream = multipartFiles.getInputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
            fos.close();
            List<File> files = PPT2Util.pptx2Png(file);
            for (File file1 : files) {
                //将文件上传到腾讯云cos上
                String url = cosFileUploadManager.uploadFile(file1, null, COS_PATH_CONTENT_IMAGE, true, true);
                if (StringUtils.isNotBlank(url)) {
                    urlList.add(url);
                }
            }

            List<String> remarkList = PPTUtil.pptxRemark(file);
            for (int i = 0; i < urlList.size(); i++) {
                PPTVO vo = new PPTVO();
                vo.setImageUrl(urlList.get(i));
                if (CollectionUtils.isNotEmpty(remarkList) && StringUtils.isNotBlank(remarkList.get(i))) {
                    vo.setRemark(remarkList.get(i));
                }
                PPTVOList.add(vo);
            }
        } catch (Exception e) {
            log.error("ppt转图片失败", e);
        } finally {
            file.delete();
        }
        return ResultModel.success(PPTVOList);
    }

    public ResultModel<List<PPTTemplateVO>> pptTemplate() {
        List<PPTTemplateVO> templateVOList = new ArrayList<>();
//        List<Long> templateList = new ArrayList<>();
//        if (envUtil.isTest()) {
//            templateList = Collections.singletonList(1163708051011024667L);
//        } else {
//            templateList = Arrays.asList(1199738127659025987L,1199738127208138308L,1190730927947710671L);
//        }
        List<VisualTemplatePO> pptTemplateList = visualTemplateManager.lambdaQuery()
                .eq(VisualTemplatePO::getIsSys, 1)
                .eq(VisualTemplatePO::getIsPPT, 1)
                .orderByDesc(VisualTemplatePO::getId)
                .last("limit 10").list();
        Assert.isTrue(CollectionUtils.isNotEmpty(pptTemplateList), "未查询到模板");

        pptTemplateList.forEach(pptTemplate -> {
            PPTTemplateVO pptTemplateVO = new PPTTemplateVO();
            pptTemplateVO.setShortVideoUrl(pptTemplate.getShortVideoUrl());
            pptTemplateVO.setDuration(pptTemplate.getDuration());
            pptTemplateVO.setName(pptTemplate.getName());
            pptTemplateVO.setCoverUrl(pptTemplate.getCoverUrl());
            pptTemplateVO.setResolution(pptTemplate.getResolution());
            pptTemplateVO.setResolutionType(pptTemplate.getResolutionType());
            pptTemplateVO.setTemplateId(pptTemplate.getTemplateId().toString());
            templateVOList.add(pptTemplateVO);
        });

        return ResultModel.success(templateVOList);
    }

    private void saveAddTemplateLog(Long templateId, String ip, Long userId, String userName, String tenantCode) {
        OpLogAddReq opLog = new OpLogAddReq();
        opLog.setBizCode(OpLogConsts.BIZ_CODE);
        opLog.setTenantCode(tenantCode);
        opLog.setOpObject(OpObjectEnum.TEMPLATE.getCode());
        opLog.setOpType(OpTypeEnum.ADD.getOpType());
        opLog.setOpKey(String.valueOf(templateId));
        opLog.setOpUserId(String.valueOf(userId));
        opLog.setOpUserName(userName);
        opLog.setOpDt(new Date());
        Map<String, String> extDataMap = new HashMap<>(4);
        extDataMap.put(OpLogConsts.IP, ip);
        opLog.setExtData(JSONUtil.toJsonStr(extDataMap));
        try {
            opLogManager.newOpLog(opLog);
        } catch (Exception e) {
            //抛异常就吃掉，不能影响主流程
            log.error("保存新增模板的操作日志发生异常,opLog:{},,,e:{}", JSONUtil.toJsonStr(opLog), e);
        }
    }

    private void saveUpdateTemplateLog(VisualTemplatePO exitTemplatePO, List<VisualDynamicNodePO> existNodes,
                                       VisualTemplateUpdateParam param, String ip, Long userId, String userName) {
        boolean updateFlag = false;

        List<String> remarkList = new ArrayList<>();
        //模板名修改了
        if (!StringUtils.equals(exitTemplatePO.getName(), param.getName())) {
            updateFlag = true;
            remarkList.add("将标题【" + exitTemplatePO.getName() + "】修改为【" + param.getName() + "】");
        }
        if (CollectionUtils.isNotEmpty(existNodes) && CollectionUtils.isNotEmpty(param.getCards()) && Objects
                .nonNull(param.getCards().get(0)) && CollectionUtils
                .isNotEmpty(param.getCards().get(0).getDynamicNodes())) {
            //片段数修改了
            if (existNodes.size() != param.getCards().get(0).getDynamicNodes().size()) {
                updateFlag = true;
                remarkList.add("模板片段【" + existNodes.size() + "】张变更为【" + param.getCards().get(0).getDynamicNodes().size()
                        + "】张");
            }
        }
        //无修改，直接返回
        if (!updateFlag) {
            return;
        }

        OpLogAddReq opLog = new OpLogAddReq();
        opLog.setBizCode(OpLogConsts.BIZ_CODE);
        opLog.setTenantCode(exitTemplatePO.getTenantCode());
        opLog.setOpObject(OpObjectEnum.TEMPLATE.getCode());
        opLog.setOpType(OpTypeEnum.UPDATE.getOpType());
        opLog.setOpKey(String.valueOf(exitTemplatePO.getTemplateId()));
        opLog.setOpUserId(String.valueOf(userId));
        opLog.setOpUserName(userName);
        opLog.setOpDt(new Date());

        opLog.setRemark(StringUtils.join(remarkList, OpLogConsts.SEPARATOR));
        Map<String, String> extDataMap = new HashMap<>(4);
        extDataMap.put(OpLogConsts.IP, ip);
        opLog.setExtData(JSONUtil.toJsonStr(extDataMap));
        try {
            opLogManager.newOpLog(opLog);
        } catch (Exception e) {
            //抛异常就吃掉，不能影响主流程
            log.error("保存修改模板的操作日志发生异常,opLog:{},,,e:{}", JSONUtil.toJsonStr(opLog), e);
        }
    }

    private static String genExcelBatchProduceLockKey(Long userId, String fileName) {
        return EXCEL_BATCH_PRODUCE_LOCK_KEY_PREFIX + userId + SymbolE.COLON_EN.getValue() + fileName;
    }

    public ResultModel<List<ComponentVersionDetailVO>> version() {
        List<ComponentVersionDetailVO> componentVersionDetailVOList = new ArrayList<>();
        List<ComponentVersionDetailPO> versionList = componentVersionDetailManager.getVersionList();
        for (ComponentVersionDetailPO componentVersionDetailPO : versionList) {
            ComponentVersionDetailVO componentVersionDetailVO = new ComponentVersionDetailVO();
            componentVersionDetailVO.setVersion(componentVersionDetailPO.getVersion());
            componentVersionDetailVO.setUrl(componentVersionDetailPO.getUrl());
            componentVersionDetailVOList.add(componentVersionDetailVO);
        }
        return ResultModel.success(componentVersionDetailVOList);
    }

    public ResultModel<ComponentVersionDetailVO> getUrlByVersion(UrlParam param) {
        ComponentVersionDetailPO urlByVersion = componentVersionDetailManager.getUrlByVersion(param.getVersion());
        if (Objects.isNull(urlByVersion)) {
            return null;
        }

        ComponentVersionDetailVO componentVersionDetailVO = new ComponentVersionDetailVO();
        componentVersionDetailVO.setUrl(urlByVersion.getUrl());
        componentVersionDetailVO.setVersion(urlByVersion.getVersion());
        return ResultModel.success(componentVersionDetailVO);
    }

    public ResultPageModel<TagVO> tagList(TagPageQueryParam param) {
        TagPageBO bo = new TagPageBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setTagType(param.getTagType());
        IPage<TagDTO> resp = tagManager.pageQuery(bo);
        ResultPageModel<TagVO> result = pageQueryModel(resp, resp.getRecords().stream().map(e -> {
            TagVO vo = new TagVO();
            vo.setTagId(e.getTagId().toString());
            vo.setTagName(e.getTagName());
            return vo;
        }).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        return result;
    }

    public ResultModel<Void> collect(CollectParam param) {
        TemplateCollectPO po = new TemplateCollectPO();
        po.setUserId(operatorUtil.getOperator());
        po.setTemplateId(param.getTemplateId());
        po.setId(hostTimeIdg.generateId().longValue());
        templateCollectManager.save(po);
        return ResultModel.success(null);
    }

    public ResultModel<Void> cancelCollect(CollectParam param) {
        TemplateCollectPO templateCollectPO = templateCollectManager.lambdaQuery().eq(TemplateCollectPO::getUserId, operatorUtil.getOperator()).eq(TemplateCollectPO::getTemplateId, param.getTemplateId()).one();
        Assert.notNull(templateCollectPO, "您还未收藏过该模板");
        templateCollectManager.removeById(templateCollectPO.getId());
        return ResultModel.success(null);
    }

    public ResultModel<List<BatchVideoVisualTemplateVO>> batchTemplateList() {
        String[] templateIds = batchTemplateIds.split(",");
        List<VisualTemplatePO> list = visualTemplateManager.lambdaQuery().in(VisualTemplatePO::getTemplateId, Arrays.asList(templateIds))
                .eq(VisualTemplatePO::getStatus, Const.ZERO).eq(VisualTemplatePO::getIsDeleted, Const.ZERO).list();
        List<BatchVideoVisualTemplateVO> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {

            list.forEach(e->{
                BatchVideoVisualTemplateVO vo = new BatchVideoVisualTemplateVO();
                vo.setTtsParam(e.getTtsParam());
                vo.setTemplateId(e.getTemplateId().toString());
                vo.setCoverUrl(e.getCoverUrl());
                vo.setPreviewVideoUrl(e.getPreviewVideoUrl());
                vo.setShortVideoUrl(e.getShortVideoUrl());
                vo.setDuration(e.getDuration());
                vo.setName(e.getName());
                voList.add(vo);
            });
        }
        return ResultModel.success(voList);
    }
}
