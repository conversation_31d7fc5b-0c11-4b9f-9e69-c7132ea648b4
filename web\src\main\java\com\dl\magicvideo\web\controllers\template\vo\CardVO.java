package com.dl.magicvideo.web.controllers.template.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: ClipVO
 * @author: zhousx
 * @date: 2023/2/7 17:47
 */
@Data
public class CardVO {
    @ApiModelProperty("卡片id")
    private String cardId;

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("卡片名称")
    private String name;

    @ApiModelProperty("卡片封面")
    private String coverUrl;

    @ApiModelProperty("尺寸")
    private String resolution;

    @ApiModelProperty("渲染数据")
    private String renderData;

    @ApiModelProperty("轻编辑配置")
    private String lightEditConfigs;

    @ApiModelProperty("跨片段配置")
    private String crossClips;

    @ApiModelProperty("动态节点")
    private List<DynamicNodeVO> dynamicNodes;
}
