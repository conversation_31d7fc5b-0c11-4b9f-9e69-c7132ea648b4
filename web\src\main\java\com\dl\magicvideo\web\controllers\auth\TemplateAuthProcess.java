package com.dl.magicvideo.web.controllers.auth;

import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplateAuthPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateAuthManager;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateCopyBO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateCopyDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.auth.param.AddTemplateAndAuthParam;
import com.dl.magicvideo.web.controllers.auth.param.AuthParam;
import com.dl.magicvideo.web.controllers.auth.param.SwitchStatusParam;
import com.dl.magicvideo.web.controllers.auth.param.TemplateAuthPageQueryParam;
import com.dl.magicvideo.web.controllers.auth.param.TenantInfoParam;
import com.dl.magicvideo.web.controllers.auth.vo.TemplateAuthVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @describe: TemplateAuthProcess
 * @author: zhousx
 * @date: 2023/6/18 11:22
 */
@Slf4j
@Component
public class TemplateAuthProcess extends AbstractController {
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private VisualTemplateManager visualTemplateManager;
    @Autowired
    private VisualTemplateAuthManager visualTemplateAuthManager;

    public ResultModel<TemplateAuthVO> addAndAuth(AddTemplateAndAuthParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getSourceTemplateId()), "模板Id格式错误");
        TemplateCopyDTO sysTemplateId = visualTemplateManager.copy(buildTemplateCopyBO(param));
        List<VisualTemplateAuthPO> visualTemplateAuthPOList = new ArrayList<>();
        for (TenantInfoParam tenant : param.getTenants()) {
            VisualTemplateAuthPO visualTemplateAuthPO = new VisualTemplateAuthPO();
            visualTemplateAuthPO.setTemplateId(sysTemplateId.getTemplateId());
            visualTemplateAuthPO.setTenantCode(tenant.getTenantCode());
            visualTemplateAuthPO.setTenantName(tenant.getTenantName());
            String userName = operatorUtil.getUserName();
            visualTemplateAuthPO.setCreatorName(userName);
            visualTemplateAuthPO.setModifyName(userName);
            visualTemplateAuthPOList.add(visualTemplateAuthPO);
        }
        visualTemplateAuthManager.saveBatch(visualTemplateAuthPOList);
        TemplateAuthVO vo = new TemplateAuthVO();
        vo.setTemplateId(sysTemplateId.getTemplateId() + "");
        return ResultModel.success(vo);
    }

    private TemplateCopyBO buildTemplateCopyBO(AddTemplateAndAuthParam param){
        TemplateCopyBO bo = new TemplateCopyBO();
        bo.setTemplateId(Long.valueOf(param.getSourceTemplateId()));
        bo.setName(param.getName());
        bo.setCoverUrl(param.getCoverUrl());
        bo.setPreviewVideoUrl(param.getPreviewVideoUrl());
        bo.setSys(true);
        bo.setTenantCode(Const.DEFAULT_TENANT_CODE);
        bo.setIsManager(param.getIsManager());
        bo.setFirstCategory(param.getFirstCategory());
        bo.setSecondCategory(param.getSecondCategory());
        bo.setCopySource(0);
        bo.setTagIds(param.getTagIds());
        bo.setTagNames(param.getTagNames());
        return bo;
    }

    public ResultModel<Void> auth(AuthParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板Id格式错误");
        TemplateAuthBO bo = new TemplateAuthBO();
        bo.setTemplateId(Long.valueOf(param.getTemplateId()));
        bo.setCoverUrl(param.getCoverUrl());
        bo.setName(param.getName());
        bo.setPreviewVideoUrl(param.getPreviewVideoUrl());
        if(CollectionUtils.isNotEmpty(param.getTenants())) {
            bo.setTenants(param.getTenants().stream().map(tenantInfoParam -> {
                TemplateAuthBO.Tenant tenant = new TemplateAuthBO.Tenant();
                tenant.setTenantCode(tenantInfoParam.getTenantCode());
                tenant.setTenantName(tenantInfoParam.getTenantName());
                return tenant;
            }).collect(Collectors.toList()));
        }
        bo.setIsManager(param.getIsManager());
        bo.setFirstCategory(param.getFirstCategory());
        bo.setSecondCategory(param.getSecondCategory());
        visualTemplateAuthManager.auth(bo);
        return ResultModel.success(null);
    }

    public ResultModel<Void> switchStatus(SwitchStatusParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()), "模板Id格式错误");
        visualTemplateManager.lambdaUpdate().eq(VisualTemplatePO::getTemplateId, Long.valueOf(param.getTemplateId())).set(VisualTemplatePO::getStatus, param.getStatus()).
                set(VisualTemplatePO::getModifyName,operatorUtil.getUserName()).update();
        return ResultModel.success(null);
    }

    public ResultPageModel<TemplateAuthVO> list(@RequestBody @Validated TemplateAuthPageQueryParam param) {
        TemplateAuthSearchBO bo = new TemplateAuthSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setName(param.getName());
        bo.setResolutionType(param.getResolutionType());

        ResponsePageQueryDO<List<TemplateAuthDTO>> result = visualTemplateAuthManager.pageQuery(bo);
        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<TemplateAuthVO> vos = result.getDataResult().stream().map(this::cnvTemplateAuthDTO2VO).collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    private TemplateAuthVO cnvTemplateAuthDTO2VO(TemplateAuthDTO dto) {
        if(Objects.isNull(dto)) {
            return null;
        }

        TemplateAuthVO vo = new TemplateAuthVO();
        vo.setTemplateId(dto.getTemplateId() + "");
        vo.setStatus(dto.getStatus());
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setName(dto.getName());
        vo.setResolution(dto.getResolution());
        vo.setResolutionType(dto.getResolutionType());
        vo.setBgMusic(dto.getBgMusic());
        vo.setBgMusicParam(dto.getBgMusicParam());
        vo.setTtsParam(dto.getTtsParam());
        vo.setReplaceData(dto.getReplaceData());
        vo.setCreatorName(dto.getCreatorName());
        vo.setCreateDt(dto.getCreateDt());
        vo.setDuration(dto.getDuration());
        vo.setPreviewVideoUrl(dto.getPreviewVideoUrl());
        vo.setShortVideoUrl(dto.getShortVideoUrl());
        vo.setModifyName(dto.getModifyName());
        vo.setModifyDt(dto.getModifyDt());
        if(CollectionUtils.isNotEmpty(dto.getAuthTenantList())) {
            vo.setAuthTenantList(dto.getAuthTenantList().stream().map(t -> {
                TemplateAuthVO.Tenant tenant = new TemplateAuthVO.Tenant();
                tenant.setTenantCode(t.getTenantCode());
                tenant.setTenantName(t.getTenantName());
                return tenant;
            }).collect(Collectors.toList()));
        }
        return vo;
    }
}
