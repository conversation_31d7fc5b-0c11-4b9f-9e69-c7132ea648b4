package com.dl.magicvideo.biz.mq.consumer;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.util.PlaceHolderUtils;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.dal.account.trial.emuns.TenantDosageEnum;
import com.dl.magicvideo.biz.dal.account.trial.po.TenantDosageConfigPO;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobDurationBO;
import com.dl.magicvideo.biz.dal.visual.param.VisualAiJobPageBO;
import com.dl.magicvideo.biz.dal.visual.po.*;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordContentTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatRecordTypeEnum;
import com.dl.magicvideo.biz.es.aigc.chatrecord.enums.AigcChatToolEnum;
import com.dl.magicvideo.biz.manager.account.trial.TenantDosageConfigManager;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.AigcChatRecordManager;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordProduceSuccessBO;
import com.dl.magicvideo.biz.manager.sse.dto.SSEProduceEvent;
import com.dl.magicvideo.biz.manager.sse.enums.SSEEventEnum;
import com.dl.magicvideo.biz.manager.visual.*;
import com.dl.magicvideo.biz.manager.visual.bo.MagicVideoJobBO;
import com.dl.magicvideo.biz.manager.visual.enums.JobTypeEnum;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.biz.mq.dto.ProduceFailDTO;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 视频合成成功消费者
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-08 18:47
 */
@Component
public class ProduceSuccessConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProduceSuccessConsumer.class);

    @Resource
    private VisualProduceBatchManager visualProduceBatchManager;
    @Resource
    private VisualProduceJobManager visualProduceJobManager;
    @Resource
    private DeliveryPlanManager deliveryPlanManager;
    @Resource
    private VisualProduceJobCallbackManager visualProduceJobCallbackManager;
    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;
    @Resource
    private AigcChatRecordManager aigcChatRecordManager;
    @Resource
    private TenantDosageConfigManager tenantDosageConfigManager;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private VisualAiJobManager visualAiJobManager;

    private OkHttpClient httpClient = new OkHttpClient();

    private static final String PLAN_PRODUCE_COMPLETE_NOTICE =
            "{\n" + "\t\"msg_type\": \"post\",\n" + "\t\"content\": {\n" + "\t\t\"post\": {\n" + "\t\t\t\"zh_cn\": {\n"
                    + "\t\t\t\t\"title\": \"合成成功\",\n" + "\t\t\t\t\"content\": [\n" + "\t\t\t\t\t[\n"
                    + "\t\t\t\t\t\t{\n" + "\t\t\t\t\t\t\t\"tag\": \"text\",\n" + "\t\t\t\t\t\t\t\"text\": \"%s\"\n"
                    + "\t\t\t\t\t\t},\n" + "\t\t\t\t\t\t{\n" + "\t\t\t\t\t\t\t\"tag\": \"at\",\n"
                    + "\t\t\t\t\t\t\t\"user_id\": \"all\",\n" + "\t\t\t\t\t\t\t\"user_name\": \"所有人\"\n"
                    + "\t\t\t\t\t\t}\n" + "\t\t\t\t\t]\n" + "\t\t\t\t]\n" + "\t\t\t}\n" + "\t\t}\n" + "\t}\n" + "}";

    private static final String SUCCESS_ALARM_MSG =
            "{\n" + "\t\"msg_type\": \"post\",\n" + "\t\"content\": {\n" + "\t\t\"post\": {\n" + "\t\t\t\"zh_cn\": {\n"
                    + "\t\t\t\t\"title\": \"视频合成成功提示\",\n" + "\t\t\t\t\"content\": [\n" + "\t\t\t\t\t[{\n"
                    + "\t\t\t\t\t\t\t\"tag\": \"text\",\n"
                    + "\t\t\t\t\t\t\t\"text\": \"租户:【${tenantName}${tenantCode}】, jobId:【${jobId}】, jobName:【${jobName}】, 发起人:【${creatorName}】, 【${createDt}】发起视频合成, 【${successDt}】合成成功 \"\n"
                    + "\t\t\t\t\t\t}\n" + "\t\t\t\t\t]\n" + "\t\t\t\t]\n" + "\t\t\t}\n" + "\t\t}\n" + "\t}\n" + "}";

    @StreamListener("producesuccessconsumer")
    public void consume(@Payload MagicVideoJobBO input) {
        LOGGER.info("收到视频合成成功消息，input:{}", JSONUtil.toJsonStr(input));

        //查询作品信息
        VisualProduceJobPO jobPO = visualProduceJobManager.getOne(Wrappers.lambdaQuery(VisualProduceJobPO.class)
                .eq(VisualProduceJobPO::getJobId, input.getJobId()).select(VisualProduceJobPO.class,
                        po -> !"replace_data".equals(po.getColumn()) && !"preview_data".equals(po.getColumn())
                                && !"template_data".equals(po.getColumn()) && !"api_data".equals(po.getColumn())));

        //发送告警
        this.alarm(jobPO);

        //回调第三方
        this.callbackThird(input, jobPO);

        //aigc作品插入聊天记录
        this.aigcJobAddChatRecord(jobPO);

        //交付计划通知
        this.pushProcess(input);

        //更新租户用量信息
        this.chgDosage(jobPO);

        //当接收到消息时，通过SSE发送给所有连接的客户端
        handleSSEMessage(input);
    }

    private void chgDosage(VisualProduceJobPO jobPO) {
        List<TenantDosageConfigPO> list = tenantDosageConfigManager.lambdaQuery().eq(TenantDosageConfigPO::getTenantCode, jobPO.getTenantCode()).list();
        if (CollectionUtils.isEmpty(list)) {
            List<TenantDosageConfigPO> poList = new ArrayList<>();
            TenantDosageConfigPO po = new TenantDosageConfigPO();
            long id1 = hostTimeIdg.generateId().longValue();
            po.setId(id1);
            po.setTenantCode(jobPO.getTenantCode());
            po.setConfigType(TenantDosageEnum.DIGITAL_MAN.getType());
            po.setBalance(Const.MINUS_ONE_LONG);
            po.setCreateBy(0L);
            po.setModifyBy(0L);
            poList.add(po);

            TenantDosageConfigPO po2 = new TenantDosageConfigPO();
            long id2 = hostTimeIdg.generateId().longValue();
            po2.setId(id2);
            po2.setTenantCode(jobPO.getTenantCode());
            po2.setConfigType(TenantDosageEnum.VIDEO.getType());
            po2.setBalance(Const.MINUS_ONE_LONG);
            po2.setCreateBy(0L);
            po2.setModifyBy(0L);
            poList.add(po2);

            TenantDosageConfigPO po3 = new TenantDosageConfigPO();
            long id3 = hostTimeIdg.generateId().longValue();
            po3.setId(id3);
            po3.setTenantCode(jobPO.getTenantCode());
            po3.setConfigType(TenantDosageEnum.TTS.getType());
            po3.setBalance(Const.MINUS_ONE_LONG);
            po3.setCreateBy(0L);
            po3.setModifyBy(0L);
            poList.add(po3);
            tenantDosageConfigManager.saveBatch(poList);

            list = new ArrayList<>(poList);
        }
        for (TenantDosageConfigPO po : list) {
            if (po.getConfigType().equals(TenantDosageEnum.DIGITAL_MAN.getType())) {
                //获取数字人剩余分钟数
                VisualAiJobDurationBO query = new VisualAiJobDurationBO();
                query.setProduceJobId(jobPO.getId());
                query.setJobType(AiJobTypeE.DIGITAL_MAN.getCode());
                Long l = visualAiJobManager.countDuration(query);
                long duration = l == null ? 0L : l;
                po.setUsed(po.getBalance() + duration);
            } else if (po.getConfigType().equals(TenantDosageEnum.VIDEO.getType())) {
                po.setUsed(po.getBalance() + jobPO.getDuration());
            } else {
                VisualAiJobDurationBO query = new VisualAiJobDurationBO();
                query.setProduceJobId(jobPO.getId());
                query.setJobType(AiJobTypeE.TTS.getCode());
                Long l = visualAiJobManager.countDuration(query);
                long duration = l == null ? 0L : l;
                po.setUsed(po.getBalance() + duration);
            }
        }
        tenantDosageConfigManager.saveOrUpdateBatch(list);

    }

    private void handleSSEMessage(MagicVideoJobBO input) {
        SSEProduceEvent event = new SSEProduceEvent();
        event.setJobId(input.getJobId().toString());
        event.setStatus(2);
        event.setTenantCode(input.getTenantCode());
        StringBuilder sseMsg = new StringBuilder();
        sseMsg.append("event:").append(SSEEventEnum.PRODUCE.getDesc());
        sseMsg.append("\n");
        sseMsg.append("data:").append(JsonUtils.toJSON(event));
        sseMsg.append("\n");

        //推送所有的key
        Set<String> sseSet = redisUtil.getSet(Const.VISUAL_SSE_QUEUE_KEY_LIST);
        for (String key : sseSet){
            redisUtil.leftPush(key, sseMsg.toString());
        }

    }

    /**
     * 告警
     *
     */
    private void alarm(VisualProduceJobPO jobPO) {
        if (!Const.DEFAULT_TENANT_CODE.equals(jobPO.getTenantCode())){
            return;
        }
        Properties properties = new Properties();
        properties.put("tenantName", jobPO.getTenantName());
        properties.put("tenantCode", jobPO.getTenantCode());
        properties.put("jobId", jobPO.getJobId() + "");
        properties.put("jobName", jobPO.getName());
        properties.put("creatorName", jobPO.getCreatorName());
        properties.put("createDt", com.dl.magicvideo.biz.common.util.DateUtil.format(jobPO.getCreateDt(), com.dl.magicvideo.biz.common.util.DateUtil.Y_M_D_H_M_S));
        properties.put("successDt", com.dl.magicvideo.biz.common.util.DateUtil.format(new Date(), com.dl.magicvideo.biz.common.util.DateUtil.Y_M_D_H_M_S));

        String noticeMsg = PlaceHolderUtils.resolveValue(SUCCESS_ALARM_MSG, properties);

        LOGGER.info("jobId:{},,,noticeMsg:{}", jobPO.getJobId(), noticeMsg);
    }

    private void callbackThird(MagicVideoJobBO input, VisualProduceJobPO jobPO) {
        try {
            //回调第三方
            visualProduceJobCallbackManager.callbackThird(input.getJobId(), input.getTenantCode(), jobPO);
        } catch (Exception e) {
            LOGGER.error("视频合成成功的消费者内，回调第三方 发生异常! input:{},e:{}", JSONUtil.toJsonStr(input), e);
        }
    }

    /**
     * aigc作品插入聊天记录
     *
     * @param jobPO
     */
    private void aigcJobAddChatRecord(VisualProduceJobPO jobPO) {
        VisualProduceJobExtendPO jobExtendPO = visualProduceJobExtendManager
                .getOne(Wrappers.lambdaQuery(VisualProduceJobExtendPO.class)
                        .eq(VisualProduceJobExtendPO::getProduceJobId, jobPO.getJobId()));
        if (!JobTypeEnum.AIGC.getType().equals(jobExtendPO.getType())) {
            LOGGER.info("该作品不是aigc作品，跳过。jobId:{}", jobPO.getJobId());
            return;
        }

        LOGGER.info("准备将aigc作品插入聊天记录,jobId:{}", jobPO.getJobId());
        AigcChatRecordAddBO recordAddBO = new AigcChatRecordAddBO();
        try {
            recordAddBO.setUserId(jobPO.getCreateBy());
            recordAddBO.setTenantCode(jobPO.getTenantCode());
            recordAddBO.setSendDt(new Date());
            recordAddBO.setType(AigcChatRecordTypeEnum.SYSTEM.getType());
            recordAddBO.setContentType(AigcChatRecordContentTypeEnum.PRODUCE_SUCCESS.getType());
            if (VisualProduceJobSourceEnum.AIGC_PRODUCE_HOT_EVENT_SUBJECT_MATTER_TOOL.getCode()
                    .equals(jobPO.getSource())) {
                recordAddBO.setFromTool(AigcChatToolEnum.HOT_EVENT_SUBJECT_MATTER_TOOL.getCode());
            } else {
                recordAddBO.setFromTool(AigcChatToolEnum.COMMON_TOOL.getCode());
            }

            AigcChatRecordProduceSuccessBO successBO = new AigcChatRecordProduceSuccessBO();
            successBO.setJobId(jobPO.getJobId());
            successBO.setCoverUrl(jobPO.getCoverUrl());
            successBO.setVideoUrl(jobPO.getVideoUrl());
            successBO.setVideoName(jobPO.getName());
            successBO.setTemplateId(jobPO.getTemplateId());
            recordAddBO.setContent(JSONUtil.toJsonStr(successBO));
            aigcChatRecordManager.add(recordAddBO);
        } catch (Exception e) {
            LOGGER.error("视频合成成功的消费者内，aigc作品插入聊天记录 发生异常! jobId:{},,,recordAddBO:{},e:{}", jobPO.getJobId(),
                    JSONUtil.toJsonStr(recordAddBO), e);
        }
    }

    private void pushProcess(MagicVideoJobBO input) {
        if (Objects.isNull(input.getBatchId())) {
            LOGGER.warn("批次id不存在，input:{}", JSONUtil.toJsonStr(input));
            return;
        }
        // 查询交付计划
        VisualProduceBatchPO batch = visualProduceBatchManager.lambdaQuery()
                .eq(VisualProduceBatchPO::getBatchId, input.getBatchId()).one();
        if (Objects.isNull(batch)) {
            LOGGER.warn("批次不存在，input:{}", JSONUtil.toJsonStr(input));
            return;
        }
        if (Objects.isNull(batch.getPlanId())) {
            LOGGER.info("计划id不存在，input:{}", JSONUtil.toJsonStr(input));
            return;
        }

        DeliveryPlanPO plan = deliveryPlanManager.lambdaQuery().eq(DeliveryPlanPO::getPlanId, batch.getPlanId()).one();
        if (Objects.isNull(plan)) {
            LOGGER.warn("计划不存在，input:{}", JSONUtil.toJsonStr(input));
            return;
        }

        VisualProduceJobPO job = visualProduceJobManager.lambdaQuery()
                .eq(VisualProduceJobPO::getJobId, input.getJobId()).one();
        if (Objects.isNull(job)) {
            LOGGER.warn("作业不存在，input:{}", JSONUtil.toJsonStr(input));
            return;
        }

        // 通知飞书
        if (Objects.equals(plan.getIsNotify(), Const.ONE)) {
            if (StringUtils.isBlank(plan.getNotifyUrl())) {
                LOGGER.info("通知Url不存在，input:{}", JSONUtil.toJsonStr(input));
                return;
            }
            CompletableFuture.runAsync(() -> {
                String noticeMsg = String.format(PLAN_PRODUCE_COMPLETE_NOTICE,
                        "【" + plan.getName() + "】于" + DateUtil.format(new Date(), DatePattern.CHINESE_DATE_TIME_PATTERN)
                                + "生产1条视频，已全部成功");
                RequestBody requestBody = RequestBody
                        .create(MediaType.parse("application/json;charset=utf-8"), noticeMsg);
                Request.Builder builder = new Request.Builder();
                Request request = builder.addHeader("content-type", "application/json").url(plan.getNotifyUrl())
                        .post(requestBody).build();
                Call call = httpClient.newCall(request);

                call.enqueue(new Callback() {
                    //请求时失败时调用
                    @Override
                    public void onFailure(Call call, IOException e) {
                        LOGGER.error("交付计划飞书消息通知失败", e);
                    }

                    //请求成功时调用
                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        LOGGER.info("交付计划飞书消息通知成功");
                    }
                });
            });
        }

        // 通知客户
        CompletableFuture.runAsync(() -> {
            if (StringUtils.isBlank(plan.getCallbackUrl())) {
                LOGGER.info("回调Url不存在，input:{}", JSONUtil.toJsonStr(input));
                return;
            }
            Map<String, String> pushBody = new HashMap<>();
            pushBody.put("planId", plan.getPlanId() + "");
            pushBody.put("videoUrl", job.getVideoUrl());
            pushBody.put("jobId", input.getJobId() + "");
            RequestBody requestBody = RequestBody
                    .create(MediaType.parse("application/json;charset=utf-8"), JSONUtil.toJsonStr(pushBody));
            Request.Builder builder = new Request.Builder();
            Request request = builder.addHeader("content-type", "application/json").url(plan.getCallbackUrl())
                    .post(requestBody).build();
            Call call = httpClient.newCall(request);

            call.enqueue(new Callback() {
                //请求时失败时调用
                @Override
                public void onFailure(Call call, IOException e) {
                    LOGGER.error("视频合成客户回调失败", e);
                    job.setPushStatus(Const.TWO);
                    visualProduceJobManager.updateById(job);
                }

                //请求成功时调用
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    final String res = response.body().string();
                    if ("success".equals(res)) {
                        LOGGER.info("视频合成客户回调成功");
                        job.setPushStatus(Const.ONE);
                        visualProduceJobManager.updateById(job);
                    } else {
                        LOGGER.error("视频合成客户回调失败, reponse={}", res);
                        job.setPushStatus(Const.TWO);
                        visualProduceJobManager.updateById(job);
                    }
                }
            });
        });
    }
}
