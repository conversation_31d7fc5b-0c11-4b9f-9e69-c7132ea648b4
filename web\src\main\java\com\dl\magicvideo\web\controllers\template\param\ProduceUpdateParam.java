package com.dl.magicvideo.web.controllers.template.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class ProduceUpdateParam {

    @ApiModelProperty("任务id")
    @NotBlank(message = "任务id不能为空")
    private String jobId;

    @ApiModelProperty("推荐使用状态 0-未启用 1-已启用")
    @Max(1)
    @Min(0)
    private Integer recommendState;
}
