package com.dl.magicvideo.biz.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.poi.hslf.usermodel.*;
import org.apache.poi.sl.usermodel.Placeholder;
import org.apache.poi.xslf.usermodel.*;

/**
 * poi的转换方式
 */
@Slf4j
public class PPTUtil {
    /**
     * ppt转为图片列表
     * @param pptFile 在（服务器）本地的ppt文件 比如：123.ppt
     * @return 转换后的图片集合
     */
    public static List<File> ppt2Png(File pptFile) {
        List<File> pngFileList = new ArrayList<>();
        long startTime = System.currentTimeMillis();

        FileInputStream is = null;
        // 将ppt文件转换成每一帧的图片
        HSLFSlideShow ppt = null;

        try {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            is = new FileInputStream(pptFile);
            ppt = new HSLFSlideShow(is);
            int idx = 1;

            Dimension pageSize = ppt.getPageSize();
            double image_rate = 1.0;
            int imageWidth = (int) Math.floor(image_rate * pageSize.getWidth());
            int imageHeight = (int) Math.floor(image_rate * pageSize.getHeight());

            for (HSLFSlide slide : ppt.getSlides()) {
                BufferedImage img = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_ARGB);
                Graphics2D graphics = img.createGraphics();
                graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                graphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
                // clear the drawing area
                graphics.setPaint(Color.white);
                graphics.fill(new Rectangle2D.Float(0, 0, imageWidth, imageHeight));
                graphics.scale(image_rate, image_rate);

                //防止中文乱码
                for (HSLFShape shape : slide.getShapes()) {
                    if (shape instanceof HSLFTextShape) {
                        HSLFTextShape hslfTextShape = (HSLFTextShape) shape;
                        for (HSLFTextParagraph hslfTextParagraph : hslfTextShape) {
                            for (HSLFTextRun hslfTextRun : hslfTextParagraph) {
                                hslfTextRun.setFontFamily("宋体");
                            }
                        }
                    }
                }

                FileOutputStream out = null;
                try {
                    slide.draw(graphics);
                    File pngFile = new File(pptFile.getPath().replace(".ppt", String.format("-%04d.png", idx++)));
                    out = new FileOutputStream(pngFile);
                    ImageIO.write(img, "png", out);
                    pngFileList.add(pngFile);
                } catch (Exception e) {
                    log.error("ppt2Png exception", e);
                } finally {
                    try {
                        if (out != null) {
                            out.flush();
                            out.close();
                        }

                        if (graphics != null) {
                            graphics.dispose();
                        }

                        if (img != null) {
                            img.flush();
                        }
                    } catch (IOException e) {
                        log.error("ppt2Png close exception", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("ppt2Png exception", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }

                if (ppt != null) {
                    ppt.close();
                }
            } catch (Exception e) {
                log.error("ppt2Png exception", e);
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("ppt2Png的时间：{}", endTime - startTime);
        return pngFileList;
    }

    public static List<File> pptx2Png(File pptxFile) {
        List<File> pngFileList = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        FileInputStream is = null;
        // 将ppt文件转换成每一帧的图片
        XMLSlideShow pptx = null;

        try {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            is = new FileInputStream(pptxFile);
            pptx = new XMLSlideShow(is);
            int idx = 1;

            Dimension pageSize = pptx.getPageSize();
            double image_rate = 1.0;
            int imageWidth = (int) Math.floor(image_rate * pageSize.getWidth());
            int imageHeight = (int) Math.floor(image_rate * pageSize.getHeight());

            for (XSLFSlide xslfSlide : pptx.getSlides()) {
                BufferedImage img = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_ARGB);
                Graphics2D graphics = img.createGraphics();
                graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                graphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
                // clear the drawing area
                graphics.setPaint(Color.white);
                graphics.fill(new Rectangle2D.Float(0, 0, imageWidth, imageHeight));
                graphics.scale(image_rate, image_rate);

                //防止中文乱码
                for (XSLFShape shape : xslfSlide.getShapes()) {
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape xslfTextShape = (XSLFTextShape) shape;
                        for (XSLFTextParagraph xslfTextParagraph : xslfTextShape) {
                            for (XSLFTextRun xslfTextRun : xslfTextParagraph) {
                                //xslfTextRun.setFontFamily("宋体");
                                xslfTextRun.setFontFamily("SimSun");
                            }
                        }
                    }
                }

                FileOutputStream out = null;
                try {
                    xslfSlide.draw(graphics);
                    File pngFile = new File(pptxFile.getPath().replace(".pptx", String.format("-%04d.png", idx++)));
                    out = new FileOutputStream(pngFile);
                    ImageIO.write(img, "png", out);
                    pngFileList.add(pngFile);
                } catch (Exception e) {
                    log.error("pptx2Png exception", e);
                } finally {
                    try {
                        if (out != null) {
                            out.flush();
                            out.close();
                        }

                        if (graphics != null) {
                            graphics.dispose();
                        }

                        if (img != null) {
                            img.flush();
                        }
                    } catch (IOException e) {
                        log.error("pptx2Png close exception", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("pptx2Png exception", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }

                if (pptx != null) {
                    pptx.close();
                }
            } catch (Exception e) {
                log.error("pptx2Png exception", e);
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("pptx2Png耗时：{}", endTime - startTime);
        return pngFileList;
    }

    /**
     * 获取pptx的备注
     * @param pptxFile
     * @return
     */
    public static List<String> pptxRemark(File pptxFile){
        List<String> remarkList = new ArrayList<>();
        try {
            // 读取PPTX文件
            FileInputStream fis = new FileInputStream(pptxFile.getPath());
            XMLSlideShow ppt = new XMLSlideShow(fis);

            // 获取每一页的备注
            for (XSLFSlide slide : ppt.getSlides()) {
                // 获取备注文本框
                XSLFNotes notes = slide.getNotes();
                if (notes != null && Objects.nonNull(notes.getPlaceholders()) && notes.getPlaceholders().length > 0) {
                    // 打印备注内容
                    for (XSLFTextShape textShape : notes.getPlaceholders()) {
                        if (Objects.equals(Placeholder.BODY, textShape.getTextType())) {
                            remarkList.add(textShape.getText());
                        }
                    }
                }else {
                    remarkList.add("");
                }
            }

            ppt.close();
            fis.close();
        } catch (IOException e) {
            log.error("ppt获取备注失败");
        }
        return remarkList;
    }

    public static void main(String[] args) {
        try {
            File file = new File("C:\\Users\\<USER>\\Downloads\\区块链.pptx");
            List<String> files = pptxRemark(file);
            System.out.println(files.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
