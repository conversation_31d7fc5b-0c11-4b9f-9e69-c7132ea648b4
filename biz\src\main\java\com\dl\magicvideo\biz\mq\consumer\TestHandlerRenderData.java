package com.dl.magicvideo.biz.mq.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-03 16:49
 */
public class TestHandlerRenderData {

    /*public static void main(String[] args) throws Exception {
        String jsonString = "{\"a\":{\"b\":[{\"c\":\"oldValue1\"},{\"c\":\"oldValue2\"}],\"d\":\"oldValue3\",\"e\":{\"f\":\"oldValue4\",\"g\":{\"h\":[{\"i\":\"oldValue5\"},{\"j\":\"oldValue6\"}]}}}}";

        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree(jsonString);

       *//* // 更新路径"a.b.[0].c"的值
        updateJsonValue(jsonNode, "a.b.[0].c", "newValue1", mapper);

        // 更新路径"a.b.[1].c"的值
        updateJsonValue(jsonNode, "a.b.[1].c", "newValue2", mapper);

        // 更新路径"a.d"的值
        updateJsonValue(jsonNode, "a.d", "newValue3", mapper);

        updateJsonValue(jsonNode, "a.e.f", "newValue4", mapper);

        updateJsonValue(jsonNode, "a.e.g.[0].i", "newValue5", mapper);

        updateJsonValue(jsonNode, "a.e.g.[1].j", "newValue6", mapper);*//*

        // 将更新后的JsonNode转回字符串
        String updatedJsonString = mapper.writeValueAsString(jsonNode);
        System.out.println(updatedJsonString);
    }*/




    /*public static void main(String[] args) {
        String json = "{\"a\":{\"b\":[{\"c\":\"oldValue1\"},{\"c\":\"oldValue2\"}],\"d\":\"oldValue3\",\"e\":{\"f\":\"oldValue4\",\"g\":{\"h\":[{\"i\":\"oldValue5\"},{\"j\":\"oldValue6\"}]}}}}";

        // 创建 ObjectMapper 对象
        ObjectMapper mapper = new ObjectMapper();

        // 要更新的值
        Map<String, String> updates = new HashMap<String, String>();
        updates.put("a.b.[0].c", "newValue1");
        updates.put("a.b.[1].c", "newValue2");
        updates.put("a.d", "newValue3");
        updates.put("a.e.f", "newValue4");
        updates.put("a.e.g.h.[0].i", "newValue5");
        updates.put("a.e.g.h.[1].j", "newValue6");

        try {
            // 将 JSON 字符串解析成 JsonNode 对象
            JsonNode rootNode = mapper.readTree(json);

            // 更新指定路径的值
            for (Map.Entry<String, String> entry : updates.entrySet()) {
                String path = entry.getKey();
                String newValue = entry.getValue();
                updateNode(rootNode, path, newValue, mapper);
            }

            // 将更新后的 JsonNode 转换回 JSON 字符串
            String updatedJson = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
            System.out.println(updatedJson);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void updateNode(JsonNode node, String path, String newValue, ObjectMapper mapper) {
        String[] tokens = path.split("\\.");
        JsonNode current = node;
        for (int i = 0; i < tokens.length; i++) {
            String token = tokens[i];
            if (i == tokens.length - 1) {
                // 最后一个元素，更新值
                if (current instanceof ObjectNode) {
                    ((ObjectNode) current).put(token, newValue);
                } else if (current instanceof ArrayNode) {
                    ArrayNode arrayNode = (ArrayNode) current;
                    int index = Integer.parseInt(token);
                    arrayNode.set(index, mapper.getNodeFactory().objectNode().put(token, newValue));
                }
            } *//*else if (token.startsWith("[")) {
                int index = Integer.parseInt(token.substring(1,2));
                if (current instanceof ArrayNode) {
                    ArrayNode arrayNode = (ArrayNode) current;

                    arrayNode.set(index, mapper.getNodeFactory().objectNode().put(token, newValue));
                }

            }*//* else {
                // 非最后一个元素，找到对应的子节点
                if (current.has(token)) {
                    current = current.get(token);
                } else {
                    // 如果子节点不存在，创建一个新的对象节点
                    if (current instanceof ObjectNode) {
                        ((ObjectNode) current).put(token, mapper.getNodeFactory().objectNode());
                        current = current.get(token);
                    } else if (current instanceof ArrayNode) {
                        ArrayNode arrayNode = (ArrayNode) current;
                        int index = arrayNode.size();
                        arrayNode.add(mapper.getNodeFactory().objectNode()
                                .put(token, mapper.getNodeFactory().textNode("")));
                        current = arrayNode.get(index);
                    }
                }
            }
        }
    }*/

    public static void main(String[] args) {
        String filPath = "/Users/<USER>/work/定力/aigc小助手/renderdata替换/1163723983999552034的renderData.txt";
        String content = "";
        try {
            byte[] encodeBytes = Files.readAllBytes(Paths.get(filPath));
            content = new String(encodeBytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            System.out.println("发生异常，e:" + e);
            return;
        }

        String newRenderData = content.replaceAll("\n", "").replaceAll("\r", "");
        // 创建ObjectMapper实例
        ObjectMapper mapper = new ObjectMapper();

        try {
            // 将JSON字符串解析为JsonNode对象
            JsonNode rootNode = mapper.readTree(newRenderData);

            // 检查并获取viewData节点
            JsonNode viewDataNode = rootNode.path("viewData");
            if (viewDataNode != null && viewDataNode.isObject()) {
                // 获取zIndexList节点
                JsonNode zIndexListNode = viewDataNode.path("zIndexList");
                if (zIndexListNode != null && zIndexListNode.isArray()) {
                    // 遍历zIndexList数组，寻找特定ID的zIndex
                    ArrayNode zIndices = (ArrayNode) zIndexListNode;
                    for (JsonNode zIndex : zIndices) {
                        if ("--Group--_1_561336366".equals(zIndex.path("id").asText())) {

                            ObjectNode zIndexNode = (ObjectNode) zIndex;
                            zIndexNode.put("duration",123456);

                            // 找到目标zIndex，修改duration值
                            ArrayNode childrenNode = (ArrayNode) zIndex.path("children");
                            if (childrenNode != null) {
                                for (JsonNode child : childrenNode) {
                                    ((ObjectNode) child).put("duration", 123456);
                                }
                            }
                            break; // 找到目标后退出循环
                        }
                    }
                }
            }

            // 将修改后的JsonNode对象转换回JSON字符串
            String modifiedJson = mapper.writeValueAsString(rootNode);
            System.out.println(modifiedJson);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
