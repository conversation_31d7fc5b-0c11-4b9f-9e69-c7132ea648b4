package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateCopyBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSimpleInfoUpdateBO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualTemplateBO;
import com.dl.magicvideo.biz.manager.visual.dto.TemplateCopyDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【visual_template】的数据库操作Service
* @createDate 2023-04-24 10:22:23
*/
public interface VisualTemplateManager extends IService<VisualTemplatePO> {
    /**
     * 功能描述: <br>可视化模板创建
     * @Param: [bo]
     * @Return: java.lang.Long
     * @Author: zhousx
     * @Date: 2023/4/24 10:32
     */
    Long add(VisualTemplateBO bo);

    /**
     * 功能描述: <br>
     *
     * @Param: [bo]
     * @Return: java.lang.Long
     * @Author: zhousx
     * @Date: 2023/4/25 13:48
     */
    Long update(VisualTemplateBO bo);

    /**
     * 新增模板完整信息
     *
     * @param bo
     * @return
     */
    Long addIntegrity(VisualTemplateBO bo);

    /**
     * 更新模板简要信息
     *
     * @param updateBO
     * @return
     */
    Long updateTemplateSimpleInfo(TemplateSimpleInfoUpdateBO updateBO);

    /**
     * 功能描述: <br>删除模板
     *
     * @Param: [templateId]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/3/10 9:30
     */
    void delete(Long templateId);

    /**
     * 功能描述: <br>
     *
     * @Param: [templateId]
     * @Return: com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO
     * @Author: zhousx
     * @Date: 2023/4/24 11:40
     */
    VisualTemplateDTO detail(Long templateId, String tenantCode);

    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List<com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO>>
     * @Author: zhousx
     * @Date: 2023/4/24 13:46
     */
    ResponsePageQueryDO<List<VisualTemplateDTO>> pageQuery(TemplateSearchBO bo);

    /**
     * 功能描述: <br>
     * @Param: [templateCopyBO]
     * @Return: java.lang.Long
     * @Author: zhousx
     * @Date: 2023/6/18 11:36
     */
    TemplateCopyDTO copy(TemplateCopyBO templateCopyBO);

    /**
     * 功能描述: <br>
     * @Param: [bo]
     * @Return: com.dl.framework.common.bo.ResponsePageQueryDO<java.util.List<com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO>>
     * @Author: zhousx
     * @Date: 2023/4/24 13:46
     */
    ResponsePageQueryDO<List<VisualTemplateDTO>> authedTemplatePageQuery(TemplateSearchBO bo);

    /**
     * 功能描述: <br>
     * @Param: [originalVideoUrl, templateId]
     * @Return: void
     * @Author: zhousx
     * @Date: 2023/7/7 15:51
     */
    void generateShortVideo(String originalVideoUrl, Long templateId);

    /**
     * 授权给本地化租户的模版分页查询
     * @param bo
     * @return
     */
    ResponsePageQueryDO<List<VisualTemplateDTO>> syncCloudAuthedTemplatePage(TemplateSearchBO bo);

    /**
     * 授权给本地化租户的模板列表查询
     *
     * @param bo
     * @return
     */
    List<VisualTemplateDTO> syncCloudAuthedTemplateList(TemplateSearchBO bo);

    /**
     * 获取模板下最大的数字
     * @return
     */
    Integer getTemplateNameMaxIndex(String sourceTemplateName);
}
