package com.dl.magicvideo.biz.dal.shorturl.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-15 15:13
 */
@Data
@TableName("oc_short_url")
public class ShortURLPO extends BasePO {
    private static final long serialVersionUID = 1979067202952154920L;

    @TableId
    private Long id;

    @TableField("code")
    private String code;

    @TableField("target_url")
    private String targetURL;

    @TableField("short_url")
    private String shortURL;

    @TableField("tenant_code")
    private String tenantCode;

    @TableField("expire_dt")
    private Date expireDate;

    @TableField("is_deleted")
    private Integer isDeleted;
}
