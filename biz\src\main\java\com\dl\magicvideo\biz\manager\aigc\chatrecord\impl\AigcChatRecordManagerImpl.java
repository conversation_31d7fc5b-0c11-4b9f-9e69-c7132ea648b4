package com.dl.magicvideo.biz.manager.aigc.chatrecord.impl;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.toolkit.EsWrappers;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.EsPageInfoUtil;
import com.dl.magicvideo.biz.es.aigc.chatrecord.EsIndexAigcChatRecordMapper;
import com.dl.magicvideo.biz.es.aigc.chatrecord.po.EsIndexAigcChatRecord;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.AigcChatRecordManager;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordAddBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordSearchBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.bo.AigcChatRecordUpdateBO;
import com.dl.magicvideo.biz.manager.aigc.chatrecord.helper.AigcChatRecordHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 14:54
 */
@Component
@Slf4j
public class AigcChatRecordManagerImpl implements AigcChatRecordManager {

    @Resource
    private EsIndexAigcChatRecordMapper esIndexAigcChatRecordMapper;

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public EsPageInfo<EsIndexAigcChatRecord> pageSearch(AigcChatRecordSearchBO searchBO) {
        // 创建 SearchRequest
        SearchRequest searchRequest = new SearchRequest("es_index_aigc_chat_record"); // 替换为你的索引名

        // 构建 BoolQuery
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(new TermQueryBuilder("user_id", searchBO.getUserId()))
                .must(new TermQueryBuilder("is_deleted", 0));

        if (StringUtils.isNotBlank(searchBO.getTenantCode())) {
            boolQuery.must(new TermQueryBuilder("tenant_code", searchBO.getTenantCode()));
        }

        if (Objects.nonNull(searchBO.getAfterRecordId())) {
            boolQuery.must(new RangeQueryBuilder("record_id").gt(searchBO.getAfterRecordId()));
        }

        if (Objects.nonNull(searchBO.getAfterSendDt())) {
            boolQuery.must(new RangeQueryBuilder("send_dt").gt(searchBO.getAfterSendDt()));
        }


        // 构建 should 子句
        boolQuery.should(new TermsQueryBuilder("content_type", new int[]{12, 13}))
                .should(QueryBuilders.boolQuery()
                        .must(new TermQueryBuilder("content_type", 1))
                        .must(new TermQueryBuilder("type", 2)));
        //两个条件必须满足一个
        boolQuery.minimumShouldMatch(1);

        // 创建 SearchSourceBuilder
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQuery)
                .from(searchBO.getOffset())
                .size(searchBO.getPageSize())
                .sort("send_dt", SortOrder.DESC)
                .sort("record_id", SortOrder.DESC);

        searchRequest.source(sourceBuilder);

        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            log.info("AI视频查询响应结果,sourceBuilder={}, searchResponse={}", sourceBuilder, searchResponse);
            EsPageInfo<EsIndexAigcChatRecord> pageResult = EsPageInfoUtil.fromSearchResponse(searchResponse, EsIndexAigcChatRecord.class, searchBO.getPageIndex(), searchBO.getPageSize());
            return pageResult;
        } catch (IOException e) {
            log.error("AI视频查询发生异常,text:{},e.message:{},e", JSONUtil.toJsonStr(searchBO), e.getMessage(), e);
            throw BusinessServiceException.getInstance("AI视频查询发生异常!");
        }
    }

    @Override
    public EsIndexAigcChatRecord add(AigcChatRecordAddBO addBO) {
        //参数校验
        this.precheck(addBO);

        Long recordId = hostTimeIdg.generateId().longValue();
        EsIndexAigcChatRecord addRecord = AigcChatRecordHelper.cnvAigcChatRecordAddBO2EsIndexAigcChatRecord(addBO);
        addRecord.setRecordId(recordId);
        esIndexAigcChatRecordMapper.insert(addRecord);
        return addRecord;
    }

    @Override
    public List<EsIndexAigcChatRecord> batchAdd(List<AigcChatRecordAddBO> addBOList) {
        //参数校验
        addBOList.forEach(this::precheck);

        List<EsIndexAigcChatRecord> addRecordList = new ArrayList<>();
        for (AigcChatRecordAddBO addBO : addBOList) {
            Long recordId = hostTimeIdg.generateId().longValue();
            EsIndexAigcChatRecord addRecord = AigcChatRecordHelper.cnvAigcChatRecordAddBO2EsIndexAigcChatRecord(addBO);
            addRecord.setRecordId(recordId);
            addRecordList.add(addRecord);
        }
        esIndexAigcChatRecordMapper.insertBatch(addRecordList);
        return addRecordList;
    }

    @Override
    public void updateContent(AigcChatRecordUpdateBO updateBO) {
        Assert.notNull(updateBO, "聊天记录修改对象不能为空");
        Assert.isTrue(StringUtils.isNotBlank(updateBO.getContent()), "聊天内容不能为空");

        EsIndexAigcChatRecord updateRecord = new EsIndexAigcChatRecord();
        updateRecord.setContent(updateBO.getContent());
        updateRecord.setModifyDt(new Date());
        if (Objects.nonNull(updateBO.getContentType())) {
            updateRecord.setContentType(updateBO.getContentType());
        }
        esIndexAigcChatRecordMapper.update(updateRecord, EsWrappers.lambdaUpdate(EsIndexAigcChatRecord.class)
                .eq(EsIndexAigcChatRecord::getRecordId, updateBO.getRecordId()));
    }

    @Override
    public EsIndexAigcChatRecord getOne(Long recordId) {
        Assert.notNull(recordId, "聊天记录id不能为空");
        return esIndexAigcChatRecordMapper.selectOne(
                EsWrappers.lambdaQuery(EsIndexAigcChatRecord.class).eq(EsIndexAigcChatRecord::getRecordId, recordId)
                        .eq(EsIndexAigcChatRecord::getIsDeleted, Const.ZERO));
    }

    @Override
    public void deleteRecord(EsIndexAigcChatRecord record) {
        esIndexAigcChatRecordMapper.updateById(record);
    }

    private void precheck(AigcChatRecordAddBO addBO) {
        Assert.notNull(addBO, "聊天记录对象不能为空");
        Assert.notNull(addBO.getUserId(), "用户id不能为空");
        Assert.isTrue(StringUtils.isNotBlank(addBO.getTenantCode()), "租户编号不能为空");
        Assert.notNull(addBO.getContent(), "聊天内容不能为空");
        Assert.notNull(addBO.getContentType(), "聊天内容类型不能为空");
        Assert.notNull(addBO.getSendDt(), "消息发送时间不能为空");
    }
}
