package com.dl.magicvideo.biz.manager.visual;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.visual.po.ComponentVersionDetailPO;

import java.util.List;

public interface ComponentVersionDetailManager extends IService<ComponentVersionDetailPO> {

    /**
     * 获取版本号列表
     * @return
     */
    List<ComponentVersionDetailPO> getVersionList();

    /**
     * 根据版本号获取组件url
     * @param version 版本号
     * @return
     */
    ComponentVersionDetailPO getUrlByVersion(String version);
}
