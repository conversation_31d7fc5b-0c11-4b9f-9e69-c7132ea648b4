package com.dl.magicvideo.biz.manager.visual.dto;

import lombok.Data;

/**
 * @description：TODO
 * @author： <PERSON><PERSON><PERSON>
 * @create： 2024/11/27 16:36
 */
@Data
public class DataSheetContentDTO {
    /**
     * 动画时长
     */
    private Long animationTime;
    /**
     *动态时长
     */
    private boolean dynamicDuration;
    /**
     *分页数量，用于计算动态时长
     */
    private Integer pageSize;

    /**
     * 每页时间
     */
    private Integer pageScrollTime;

    /**
     * 绑定的key
     */
    private String bindKey;
}
