package com.dl.magicvideo.web.controllers.oplog.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 17:20
 */
@Data
public class OpLogPageParam extends AbstractPageParam {

    @ApiModelProperty(value = "操作对象")
    private String opObject;

    @ApiModelProperty(value = "操作对象主键")
    private String opKey;

    @ApiModelProperty(value = "数影操作类型")
    private String magicOpType;

    @ApiModelProperty("操作人id")
    private String opUserId;

    @ApiModelProperty("操作开始时间")
    private Date opSince;

    @ApiModelProperty("操作结束时间")
    private Date opUntil;

}
