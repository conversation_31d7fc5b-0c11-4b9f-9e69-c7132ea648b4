package com.dl.magicvideo.web.controllers.internal.visual;

import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.VisualDmJobInternalInfoDTO;
import com.dl.magicvideo.web.controllers.internal.statistics.dto.VisualTtsJobInternalInfoDTO;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualAiJobInternalPageParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 11:38
 */
@RestController
@RequestMapping("/visual/internal/aijob")
public class VisualAiJobInternalController {

    @Resource
    private VisualAiJobInternalProcess visualAiJobInternalProcess;

    @NotLogin
    @PostMapping("/pagedmjob")
    @ApiOperation("分页查询数字人合成任务")
    public ResultPageModel<VisualDmJobInternalInfoDTO> pageDmJob(
            @RequestBody @Validated VisualAiJobInternalPageParam param) {
        return visualAiJobInternalProcess.pageDmJob(param);
    }

    @NotLogin
    @PostMapping("/pagettsjob")
    @ApiOperation("分页查询tts合成任务")
    public ResultPageModel<VisualTtsJobInternalInfoDTO> pageTtsJob(
            @RequestBody @Validated VisualAiJobInternalPageParam param) {
        return visualAiJobInternalProcess.pageTtsJob(param);
    }

}
