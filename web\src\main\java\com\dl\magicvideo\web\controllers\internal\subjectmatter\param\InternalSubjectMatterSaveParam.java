package com.dl.magicvideo.web.controllers.internal.subjectmatter.param;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 14:35
 */
@Data
public class InternalSubjectMatterSaveParam {

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 题材名称
     */
    @NotBlank(message = "题材名称不能为空")
    private String name;

    /**
     * 图片链接地址
     */
    @NotBlank(message = "图片链接地址不能为空")
    private String imgUrl;

    /**
     * excel链接地址
     */
    @NotBlank(message = "excel链接地址不能为空")
    private String excelUrl;

    /**
     * 题材级别，1-一级题材，2-二级题材
     */
    @NotNull(message = "题材级别不能为空")
    private Integer level;

    /**
     * 父级题材bizId
     */
    private Long parentId;

    /**
     * 操作人名称
     */
    @NotBlank(message = "操作人名称不能为空")
    private String operatorName;

    /**
     * 操作人id
     */
    @NotNull(message = "操作人id不能为空")
    private Long operatorId;

    /**
     * json文件地址
     */
    private String jsonUrl;

    /**
     * 股票参数列表
     */
    private List<InternalSubjectMatterStockParam> stockParams;
}
