package com.dl.magicvideo.biz.dal.chartlet.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-12 16:39
 */
@Data
@TableName("chartlet_info")
public class ChartletInfoPO extends BasePO {

    private static final long serialVersionUID = 1396585143356035295L;
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("biz_id")
    private Long bizId;

    @TableField("name")
    private String name;

    @TableField("content_url")
    private String contentUrl;

    @TableField("resolution_ratio")
    private String resolutionRatio;

    @TableField("cover_img")
    private String coverImg;

    @TableField("duration")
    private Long duration;

    @TableField("creator_name")
    private String creatorName;

    @TableField("modify_name")
    private String modifyName;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("category")
    private Integer category;

}
