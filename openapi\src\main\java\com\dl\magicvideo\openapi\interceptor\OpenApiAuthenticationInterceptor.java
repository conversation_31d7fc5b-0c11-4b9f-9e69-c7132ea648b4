package com.dl.magicvideo.openapi.interceptor;

import com.dl.framework.core.interceptor.expdto.CertificateException;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.constant.OpenApiConstant;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.RedisUtil;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;

import static com.dl.magicvideo.biz.common.constant.OpenApiConstant.OPENAPI_TOKEN_CACHE_KEY_PREFIX;

@Component
public class OpenApiAuthenticationInterceptor implements HandlerInterceptor, Ordered {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private TenantInfoManager tenantInfoManager;

    private final PathMatcher pathMatcher = new AntPathMatcher();

    private boolean isIgnoreUrl(HttpServletRequest request) {
        return false;
    }

    private boolean isOpenApi(HttpServletRequest request) {
        String url = request.getRequestURI();
        return pathMatcher.match("/openapi/**", url);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        //判断是否为app端，app端请求不走此判断
        if (!isOpenApi(request)) {
            return true;
        }
        //判断是否忽略url
        if (isIgnoreUrl(request)) {
            return true;
        } else if (handler instanceof ResourceHttpRequestHandler) {
            //静态资源
            return false;
        }

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //2、判断 NotLogin，有则跳过认证
        if (method.isAnnotationPresent(NotLogin.class)) {
            NotLogin loginToken = method.getAnnotation(NotLogin.class);
            if (loginToken.required()) {
                return true;
            }
        }
        // 从 http 请求头中取出 token
        String token = request.getHeader(OpenApiConstant.OPENAPI_TOKEN_HEADER_NAME);
        String accessKey = request.getHeader(OpenApiConstant.ACCESS_KEY);
        //如果token为空的情况下，返回非法token，禁止访问
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(accessKey)) {
            //非法token
            throw new CertificateException();
        } else {
            String redisValue = redisUtil.get(OPENAPI_TOKEN_CACHE_KEY_PREFIX + accessKey);
            if (!Objects.equals(redisValue, token)) {
                throw new CertificateException();
            }
            AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfoFromCache(accessKey);
            operatorUtil.init(Const.SYS_USER_ID, "", accessKey, tenantInfo.getName());
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        if (!isOpenApi(request)) {
            //去除biz端的用户注入
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
