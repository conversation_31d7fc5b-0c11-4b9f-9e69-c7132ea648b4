package com.dl.magicvideo.biz.manager.visual.bo;

import com.dl.magicvideo.biz.common.SearchBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @describe: TemplateSearchBO
 * @author: zhousx
 * @date: 2023/2/8 11:00
 */
@Data
public class TemplateSearchBO extends SearchBO {
    private String name;

    private Integer status;

    private String tenantCode;

    private Long createBy;

    private Integer isSys;

    private Integer resolutionType;

    private Integer sortType;

    /**
     * 转发配置完成状态 0-未配置，1-已配置基本转发配置，2-已配置基本转发配置和交互式配置
     */
    private Integer shareConfState;

    private Integer firstCategory;

    private Integer secondCategory;

    private Integer isManager;

    private Integer isPPTType;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板id列表
     */
    private List<Long> templateIds;

    /**
     * 模板类型，1-常规模板，2-数据图表模板
     * @see: com.dl.magicvideo.biz.manager.visual.enums.TemplateTypeEnum
     */
    private Integer type;

    /**
     * 是否展示 0-否,1-是 默认为1
     */
    private Integer isShow = 1;

    /**
     * 标签ID
     */
    private List<Long> tagIds;

    /**
     * 收藏用户
     */
    private Long collectUserId;
}
