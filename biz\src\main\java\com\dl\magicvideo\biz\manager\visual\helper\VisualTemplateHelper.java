package com.dl.magicvideo.biz.manager.visual.helper;

import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.bo.VisualTemplateBO;

public class VisualTemplateHelper {

    public static VisualTemplatePO cnvVisualTemplateBO2PO(VisualTemplateBO bo){
        VisualTemplatePO newTemplate = new VisualTemplatePO();
        newTemplate.setName(bo.getName());
        newTemplate.setCoverUrl(bo.getCoverUrl());
        newTemplate.setResolution(bo.getResolution());
        newTemplate.setBgMusic(bo.getBgMusic());
        newTemplate.setBgMusicParam(bo.getBgMusicParam());
        newTemplate.setTtsParam(bo.getTtsParam());
        newTemplate.setReplaceData(bo.getReplaceData());
        newTemplate.setResolution(bo.getResolution());
        newTemplate.setResolutionType(bo.getResolutionType());
        newTemplate.setDuration(bo.getDuration());
        newTemplate.setTenantCode(bo.getTenantCode());
        newTemplate.setCreatorName(bo.getCreatorName());
        newTemplate.setIsManager(bo.getIsManager());
        newTemplate.setFirstCategory(bo.getFirstCategory());
        newTemplate.setSecondCategory(bo.getSecondCategory());
        newTemplate.setComponentVersion(bo.getComponentVersion());
        newTemplate.setIsShow(bo.getIsShow());
        return newTemplate;
    }

}
