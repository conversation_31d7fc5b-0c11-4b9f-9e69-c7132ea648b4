package com.dl.magicvideo.biz.manager.visual.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.visual.po.VisualCardPO;
import com.dl.magicvideo.biz.manager.visual.VisualCardManager;
import com.dl.magicvideo.biz.dal.visual.VisualCardMapper;
import com.dl.magicvideo.biz.manager.visual.bo.CardBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualCardDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
* <AUTHOR>
* @description 针对表【visual_card】的数据库操作Service实现
* @createDate 2023-04-24 16:16:02
*/
@Service
public class VisualCardManagerImpl extends ServiceImpl<VisualCardMapper, VisualCardPO>
    implements VisualCardManager {
    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Override
    public VisualCardDTO add(CardBO bo) {
        Assert.notNull(bo.getTemplateId(), "模板id不可为空。");
        Assert.isTrue(StringUtils.isNotBlank(bo.getName()), "卡片名称不可为空。");

        // 新增卡片
        VisualCardPO visualCardPO = new VisualCardPO();
        visualCardPO.setTemplateId(bo.getTemplateId());
        visualCardPO.setCardId(hostTimeIdg.generateId().longValue());
        visualCardPO.setName(bo.getName());
        visualCardPO.setCoverUrl(bo.getCoverUrl());
        visualCardPO.setRenderData(bo.getRenderData());
        visualCardPO.setRenderData(bo.getRenderData());
        save(visualCardPO);

        VisualCardDTO visualCardDTO = new VisualCardDTO();
        visualCardDTO.setCardId(visualCardPO.getCardId());
        visualCardDTO.setName(visualCardPO.getName());
        visualCardDTO.setCoverUrl(visualCardPO.getCoverUrl());
        visualCardDTO.setResolution(visualCardPO.getResolution());
        visualCardDTO.setTemplateId(visualCardPO.getTemplateId());
        visualCardDTO.setRenderData(visualCardPO.getRenderData());
        return visualCardDTO;
    }

    @Override
    public void delete(Long cardId) {
        this.lambdaUpdate().eq(VisualCardPO::getCardId, cardId).set(VisualCardPO::getIsDeleted, Const.ONE).update();
    }

    @Override
    public void update(CardBO bo) {
        Assert.notNull(bo.getCardId(), "卡片id不可为空。");
        VisualCardPO visualCardPO = this.lambdaQuery().eq(VisualCardPO::getCardId, bo.getCardId()).one();
        Assert.notNull(visualCardPO, "卡片不存在。");

        visualCardPO.setName(bo.getName());
        visualCardPO.setCoverUrl(bo.getCoverUrl());
        visualCardPO.setResolution(bo.getResolution());
        visualCardPO.setRenderData(bo.getRenderData());
        updateById(visualCardPO);
    }
}




