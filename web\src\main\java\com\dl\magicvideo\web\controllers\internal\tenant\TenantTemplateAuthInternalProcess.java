package com.dl.magicvideo.web.controllers.internal.tenant;

import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.manager.visual.VisualTenantTemplateAuthManager;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateAuthSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TenantTemplateAuthBO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.tenant.dto.TenantTemplateAuthDTO;
import com.dl.magicvideo.web.controllers.internal.tenant.param.TenantAuthParam;
import com.dl.magicvideo.web.controllers.internal.tenant.param.TenantTemplateAuthPageQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TenantTemplateAuthInternalProcess extends AbstractController {

    @Autowired
    private VisualTenantTemplateAuthManager visualTenantTemplateAuthManager;

    public ResultModel<Void> auth(TenantAuthParam param) {
        Assert.isTrue(StringUtils.isNumeric(param.getTemplateId()),"模板Id格式错误");
        //构建查询参数
        TenantTemplateAuthBO bo = new TenantTemplateAuthBO();
        bo.setTemplateId(Long.valueOf(param.getTemplateId()));
        bo.setStatus(param.getStatus());
        bo.setTenantName(param.getTenantName());
        bo.setTenantCode(param.getTenantCode());
        visualTenantTemplateAuthManager.auth(bo);
        return ResultModel.success(null);
    }

    public ResultPageModel<TenantTemplateAuthDTO> list(TenantTemplateAuthPageQueryParam param) {
        ResponsePageQueryDO<List<com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO>> result = null;
        //构建BO对象存放查询属性
        TemplateAuthSearchBO templateAuthSearchBO = new TemplateAuthSearchBO();
        //设置属性
        templateAuthSearchBO.setPageIndex(param.getPageIndex());
        templateAuthSearchBO.setPageSize(param.getPageSize());
        templateAuthSearchBO.setTemplateId(param.getTemplateId());
        templateAuthSearchBO.setName(param.getName());
        templateAuthSearchBO.setTenantCode(param.getTenantCode());
        //传入了授权/未授权状态
        if (Objects.nonNull(param.getStatus())){
            templateAuthSearchBO.setStatus(param.getStatus());
            result = visualTenantTemplateAuthManager.pageQueryByStatus(templateAuthSearchBO);
        }else {
            //没有传入授权状态
            //com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO真正的DTO
            result = visualTenantTemplateAuthManager.pageQuery(templateAuthSearchBO);
        }

        if (CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<TenantTemplateAuthDTO> vos = result.getDataResult().stream().map(this::cnvTemplateAuthDTO2VO)
                .collect(Collectors.toList());
        return pageQueryModel(result, vos);
    }

    /**
     * 创建私有方法cnvTemplateAuthDTO2VO
     */
    private TenantTemplateAuthDTO cnvTemplateAuthDTO2VO(com.dl.magicvideo.biz.manager.visual.dto.TemplateAuthDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        //创建VO对象并赋值
        TenantTemplateAuthDTO vo = new TenantTemplateAuthDTO();
        vo.setTemplateId(dto.getTemplateId() + "");
        vo.setName(dto.getName());
        vo.setPreviewVideoUrl(dto.getPreviewVideoUrl());
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setCreateDt(dto.getCreateDt());
        vo.setStatus(dto.getStatus());
        return vo;
    }
}
