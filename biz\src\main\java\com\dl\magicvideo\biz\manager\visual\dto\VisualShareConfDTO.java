package com.dl.magicvideo.biz.manager.visual.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * DTO: 视频转发配置表
 */
@Data
public class VisualShareConfDTO {

    @ApiModelProperty("业务来源id")
    private Long bizId;

    @ApiModelProperty("业务类型 1模板 2作品")
    private Integer bizType;

    @ApiModelProperty("导航栏标题")
    private String navigationBarTitle;

    @ApiModelProperty("视频封面图")
    private String coverImg;

    @ApiModelProperty("推荐转发文案")
    private String recommendShareCnt;

    @ApiModelProperty("小程序转发封面图")
    private String mpShareCoverImg;

    @ApiModelProperty("h5转发封面图")
    private String h5ShareCoverImg;

    @ApiModelProperty("转发标题")
    private String shareTitle;

    @ApiModelProperty("转发摘要")
    private String shareRemark;

    @ApiModelProperty("使用场景 0-朋友圈/群发/视频号 1-私聊")
    private Integer useCase;

    @ApiModelProperty("视频名称")
    private String videoName;

    @ApiModelProperty("视频链接")
    private String videoUrl;

    @ApiModelProperty("是否删除")
    private Integer isDeleted;

    @ApiModelProperty("视频时长")
    private Long duration;

    @ApiModelProperty("租户编号")
    private String tenantCode;

    @ApiModelProperty("视频方向 1-竖版 2-横版")
    private Integer resolutionType;


}
