package com.dl.magicvideo.biz.manager.subjectmatter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterStockPO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterStockSaveBO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-30 17:30
 */
public interface SubjectMatterStockManager extends IService<SubjectMatterStockPO> {

    void batchSave(Long smBizId, List<SubjectMatterStockSaveBO> stockSaveBOList);
}
