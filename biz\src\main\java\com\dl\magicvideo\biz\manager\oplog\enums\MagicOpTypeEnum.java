package com.dl.magicvideo.biz.manager.oplog.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 数影的操作类型
 * 将object+opType转换为数影的操作类型，给前端使用。
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 17:43
 */
public enum MagicOpTypeEnum {

    LOGIN("login", "登录", OpObjectEnum.USER, OpTypeEnum.LOGIN),
    ADD_TEMPLATE("add_template", "新增模板", OpObjectEnum.TEMPLATE, OpTypeEnum.ADD),
    UPDATE_TEMPLATE("update_template", "修改模板", OpObjectEnum.TEMPLATE, OpTypeEnum.UPDATE);

    private String type;

    private String desc;

    private OpObjectEnum opObjectEnum;

    private OpTypeEnum opTypeEnum;

    MagicOpTypeEnum(String type, String desc, OpObjectEnum opObjectEnum, OpTypeEnum opTypeEnum) {
        this.type = type;
        this.desc = desc;
        this.opObjectEnum = opObjectEnum;
        this.opTypeEnum = opTypeEnum;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public OpObjectEnum getOpObjectEnum() {
        return opObjectEnum;
    }

    public OpTypeEnum getOpTypeEnum() {
        return opTypeEnum;
    }

    public static MagicOpTypeEnum convert(String magicOpType) {
        if (StringUtils.isBlank(magicOpType)) {
            return null;
        }
        for (MagicOpTypeEnum magicOpTypeEnum : MagicOpTypeEnum.values()) {
            if (magicOpTypeEnum.type.equals(magicOpType)) {
                return magicOpTypeEnum;
            }
        }
        return null;
    }

    public static MagicOpTypeEnum convert(String opObject, String opType) {
        if (StringUtils.isBlank(opObject) || StringUtils.isBlank(opType)) {
            return null;
        }

        for (MagicOpTypeEnum magicOpTypeEnum : MagicOpTypeEnum.values()) {
            if (magicOpTypeEnum.opObjectEnum.getCode().equals(opObject) && magicOpTypeEnum.opTypeEnum.getOpType()
                    .equals(opType)) {
                return magicOpTypeEnum;
            }
        }
        return null;
    }
}
