package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.aiservice.share.digitalasset.DaVirtualManSceneVoiceRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.enums.ServiceChannelEnum;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.utils.JsonUtils;
import com.dl.framework.core.controller.param.AbstractPageParam;
import com.dl.framework.core.converter.JSONObjectMapper;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.client.aiservice.AiServiceClient;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.enums.AiJobTypeE;
import com.dl.magicvideo.biz.common.enums.BatchStatusE;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.common.enums.SymbolE;
import com.dl.magicvideo.biz.common.service.CommonService;
import com.dl.magicvideo.biz.common.util.BindKeyUtils;
import com.dl.magicvideo.biz.common.util.DateUtil;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.common.util.PlaceHolderUtils;
import com.dl.magicvideo.biz.config.TtsThreadPoolConfig;
import com.dl.magicvideo.biz.dal.visual.VisualProduceJobMapper;
import com.dl.magicvideo.biz.dal.visual.dto.VisualProduceJoBJoinTemplateDTO;
import com.dl.magicvideo.biz.dal.visual.po.*;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.basicservice.TenantUserBindManager;
import com.dl.magicvideo.biz.manager.cos.CosFileUploadManager;
import com.dl.magicvideo.biz.manager.transaction.TransactionProxyManager;
import com.dl.magicvideo.biz.manager.util.MagicExpressionUtils;
import com.dl.magicvideo.biz.manager.util.VisualProduceJobUtil;
import com.dl.magicvideo.biz.manager.visual.*;
import com.dl.magicvideo.biz.manager.visual.bo.*;
import com.dl.magicvideo.biz.manager.visual.consts.VoiceConst;
import com.dl.magicvideo.biz.manager.visual.dto.*;
import com.dl.magicvideo.biz.manager.visual.dto.DynamicNodeDTO.DigitalManConfigDTO;
import com.dl.magicvideo.biz.manager.visual.enums.*;
import com.dl.magicvideo.biz.manager.visual.helper.ProduceJobHelper;
import com.dl.magicvideo.biz.manager.visual.vo.FundMarketDetailVO;
import com.dl.magicvideo.biz.manager.visual.vo.ManagerDetailVO;
import com.dl.magicvideo.biz.manager.visual.vo.WeeklyReviewVO;
import com.dl.magicvideo.biz.mq.DlChannels;
import com.dl.magicvideo.biz.mq.producer.GeneratePreviewDataProducer;
import com.dl.magicvideo.biz.mq.producer.ProduceFailProducer;
import com.dl.magicvideo.biz.mq.producer.ProduceJobCreatedProducer;
import com.dl.magicvideo.biz.mq.producer.ProduceJobSubordinateTtsSuccessProducer;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.CallableWrapper;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【visual_produce_job】的数据库操作Service实现
 * @createDate 2023-04-26 14:05:39
 */
@Slf4j
@Service
public class VisualProduceJobManagerImpl extends ServiceImpl<VisualProduceJobMapper, VisualProduceJobPO>
        implements VisualProduceJobManager, CommonService {
    @Autowired
    private OperatorUtil operatorUtil;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private VisualTemplateManager visualTemplateManager;
    @Autowired
    private VisualAiJobManager visualAiJobManager;
    @Autowired
    private VisualProduceBatchManager visualProduceBatchManager;
    @Resource
    private VisualProduceJobExtendManager visualProduceJobExtendManager;
    @Resource
    private DlChannels dlChannels;
    @Resource
    private TenantInfoManager tenantInfoManager;
    @Resource
    private DataProductManager dataProductManager;
    @Resource
    private ProduceFailProducer produceFailProducer;
    @Resource
    private ProduceJobCreatedProducer produceJobCreatedProducer;
    @Resource
    private TenantUserBindManager tenantUserBindManager;
    @Resource
    private TransactionProxyManager transactionProxyManager;
    @Resource
    private GeneratePreviewDataProducer generatePreviewDataProducer;
    @Resource
    private CosFileUploadManager cosFileUploadManager;
    @Resource
    private TtsThreadPoolConfig ttsThreadPoolConfig;
    @Resource
    private ProduceJobSubordinateTtsSuccessProducer produceJobSubordinateTtsSuccessProducer;
    @Resource
    private AiServiceClient aiServiceClient;

    @Value("${visual.fileTempPath}")
    public String LOCAL_PATH_PREFIX;

    public static final String TTS_COS_PATH = "temp/visual/tts";

    private ExecutorService jobPool = new ThreadPoolExecutor(1, 1, 30, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(512));

    private OkHttpClient httpClient = new OkHttpClient();

    private static final String START_ALARM_MSG =
            "{\n" + "\t\"msg_type\": \"post\",\n" + "\t\"content\": {\n" + "\t\t\"post\": {\n" + "\t\t\t\"zh_cn\": {\n"
                    + "\t\t\t\t\"title\": \"视频合成发起提示\",\n" + "\t\t\t\t\"content\": [\n" + "\t\t\t\t\t[{\n"
                    + "\t\t\t\t\t\t\t\"tag\": \"text\",\n"
                    + "\t\t\t\t\t\t\t\"text\": \"租户:【${tenantName}${tenantCode}】, jobId:【${jobId}】, jobName:【${jobName}】, 发起人:【${creatorName}】, 【${createDt}】发起视频合成 \"\n"
                    + "\t\t\t\t\t\t}\n" + "\t\t\t\t\t]\n" + "\t\t\t\t]\n" + "\t\t\t}\n" + "\t\t}\n" + "\t}\n" + "}";

    @Override
    public ResponsePageQueryDO<List<VisualProduceJobDTO>> pageQuery(ProduceJobSearchBO bo) {
        ResponsePageQueryDO<List<VisualProduceJobDTO>> response = new ResponsePageQueryDO<>();
        //查询user表全部字段
        MPJLambdaWrapper<VisualProduceJobPO> wrapper = new MPJLambdaWrapper<VisualProduceJobPO>()
                .selectAll(VisualProduceJobPO.class)
                .selectIgnore(VisualProduceJobPO::getReplaceData, VisualProduceJobPO::getPreviewData,
                        VisualProduceJobPO::getTemplateData)
                .select(VisualTemplatePO::getResolutionType, VisualTemplatePO::getResolution,
                        VisualTemplatePO::getName)//查询visual_template的resolutionType 字段
                .select(VisualProduceJobExtendPO::getRecommendState, VisualProduceJobExtendPO::getRecommendEnableDt,
                        VisualProduceJobExtendPO::getShareConfState, VisualProduceJobExtendPO::getOtherFormatVideoUrl,
                        VisualProduceJobExtendPO::getFailReason,
                        VisualProduceJobExtendPO::getType)//查询visual_produce_job_extend的recommendState 字段
                .selectAs(VisualTemplatePO::getResolutionType, VisualProduceJoBJoinTemplateDTO::getResolutionType)
                .selectAs(VisualTemplatePO::getName, VisualProduceJoBJoinTemplateDTO::getTemplateName)
                .selectAs(VisualTemplatePO::getResolution, VisualProduceJoBJoinTemplateDTO::getResolution)
                .leftJoin(VisualTemplatePO.class, VisualTemplatePO::getTemplateId, VisualProduceJobPO::getTemplateId)
                .leftJoin(VisualProduceJobExtendPO.class, VisualProduceJobExtendPO::getProduceJobId,
                        VisualProduceJobPO::getJobId)
                .eq(Objects.nonNull(bo.getResolutionType()), VisualTemplatePO::getResolutionType,
                        bo.getResolutionType())
                .like(StringUtils.isNotBlank(bo.getName()), VisualProduceJobPO::getName, bo.getName())
                .in(CollectionUtils.isNotEmpty(bo.getStatusList()), VisualProduceJobPO::getStatus, bo.getStatusList())
                .eq(!bo.getEncludeDeleted(), VisualProduceJobPO::getIsDeleted, Const.ZERO)
                .eq(Objects.nonNull(bo.getBatchId()), VisualProduceJobPO::getBatchId, bo.getBatchId())
                .eq(StringUtils.isNotBlank(bo.getTenantCode()), VisualProduceJobPO::getTenantCode, bo.getTenantCode())
                .eq(Objects.nonNull(bo.getCreatorId()), VisualProduceJobPO::getCreateBy, bo.getCreatorId())
                .eq(Objects.nonNull(bo.getType()), VisualProduceJobExtendPO::getType, bo.getType());
        //推荐可用状态
        if (Objects.nonNull(bo.getRecommendState())) {
            if (bo.getRecommendState().equals(Const.ONE)) {
                wrapper.eq(VisualProduceJobExtendPO::getRecommendState, Const.ONE);
            }
            if (bo.getRecommendState().equals(Const.ZERO)) {
                wrapper.and(i -> i.eq(VisualProduceJobExtendPO::getRecommendState, Const.ZERO)
                        .or(j -> j.isNull(VisualProduceJobExtendPO::getRecommendState)));
            }
        }

        //排序处理
        if (Objects.equals(bo.getSortType(), Const.ONE)) {
            wrapper.orderByAsc(VisualProduceJobPO::getCompleteDt);
        } else if (Objects.equals(bo.getSortType(), Const.TWO)) {
            wrapper.orderByDesc(VisualProduceJobPO::getCompleteDt);
        } else {
            wrapper.orderByDesc(VisualProduceJobPO::getCreateDt);
        }
        //时间过滤
        if (Objects.nonNull(bo.getStartTime()) && Objects.nonNull(bo.getEndTime())) {
            wrapper.apply("COALESCE(t.complete_dt, t.modify_dt) between {0} and {1}", new Date(bo.getStartTime()),
                    new Date(bo.getEndTime()));
        } else if (Objects.nonNull(bo.getStartTime())) {
            wrapper.apply("COALESCE(t.complete_dt, t.modify_dt) >= {0}", new Date(bo.getStartTime()));
        } else if (Objects.nonNull(bo.getEndTime())) {
            wrapper.apply("COALESCE(t.complete_dt, t.modify_dt) <= {0}", new Date(bo.getEndTime()));
        }
        IPage<VisualProduceJoBJoinTemplateDTO> pageResult = baseMapper.selectJoinPage(convert(bo), VisualProduceJoBJoinTemplateDTO.class, wrapper);
        List<VisualProduceJoBJoinTemplateDTO> data = pageResult.getRecords();
        log.debug("baseMapper.pageQuery result={}", JsonUtils.toJSON(pageResult));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        List<VisualProduceJobDTO> dtos = data.stream().map(po -> {
            VisualProduceJobDTO dto = new VisualProduceJobDTO();
            dto.setTemplateId(po.getTemplateId());
            dto.setName(po.getName());
            dto.setStatus(po.getStatus());
            dto.setCoverUrl(po.getCoverUrl());
            dto.setJobId(po.getJobId());
            dto.setVideoUrl(po.getVideoUrl());
            dto.setCreateDt(po.getCreateDt());
            dto.setDuration(po.getDuration());
            dto.setResolutionType(po.getResolutionType());
            dto.setCompleteDt(po.getCompleteDt());
            dto.setProcessDt(po.getProcessDt());
            dto.setSize(po.getSize());
            dto.setResolution(po.getResolution());
            dto.setCreatorName(po.getCreatorName());
            dto.setShareConfState(Objects.isNull(po.getShareConfState()) ? Const.ZERO : po.getShareConfState());
            dto.setRecommendState(Objects.isNull(po.getRecommendState()) ? Const.ZERO : po.getRecommendState());
            dto.setTemplateName(po.getTemplateName());
            dto.setPushStatus(po.getPushStatus());
            dto.setOtherFormatVideoUrl(po.getOtherFormatVideoUrl());
            dto.setFailReason(po.getFailReason());
            dto.setType(po.getType());
            return dto;
        }).collect(Collectors.toList());
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        response.setDataResult(dtos);
        return response;
    }

    @Override
    public ResponsePageQueryDO<List<VisualProduceJobDTO>> pageQuery(String tenantCode) {
        ResponsePageQueryDO<List<VisualProduceJobDTO>> response = new ResponsePageQueryDO<>();
        MPJLambdaWrapper<VisualProduceJobPO> wrapper = new MPJLambdaWrapper<VisualProduceJobPO>()
                .selectAll(VisualProduceJobPO.class)
                .selectIgnore(VisualProduceJobPO::getReplaceData, VisualProduceJobPO::getPreviewData,
                        VisualProduceJobPO::getTemplateData)
                .eq(StringUtils.isNotBlank(tenantCode), VisualProduceJobPO::getTenantCode, tenantCode);
        wrapper.orderByDesc(VisualProduceJobPO::getCreateDt);
        IPage<VisualProduceJoBJoinTemplateDTO> pageResult = baseMapper
                .selectJoinPage(convert(new AbstractPageParam()), VisualProduceJoBJoinTemplateDTO.class, wrapper);
        List<VisualProduceJoBJoinTemplateDTO> data = pageResult.getRecords();
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }
        List<VisualProduceJobDTO> dtos = new ArrayList<>();
        dtos.addAll(data.stream().map(po -> {
            VisualProduceJobDTO dto = new VisualProduceJobDTO();
            dto.setTemplateId(po.getTemplateId());
            dto.setName(po.getName());
            dto.setStatus(po.getStatus());
            dto.setCoverUrl(po.getCoverUrl());
            dto.setJobId(po.getJobId());
            dto.setVideoUrl(po.getVideoUrl());
            dto.setCreateDt(po.getCreateDt());
            dto.setDuration(po.getDuration());
            dto.setResolutionType(po.getResolutionType());
            dto.setProcessDt(po.getProcessDt());
            dto.setCompleteDt(po.getCompleteDt());
            dto.setSize(po.getSize());
            dto.setResolution(po.getResolution());
            dto.setCreatorName(po.getCreatorName());
            return dto;
        }).collect(Collectors.toList()));
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        response.setDataResult(dtos);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long doProduce(ProduceJobContextBO bo) {
        Assert.notNull(bo.getTemplateId(), "模板id不可为空。");
        VisualTemplateDTO templateDTO = visualTemplateManager.detail(bo.getTemplateId(), operatorUtil.getTenantCode());
        Assert.notNull(templateDTO, "模板不存在。");
        Assert.notEmpty(templateDTO.getCards(), "模板数据不可为空");
        //1.创建批次
        VisualProduceBatchPO produceBatch = new VisualProduceBatchPO();
        produceBatch.setBatchId(bo.getBatchId());
        produceBatch.setJobNumTotal(1);
        produceBatch.setJobNumSuccess(0);
        produceBatch.setStatus(BatchStatusE.QUEUED.getCode());
        produceBatch.setBatchName(templateDTO.getName() + System.currentTimeMillis());
        produceBatch.setTenantCode(operatorUtil.getTenantCode());
        produceBatch.setTenantName(operatorUtil.getTenantName());
        produceBatch.setCreatorName(operatorUtil.getUserName());
        produceBatch.setPlanId(bo.getPlanId());
        visualProduceBatchManager.save(produceBatch);

        //2.创建任务
        Long jobId = hostTimeIdg.generateId().longValue();
        VisualProduceJobPO produceJob = new VisualProduceJobPO();
        produceJob.setJobId(jobId);
        produceJob.setBatchId(bo.getBatchId());
        produceJob.setTemplateId(bo.getTemplateId());
        if (StringUtils.isNotBlank(bo.getTemplateName())) {
            produceJob.setName(bo.getTemplateName());
            templateDTO.setName(bo.getTemplateName());
        } else {
            produceJob.setName(templateDTO.getName());
        }
        produceJob.setStatus(JobStatusE.INIT.getCode());
        produceJob.setReplaceData(mergeReplaceData(bo.getReplaceData(), templateDTO.getReplaceData()));
        if (StringUtils.isNotBlank(bo.getApiData())) {
            produceJob.setApiData(bo.getApiData());
        }
        //替换前端参数和节点enable属性
        replaceFrontParameters(bo, templateDTO);

        produceJob.setTemplateData(JSONUtil.toJsonStr(templateDTO));
        produceJob.setTenantCode(operatorUtil.getTenantCode());
        produceJob.setTenantName(operatorUtil.getTenantName());
        produceJob.setSource(bo.getSource());
        produceJob.setCoverUrl(bo.getJobCoverUrl());
        produceJob.setCreatorName(operatorUtil.getUserName());
        this.save(produceJob);

        //3.保存扩展信息表
        VisualProduceJobExtendBO extendBO = new VisualProduceJobExtendBO();
        extendBO.setBizId(jobId);
        //判断数字人合成模式
        Integer dmProduceMode = this
                .judgeDmProduceMode(bo.getDmProduceMode(), bo.getDigitalManParamBO(), templateDTO.getCards());

        extendBO.setDmProduceMode(dmProduceMode);
        extendBO.setExtUserId(bo.getExtUserId());
        extendBO.setType(this.calJobType(templateDTO.getType(), bo.getSource()));
        extendBO.setResolution(
                StringUtils.isNotBlank(bo.getResolution()) ? bo.getResolution() : templateDTO.getResolution());
        visualProduceJobExtendManager.saveOrUpdateExtend(extendBO);

        Long userId = operatorUtil.getOperator();
        String userName = operatorUtil.getUserName();
        String tenantCode = operatorUtil.getTenantCode();
        String tenantName = operatorUtil.getTenantName();
        //4.发送视频创建的消息
        produceJobCreatedProducer.sendProduceJobCreatedMsg(produceJob);
        //发送告警
        this.alarm(produceJob);
        //5.异步提交tts、数字人任务
        jobPool.submit(RunnableWrapper.of(() -> {
            try {
                operatorUtil.init(userId, userName, tenantCode, tenantName);
                this.submitTtsAndDmJob(produceJob, bo.getTtsParam(), dmProduceMode, bo.getDigitalManParamBO());
                produceJob.setStatus(JobStatusE.READY.getCode());

                this.update(Wrappers.lambdaUpdate(VisualProduceJobPO.class)
                        .eq(VisualProduceJobPO::getId, produceJob.getId())
                        .set(VisualProduceJobPO::getStatus, produceJob.getStatus())
                        .set(VisualProduceJobPO::getDigitalManType, produceJob.getDigitalManType())
                        .set(VisualProduceJobPO::getModifyDt, new Date()));
                log.info("=========update produceJob status to ready success,,,jobId:{},jobStatus:{},job.id:{}",
                        produceJob.getJobId(), produceJob.getStatus(), produceJob.getId());
            } catch (Throwable e) {
                log.error("合成tts和数字人发生异常！jobId:{},e:{}", jobId, e.getMessage(), e);
                produceJob.setStatus(JobStatusE.FAILED.getCode());
                updateById(produceJob);

                produceBatch.setStatus(BatchStatusE.FAILED.getCode());
                visualProduceBatchManager.updateById(produceBatch);

                //发送视频合成失败的mq
                produceFailProducer
                        .sendProduceFailMsg(produceJob.getJobId(), produceJob.getTenantCode(), e.getMessage());
            }
        }));
        log.info("=========update produceJob status to ready success,,,jobId:{}", produceJob.getJobId());
        return jobId;
    }

    /**
     * 告警
     *
     */
    private void alarm(VisualProduceJobPO jobPO) {
        if (!Const.DEFAULT_TENANT_CODE.equals(jobPO.getTenantCode())){
            return;
        }
        Properties properties = new Properties();
        properties.put("tenantName", jobPO.getTenantName());
        properties.put("tenantCode", jobPO.getTenantCode());
        properties.put("jobId", jobPO.getJobId() + "");
        properties.put("jobName", jobPO.getName());
        properties.put("creatorName", jobPO.getCreatorName());
        properties.put("createDt", com.dl.magicvideo.biz.common.util.DateUtil.format(jobPO.getCreateDt(), com.dl.magicvideo.biz.common.util.DateUtil.Y_M_D_H_M_S));

        String noticeMsg = PlaceHolderUtils.resolveValue(START_ALARM_MSG, properties);

        log.info("jobId:{},,,noticeMsg:{}", jobPO.getJobId(), noticeMsg);
    }

    private static void replaceFrontParameters(ProduceJobContextBO bo, VisualTemplateDTO templateDTO) {
        //将模板中lightEditConfig替换为提交作品时的lightEditConfig
        if (Objects.nonNull(bo.getLightEditConfigs())) {
            if (VisualProduceJobSourceEnum.OPEN_PRODUCE.getCode().equals(bo.getSource())){
                //前端传入数字人或者TTS的内容，替换模板中的数字人或者TTS内容
                replaceTtsDmTextWithOpenApi(bo.getLightEditConfigs(), templateDTO);
            } else {
                replaceTtsDmText(bo.getLightEditConfigs(), templateDTO);
            }
        }
        //以前端传入randerData为准
        if (StringUtils.isNotBlank(bo.getRenderData())) {
            templateDTO.getCards().forEach(card -> card.setRenderData(bo.getRenderData()));
        }
        //以前端传入dynamicNodes为准
        if (CollectionUtils.isNotEmpty(bo.getDynamicNodes())) {
            handelDynamicNode(bo.getDynamicNodes(), bo.getApiData());
            templateDTO.getCards().forEach(card -> card.setDynamicNodes(bo.getDynamicNodes()));
        }
    }

    /**
     * 程序设置enable
     * @param dynamicNodeDTOList
     * @param apiData
     */
    private static void handelDynamicNode(List<DynamicNodeDTO> dynamicNodeDTOList, String apiData){
        if (CollectionUtils.isEmpty(dynamicNodeDTOList)){
            return;
        }
        dynamicNodeDTOList.forEach(e->{
            //片段是打开的，才会进入逻辑
            if (Const.ONE.equals(e.getIsEnabled()) && Const.ONE.equals(e.getExpressionFlag())
                    && StringUtils.isNotBlank(e.getExpression())){
                boolean evaluate = MagicExpressionUtils.evaluate(apiData, e.getExpression());
                e.setIsEnabled(evaluate ? Const.ONE : Const.ZERO);
            }

            if (CollectionUtils.isNotEmpty(e.getDataSheetList())){
                //数据图表设置
                List<DynamicNodeDTO.DataSheetDTO> dataSheetList = e.getDataSheetList();
                dataSheetList.forEach(dataSheet -> {
                    String content = dataSheet.getContent();
                    DataSheetContentDTO dataSheetContentDTO = JsonUtils.fromJSON(content, DataSheetContentDTO.class);
                    if (dataSheetContentDTO.isDynamicDuration()) {
                        //数据数量
                        Integer dataSheetSize = BindKeyUtils.getJsonArryaSize(apiData, dataSheetContentDTO.getBindKey());
                        //公式：scrollStartDelay+ scrollEndDelay + ceil（数据数量/pageSize) * pageScrollTime  + animationTime
                        Long duration = (long) Math.ceil((double) dataSheetSize / dataSheetContentDTO.getPageSize()) * dataSheetContentDTO.getPageScrollTime() + dataSheetContentDTO.getAnimationTime();
                        dataSheet.setDuration(duration);
                    }
                });
            }
        });
    }

    private static void replaceTtsDmText(TemplateLightEditConfigDTO lightEditConfigs, VisualTemplateDTO templateDTO) {
        //前端传入内容Map
        HashMap<String, LightEditConfigDTO> clipIdConfigMap = buildLightEditConfigMap(lightEditConfigs);
        templateDTO.getCards().forEach(card -> {
            card.setLightEditConfigs(JSONUtil.toJsonStr(lightEditConfigs));
            card.getDynamicNodes().forEach(dynamicNode -> {
                //前端传入数字人或者TTS的内容，替换模板中的数字人或者TTS内容
                replaceDmTtsContent(clipIdConfigMap, dynamicNode);
            });
        });
    }

    private static void replaceTtsDmTextWithOpenApi(TemplateLightEditConfigDTO lightEditConfigs, VisualTemplateDTO templateDTO) {
        //过滤不展示的node节点
        Set<String> notShowNode = lightEditConfigs.getLightEditConfigs().stream()
                .filter(e->Objects.equals(e.getEnable(), Const.ZERO)).map(LightEditConfigDTO::getClipId).collect(Collectors.toSet());
        Set<String> showNode = lightEditConfigs.getLightEditConfigs().stream()
                .filter(e->Objects.equals(e.getEnable(), Const.ONE)).map(LightEditConfigDTO::getClipId).collect(Collectors.toSet());
        //前端传入内容Map
        HashMap<String, LightEditConfigDTO> clipIdConfigMap = buildLightEditConfigMap(lightEditConfigs);
        templateDTO.getCards().forEach(card -> {
            card.setLightEditConfigs(JSONUtil.toJsonStr(lightEditConfigs));
            card.getDynamicNodes().forEach(dynamicNode -> {
                if (CollectionUtils.isNotEmpty(notShowNode) && notShowNode.contains(dynamicNode.getType())){
                    dynamicNode.setIsEnabled(Const.ZERO);
                } if (CollectionUtils.isNotEmpty(showNode) && showNode.contains(dynamicNode.getType())){
                    dynamicNode.setIsEnabled(Const.ONE);
                    replaceDmTtsContent(clipIdConfigMap, dynamicNode);
                } else {
                    //前端传入数字人或者TTS的内容，替换模板中的数字人或者TTS内容
                    replaceDmTtsContent(clipIdConfigMap, dynamicNode);
                }
            });
        });
    }

    @Override
    public List<StatisticsJobCountPO> statistics(StatisticsJobCountBO statisticsJobCountBO) {
        return this.baseMapper.statistics(statisticsJobCountBO);
    }

    @Override
    public StatisticsJobEfficiencyPO efficiency(StatisticsJobEfficiencyBO statisticsJobEfficiencyBO) {
        return this.baseMapper.efficiency(statisticsJobEfficiencyBO);
    }

    @Override
    public Long produce(ProduceJobBO produceJobBO) {
        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfoFromCache(operatorUtil.getTenantCode());
        //查询外部用户id
        String extUserId = this.queryExtUserId(produceJobBO.getExtUserId(), tenantInfo);

        Long batchId = hostTimeIdg.generateId().longValue();
        String apiData = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(produceJobBO.getApiData())) {
            List<InterfaceDTO> interfaceList = JSONUtil
                    .toBean(produceJobBO.getApiData(), new TypeReference<List<InterfaceDTO>>() {
                    }, false);
            apiData = suppleApiData(interfaceList);
        }

        AtomicReference<Long> jobIdRef = new AtomicReference<>();
        //构建视频合成上下文
        ProduceJobContextBO context = ProduceJobHelper
                .buildProduceJobContext(produceJobBO, tenantInfo, extUserId, batchId, apiData);
        transactionProxyManager.process(() -> jobIdRef.set(this.doProduce(context)));
        return jobIdRef.get();
    }

    private void submitTtsAndDmJob(VisualProduceJobPO produceJob, TtsParamBO ttsParam, Integer dmProduceMode,
            DigitalManParamBO digitalManParamBO) {
        VisualTemplateDTO visualTemplateDTO = JSONUtil.toBean(produceJob.getTemplateData(), VisualTemplateDTO.class);

        if (CollectionUtils.isEmpty(visualTemplateDTO.getCards())) {
            //发送生成预览数据的消息
            generatePreviewDataProducer.sendGeneratePreviewDataMsg(produceJob.getJobId());
            return;
        }
        Map<String, Object> ttsConfigMap = JsonUtils.getMap4Json(visualTemplateDTO.getTtsParam());
        String voiceName = getVoiceName(ttsConfigMap, ttsParam);
        Integer ttsChannel = getTtsChannel(ttsConfigMap, ttsParam);
        String ttsVolume = getVolume(ttsConfigMap, ttsParam);
        String ttsSpeed = getSpeed(ttsConfigMap, ttsParam);
        String ttsPitch = getPitch(ttsConfigMap, ttsParam);
        String finalVoiceName = this.specialReplaceVoiceKey(voiceName, ttsChannel);
        Integer finalTtsChannel = this.specialReplaceVoiceChannel(voiceName, ttsChannel);

        Long userId = operatorUtil.getOperator();
        //获取tts的线程池
        ExecutorService ttsThreadPool = ttsThreadPoolConfig.getTtsThreadPool(ttsChannel);

        //数字人配置列表
        List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList = new ArrayList<>();
        List<Future<VisualAiJobPO>> ttsJobs = new ArrayList<>();
        for (VisualCardDTO visualCardDTO : visualTemplateDTO.getCards()) {
            if (CollectionUtils.isEmpty(visualCardDTO.getDynamicNodes())) {
                continue;
            }

            for (DynamicNodeDTO dynamicNodeDTO : visualCardDTO.getDynamicNodes()) {
                if (Objects.equals(dynamicNodeDTO.getIsEnabled(), Const.ZERO)) {
                    continue;
                }
                //提取数字人配置列表
                if (CollectionUtils.isNotEmpty(dynamicNodeDTO.getDmList())) {
                    dmConfigDTOList.addAll(dynamicNodeDTO.getDmList().stream()
                            .filter(dmConfigDTO -> BooleanUtils.isFalse(dmConfigDTO.isHide()))
                            .collect(Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(dynamicNodeDTO.getTtsList())) {
                    for (DynamicNodeDTO.TtsConfigDTO ttsConfigDTO : dynamicNodeDTO.getTtsList()) {
                        if (BooleanUtils.isTrue(ttsConfigDTO.isHide())) {
                            continue;
                        }
                        ttsJobs.add(ttsThreadPool.submit(CallableWrapper
                                .of(() -> submitTts(produceJob, digitalManParamBO, visualTemplateDTO, finalVoiceName,
                                        finalTtsChannel, ttsVolume, ttsSpeed, userId, ttsConfigDTO, ttsPitch))));
                    }
                }
            }
        }

        List<VisualAiJobPO> ttsJobList = new ArrayList<>();
        for (Future<VisualAiJobPO> ttsJob : ttsJobs) {
            try {
                ttsJobList.add(ttsJob.get());
            } catch (ExecutionException e) {
                Throwable throwable = e.getCause();
                log.error("tts语音合成失败!produceJobId:{},,,e:", produceJob.getJobId(), e);
                if (throwable instanceof BusinessServiceException) {
                    throw (BusinessServiceException) throwable;
                }
                throw BusinessServiceException.getInstance("语音合成失败");
            } catch (Exception e) {
                log.error("语音合成失败!produceJobId:{},,,e:{}", produceJob.getJobId(), e.getMessage(), e);
                throw BusinessServiceException.getInstance("语音合成失败");
            }
        }

        //不存在数字人片段，则更新任务表并返回
        if (CollectionUtils.isEmpty(dmConfigDTOList)) {
            //该任务里没有数字人信息
            produceJob.setDigitalManType(Const.ZERO);
            //发送生成预览数据的消息
            generatePreviewDataProducer.sendGeneratePreviewDataMsg(produceJob.getJobId());

            //发送视频任务下属的tts合成成功的消息
            produceJobSubordinateTtsSuccessProducer
                    .sendTtsSuccessMsg(produceJob.getJobId(), produceJob.getTemplateId(), ttsJobList);
            return;
        }

        Integer dmChannel;
        String sceneId;
        String voiceCode;
        Double speed = null;
        if (Objects.nonNull(digitalManParamBO)) {
            dmChannel = digitalManParamBO.getChannel();
            sceneId = digitalManParamBO.getSceneId();
            voiceCode = digitalManParamBO.getVoiceCode();
            speed = digitalManParamBO.getSpeed();
        } else {
            //暂时只支持同一个数字人，故取第0个
            DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO = dmConfigDTOList.get(0);
            Map<String, Object> dmContentMap = JsonUtils.getMap4Json(dmConfigDTO.getContent());
            dmChannel = Integer.valueOf(dmContentMap.get("channel").toString());
            sceneId = dmContentMap.get("sceneId").toString();
            voiceCode = dmContentMap.get("voiceCode").toString();
            if (Objects.nonNull(dmContentMap.get("speed"))) {
                speed = Double.valueOf(dmContentMap.get("speed").toString());
            }
        }
        //数字人声音厂商
        Integer dmVoiceChannel = this
                .getVmVoiceChannel(digitalManParamBO, dmConfigDTOList.get(0), sceneId, voiceCode, dmChannel);
        String finalVoiceCode = this.specialReplaceVoiceKey(voiceCode, dmVoiceChannel);
        dmVoiceChannel = this.specialReplaceVoiceChannel(voiceCode, dmVoiceChannel);

        //合并提交数字人任务
        if (DmProduceModeEnum.MERGE_GEN_DM_THEN_ASR.getMode().equals(dmProduceMode)) {
            this.mergeSubmitDmJob(dmConfigDTOList, visualTemplateDTO.getTemplateId(), produceJob.getJobId(),
                    produceJob.getReplaceData(), digitalManParamBO, produceJob.getApiData(), dmProduceMode, dmChannel,
                    sceneId, finalVoiceCode, speed, dmVoiceChannel);
            return;
        }

        //分别提交数字人任务
        if (DmProduceModeEnum.SEPARATE_GEN_DM.getMode().equals(dmProduceMode)) {
            this.separateSubmitDmJob(dmConfigDTOList, visualTemplateDTO.getTemplateId(), produceJob.getJobId(),
                    produceJob.getReplaceData(), digitalManParamBO, produceJob.getApiData(), dmProduceMode, dmChannel,
                    sceneId, finalVoiceCode, speed, dmVoiceChannel);
            return;
        }

        //使用音频合并提交数字人任务
        if (DmProduceModeEnum.MERGE_GEN_DM_WITH_TTS.getMode().equals(dmProduceMode)) {
            this.mergeSubmitDmJobWithTTS(dmConfigDTOList, visualTemplateDTO.getTemplateId(), produceJob.getJobId(),
                    produceJob.getReplaceData(), digitalManParamBO, produceJob.getApiData(), dmProduceMode, ttsJobList,
                    dmChannel, sceneId, finalVoiceCode, speed, dmVoiceChannel);
        }

        //发送视频任务下属的tts合成成功的消息
        produceJobSubordinateTtsSuccessProducer
                .sendTtsSuccessMsg(produceJob.getJobId(), produceJob.getTemplateId(), ttsJobList);
    }

    private VisualAiJobPO submitTts(VisualProduceJobPO produceJob, DigitalManParamBO digitalManParamBO,
            VisualTemplateDTO visualTemplateDTO, String voiceName, Integer channel, String volume, String speed,
            Long userId, DynamicNodeDTO.TtsConfigDTO ttsConfigDTO, String pitch) {
        operatorUtil.init(userId, produceJob.getCreatorName(), produceJob.getTenantCode(), produceJob.getTenantName());
        Map<String, Object> ttsContentMap = JsonUtils.getMap4Json(ttsConfigDTO.getContent());
        TtsJobBO ttsJobBO = new TtsJobBO();
        ttsJobBO.setProduceJobId(produceJob.getJobId());
        ttsJobBO.setConfigId(ttsConfigDTO.getTtsId());
        ttsJobBO.setVoiceName(voiceName);
        ttsJobBO.setText(
                VisualProduceJobUtil.getText(ttsContentMap, produceJob.getReplaceData(), produceJob.getApiData()));
        ttsJobBO.setTemplateId(visualTemplateDTO.getTemplateId());
        ttsJobBO.setChannel(channel);
        ttsJobBO.setEnableSubtitle(ttsConfigDTO.getEnableSubtitle());
        ttsJobBO.setMaxLength(ttsConfigDTO.getMaxLength());
        ttsJobBO.setVolume(volume);
        ttsJobBO.setSpeed(speed);
        ttsJobBO.setUserId(userId);
        ttsJobBO.setPitch(pitch);
        ttsJobBO.setAiJobType(AiJobTypeE.TTS.getCode());
        ttsJobBO.setSubtitleKeyWordsHighlight(
                Const.ONE.equals(ttsConfigDTO.getSubtitleKeyWordsHighlight()) ? Const.ONE : Const.ZERO);
        if (Objects.nonNull(digitalManParamBO)) {
            //传入数字人语音，以数字人的为准
            ttsJobBO.setChannel(digitalManParamBO.getChannel());
            ttsJobBO.setVoiceName(digitalManParamBO.getVoiceCode());
            ttsJobBO.setSpeed(digitalManParamBO.getSpeed().toString());
        }

        return visualAiJobManager.submitTtsJob(ttsJobBO);
    }

    private static HashMap<String, LightEditConfigDTO> buildLightEditConfigMap(
            TemplateLightEditConfigDTO templateLightEditConfigDTO) {
        HashMap<String, LightEditConfigDTO> clipIdConfigMap = Maps.newHashMap();
        templateLightEditConfigDTO.getLightEditConfigs().forEach(config -> {
            List<TemplateComponentsDTO> componentsDTOList = config.getComponents().stream()
                    .filter(i -> i.getType().equals(TemplateLightEditConfigEnum.ANCHOR_CONTENT.getDesc()) || i.getType()
                            .equals(TemplateLightEditConfigEnum.TTS_CONTENT.getDesc())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(componentsDTOList)) {
                //存在TTS或者数字人
                clipIdConfigMap.put(config.getClipId(), config);
            }
        });
        return clipIdConfigMap;
    }
    private void sendSuccessMsgQueue(VisualProduceJobPO input){
        try {
            log.info("准备进行发送生产job消息，input = {}",JSONUtil.toJsonStr(input));
            VisualProduceJobMsgDTO target = new VisualProduceJobMsgDTO();
            BeanUtils.copyProperties(input, target, "replaceData", "templateData", "previewData");
            Message message = MessageBuilder.withPayload(target).build();
            boolean resp = dlChannels.visualproduceJobready().send(message, 1000L);
            log.info("发送生产job消息,message:{},resp:{}", JSONUtil.toJsonStr(message),resp);
        }catch (Exception e){
            log.error("发送生产job信息发送异常,msgDTO:" + JSONUtil.toJsonStr(input), e);
        }
    }


    private static void replaceDmTtsContent(HashMap<String, LightEditConfigDTO> clipIdConfigMap, DynamicNodeDTO dynamicNodeDTO) {
        if (Objects.nonNull(clipIdConfigMap.get(dynamicNodeDTO.getType()))) {
            LightEditConfigDTO templateLightDTO = clipIdConfigMap.get(dynamicNodeDTO.getType());
            Map<String, TemplateComponentsDTO> componentsMap = templateLightDTO.getComponents().stream().collect(Collectors.toMap(TemplateComponentsDTO::getId, Function.identity()));
            if (CollectionUtils.isNotEmpty(dynamicNodeDTO.getDmList())) {
                dynamicNodeDTO.getDmList().forEach(dm -> {
                    if (Objects.nonNull(componentsMap.get(dm.getDmId()))) {
                        DynamicNodeDTO.DigitalManConfigDTO dmConfig = dynamicNodeDTO.getDmList().get(0);
                        Map<String, Object> dmContentMap = JsonUtils.getMap4Json(dmConfig.getContent());
                        if (Objects.nonNull(dmContentMap.get("text"))) {
                            dmContentMap.put("text", componentsMap.get(dm.getDmId()).getValue());
                            dm.setContent(JSONUtils.toJSONString(dmContentMap));
                        }
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(dynamicNodeDTO.getTtsList())) {
                dynamicNodeDTO.getTtsList().forEach(tts -> {
                    if (Objects.nonNull(componentsMap.get(tts.getTtsId()))) {
                        Map<String, Object> ttsContentMap = JsonUtils.getMap4Json(tts.getContent());
                        if (Objects.nonNull(ttsContentMap.get("text"))) {
                            ttsContentMap.put("text", componentsMap.get(tts.getTtsId()).getValue());
                            tts.setContent(JSONUtils.toJSONString(ttsContentMap));
                        }
                    }
                });
            }
        }
    }

    private String getVoiceName(Map<String, Object> ttsConfigMap, TtsParamBO ttsParam) {
        //优先使用入参，入参为空则使用模板配置的声音
        if (Objects.nonNull(ttsParam) && StringUtils.isNotBlank(ttsParam.getVoiceName())){
            return ttsParam.getVoiceName();
        } else if (MapUtils.isNotEmpty(ttsConfigMap) && Objects.nonNull(ttsConfigMap.get("per"))) {
            return ttsConfigMap.get("per").toString();
        }
        return "zhimiao_emo";
    }

    private String getVolume(Map<String, Object> ttsConfigMap, TtsParamBO ttsParam) {
        //优先使用入参
        if (Objects.nonNull(ttsParam) && StringUtils.isNotBlank(ttsParam.getVolume())){
            return ttsParam.getVolume();
        }else if (MapUtils.isNotEmpty(ttsConfigMap) && Objects.nonNull(ttsConfigMap.get("volume"))) {
            return ttsConfigMap.get("volume").toString();
        }
        return "";
    }

    private Integer getTtsChannel(Map<String, Object> ttsConfigMap, TtsParamBO ttsParam) {
        //优先使用入参
        if (Objects.nonNull(ttsParam) && Objects.nonNull(ttsParam.getChannel())){
            return ttsParam.getChannel();
        }else if (MapUtils.isNotEmpty(ttsConfigMap) && Objects.nonNull(ttsConfigMap.get("channel"))) {
            return Integer.valueOf(ttsConfigMap.get("channel").toString());
        }
        return ServiceChannelEnum.ALIYUN.getCode();
    }

    private String getPitch(Map<String, Object> ttsConfigMap, TtsParamBO ttsParam) {
        //优先使用入参
        if (Objects.nonNull(ttsParam) && StringUtils.isNotBlank(ttsParam.getPitch())) {
            return ttsParam.getPitch();
        } else if (MapUtils.isNotEmpty(ttsConfigMap) && Objects.nonNull(ttsConfigMap.get("pitch"))) {
            return ttsConfigMap.get("pitch").toString();
        }
        return "";
    }

    /**
     * 特殊替换声音编码
     *
     * @param voiceKey
     * @param voiceChannel
     */
    private String specialReplaceVoiceKey(String voiceKey, Integer voiceChannel) {
        //如果声音编码是腾讯云的张忆南，则替换声音编码和厂商。
        //腾讯云的张忆南声音一年2500，而用火山云克隆音一年只要138+12=150块，因此腾讯云张忆南声音到期后就不再续费，转而使用火山云克隆音。
        //这是因为用到张忆南的模板实在太多了，不仅我们内部，客户也在用，无法让他们一一修改模板，那么只能替换处理。
        if (ServiceChannelEnum.IVH.getCode().equals(voiceChannel) && VoiceConst.IVH_ZHANGYINAN_VOICE_KEY
                .equals(voiceKey)) {
            return VoiceConst.VOLC_ENGINE_ZHANGYINAN_VOICE_KEY;
        }
        return voiceKey;
    }

    /**
     * 特殊替换声音厂商
     *
     * @param voiceKey
     * @param voiceChannel
     */
    private Integer specialReplaceVoiceChannel(String voiceKey, Integer voiceChannel) {
        //如果声音编码是腾讯云的张忆南，则替换声音编码和厂商。
        //腾讯云的张忆南声音一年2500，而用火山云克隆音一年只要138+12=150块，因此腾讯云张忆南声音到期后就不再续费，转而使用火山云克隆音。
        //这是因为用到张忆南的模板实在太多了，不仅我们内部，客户也在用，无法让他们一一修改模板，那么只能替换处理。
        if (ServiceChannelEnum.IVH.getCode().equals(voiceChannel) && VoiceConst.IVH_ZHANGYINAN_VOICE_KEY
                .equals(voiceKey)) {
            return ServiceChannelEnum.VOLC_ENGINE.getCode();
        }
        return voiceChannel;
    }

    /**
     * 添加句号
     *
     * @param text
     * @return
     */
    private String addFullStop(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }

        return text + SymbolE.FULL_STOP.getValue();
    }

    private String mergeReplaceData(String produceRD, String templateRD) {
        if (StringUtils.isBlank(templateRD) || "{}".equals(templateRD)) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.isBlank(produceRD) || "{}".equals(produceRD)) {
            return templateRD;
        }
        Map<String, Object> templateRDMap = JsonUtils.getMap4Json(templateRD);
        Map<String, Object> produceRDMap = JsonUtils.getMap4Json(produceRD);
        for (String key : templateRDMap.keySet()) {
            Object pVal = produceRDMap.get(key);
            if (Objects.isNull(pVal)) {
                produceRDMap.put(key, templateRDMap.get(key));
                continue;
            }

            if((pVal instanceof String) && StringUtils.isBlank(pVal.toString()) && !" ".equals(pVal.toString())) {
                produceRDMap.put(key, templateRDMap.get(key));
            }
        }
        return JsonUtils.toJSON(produceRDMap);
    }

    private String getSpeed(Map<String, Object> ttsConfigMap, TtsParamBO ttsParam) {
        //优先使用入参
        if (Objects.nonNull(ttsParam) && StringUtils.isNotBlank(ttsParam.getSpeed())){
            return ttsParam.getSpeed();
        }

        String speed = "";
        if (MapUtils.isNotEmpty(ttsConfigMap) && Objects.nonNull(ttsConfigMap.get("speed"))) {
            speed = ttsConfigMap.get("speed").toString();
        }
        return speed;
    }

    /**
     * 分别提交数字人任务
     *
     * @param dmConfigDTOList
     * @param templateId
     * @param jobId
     * @param relaceData
     */
    private void separateSubmitDmJob(List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList, Long templateId,
            Long jobId, String relaceData, DigitalManParamBO digitalManParamBO, String apiData, Integer dmProduceMode,
            Integer dmChannel, String sceneId, String voiceCode, Double speed, Integer voiceChannel) {

        for (DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO : dmConfigDTOList) {
            Map<String, Object> dmContentMap = JsonUtils.getMap4Json(dmConfigDTO.getContent());
            //脚本
            String text = VisualProduceJobUtil.getText(dmContentMap, relaceData, apiData);
            //如果声音厂商和数字人厂商不一致，则使用音频驱动数字人
            if (!Objects.equals(voiceChannel, dmChannel)) {
                this.sumbitTtsAndAudioDriveDigitalMan(dmConfigDTO, templateId, jobId, dmProduceMode, dmChannel, sceneId,
                        voiceCode, speed, voiceChannel, text, true);
                continue;
            }

            //声音厂商和数字人厂商一致，则使用文本驱动数字人
            DigitalManJobBO digitalManJobBO = new DigitalManJobBO();
            digitalManJobBO.setTemplateId(templateId);
            digitalManJobBO.setProduceJobId(jobId);
            digitalManJobBO.setDmProduceMode(dmProduceMode);
            digitalManJobBO.setUserId(operatorUtil.getOperator());
            digitalManJobBO.setChannel(dmChannel);
            digitalManJobBO.setSceneId(sceneId);
            digitalManJobBO.setVoiceCode(voiceCode);
            digitalManJobBO.setSpeed(speed);
            digitalManJobBO.setDriveType(Const.ONE);
            digitalManJobBO.setText(text);
            digitalManJobBO.setConfigId(dmConfigDTO.getDmId());
            digitalManJobBO.setEnableSubtitle(dmConfigDTO.getEnableSubtitle());
            digitalManJobBO.setMaxLength(dmConfigDTO.getMaxLength());
            visualAiJobManager.submitDigitalManJob(digitalManJobBO);
        }
    }

    /**
     * 合并提交数字人任务
     *
     * @param dmConfigDTOList
     * @param templateId
     * @param jobId
     * @param relaceData
     */
    private void mergeSubmitDmJob(List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList, Long templateId, Long jobId,
            String relaceData, DigitalManParamBO digitalManParamBO, String apiData, Integer dmProduceMode,
            Integer dmChannel, String sceneId, String voiceCode, Double speed, Integer voiceChannel) {

        //所有数字人需要一起提交，生成一个视频。所以拼接脚本
        StringBuffer textSbf = new StringBuffer();
        for (int i = 0; i < dmConfigDTOList.size(); i++) {
            DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO = dmConfigDTOList.get(i);
            Map<String, Object> dmContentMap = JsonUtils.getMap4Json(dmConfigDTO.getContent());
            String text = VisualProduceJobUtil.getText(dmContentMap, relaceData, apiData);
            text = this.addFullStop(text);
            textSbf.append(text);
        }

        this.sumbitTtsAndAudioDriveDigitalMan(dmConfigDTOList.get(0), templateId, jobId, dmProduceMode, dmChannel,
                sceneId, voiceCode, speed, voiceChannel, textSbf.toString(), false);
    }

    /**
     * 提交tts任务并音频驱动数字人
     *
     * @param dmConfigDTO
     * @param templateId
     * @param jobId
     * @param dmProduceMode
     * @param dmChannel
     * @param sceneId
     * @param voiceCode
     * @param speed
     * @param voiceChannel
     * @param text
     * @return
     */
    public Long sumbitTtsAndAudioDriveDigitalMan(DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO, Long templateId,
            Long jobId, Integer dmProduceMode, Integer dmChannel, String sceneId, String voiceCode, Double speed,
            Integer voiceChannel, String text, boolean needSaveDmId) {
        //1.TTS
        TtsJobBO ttsJobBO = new TtsJobBO();
        ttsJobBO.setProduceJobId(jobId);
        ttsJobBO.setConfigId(needSaveDmId ? dmConfigDTO.getDmId() : null);
        ttsJobBO.setText(text);
        ttsJobBO.setTemplateId(templateId);
        ttsJobBO.setEnableSubtitle(dmConfigDTO.getEnableSubtitle());
        ttsJobBO.setMaxLength(dmConfigDTO.getMaxLength());
        ttsJobBO.setAiJobType(AiJobTypeE.DM_TTS.getCode());
        ttsJobBO.setUserId(operatorUtil.getOperator());
        ttsJobBO.setChannel(voiceChannel);
        ttsJobBO.setVoiceName(voiceCode);
        ttsJobBO.setSpeed(speed.toString());

        //获取tts的线程池
        ExecutorService ttsThreadPool = ttsThreadPoolConfig.getTtsThreadPool(voiceChannel);

        String userName = operatorUtil.getUserName();
        String tenantCode = operatorUtil.getTenantCode();
        String tenantName = operatorUtil.getTenantName();
        //异步提交tts任务
        Future<VisualAiJobPO> ttsJobFuture = ttsThreadPool.submit(CallableWrapper.of(() -> {
            operatorUtil.init(ttsJobBO.getUserId(), userName, tenantCode, tenantName);
            return visualAiJobManager.submitTtsJob(ttsJobBO);
        }));
        VisualAiJobPO visualAiJobPO = null;
        try {
            visualAiJobPO = ttsJobFuture.get();
        } catch (ExecutionException e) {
            Throwable throwable = e.getCause();
            log.error("音频驱动数字人。tts语音合成失败!produceJobId:{},,,e:", jobId, e);
            if (throwable instanceof BusinessServiceException) {
                throw (BusinessServiceException) throwable;
            }
            throw BusinessServiceException.getInstance("语音合成失败");
        } catch (Exception e) {
            log.error("语音合成失败!jobId:{},,,e:{}", jobId, e);
            throw BusinessServiceException.getInstance("语音合成失败");
        }

        //2.音频驱动数字人
        DigitalManJobBO digitalManJobBO = new DigitalManJobBO();
        digitalManJobBO.setConfigId(needSaveDmId ? dmConfigDTO.getDmId() : null);
        digitalManJobBO.setTemplateId(templateId);
        digitalManJobBO.setProduceJobId(jobId);
        digitalManJobBO.setDmProduceMode(dmProduceMode);
        digitalManJobBO.setUserId(operatorUtil.getOperator());
        digitalManJobBO.setDriveType(Const.ZERO);
        digitalManJobBO.setAudioUrl(visualAiJobPO.getMediaInfo());
        digitalManJobBO.setChannel(dmChannel);
        digitalManJobBO.setSceneId(sceneId);
        digitalManJobBO.setVoiceCode(voiceCode);
        digitalManJobBO.setSpeed(speed);
        digitalManJobBO.setEnableSubtitle(dmConfigDTO.getEnableSubtitle());
        digitalManJobBO.setMaxLength(dmConfigDTO.getMaxLength());
        return visualAiJobManager.submitDigitalManJob(digitalManJobBO);
    }

    /**
     * 分别使用音频驱动数字人
     *
     * @param dmConfigDTOList
     * @param templateId
     * @param jobId
     * @param relaceData
     */
    private void mergeSubmitDmJobWithTTS(List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList, Long templateId,
            Long jobId, String relaceData, DigitalManParamBO digitalManParamBO, String apiData, Integer dmProduceMode,
            List<VisualAiJobPO> ttsJobList, Integer dmChannel, String sceneId, String voiceCode, Double speed,
            Integer voiceChannel) {

        //1.分别提交tts任务
        log.info("1.提交tts任务先生成tts音频,jobId:{}", jobId);
        List<Future<VisualAiJobPO>> ttsJobFutures = new ArrayList<>();
        for (DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO : dmConfigDTOList) {
            Map<String, Object> dmContentMap = JsonUtils.getMap4Json(dmConfigDTO.getContent());
            TtsJobBO ttsJobBO = new TtsJobBO();
            ttsJobBO.setProduceJobId(jobId);
            ttsJobBO.setConfigId(dmConfigDTO.getDmId());
            ttsJobBO.setText(VisualProduceJobUtil.getText(dmContentMap, relaceData, apiData));
            ttsJobBO.setTemplateId(templateId);
            ttsJobBO.setEnableSubtitle(dmConfigDTO.getEnableSubtitle());
            ttsJobBO.setMaxLength(dmConfigDTO.getMaxLength());
            ttsJobBO.setAiJobType(AiJobTypeE.DM_TTS.getCode());
            ttsJobBO.setUserId(operatorUtil.getOperator());
            ttsJobBO.setChannel(voiceChannel);
            ttsJobBO.setVoiceName(voiceCode);
            ttsJobBO.setSpeed(speed.toString());

            //获取tts的线程池
            ExecutorService ttsThreadPool = ttsThreadPoolConfig.getTtsThreadPool(ttsJobBO.getChannel());
            String tenantCode = operatorUtil.getTenantCode();
            //异步提交tts任务
            ttsJobFutures.add(ttsThreadPool.submit(CallableWrapper.of(() -> {
                operatorUtil.init(ttsJobBO.getUserId(), "", tenantCode, "");
                return visualAiJobManager.submitTtsJob(ttsJobBO);
            })));
        }

        //数字人的tts任务列表
        List<VisualAiJobPO> dmTtsJobList = new ArrayList<>();
        for (Future<VisualAiJobPO> ttsJob : ttsJobFutures) {
            try {
                dmTtsJobList.add(ttsJob.get());
            } catch (ExecutionException e) {
                Throwable throwable = e.getCause();
                log.error("音频驱动数字人模式。tts语音合成失败!produceJobId:{},,,e:", jobId, e);
                if (throwable instanceof BusinessServiceException) {
                    throw (BusinessServiceException) throwable;
                }
                throw BusinessServiceException.getInstance("语音合成失败");
            } catch (Exception e) {
                log.error("语音合成失败!jobId:{},,,e:{}", jobId, e);
                throw BusinessServiceException.getInstance("语音合成失败");
            }
        }
        ttsJobList.addAll(dmTtsJobList);

        //2.将tts音频拼接为一个大音频
        //需要根据小音频的文件格式设置大音频的格式
        log.info("2.将tts音频拼接为一个大音频,jobId:{}", jobId);
        String audioFormat = FileUtil.extName(ttsJobList.get(0).getMediaInfo());

        //创建临时目录，存放中间产物-音频
        String dirPath = LOCAL_PATH_PREFIX + jobId;
        File tmpDir = new File(dirPath);
        tmpDir.mkdirs();

        String bigAudioPath = dirPath + "/ttsdrivedm-" + jobId + "." + audioFormat;
        try {
            this.mergeSmallAudio(dmConfigDTOList, jobId, dmTtsJobList, bigAudioPath);
        } catch (Exception e) {
            log.error("jobId:{},处理数字人的小音频合并为大音频发生异常!,e:{}", jobId, e);
            FileUtils.deleteQuietly(tmpDir);
            throw BusinessServiceException.getInstance("处理数字人的小音频合并为大音频发生异常:" + StringUtils
                    .substring(e.getMessage(), 0, Math.min(StringUtils.length(e.getMessage()), 200)));
        }

        //3.将大音频文件上传到对象存储
        log.info("3.将大音频文件上传到对象存储,jobId:{}", jobId);
        File bigAudio = new File(bigAudioPath);
        String bigAudioUrl = "";
        try {
            bigAudioUrl = cosFileUploadManager.uploadFile(bigAudio, null, TTS_COS_PATH, false, true);
        } catch (Exception e) {
            log.error("将大音频文件上传到对象存储发生异常！jobId:{},,,bigAudioPath:{},,,e:", jobId, bigAudioPath, e);
            throw BusinessServiceException.getInstance("处理数字人的小音频合并为大音频发生异常:" + StringUtils
                    .substring(e.getMessage(), 0, Math.min(StringUtils.length(e.getMessage()), 200)));
        } finally {
            //删除目录
            FileUtils.deleteQuietly(tmpDir);
        }

        if (StringUtils.isBlank(bigAudioUrl)) {
            log.error("将大音频文件上传到对象存储发生异常！jobId:{},,,bigAudioPath:{}", jobId, bigAudioPath);
            throw BusinessServiceException.getInstance("处理数字人的小音频合并为大音频发生异常:文件上传失败");
        }

        //4.构建参数，提交数字人任务
        log.info("4.提交数字人任务,jobId:{}", jobId);
        DynamicNodeDTO.DigitalManConfigDTO firstDmConfig = dmConfigDTOList.get(0);
        DigitalManJobBO digitalManJobBO = new DigitalManJobBO();
        digitalManJobBO.setTemplateId(templateId);
        digitalManJobBO.setProduceJobId(jobId);
        digitalManJobBO.setDmProduceMode(dmProduceMode);
        digitalManJobBO.setUserId(operatorUtil.getOperator());
        digitalManJobBO.setDriveType(Const.ZERO);
        digitalManJobBO.setAudioUrl(bigAudioUrl);
        digitalManJobBO.setChannel(dmChannel);
        digitalManJobBO.setSceneId(sceneId);
        digitalManJobBO.setVoiceCode(voiceCode);
        digitalManJobBO.setSpeed(speed);
        digitalManJobBO.setEnableSubtitle(firstDmConfig.getEnableSubtitle());
        digitalManJobBO.setMaxLength(firstDmConfig.getMaxLength());
        visualAiJobManager.submitDigitalManJob(digitalManJobBO);
    }

    private void mergeSmallAudio(List<DynamicNodeDTO.DigitalManConfigDTO> dmConfigDTOList, Long jobId,
            List<VisualAiJobPO> ttsJobList, String bigAudioPath) {
        //String ffmpeg = Loader.load(org.bytedeco.ffmpeg.ffmpeg.class);

        Map<String, VisualAiJobPO> ttsJobMap = ttsJobList.stream()
                .collect(Collectors.toMap(VisualAiJobPO::getConfigId, Function.identity()));

        List<String> command = new ArrayList<>();
        command.add("sudo");
        command.add("/usr/local/ffmpeg/bin/ffmpeg");
        String inputs = "";
        int index = 0;
        //在每个音频后面拼接100ms的静音片段
        int delay = 100;
        StringBuilder filter = new StringBuilder();
        for (DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO : dmConfigDTOList) {
            VisualAiJobPO ttsJob = ttsJobMap.get(dmConfigDTO.getDmId());

            String delayTempName = "[delayed" + index + "]";

            command.add("-i");
            command.add(ttsJob.getMediaInfo());

            filter.append("[" + index + ":a]").append("adelay=").append(delay).append("|").append(delay)
                    .append(delayTempName).append(";");
            inputs += delayTempName;
            index++;
        }
        filter.append(inputs).append("concat=n=").append(ttsJobList.size()).append(":v=0:a=1");

        command.add("-filter_complex");
        command.add(filter.toString());

        command.add("-c:a");
        command.add("libmp3lame");
        command.add(bigAudioPath);
        log.info("jobId:{},处理数字人的小音频合并为大音频的完整命令:{}", jobId, String.join(StringUtils.SPACE, command));

        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            process.waitFor();

            BufferedReader br1;
            br1 = new BufferedReader(new InputStreamReader(process.getInputStream(), "utf-8"));
            String line1;
            while ((line1 = br1.readLine()) != null) {
                log.info(line1);
            }
            // 关闭Process
            if (process.isAlive()) {
                process.destroy();
            }
            log.info("jobId:{},处理数字人的小音频合并为大音频成功", jobId);
        } catch (IOException | InterruptedException e) {
            log.error("jobId:{},处理数字人的小音频合并为大音频发生异常!,e:{}", jobId, e);
            throw BusinessServiceException.getInstance("处理数字人的小音频合并为大音频发生异常:" + StringUtils
                    .substring(e.getMessage(), 0, Math.min(StringUtils.length(e.getMessage()), 200)));
        }
    }

    @Override
    public String suppleApiData(List<InterfaceDTO> interfaceList) {
        if (CollectionUtils.isEmpty(interfaceList)) {
            return StringUtils.EMPTY;
        }

        try {
            JSONObjectMapper objectMapper = new JSONObjectMapper();
            // 创建一个新的 ObjectNode 用于合并结果
            ObjectNode mergedNode = objectMapper.createObjectNode();
            for (InterfaceDTO e : interfaceList) {
                String interfaceName = strValue(e.getName(), true);
                if ("/common/manager/detail".equals(interfaceName)) {
                    processManagerDetail(e, mergedNode, objectMapper);
                    continue;
                }
                if ("/common/fund/marketdetail".equals(interfaceName)) {
                    processMarketDetail(e, mergedNode, objectMapper);
                    continue;
                }
            }
            return objectMapper.writeValueAsString(mergedNode);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 补充replace
     *
     * @return
     */
    private void processManagerDetail(InterfaceDTO e, ObjectNode mergedNode, ObjectMapper objectMapper) {
        // 第一个key 接口名称，如/common/manager/detail/123
        //第二个key为接口入参code 如 23
        Map<String, Map<String, ManagerDetailVO>> dtoDetailList = new HashMap<>();
        Map<String, Object> keyMap = e.getKeyMap();
        List<String> codeList;
        String codeStr = (String) keyMap.get("code");
        if (codeStr.contains(",")) {
            String[] paramValueArr = codeStr.split(",");
            codeList = Arrays.asList(paramValueArr);
        } else {
            codeList = Arrays.asList(codeStr);
        }
        if (CollectionUtils.isNotEmpty(codeList)) {
            Map<String, ManagerDetailVO> map = new HashMap<>();
            int i = 1;
            for (String code : codeList) {
                ManagerDetailVO dto = new ManagerDetailVO();
                dto.setAge(18);
                dto.setCode(code);
                dto.setName("测试数据:" + code);
                if (i == 1) {
                    map.put("result", dto);
                } else {
                    map.put("result" + i, dto);
                }
                i++;
            }
            dtoDetailList.put(e.getName(), map);
            mergeJson(objectMapper, mergedNode, dtoDetailList);
        }
    }

    private void processMarketDetail(InterfaceDTO e, ObjectNode mergedNode, ObjectMapper objectMapper) {
        // 第一个key 接口名称，如/common/manager/detail/123
        //第二个key为接口入参code 如 23
        Map<String, Map<String, FundMarketDetailVO>> dtoDetailList = new HashMap<>();
        Map<String, Object> keyMap = e.getKeyMap();
        List<String> codeList;
        String codeStr = (String) keyMap.get("code");
        if (codeStr.contains(",")) {
            String[] paramValueArr = codeStr.split(",");
            codeList = Arrays.asList(paramValueArr);
        } else {
            codeList = Arrays.asList(codeStr);
        }

        Long time = handleDateTime(keyMap.get("time")).getTime();
        if (CollectionUtils.isNotEmpty(codeList)) {
            Map<String, FundMarketDetailVO> map = new HashMap<>();
            int i = 1;
            for (String code : codeList) {
                FundMarketDetailVO dto = new FundMarketDetailVO();
                dto.setCode(code);
                dto.setName("测试数据:" + code);
                dto.setTime(time);
                if (i == 1) {
                    map.put("result", dto);
                } else {
                    map.put("result" + i, dto);
                }
                i++;
            }
            dtoDetailList.put(e.getName(), map);
            mergeJson(objectMapper, mergedNode, dtoDetailList);
        }
    }

    private void processWeeklyReview(InterfaceDTO e, ObjectNode mergedNode, ObjectMapper objectMapper) {
        // 第一个key 接口名称，如/common/manager/detail/123
        //第二个key为接口入参code 如 23
        Map<String, Object> keyMap = e.getKeyMap();
        long time = handleDateTime(keyMap.get("time")).getTime();
        Map<String, Map<String, WeeklyReviewVO>> dtoDetailList = new HashMap<>();
        String yyyyMMdd = DateUtil.format(new Date(time), "yyyy/MM/dd");
        DataProductPO dataProductPO = dataProductManager.lambdaQuery().eq(DataProductPO::getProdCode, yyyyMMdd).one();
        if (Objects.nonNull(dataProductPO)) {
            String replaceData = dataProductPO.getReplaceData();
            Map<String, WeeklyReviewVO> map = new HashMap<>();
            WeeklyReviewDTO weeklyReviewDTO = JsonUtils.fromJSON(replaceData, WeeklyReviewDTO.class);
            if (Objects.nonNull(weeklyReviewDTO)) {
                WeeklyReviewVO vo = new WeeklyReviewVO();
                BeanUtils.copyProperties(weeklyReviewDTO, vo);
                map.put("result", vo);
                dtoDetailList.put(e.getName(), map);
                mergeJson(objectMapper, mergedNode, dtoDetailList);
            }
        }
    }


    /**
     * @param str  字符串
     * @param head true为去除/前的结果，false为/之后的结果尾部
     * @return
     */
    public String strValue(String str, boolean head) {
        if (head) {
            int lastIndex = str.lastIndexOf("/");
            return str.substring(0, lastIndex);
        } else {
            String[] parts = str.split("/");
            return parts[parts.length - 1];
        }
    }


    /**
     * 合并json
     *
     * @param object
     */
    private void mergeJson(ObjectMapper objectMapper, ObjectNode mergedNode, Object object) {
        // 解析第一个 JSON
        try {
            // 将第一个 JSON 合并到 mergedObject 中
            JsonNode node1 = objectMapper.readTree(objectMapper.writeValueAsString(object));
            mergedNode.setAll((ObjectNode) node1);
        } catch (Exception e) {
            log.error("json合并异常");
        }

    }

    /**
     * 查询外部用户id
     *
     * @param extUserId
     * @param tenantInfoDTO
     * @return
     */
    private String queryExtUserId(String extUserId, AdmTenantInfoDTO tenantInfoDTO) {
        if (StringUtils.isNotBlank(extUserId)) {
            return extUserId;
        }

        return tenantUserBindManager.queryExtUserIdByUserId(tenantInfoDTO.getTenantCode(), operatorUtil.getOperator());
    }

    /**
     * 处理日期时间
     * @param dateObj
     * @return
     */
    private static Date handleDateTime(Object dateObj) {
        if (Objects.isNull(dateObj)) {
            throw BusinessServiceException.getInstance("时间不能为空");
        }
        String dateStr = "";
        if (dateObj instanceof String) {
            dateStr = (String) dateObj;
        } else if (dateObj instanceof Long) {
            dateStr = String.valueOf(dateObj);
        }

        //如果传入的是时间戳，则返回该时间戳对应的时间
        if (isTimestamp(dateStr)) {
            return new Date(Long.parseLong(dateStr));
        }

        if (dateStr.contains("Q")) {
            return convertQuarterToDateTime(dateStr);
        } else {
            try {
                String[] arr = dateStr.split("-");
                int year = Integer.parseInt(arr[0]);
                int month = Integer.parseInt(arr[1]);
                int day = Integer.parseInt(arr[2]);
                LocalDate date = LocalDate.of(year, month, day);
                LocalTime time = LocalTime.of(0, 0, 0);

                LocalDateTime localDateTime = LocalDateTime.of(date, time);

                // 转换为 Date
                return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
            } catch (Exception e) {
                log.error("无法解析该日期,dateStr:{},e:{}", dateStr, e);
                throw BusinessServiceException.getInstance("无法解析该日期");
            }
        }
    }

    public static boolean isTimestamp(String input) {
        try {
            long timestamp = Long.parseLong(input);
            return isValidTimestamp(timestamp);
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private static boolean isValidTimestamp(long timestamp) {
        // 假设合理的时间范围为从1970年1月1日开始
        return timestamp >= 0;
    }

    public static Date convertQuarterToDateTime(String input) {
        String[] parts = input.split("-");
        int year = Integer.parseInt(parts[0]);
        String quarter = parts[1];

        int month = getEndMonthOfQuarter(quarter);
        YearMonth yearMonth = YearMonth.of(year, month);
        int lastDayOfMonth = yearMonth.lengthOfMonth();

        LocalDate date = LocalDate.of(year, month, lastDayOfMonth);
        LocalTime time = LocalTime.of(23, 59, 59);

        LocalDateTime localDateTime = LocalDateTime.of(date, time);

        // 转换为 Date
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private static int getEndMonthOfQuarter(String quarter) {
        switch (quarter) {
        case "Q1":
            return 3;
        case "Q2":
            return 6;
        case "Q3":
            return 9;
        case "Q4":
            return 12;
        default:
            throw new IllegalArgumentException("Invalid quarter: " + quarter);
        }
    }

    /**
     * 判断数字人合成模式
     *
     * @param tenantDmProduceMode
     * @param digitalManParamBO
     * @param cards
     * @return
     */
    private Integer judgeDmProduceMode(Integer tenantDmProduceMode, DigitalManParamBO digitalManParamBO,
            List<VisualCardDTO> cards) {
        if (CollectionUtils.isEmpty(cards)) {
            return tenantDmProduceMode;
        }
        //若租户的数字人合成模式是0和1 则直接返回
        if (DmProduceModeEnum.MERGE_GEN_DM_THEN_ASR.getMode().equals(tenantDmProduceMode)
                || DmProduceModeEnum.SEPARATE_GEN_DM.getMode().equals(tenantDmProduceMode)) {
            return tenantDmProduceMode;
        }

        //提取数字人的厂商
        Integer dmChannel;
        if (Objects.nonNull(digitalManParamBO)) {
            dmChannel = digitalManParamBO.getChannel();
        } else {
            VisualCardDTO cardDTO = cards.get(0);
            if (CollectionUtils.isEmpty(cardDTO.getDynamicNodes())) {
                return tenantDmProduceMode;
            }
            Optional<DigitalManConfigDTO> firstDmConfigOpt = cardDTO.getDynamicNodes().stream()
                    .filter(node -> Objects.equals(node.getIsEnabled(), Const.ONE)).map(node -> {
                        if (CollectionUtils.isEmpty(node.getDmList())) {
                            return null;
                        }
                        for (DigitalManConfigDTO dmConfig : node.getDmList()) {
                            if (BooleanUtils.isFalse(dmConfig.isHide())) {
                                return dmConfig;
                            }
                        }
                        return null;
                    }).filter(Objects::nonNull).findFirst();
            if (!firstDmConfigOpt.isPresent()) {
                return tenantDmProduceMode;
            }
            Map<String, Object> dmContentMap = JsonUtils.getMap4Json(firstDmConfigOpt.get().getContent());
            dmChannel = Integer.valueOf(dmContentMap.get("channel").toString());
        }

        //需要根据数字人厂商是否支持音频驱动来判断数字人合并合成模式。
        //腾讯云、阿里云、硅基 支持音频驱动。
        if (ServiceChannelEnum.IVH.getCode().equals(dmChannel)) {
            return tenantDmProduceMode;
        }
        if (ServiceChannelEnum.ALIYUN_DIGIITAL.getCode().equals(dmChannel)) {
            return tenantDmProduceMode;
        }
        if (ServiceChannelEnum.GUI_JI.getCode().equals(dmChannel)) {
            return tenantDmProduceMode;
        }
        if (ServiceChannelEnum.FUJIA_IVH.getCode().equals(dmChannel)) {
            return tenantDmProduceMode;
        }
        if (Objects.equals(16, dmChannel)) {
            return tenantDmProduceMode;
        }
        if (ServiceChannelEnum.CJHX_GUIJI.getCode().equals(dmChannel)) {
            return tenantDmProduceMode;
        }

        //其余厂商  使用MERGE_GEN_DM_THEN_ASR模式
        return DmProduceModeEnum.MERGE_GEN_DM_THEN_ASR.getMode();
    }

    /**
     * 根据模板类型和作品来源计算作品类型
     *
     * @param templateType
     * @param jobSource
     * @return
     */
    private Integer calJobType(Integer templateType, Integer jobSource) {
        if (TemplateTypeEnum.DATA_CHART.getType().equals(templateType)) {
            return JobTypeEnum.DATA_CHART.getType();
        }
        if (TemplateTypeEnum.NORMAL.getType().equals(templateType)) {
            if (VisualProduceJobSourceEnum.AIGC_PRODUCE_COMMON_TOOL.getCode().equals(jobSource)) {
                return JobTypeEnum.AIGC.getType();
            }
            if (VisualProduceJobSourceEnum.AIGC_PRODUCE_HOT_EVENT_SUBJECT_MATTER_TOOL.getCode().equals(jobSource)) {
                return JobTypeEnum.AIGC.getType();
            }
            return JobTypeEnum.NORMAL.getType();
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(handleDateTime("2024-Q1"));
        System.out.println(handleDateTime("2024-Q2"));
        System.out.println(handleDateTime("2024-Q3"));
        System.out.println(handleDateTime("2024-Q4"));
        System.out.println(handleDateTime("2021-Q1"));
        System.out.println(handleDateTime("2024-11-30"));
        System.out.println(handleDateTime("2024-2-29"));
        System.out.println(handleDateTime(1705889969330L));
        System.out.println(handleDateTime(1705888919000L));
    }

    /**
     * 获取数字人音频厂商
     * dmVoiceChannel是新加的字段。原本数字人的厂商和他关联的音频厂商是同一个，现在支持不同厂商了。
     * 因此需要获取数字人音频厂商。
     * 对于新增的模板，前端在保存时就会将dmVoiceChannel保存到DynamicNodeDTO.DigitalManConfigDTO的content中。
     * 对于历史模板，在没有切换数字人时，点击模板保存，前端不会保存dmVoiceChannel，但是没关系，因为这时数字人的厂商和他关联的音频厂商扔是同一个，故dmVoiceChannel取dmChannel。
     * 对于历史模板，在切换数字人后，点击模板保存，前端会将dmVoiceChannel保存到DynamicNodeDTO.DigitalManConfigDTO的content中。
     *
     * @param digitalManParamBO
     * @param dmConfigDTO
     * @param sceneId
     * @param voiceCode
     * @param dmChannel
     * @return
     */
    private Integer getVmVoiceChannel(DigitalManParamBO digitalManParamBO,
            DynamicNodeDTO.DigitalManConfigDTO dmConfigDTO, String sceneId, String voiceCode, Integer dmChannel) {
        Integer dmVoiceChannel = null;
        if (Objects.nonNull(digitalManParamBO) && Objects.nonNull(digitalManParamBO.getDmVoiceChannel())) {
            dmVoiceChannel = digitalManParamBO.getDmVoiceChannel();
            return dmVoiceChannel;
        }

        Map<String, Object> dmContentMap = JsonUtils.getMap4Json(dmConfigDTO.getContent());
        if (Objects.nonNull(dmContentMap.get("dmVoiceChannel"))) {
            dmVoiceChannel = Integer.valueOf(dmContentMap.get("dmVoiceChannel").toString());
        }

        if (Objects.nonNull(dmVoiceChannel)) {
            return dmVoiceChannel;
        }

        //针对历史模板没有voiceChannel
        //先根据数字人场景和声音编码的绑定关系查询声音渠道
        dmVoiceChannel = this.queryVmVoiceChannelByRelation(sceneId, voiceCode);
        //若voiceChannel仍为空，则取数字人channel。
        if (Objects.isNull(dmVoiceChannel)) {
            dmVoiceChannel = dmChannel;
        }
        return dmVoiceChannel;
    }

    private Integer queryVmVoiceChannelByRelation(String sceneId, String voiceCode) {
        DaVirtualManSceneVoiceRequestDTO requestDTO = new DaVirtualManSceneVoiceRequestDTO();
        requestDTO.setSceneId(sceneId);
        requestDTO.setVoiceCode(voiceCode);
        ResultModel<DaVirtualVoiceDTO> resultModel = aiServiceClient.vmSceneSpecificVoice(requestDTO);
        if (!resultModel.isSuccess()) {
            log.error("查询单个数字人场景的指定声音信息失败，sceneId:{},voiceCode:{}", sceneId, voiceCode);
            throw BusinessServiceException.getInstance("查询单个数字人场景的指定声音信息失败");
        }
        if (Objects.isNull(resultModel.getDataResult())) {
            log.error("查询单个数字人场景的指定声音信息为空，sceneId:{},voiceCode:{},resultModel:{}", sceneId, voiceCode,
                    JSONUtil.toJsonStr(resultModel));
            return null;
        }
        return resultModel.getDataResult().getChannel();
    }

}

