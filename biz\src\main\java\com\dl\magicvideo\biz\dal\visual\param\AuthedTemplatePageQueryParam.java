package com.dl.magicvideo.biz.dal.visual.param;

import com.dl.framework.common.bo.PageQueryDO;
import lombok.Data;

import java.util.List;

/**
 * @describe: AuthedTemplatePageQueryParam
 * @author: zhousx
 * @date: 2023/6/27 19:37
 */
@Data
public class AuthedTemplatePageQueryParam extends PageQueryDO {

    private String tenantCode;

    private String resolutionType;

    private String name;

    private Integer firstCategory;

    private Integer secondCategory;

    private Integer status;

    /**
     * 是否本地化拉取云端saas模版 0否 1是
     */
    private Integer isSync = 0;

    /**
     * 是否系统模板
     */
    private Integer isSys;

    /**
     * 是否PPT模板
     */
    private Integer isPPT;

    /**
     * 模板id列表
     */
    private List<Long> templateIds;

    private Long templateId;

    private List<Long> tagIds;

}
