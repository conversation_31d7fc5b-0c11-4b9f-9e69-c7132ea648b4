<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dl.magicvideo.biz.dal.visual.VisualShareConfMapper">

    <resultMap id="QueryShareTemplate" type="com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO">
        <result property="id" column="id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="templateId" column="template_id"/>
        <result property="name" column="name"/>
        <result property="status" column="status"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="resolution" column="resolution"/>
        <result property="resolutionType" column="resolution_type"/>
        <result property="ttsParam" column="tts_param"/>
        <result property="bgMusic" column="bg_music"/>
        <result property="bgMusicParam" column="bg_music_param"/>
        <result property="replaceData" column="replace_data"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="isSys" column="is_sys"/>
        <result property="creatorName" column="creator_name"/>
        <result property="duration" column="duration"/>
        <result property="previewVideoUrl" column="preview_video_url"/>
        <result property="shortVideoUrl" column="short_video_url"/>
    </resultMap>
    <resultMap id="QueryVisualProduceJobExtendInfo" type="com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobShareInfoPO">
        <result property="jobId" column="job_id" jdbcType="BIGINT"/>
        <result property="templateId" column="template_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="coverUrl" column="cover_url" jdbcType="VARCHAR"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="BIGINT"/>
        <result property="completeDt" column="complete_dt" jdbcType="TIMESTAMP"/>
        <result property="resolutionType" column="resolution_type" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectVisualProduceJobExtendInfoPOList" resultMap="QueryVisualProduceJobExtendInfo">
        SELECT vpj.job_id,vpj.template_id,vpj.name,vpj.cover_url,vpj.video_url,vpj.duration,vpj.complete_dt,vt.resolution_type
        FROM visual_produce_job_extend vpje
        LEFT JOIN visual_produce_job vpj ON vpje.produce_job_id = vpj.job_id
        LEFT JOIN visual_template vt ON vpj.template_id = vt.template_id
        WHERE vpje.share_conf_state = 2
        AND vpj.tenant_code = #{param.tenantCode}
        AND vpj.is_deleted = 0
        AND vpje.is_deleted = 0
        <if test="param.resolutionType != null and param.resolutionType != ''">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.name != null and param.name != ''">
            AND vpj.name LIKE CONCAT(#{param.name}, '%')
        </if>
        <if test="param.startTime != null">
            AND vpj.complete_dt &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null ">
            AND vpj.complete_dt &lt;= #{param.endTime}
        </if>
        ORDER BY vpje.create_dt Desc
        LIMIT #{param.offset}, #{param.pageSize}
    </select>
    <select id="selectVisualProduceJobExtendInfoPOTotal" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM visual_produce_job_extend vpje
        LEFT JOIN visual_produce_job vpj ON vpje.produce_job_id = vpj.job_id
        LEFT JOIN visual_template vt ON vpj.template_id = vt.template_id
        WHERE vpje.share_conf_state = 2
        AND vpj.tenant_code = #{param.tenantCode}
        AND vpj.is_deleted = 0
        AND vpje.is_deleted = 0
        <if test="param.resolutionType != null">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.name != null and param.name != ''">
            AND vpj.name LIKE CONCAT(#{param.name}, '%')
        </if>
        <if test="param.startTime != null">
            AND vpj.complete_dt &gt;= #{param.startTime}
        </if>
        <if test="param.endTime != null ">
            AND vpj.complete_dt &lt;= #{param.endTime}
        </if>
    </select>
    <select id="selectVisualShareTemplateList" resultMap="QueryShareTemplate">
        select vt.template_id, vt.id, vt.tenant_code, vt.name, vt.status, vt.cover_url, vt.resolution,
        vt.resolution_type, vt.tts_param, vt.bg_music, vt.bg_music_param, vt.replace_data, vt.is_deleted, vt.is_sys,
        vt.creator_name, vt.duration, vt.short_video_url, vt.preview_video_url
            FROM visual_template vt
            where vt.share_conf_state = 2
            AND vt.status = 0
            AND vt.tenant_code = #{param.tenantCode}
            AND vt.is_deleted = 0
        <if test="param.resolutionType != null">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.name != null and param.name != ''">
            AND vt.name LIKE CONCAT(#{param.name}, '%')
        </if>
        <if test="param.sortType == 0">
            order by vt.create_dt
        </if>
        <if test="param.sortType == 1">
            order by vt.name
        </if>
        <if test="param.sortType == 2">
            order by vt.modify_dt desc
        </if>
        LIMIT #{param.offset}, #{param.pageSize}
    </select>
    <select id="selectVisualShareTemplateTotal" resultType="java.lang.Long">
        select COUNT(*)
        from visual_share_conf vsc left join visual_template vt
        on vsc.biz_id = vt.template_id
        where vsc.biz_type = 1
        AND vt.status = 0
        AND vsc.tenant_code = #{param.tenantCode}
        AND vsc.is_deleted = 0
        AND vt.is_deleted = 0
        <if test="param.resolutionType != null">
            AND vt.resolution_type = #{param.resolutionType}
        </if>
        <if test="param.name != null and param.name != ''">
            AND vt.name LIKE CONCAT(#{param.name}, '%')
        </if>
    </select>


</mapper>
