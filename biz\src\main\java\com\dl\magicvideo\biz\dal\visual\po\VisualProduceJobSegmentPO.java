package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName visual_produce_job_segment
 */
@TableName(value ="visual_produce_job_segment")
@Data
public class VisualProduceJobSegmentPO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分片id
     */
    private Long segmentId;

    /**
     * 任务id
     */
    private Long produceJobId;

    /**
     * 主机ip
     */
    private String processHost;

    /**
     * 处理开始时间
     */
    private Date processBegin;

    /**
     * 处理结束时间
     */
    private Date processEnd;

    /**
     * 分片任务状态：0-未开始 1-合成中 2-成功 3-失败
     */
    private Integer status;

    /**
     * 分片排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createDt;

    /**
     * 修改时间
     */
    private Date modifyDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}