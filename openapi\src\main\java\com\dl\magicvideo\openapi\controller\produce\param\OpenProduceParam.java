package com.dl.magicvideo.openapi.controller.produce.param;

import com.dl.magicvideo.biz.manager.visual.dto.CrossClipsDTO;
import com.dl.magicvideo.biz.manager.visual.dto.InterfaceDTO;
import com.dl.magicvideo.biz.manager.visual.dto.LightEditConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @describe: ProduceParam
 * @author: zhousx
 * @date: 2023/6/25 10:07
 */
@Data
public class OpenProduceParam {
    @ApiModelProperty(value = "模板id", required = true)
    @NotBlank(message = "模板id不能为空")
    private String templateId;

    @ApiModelProperty(value = "外部联系人id", required = true)
    @NotBlank(message = "外部联系人id不能为空")
    private String extUserId;

    @ApiModelProperty(value = "数字人信息")
    private OpenDigitalManParam digitalMan;

    @ApiModelProperty("轻编辑配置")
    private List<LightEditConfigDTO> lightEditConfigs;

    @ApiModelProperty("跨片段组建")
    private List<CrossClipsDTO> crossClips;

    @ApiModelProperty("批量合成参数")
    private List<InterfaceDTO> interfaceParam;
}
