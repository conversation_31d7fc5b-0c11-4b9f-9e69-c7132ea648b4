package com.dl.magicvideo.web.controllers.internal.visual;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.visual.po.VisualAiConfigPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualCardPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualDynamicNodePO;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.visual.*;
import com.dl.magicvideo.biz.manager.visual.bo.TagPageBO;
import com.dl.magicvideo.biz.manager.visual.bo.TemplateSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.TagDTO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualTemplateDTO;
import com.dl.magicvideo.biz.manager.visual.enums.TemplateTypeEnum;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.visual.convert.VisualTemplateAuthInternalConvert;
import com.dl.magicvideo.web.controllers.internal.visual.param.*;
import com.dl.magicvideo.web.controllers.internal.visual.vo.*;
import com.dl.magicvideo.web.controllers.template.param.TemplatePageQueryParam;
import com.dl.magicvideo.web.controllers.template.vo.CardVO;
import com.dl.magicvideo.web.controllers.template.vo.DynamicNodeVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = { "/internal/template", "/visual/internal/template" })
@Slf4j
public class VisualTemplateInternalController extends AbstractController {

    @Resource
    private VisualTemplateManager visualTemplateManager;

    @Resource
    private VisualCardManager visualCardManager;

    @Resource
    private VisualDynamicNodeManager visualDynamicNodeManager;

    @Resource
    private VisualAiConfigManager visualAiConfigManager;

    @Resource
    private TagManager tagManager;

    @PostMapping("/info")
    @NotLogin
    ResultModel<VisualTemplateInternalVO> info(@RequestBody DetailParam param) {
        Assert.notNull(param.getTemplateId(), "模板ID不能为空");
        VisualTemplatePO templatePO = visualTemplateManager.lambdaQuery()
                .eq(VisualTemplatePO::getTemplateId, param.getTemplateId()).one();
        if (Objects.isNull(templatePO)) {
            return ResultModel.success(null);
        }

        //非系统模板，校验租户号是否一致
        if (Const.ZERO.equals(templatePO.getIsSys()) && !templatePO.getTenantCode().equals(param.getTenantCode())) {
            throw BusinessServiceException.getInstance("无权查看该模板");
        }

        return ResultModel.success(convertToVo(templatePO));
    }

    @PostMapping("/batchinfo")
    @NotLogin
    ResultModel<List<VisualTemplateInternalVO>> batchInfo(
            @RequestBody @Validated VisualTemplateBatchInfoQueryParam param) {
        List<VisualTemplatePO> templatePOList = visualTemplateManager.lambdaQuery()
                .in(VisualTemplatePO::getTemplateId, param.getTemplateIds()).list();
        if (CollectionUtils.isEmpty(templatePOList)) {
            return ResultModel.success(Collections.emptyList());
        }
        return ResultModel.success(templatePOList.stream().map(this::convertToVo).collect(Collectors.toList()));
    }

    @PostMapping("/detail")
    ResultModel<VisualTemplateInternalVO> detail(@RequestBody DetailParam param){
        VisualTemplateDTO templateDTO = visualTemplateManager.detail(param.getTemplateId(), param.getTenantCode());
        Assert.notNull(templateDTO, "模板不存在。");
        return ResultModel.success(convertToVo(templateDTO));
    }

    @GetMapping("/delete/{templateId}")
    @ApiOperation("删除模板")
    public ResultModel<Void> delete(@PathVariable Long templateId) {
        visualTemplateManager.delete(templateId);
        return ResultModel.success(null);
    }

    @PostMapping("/list")
    @ApiOperation("模板列表")
    public ResultPageModel<VisualTemplateInternalVO> list(@RequestBody @Validated TemplatePageQueryParam param) {
        TemplateSearchBO bo = new TemplateSearchBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setName(param.getName());
        bo.setStatus(param.getStatus());
        bo.setCreateBy(getUserId());
        bo.setIsSys(param.getIsSys());
        bo.setResolutionType(param.getResolutionType());
        bo.setSortType(param.getSortType());
        bo.setShareConfState(param.getShareConfState());
        bo.setIsManager(param.getIsManager());
        bo.setTenantCode(param.getTenantCode());
        bo.setTemplateId(param.getTemplateId());
        bo.setType(TemplateTypeEnum.NORMAL.getType());
        ResponsePageQueryDO<List<VisualTemplateDTO>> result = visualTemplateManager.pageQuery(bo);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        List<VisualTemplateInternalVO> vos = result.getDataResult().stream().map(this::cnvVisualTemplateDTO2VO).collect(
                Collectors.toList());
        return pageQueryModel(result, vos);
    }

    @PostMapping("/authpage")
    @ApiOperation("授权模版分页")
    public ResultPageModel<VisualTemplateInternalVO> authTemplatePage(@RequestBody TemplateInternalPageQueryParam param){
        log.info("授权模版分页入参param = {}", JSONUtil.toJsonStr(param));
        TemplateSearchBO bo = new TemplateSearchBO();
        bo.setTenantCode(param.getTenantCode());
        bo.setPageIndex(param.getPageIndex());
        bo.setStatus(param.getStatus());
        bo.setPageSize(param.getPageSize());
        ResponsePageQueryDO<List<VisualTemplateDTO>> resp = visualTemplateManager.syncCloudAuthedTemplatePage(
                bo);
        log.info("本次获取授权模版分页结果 resp = {}",JSONUtil.toJsonStr(resp));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(resp.getDataResult())){
            return pageQueryModel(resp,new ArrayList<>());
        }
        return pageQueryModel(resp,resp.getDataResult().stream().map(this::cnvVisualTemplateDTO2VO).collect(Collectors.toList()));
    }

    @PostMapping("/authlistbyids")
    @ApiOperation("根据授权模版id列表查询模板信息")
    public ResultModel<List<VisualTemplateInternalVO>> authTemplateListByTemplateIds(@RequestBody @Validated TemplateInternalListQueryParam param){
        log.info("根据授权模版id列表查询模板信息入参param = {}", JSONUtil.toJsonStr(param));
        TemplateSearchBO bo = new TemplateSearchBO();
        bo.setTenantCode(param.getTenantCode());
        bo.setStatus(param.getStatus());
        bo.setTemplateIds(param.getTemplateIdList());
        List<VisualTemplateDTO> templateDTOList = visualTemplateManager.syncCloudAuthedTemplateList(bo);
        log.info("本次获取授权模版列表结果 resp = {}",JSONUtil.toJsonStr(templateDTOList));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(templateDTOList)){
            return ResultModel.success(Collections.emptyList());
        }
        return ResultModel.success(templateDTOList.stream().map(this::cnvVisualTemplateDTO2VO).collect(Collectors.toList()));
    }

    @PostMapping("/getdata")
    @ApiOperation("授权模版数据")
    public ResultModel<VisualTemplateDataVO> templateData(@RequestBody VisualTemplateInfoQueryParam param) {

        log.info("开始获取模版卡片数据 param = {}", JSONUtil.toJsonStr(param));
        //1.查询模版卡片
        List<VisualCardPO> visualCardList = visualCardManager
                .list(Wrappers.lambdaQuery(VisualCardPO.class).eq(VisualCardPO::getTemplateId, param.getTemplateId())
                        .eq(VisualCardPO::getIsDeleted, Const.ZERO));

        //2.查询模版卡片节点
        List<VisualDynamicNodePO> visualDynamicNodeList = visualDynamicNodeManager
                .list(Wrappers.lambdaQuery(VisualDynamicNodePO.class)
                        .eq(VisualDynamicNodePO::getTemplateId, param.getTemplateId()));

        //3.查询节点tts、数字人配置
        List<VisualAiConfigPO> visualAiConfigList = visualAiConfigManager.list(Wrappers.lambdaQuery(VisualAiConfigPO.class)
                .eq(VisualAiConfigPO::getTemplateId, param.getTemplateId()));

        List<VisualAiConfigInternalVO> visualAiConfigInternalVOList = visualAiConfigList.stream()
                .map(VisualTemplateAuthInternalConvert::cnvVisualAiConfigInternalPO2InternalVO)
                .collect(Collectors.toList());

        List<VisualDynamicNodeInternalVO> visualDynamicNodeInternalVOList = visualDynamicNodeList.stream()
                .map(VisualTemplateAuthInternalConvert::cnvVisualDynamicNodePO2InternalVO).collect(Collectors.toList());

        List<VisualCardInternalVO> visualCardInternalVOList = visualCardList.stream()
                .map(VisualTemplateAuthInternalConvert::cnvVisualCardPO2InternalVO).collect(Collectors.toList());

        VisualTemplateDataVO result = new VisualTemplateDataVO();
        result.setVisualCardInternalList(visualCardInternalVOList);
        result.setVisualDynamicNodeInternalList(visualDynamicNodeInternalVOList);
        result.setVisualAiConfigInternalList(visualAiConfigInternalVOList);
        log.info("返回的卡片数据 result = {}",JSONUtil.toJsonStr(result));
        return ResultModel.success(result);
    }

    @PostMapping("/tagList")
    @ApiOperation("标签列表")
    public ResultPageModel<TagVO> tagList(@RequestBody @Validated TagPageQueryParam param) {
        if (Objects.isNull(param.getTagType())){
            param.setTagType(1);
        }
        TagPageBO bo = new TagPageBO();
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        bo.setTagType(param.getTagType());
        IPage<TagDTO> resp = tagManager.pageQuery(bo);
        ResultPageModel<TagVO> result = pageQueryModel(resp, resp.getRecords().stream().map(e -> {
            TagVO vo = new TagVO();
            vo.setTagId(e.getTagId().toString());
            vo.setTagName(e.getTagName());
            return vo;
        }).collect(Collectors.toList()));

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(result.getDataResult())) {
            return new ResultPageModel<>();
        }
        return result;
    }

    private VisualTemplateInternalVO convertToVo(VisualTemplateDTO po){
        VisualTemplateInternalVO vo = new VisualTemplateInternalVO();
        //vo.setId(po.getId());
        vo.setTemplateId(po.getTemplateId() +  "");
        vo.setName(po.getName());
        vo.setStatus(po.getStatus());
        vo.setCoverUrl(po.getCoverUrl());
        vo.setResolution(po.getResolution());
        vo.setResolutionType(po.getResolutionType());
        vo.setTtsParam(po.getTtsParam());
        vo.setBgMusic(po.getBgMusic());
        vo.setBgMusicParam(po.getBgMusicParam());
        vo.setReplaceData(po.getReplaceData());
        vo.setTenantCode(po.getTenantCode());
        //vo.setIsDeleted(po.getIsDeleted());
        //vo.setIsSys(po.getIsSys());
        //vo.setCreatorName(po.getCreatorName());
        vo.setDuration(po.getDuration());
        vo.setShortVideoUrl(po.getShortVideoUrl());
        vo.setPreviewVideoUrl(po.getPreviewVideoUrl());
        vo.setShareConfState(po.getShareConfState());
        vo.setIsManager(po.getIsManager());
        vo.setFirstCategory(po.getFirstCategory());
        vo.setSecondCategory(po.getSecondCategory());
        vo.setIsPPT(po.getIsPPT());
        vo.setComponentVersion(po.getComponentVersion());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(po.getCards())) {
            vo.setCards(po.getCards().stream().map(visualCardDTO -> {
                CardVO cardVO = new CardVO();
                cardVO.setCardId(visualCardDTO.getCardId() + "");
                cardVO.setTemplateId(visualCardDTO.getTemplateId() + "");
                cardVO.setName(visualCardDTO.getName());
                cardVO.setCoverUrl(visualCardDTO.getCoverUrl());
                cardVO.setResolution(visualCardDTO.getResolution());
                cardVO.setRenderData(visualCardDTO.getRenderData());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(visualCardDTO.getDynamicNodes())) {
                    cardVO.setDynamicNodes(visualCardDTO.getDynamicNodes().stream().map(dynamicNodeDTO -> {
                        DynamicNodeVO dynamicNodeVO = new DynamicNodeVO();
                        dynamicNodeVO.setNodeId(dynamicNodeDTO.getNodeId() + "");
                        dynamicNodeVO.setType(dynamicNodeDTO.getType());
                        dynamicNodeVO.setDuration(dynamicNodeDTO.getDuration());
                        dynamicNodeVO.setCoverUrl(dynamicNodeDTO.getCoverUrl());
                        dynamicNodeVO.setIsEnabled(dynamicNodeDTO.getIsEnabled());
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dynamicNodeDTO.getTtsList())) {
                            dynamicNodeVO.setTtsList(dynamicNodeDTO.getTtsList().stream().map(ttsConfigDTO -> {
                                DynamicNodeVO.TtsConfigVO ttsConfigVO = new DynamicNodeVO.TtsConfigVO();
                                ttsConfigVO.setTtsId(ttsConfigDTO.getTtsId());
                                ttsConfigVO.setContent(ttsConfigDTO.getContent());
                                ttsConfigVO.setStart(ttsConfigDTO.getStart());
                                ttsConfigVO.setEnableSubtitle(ttsConfigDTO.getEnableSubtitle());
                                ttsConfigVO.setMaxLength(ttsConfigDTO.getMaxLength());
                                return ttsConfigVO;
                            }).collect(Collectors.toList()));
                        }
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dynamicNodeDTO.getDmList())) {
                            dynamicNodeVO.setDmList(dynamicNodeDTO.getDmList().stream().map(ttsConfigDTO -> {
                                DynamicNodeVO.DigitalManConfigVO dmConfigVO = new DynamicNodeVO.DigitalManConfigVO();
                                dmConfigVO.setDmId(ttsConfigDTO.getDmId());
                                dmConfigVO.setContent(ttsConfigDTO.getContent());
                                dmConfigVO.setStart(ttsConfigDTO.getStart());
                                dmConfigVO.setEnableSubtitle(ttsConfigDTO.getEnableSubtitle());
                                dmConfigVO.setMaxLength(ttsConfigDTO.getMaxLength());
                                return dmConfigVO;
                            }).collect(Collectors.toList()));
                        }
                        return dynamicNodeVO;
                    }).collect(Collectors.toList()));
                }
                return cardVO;
            }).collect(Collectors.toList()));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(po.getTagList())) {
            vo.setTagList(po.getTagList().stream().map(tagDTO -> {
                TagVO tagInfo = new TagVO();
                tagInfo.setTagId(tagDTO.getTagId().toString());
                tagInfo.setTagName(tagDTO.getTagName());
                return tagInfo;
            }).collect(Collectors.toList()));
        }
        return vo;
    }

    private VisualTemplateInternalVO cnvVisualTemplateDTO2VO(VisualTemplateDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        VisualTemplateInternalVO vo = new VisualTemplateInternalVO();
        vo.setTemplateId(dto.getTemplateId() + "");
        vo.setStatus(dto.getStatus());
        vo.setCoverUrl(dto.getCoverUrl());
        vo.setPreviewVideoUrl(dto.getPreviewVideoUrl());
        vo.setShortVideoUrl(dto.getShortVideoUrl());
        vo.setName(dto.getName());
        vo.setResolution(dto.getResolution());
        vo.setResolutionType(dto.getResolutionType());
        vo.setBgMusic(dto.getBgMusic());
        vo.setBgMusicParam(dto.getBgMusicParam());
        vo.setTtsParam(dto.getTtsParam());
        vo.setReplaceData(dto.getReplaceData());
        vo.setDuration(dto.getDuration());
        vo.setCreateDt(dto.getCreateDt());
        vo.setModifyDt(dto.getModifyDt());
        vo.setShareConfState(dto.getShareConfState());
        vo.setIsManager(dto.getIsManager());
        vo.setFirstCategory(dto.getFirstCategory());
        vo.setSecondCategory(dto.getSecondCategory());
        return vo;
    }

    private VisualTemplateInternalVO convertToVo(VisualTemplatePO po) {
        VisualTemplateInternalVO vo = new VisualTemplateInternalVO();
        vo.setId(po.getId());
        vo.setTemplateId(po.getTemplateId() + "");
        vo.setName(po.getName());
        vo.setStatus(po.getStatus());
        vo.setCoverUrl(po.getCoverUrl());
        vo.setResolution(po.getResolution());
        vo.setResolutionType(po.getResolutionType());
        vo.setTtsParam(po.getTtsParam());
        vo.setBgMusic(po.getBgMusic());
        vo.setBgMusicParam(po.getBgMusicParam());
        vo.setReplaceData(po.getReplaceData());
        vo.setTenantCode(po.getTenantCode());
        vo.setIsDeleted(po.getIsDeleted());
        vo.setIsSys(po.getIsSys());
        vo.setCreatorName(po.getCreatorName());
        vo.setDuration(po.getDuration());
        vo.setShortVideoUrl(po.getShortVideoUrl());
        vo.setPreviewVideoUrl(po.getPreviewVideoUrl());
        vo.setShareConfState(po.getShareConfState());
        return vo;
    }
}
