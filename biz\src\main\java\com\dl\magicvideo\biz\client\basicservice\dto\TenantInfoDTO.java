package com.dl.magicvideo.biz.client.basicservice.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-15 20:46
 */
@Data
public class TenantInfoDTO implements Serializable {

    private static final long serialVersionUID = 5606236932036879214L;

    private Long id;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("租户code")
    private String tenantCode;

    @ApiModelProperty("logo图片")
    private String logoImg;

    @ApiModelProperty("租户状态")
    private Integer status;

    @ApiModelProperty("是否供应商：0 否，默认； 1 是")
    private Integer isProvider;

    @ApiModelProperty("是否企微入驻租户：0 否； 1 是 默认")
    private Integer isWeWork;

    @ApiModelProperty("授权模式，1-微信全托管模式，2-微信第三方平台")
    private Integer authMode;

    @ApiModelProperty("小logo")
    private String smallLogo;

    @ApiModelProperty("租户访问地址")
    private String tenantUrl;

    @ApiModelProperty("租户三级域名")
    private String thirdLevelDomain;

    /**
     * 租户访问域名，包含scheme
     */
    private String tenantDomain;

    /**
     * 租户访问路径片段，常用于本地化部署
     */
    private String pathPart;

    /**
     * 是否隐藏二维码登录：0 否，默认；1 是
     */
    private Integer isHideQrLogin;

    /**
     * 租户信息描述
     */
    private String tenantDesc;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * '0-纯页面模式，1-纯API提供模式，2-页面+API模式，默认值是0'
     */
    private Integer produceType;

    public Date createDt;

    public Long createBy;

    public Date modifyDt;

    public Long modifyBy;
}
