package com.dl.magicvideo.biz.common.properties.session;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 会话配置
 */
@Configuration
@ConfigurationProperties("dl.session")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SessionProperty {

    private AdmSession adm;

    private AppSession app;

    private ToCustomerSession toCust;
}
