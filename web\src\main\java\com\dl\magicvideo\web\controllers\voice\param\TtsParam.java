package com.dl.magicvideo.web.controllers.voice.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @describe: TtsParam
 * @author: zhousx
 * @date: 2023/5/6 13:49
 */
@Data
public class TtsParam {
    @ApiModelProperty("模板id")
    @NotBlank
    private String templateId;

    @ApiModelProperty("渠道 4-阿里云 5-腾讯云 6-火山")
    private Integer channel = 4;

    @NotBlank(message = "voiceKey不能为空")
    @ApiModelProperty("音色类型")
    private String voiceKey;

    @ApiModelProperty(value = "源文本")
    @NotBlank
    private String text;

    @ApiModelProperty(value = "语速:默认值：normal")
    private String speed;

    @ApiModelProperty(value = "音量;取值范围 0.0-1.0，1.0 表示最大音量")
    private String volume;

    @ApiModelProperty("分段最大字数")
    private Integer maxLength;

    @ApiModelProperty("是否需要字幕中关键词高亮，0-否，1-是")
    private Integer subtitleKeyWordsHighlight;

}
