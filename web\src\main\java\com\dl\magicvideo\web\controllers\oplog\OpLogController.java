package com.dl.magicvideo.web.controllers.oplog;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.basicservice.dto.OpLogDTO;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogPageReq;
import com.dl.magicvideo.biz.common.annotation.NotLogin;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.visual.po.VisualTemplatePO;
import com.dl.magicvideo.biz.manager.oplog.OpLogManager;
import com.dl.magicvideo.biz.manager.oplog.enums.MagicOpTypeEnum;
import com.dl.magicvideo.biz.manager.oplog.enums.OpObjectEnum;
import com.dl.magicvideo.biz.manager.visual.VisualTemplateManager;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.oplog.convert.OpLogConvert;
import com.dl.magicvideo.web.controllers.oplog.param.OpLogPageParam;
import com.dl.magicvideo.web.controllers.oplog.vo.MagicOpTypeVO;
import com.dl.magicvideo.web.controllers.oplog.vo.OpLogVO;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 17:18
 */
@RestController
@RequestMapping("/visual/oplog")
public class OpLogController extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OpLogController.class);

    @Resource
    private OpLogManager opLogManager;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private VisualTemplateManager visualTemplateManager;

    private static List<MagicOpTypeVO> magicOpTypeList = new ArrayList<>();

    static {
        for (MagicOpTypeEnum typeEnum : MagicOpTypeEnum.values()) {
            MagicOpTypeVO magicOpTypeVO = new MagicOpTypeVO();
            magicOpTypeVO.setType(typeEnum.getType());
            magicOpTypeVO.setDesc(typeEnum.getDesc());
            magicOpTypeList.add(magicOpTypeVO);
        }
    }

    @ApiOperation("分页查询数影操作日志")
    @PostMapping("/page")
    public ResultPageModel<OpLogVO> page(@RequestBody OpLogPageParam param) {
        OpLogPageReq req = OpLogConvert.cnvOpLogPageParam2Req(param, operatorUtil.getTenantCode());
        ResultPageModel<OpLogDTO> resultDTO = opLogManager.page(req);

        ResultPageModel<OpLogVO> resultPageModel = new ResultPageModel<>();
        resultPageModel.setPageIndex(resultDTO.getPageIndex());
        resultPageModel.setPageSize(resultDTO.getPageSize());
        resultPageModel.setTotalPage(resultDTO.getTotalPage());
        resultPageModel.setTotal(resultDTO.getTotal());
        if (CollectionUtils.isEmpty(resultDTO.getDataResult())) {
            return resultPageModel;
        }

        resultPageModel.setDataResult(
                resultDTO.getDataResult().stream().map(OpLogConvert::cnvOpLogDTO2VO).collect(Collectors.toList()));
        this.fillOpObjectName(resultPageModel.getDataResult());

        return resultPageModel;
    }

    @NotLogin
    @ApiOperation("查询数影操作类型列表")
    @PostMapping("/listmagicoptype")
    public ResultModel<List<MagicOpTypeVO>> listMagicOpType(HttpServletRequest request) {
        return ResultModel.success(magicOpTypeList);
    }

    /**
     * 填充操作对象名称
     * <p>
     * 目前操作对象只有模板和用户。模板的需要查询模板名称，用户的无需处理
     *
     * @param opLogVOList
     */
    private void fillOpObjectName(Collection<OpLogVO> opLogVOList) {
        //处理操作对象是模板的日志
        List<OpLogVO> templateOpLogList = opLogVOList.stream()
                .filter(vo -> OpObjectEnum.TEMPLATE.getCode().equals(vo.getOpObject())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(templateOpLogList)) {
            Set<Long> templateIds = templateOpLogList.stream().map(vo -> Long.valueOf(vo.getOpKey()))
                    .collect(Collectors.toSet());
            List<VisualTemplatePO> templatePOList = visualTemplateManager
                    .list(Wrappers.lambdaQuery(VisualTemplatePO.class)
                            .in(VisualTemplatePO::getTemplateId, templateIds));
            Map<String, String> templateIdNameMap = templatePOList.stream()
                    .collect(Collectors.toMap(x -> String.valueOf(x.getTemplateId()), VisualTemplatePO::getName));
            templateOpLogList.forEach(vo -> {
                vo.setOpObjectName(templateIdNameMap.get(vo.getOpKey()));
            });
        }
    }
}
