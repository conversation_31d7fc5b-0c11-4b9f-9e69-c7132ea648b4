package com.dl.magicvideo.biz.common.util;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * @description：TODO
 * @author： Pelot
 * @create： 2024/11/27 17:11
 */
@Slf4j
public class BindKeyUtils {

    public static void main(String[] args) {
        // 假设这是你的 JSON 字符串
        String jsonString = "{\"/datamining/threeTopic/bb07b1\":{\"result\":{\"dayText1\":\"2024年11月20日\",\"dayText2\":\"11月20日\",\"dataSourceText\":\"数据来源：东财  数据截至：2024年11月20日个股/内容仅做数据、行业梳理及科普展示，不构成投资建议，投资有风险，入市需谨慎\",\"burstToBuyList\":{\"value\":[[\"名称\",\"净买额（亿）\",\"游资营业部\"],[\"东方财富\",\"11.2\",\"杭州帮\\n国泰君安证券股份有限公司上海分公司\"],[\"润和软件\",\"4.24\",\"炒股养家\\n华鑫证券有限责任公司\\n上海宛平南路证券营业部\"],[\"天晟新材\",\"0.76\",\"温州帮\\n中信证券股份有限公司\\n长兴明珠路证券营业部\"],[\"速达股份\",\"0.29\",\"苏南帮\\n东莞证券股份有限公司南京分公司\"],[\"衢州发展\",\"0.2\",\"成都系\\n广发证券股份有限公司\\n成都新光路证券营业部\"]]},\"burstToBuyListFlag\":true,\"knownGangList\":{\"value\":[[\"名称\",\"净买额（亿）\",\"概念板块\",\"游资\"],[\"速达股份\",\"0.15\",\"融资融券,电子商务\",\"苏南帮\"],[\"速达股份\",\"0.07\",\"融资融券,电子商务\",\"温州帮\"],[\"同花顺\",\"1.15\",\"融资融券,电子商务\",\"欢乐海岸\"],[\"同花顺\",\"-0.4\",\"融资融券,沪深300\",\"温州帮\"],[\"同花顺\",\"-1.12\",\"融资融券,沪深300\",\"山东帮\"],[\"东方财富\",\"10.77\",\"融资融券,沪深300\",\"杭州帮\"],[\"东方财富\",\"4.51\",\"融资融券,沪深300\",\"温州帮\"]]},\"knownGangListFlag\":true,\"hotMoneyFollowInList\":{\"value\":[[\"概念板块\",\"净买额（亿）\",\"概念涨跌幅\"],[\"融资融券\",\"16.528\",\"4.10%\"],[\"沪深300\",\"14.898\",\"3.40%\"],[\"小米\",\"3.377\",\"6.30%\"]]},\"hotMoneyFollowInListFlag\":true,\"hotMoneyFollowOutList\":{\"value\":[[\"概念板块\",\"净买额（亿）\",\"概念涨跌幅\"],[\"房地产\",\"-0.389\",\"6.30%\"],[\"软件\",\"-0.535\",\"6.90%\"],[\"储能\",\"-1.159\",\"5.10%\"]]},\"hotMoneyFollowOutListFlag\":true,\"firstPlatesNumberList\":{\"value\":[[\"概念\",\"公司\",\"连板数\",\"当前逻辑\"],[\"AI应用端\",\"恒信东方\",\"首板\",\"AI+机器人+\\\"东方\\\"\"],[\"AI应用端\",\"热景生物\",\"首板\",\"AI+医疗\"],[\"AI应用端\",\"实丰文化\",\"首板\",\"游戏+AI\"],[\"AI应用端\",\"华扬联众\",\"首板\",\"传媒+AI\"],[\"数据要素\",\"梦网科技\",\"首板\",\"AI+数据要素\"],[\"数据要素\",\"中远海科\",\"首板\",\"航运+数据要素\"],[\"医疗信息化\",\"宏景科技\",\"首板\",\"医疗信息化+算力\"],[\"医疗信息化\",\"万达信息\",\"首板\",\"医疗信息化\"],[\"医疗信息化\",\"国新健康\",\"首板\",\"医疗信息化+华为+AI\"],[\"医疗信息化\",\"久远银海\",\"首板\",\"医疗信息化+数据要素\"],[\"商业航天\",\"国机精工\",\"首板\",\"商业航天\"],[\"商业航天\",\"高斯贝尔\",\"首板\",\"卫星通信+机器人\"],[\"商业航天\",\"航天动力\",\"首板\",\"商业航天\"],[\"商业航天\",\"通宇通讯\",\"首板\",\"卫星通信+铜连接\"],[\"低空经济\",\"宝利国际\",\"首板\",\"股权转让+化工+低空经济\"],[\"低空经济\",\"欣贺股份\",\"首板\",\"低空经济\"],[\"低空经济\",\"华懋科技\",\"首板\",\"低空经济+光刻胶\"],[\"固态电池\",\"联创股份\",\"首板\",\"氟化工+固态电池\"],[\"固态电池\",\"德尔股份\",\"首板\",\"固态电池\"],[\"固态电池\",\"光华科技\",\"首板\",\"光刻胶+固态电池\"],[\"固态电池\",\"普利特\",\"首板\",\"固态电池+5G-A\"]]},\"firstPlatesNumberListFlag\":true,\"continuePlatesNumberList\":{\"value\":[[\"概念\",\"公司\",\"连板数\",\"当前逻辑\"],[\"并购重组\",\"大千生态\",\"12连板\",\"实控人变更\"],[\"并购重组\",\"渤海股份\",\"12天8板\",\"资产注入预期\"],[\"其他\",\"日出东方\",\"13天11板\",\"华为超充+\\\"东方\\\"\"],[\"锂电池\",\"粤桂股份\",\"10天9板\",\"硫铁矿+锂电池\"],[\"固态电池\",\"有研新材\",\"10天7板\",\"稀土+固态电池\"],[\"AI应用端\",\"中科金财\",\"8天6板\",\"AI智能体+算力\"],[\"AI应用端\",\"酷特智能\",\"5天4板\",\"AI智能体+固态电池\"],[\"AI应用端\",\"南兴股份\",\"5天4板\",\"AI智能体+算力\"],[\"商业航天\",\"长江通信\",\"9天6板\",\"卫星互联网\"],[\"商业航天\",\"魅视科技\",\"2连板\",\"AI+商业航天\"],[\"机器人\",\"东方精工\",\"5连板\",\"英伟达+机器人+\\\"东方\\\"\"],[\"机器人\",\"爱仕达\",\"4天3板\",\"机器人+家电\"],[\"机器人\",\"科大智能\",\"2连板\",\"机器人\"],[\"机器人\",\"拓斯达\",\"2连板\",\"机器人\"]]},\"continuePlatesNumberListFlag\":true,\"ddeMoney\":{\"value\":[[\"名称\",\"散户数量\",\"大单金额\",\"DDE大单净量\"],[\"金天钛业\",\"-3598\",\"3.70亿\",\"16\"],[\"宜通世纪\",\"1122.19\",\"5.81亿\",\"7.81\"],[\"川金诺\",\"143.07\",\"2.64亿\",\"6.65\"],[\"东方锆业\",\"-164.63\",\"3.49亿\",\"5.12\"],[\"天亿马\",\"-629.25\",\"7388.12万\",\"5.1\"],[\"西陇科学\",\"-384.01\",\"1.70亿\",\"4.85\"],[\"ST新亚\",\"-701.25\",\"1.06亿\",\"4.68\"],[\"广博股份\",\"-360.09\",\"1.34亿\",\"4.55\"]]},\"ddeMoneyFlag\":true,\"ddeRetail\":{\"value\":[[\"名称\",\"散户数量\",\"大单金额\",\"DDE大单净量\"],[\"晋拓股份\",\"2031.37\",\"-8745.76万\",\"-6.81\"],[\"久盛电气\",\"1709.09\",\"-8920.28万\",\"-6.45\"],[\"威力传动\",\"1336.98\",\"5596.79万\",\"4.24\"],[\"酷特智能\",\"1330.94\",\"933.35万\",\"0.15\"],[\"佳创视讯\",\"1264.08\",\"-1.47亿\",\"-5.56\"],[\"海能达\",\"1254.87\",\"-11.18亿\",\"-4.14\"],[\"宜通世纪\",\"1122.19\",\"5.81亿\",\"7.81\"],[\"易点天下\",\"1053.55\",\"-1.18亿\",\"-1.09\"]]},\"ddeRetailFlag\":true}}}";
        Integer jsonArryaSize = getJsonArryaSize(jsonString, "/datamining/threeTopic/bb07b1.result.ddeMoney");
        System.out.println(jsonArryaSize);
    }

    /**
     * 根据json 获取绑定的数组长度
     * @param jsonString
     * @param jsonArrayPath
     * @return
     */
    public static Integer getJsonArryaSize(String jsonString, String jsonArrayPath) {
        // 将 JSON 字符串解析为 JSONObject
        JSONObject jsonObject = JSONObject.parseObject(jsonString);

        // 假设这是你的动态路径，你可以将其替换为任何路径列表,这里因为前端绑定是不带上 value，但是实际解析解析时需要带上value
        String[] dynamicPath = (jsonArrayPath + ".value").split("\\.");

        // 初始化当前节点为根节点
        JSONObject currentNode = jsonObject;

        int size = 0;
        // 遍历动态路径
        for (String key : dynamicPath) {
            // 检查当前节点是否包含下一个路径的键
            if (currentNode.containsKey(key)) {
                // 获取下一个节点
                Object nextNode = currentNode.get(key);
                // 如果下一个节点是 JSONObject，则继续深入
                if (nextNode instanceof JSONObject) {
                    currentNode = (JSONObject) nextNode;
                }
                // 如果下一个节点是 JSONArray，则找到目标，跳出循环
                else if (nextNode instanceof JSONArray) {
                    JSONArray ddeMoneyValueArray = (JSONArray) nextNode;
                    // 如果你需要在这里处理数组，可以在这里添加代码
                    size = ddeMoneyValueArray.size();
                    break;
                }
                // 如果既不是 JSONObject 也不是 JSONArray，则路径错误
                else {
                    log.error("路径错误: " + key + " 不是 JSONObject 或 JSONArray");
                    break;
                }
            } else {
                log.error("路径错误: 不存在键 " + key);
                break;
            }
        }
        return size;
    }
}
