package com.dl.magicvideo.biz.common.util;

import org.springframework.stereotype.Component;

/**
 * biz端的操作人holder
 */
@Component
public class OperatorUtil {

    private static ThreadLocal<Long> userIdHolder=new ThreadLocal<>();

    private static ThreadLocal<String> userNameHolder=new ThreadLocal<>();
    private static ThreadLocal<String> tenantCodeHolder=new ThreadLocal<>();
    private static ThreadLocal<String> tenantNameHolder=new ThreadLocal<>();

    public void init(Long userId, String userName, String tenantCode, String tenantName) {
        userIdHolder.set(userId);
        userNameHolder.set(userName);
        tenantCodeHolder.set(tenantCode);
        tenantNameHolder.set(tenantName);
    }

    public Long getOperator(){
        return userIdHolder.get();
    }

    public String getTenantCode(){
        return tenantCodeHolder.get();
    }

    public String getUserName(){
        return userNameHolder.get();
    }

    public String getTenantName(){
        return tenantNameHolder.get();
    }

    public void remove(){
        userIdHolder.remove();
        userNameHolder.remove();
        tenantCodeHolder.remove();
        tenantNameHolder.remove();
    }
}
