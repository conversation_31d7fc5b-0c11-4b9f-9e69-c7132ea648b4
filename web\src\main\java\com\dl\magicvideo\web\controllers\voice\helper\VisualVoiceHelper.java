package com.dl.magicvideo.web.controllers.voice.helper;

import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.web.controllers.voice.vo.DmVoiceVO;
import com.dl.magicvideo.web.controllers.voice.vo.GenericVoiceVO;
import com.dl.magicvideo.web.controllers.voice.vo.VirtualVoiceLinkVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-30 14:12
 */
public class VisualVoiceHelper {

    public static GenericVoiceVO cnvVoiceDTO2VO(DaVirtualVoiceDTO dto) {
        GenericVoiceVO vo = new GenericVoiceVO();
        vo.setVoiceKey(dto.getVoiceKey());
        vo.setVoiceName(dto.getVoiceName());
        vo.setVoiceGender(dto.getGender());
        vo.setVoiceCategory(dto.getVoiceCategory());
        vo.setChannel(dto.getChannel());
        vo.setSampleLink(dto.getSampleLink());
        vo.setVolume(dto.getVolume());
        vo.setSpeed(dto.getSpeed());
        vo.setVoiceDesc(dto.getVoiceDesc());
        vo.setDuration(dto.getDuration());
        vo.setVoiceType(dto.getVoiceType());
        vo.setHeadImg(dto.getHeadImg());

        if (CollectionUtils.isNotEmpty(dto.getVoiceLinks())) {
            vo.setVoiceLinks(dto.getVoiceLinks().stream().map(voiceLinkDTO -> {
                VirtualVoiceLinkVO voiceLinkVO = new VirtualVoiceLinkVO();
                voiceLinkVO.setSpeed(voiceLinkDTO.getSpeed());
                voiceLinkVO.setVolume(voiceLinkDTO.getVolume());
                voiceLinkVO.setSampleLink(voiceLinkDTO.getSampleLink());
                voiceLinkVO.setDuration(voiceLinkDTO.getDuration());
                return voiceLinkVO;
            }).collect(Collectors.toList()));
        }
        return vo;
    }

    public static DmVoiceVO cnvVoiceBaseInfoVO2DmVoiceVO(DaVirtualVoiceBaseInfoDTO voiceBaseInfoDTO) {
        DmVoiceVO dmVoiceVO = new DmVoiceVO();
        dmVoiceVO.setIsBoundNow(Const.ONE);
        dmVoiceVO.setVoiceName(voiceBaseInfoDTO.getVoiceName());
        dmVoiceVO.setVoiceKey(voiceBaseInfoDTO.getVoiceKey());
        dmVoiceVO.setVoiceCategory(voiceBaseInfoDTO.getVoiceCategory());
        dmVoiceVO.setVoiceGender(voiceBaseInfoDTO.getGender());
        dmVoiceVO.setSampleLink(voiceBaseInfoDTO.getSampleLink());
        dmVoiceVO.setChannel(voiceBaseInfoDTO.getChannel());
        dmVoiceVO.setVolume(voiceBaseInfoDTO.getVolume());
        dmVoiceVO.setSpeed(voiceBaseInfoDTO.getSpeed());
        dmVoiceVO.setDuration(voiceBaseInfoDTO.getDuration());
        dmVoiceVO.setVoiceDesc(voiceBaseInfoDTO.getVoiceDesc());
        dmVoiceVO.setHeadImg(voiceBaseInfoDTO.getHeadImg());
        dmVoiceVO.setVoiceType(voiceBaseInfoDTO.getVoiceType());
        if (CollectionUtils.isNotEmpty(voiceBaseInfoDTO.getVoiceLinks())) {
            dmVoiceVO.setVoiceLinks(voiceBaseInfoDTO.getVoiceLinks().stream().map(voiceLinkDTO -> {
                VirtualVoiceLinkVO voiceLinkVO = new VirtualVoiceLinkVO();
                voiceLinkVO.setSpeed(voiceLinkDTO.getSpeed());
                voiceLinkVO.setDuration(voiceLinkDTO.getDuration());
                voiceLinkVO.setVolume(voiceLinkDTO.getVolume());
                voiceLinkVO.setSampleLink(voiceLinkDTO.getSampleLink());
                return voiceLinkVO;
            }).collect(Collectors.toList()));
        }
        return dmVoiceVO;
    }

    public static DmVoiceVO cnvDaVirtualVoiceDTO2DmVoiceVO(DaVirtualVoiceDTO input){
        DmVoiceVO result = new DmVoiceVO();
        result.setIsBoundNow(Const.ZERO);
        result.setVoiceKey(input.getVoiceKey());
        result.setVoiceName(input.getVoiceName());
        result.setVoiceCategory(input.getVoiceCategory());
        result.setVoiceGender(input.getGender());
        result.setSampleLink(input.getSampleLink());
        result.setChannel(input.getChannel());
        result.setVolume(input.getVolume());
        result.setVoiceDesc(input.getVoiceDesc());
        result.setSpeed(input.getSpeed());
        result.setDuration(input.getDuration());
        result.setVoiceType(input.getVoiceType());
        result.setHeadImg(input.getHeadImg());
        if (CollectionUtils.isNotEmpty(input.getVoiceLinks())) {
            result.setVoiceLinks(input.getVoiceLinks().stream().map(voiceLinkDTO -> {
                VirtualVoiceLinkVO voiceLinkVO = new VirtualVoiceLinkVO();
                voiceLinkVO.setSpeed(voiceLinkDTO.getSpeed());
                voiceLinkVO.setVolume(voiceLinkDTO.getVolume());
                voiceLinkVO.setDuration(voiceLinkDTO.getDuration());
                voiceLinkVO.setSampleLink(voiceLinkDTO.getSampleLink());
                return voiceLinkVO;
            }).collect(Collectors.toList()));
        }
        return result;
    }

}
