package com.dl.magicvideo.web.controllers.internal.statistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-02-27 16:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsAiJobInternalSimpleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("统计时间")
    private Date statisticsTime;

    @ApiModelProperty("总的毫秒")
    private Long totalTimeMillis;

}
