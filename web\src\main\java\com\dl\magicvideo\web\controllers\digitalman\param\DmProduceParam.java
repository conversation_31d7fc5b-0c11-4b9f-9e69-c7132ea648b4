package com.dl.magicvideo.web.controllers.digitalman.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @describe: ProduceParam
 * @author: zhousx
 * @date: 2023/6/8 16:46
 */
@Data
public class DmProduceParam {
    @ApiModelProperty(value = "数字人合成任务id", required = true)
    @NotBlank
    private String jobId;

    @ApiModelProperty(value = "渠道：0 智云 1 硅基 2 腾讯云 3 深声科技 4 阿里云", required = true)
    @NotNull
    private Integer channel;

    @ApiModelProperty(value = "场景id", required = true)
    @NotBlank
    private String sceneId;

    @ApiModelProperty(value = "模板id", required = true)
    @NotBlank
    private String templateId;

    @ApiModelProperty(value = "文本", required = true)
    @NotBlank
    private String text;

    @ApiModelProperty("发音人编号")
    private String voiceCode;

    @ApiModelProperty("分段最大字数")
    private Integer maxLength;

    @ApiModelProperty("1.0为正常语速，范围[0.5-1.5]，值为0.5时播报语速最慢，值为1.5时播报语速最快，DriverType为⾳频驱动类型时，语速控制不⽣效")
    private Double speed = 1.0;
}
