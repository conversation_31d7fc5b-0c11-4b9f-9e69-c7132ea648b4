package com.dl.magicvideo.biz.client.deepsound.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DsAudioTrainRequest implements Serializable {

    private static final long serialVersionUID = -8450722761284975366L;

    /**
     * 业务方对当前提交音色的唯一编号，长 度不超过 64 字符，仅支持字母、数字、 下划线 _ 的组合
     */
    @JsonProperty("record_id")
    private String recordId;

    /**
     * 录音人名称，如“韩梅梅”
     */
    @JsonProperty("speaker")
    private String speaker;

    /**
     * 性别，限制值：1-男性，2-女性
     */
    @JsonProperty("gender")
    private Integer gender;

    /**
     * 录音文本及其录音链接（20 句）
     */
    @JsonProperty("sources")
    private List<DsAudioTrainSource> sources;

    /**
     * 声音模型训练完成回调接口 URL，将训 练结果信息推送到指定的 URL，必须外 网可访问。
     * 在通知失败的情况下，会重 试回调三次，时间间隔为 15 秒、30 秒、 60 秒。
     * 如果未设置，可调用训练状态查询接口 进行轮询查询
     */
    @JsonProperty("notify_url")
    private String notifyUrl;

}
