package com.dl.magicvideo.web.controllers.auth;

import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.web.controllers.auth.param.AddTemplateAndAuthParam;
import com.dl.magicvideo.web.controllers.auth.param.AuthParam;
import com.dl.magicvideo.web.controllers.auth.param.SwitchStatusParam;
import com.dl.magicvideo.web.controllers.auth.param.TemplateAuthPageQueryParam;
import com.dl.magicvideo.web.controllers.auth.vo.TemplateAuthVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @describe: TemplateAuthController
 * @author: zhousx
 * @date: 2023/6/18 10:47
 */
@RestController
@RequestMapping("/visual/templateauth")
@Api("模板授权模块")
public class TemplateAuthController {
    @Autowired
    private TemplateAuthProcess templateAuthProcess;

    @PostMapping("/list")
    @ApiOperation("系统模板列表")
    public ResultPageModel<TemplateAuthVO> list(@RequestBody @Validated TemplateAuthPageQueryParam param) {
        return templateAuthProcess.list(param);
    }

    @PostMapping("/addandauth")
    @ApiOperation("创建系统模板并授权")
    public ResultModel<TemplateAuthVO> addAndAuth(@RequestBody @Validated AddTemplateAndAuthParam param) {
        return templateAuthProcess.addAndAuth(param);
    }

    @PostMapping("/auth")
    @ApiOperation("修改授权")
    public ResultModel<Void> auth(@RequestBody @Validated AuthParam param) {
        return templateAuthProcess.auth(param);
    }

    @PostMapping("/switchstatus")
    @ApiOperation("系统模板启停")
    public ResultModel<Void> switchStatus(@RequestBody @Validated SwitchStatusParam param) {
        return templateAuthProcess.switchStatus(param);
    }
}
