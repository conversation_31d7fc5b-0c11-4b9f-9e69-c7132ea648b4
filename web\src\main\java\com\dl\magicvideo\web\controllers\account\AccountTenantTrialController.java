package com.dl.magicvideo.web.controllers.account;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.dal.account.trial.po.AccountTenantTrialPO;
import com.dl.magicvideo.biz.manager.account.trial.AccountTenantTrialManager;
import com.dl.magicvideo.web.controllers.account.convert.AccountTenantTrialConvert;
import com.dl.magicvideo.web.controllers.account.param.AccountTenantInfoParam;
import com.dl.magicvideo.web.controllers.account.vo.AccountTenantTrialVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/account/tenant/trial")
public class AccountTenantTrialController {

    @Autowired
    private AccountTenantTrialManager accountTenantTrialManager;

    @PostMapping("/info")
    public ResultModel<AccountTenantTrialVO> info(@RequestBody AccountTenantInfoParam param){
        Assert.isTrue(StringUtils.isNotBlank(param.getTenantCode()),"租户号不能为空");
        AccountTenantTrialPO tenantTrialAccount = accountTenantTrialManager.getOne(Wrappers.lambdaQuery(AccountTenantTrialPO.class)
                .eq(AccountTenantTrialPO::getTenantCode, param.getTenantCode())
                .eq(AccountTenantTrialPO::getIsDeleted, Const.ZERO));
        return ResultModel.success(AccountTenantTrialConvert.cnvAccountTenantTrialPO2VO(tenantTrialAccount));
    }

}
