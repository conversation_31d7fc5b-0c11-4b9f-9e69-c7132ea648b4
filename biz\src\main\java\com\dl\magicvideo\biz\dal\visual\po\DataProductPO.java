package com.dl.magicvideo.biz.dal.visual.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.magicvideo.biz.common.BasePO;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName data_product
 */
@TableName(value ="data_product")
@Data
public class DataProductPO extends BasePO implements Serializable {
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板名称
     */
    private String prodCode;

    /**
     * 模板状态 0-启用 1-禁用
     */
    private String prodName;

    /**
     * 替换变量
     */
    private String replaceData;
}