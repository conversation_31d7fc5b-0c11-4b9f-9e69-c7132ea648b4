package com.dl.magicvideo.biz.manager.visual.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description：模板拷贝BO
 * @author： Pelot
 * @create： 2024/10/28 10:49
 */
@Data
public class TemplateCopyBO {
    /**
     * 模板ID
     */
    Long templateId;
    /**
     * 名称
     */
    String name;
    /**
     * 封面图
     */
    String coverUrl;
    /**
     * 预览视频
     */
    String previewVideoUrl;
    /**
     * 是否系统模板
     */
    boolean isSys;
    /**
     * 租户code
     */
    String tenantCode;
    /**
     * 是否包理财经理信息
     */
    Integer isManager;
    /**
     * 一级行业
     */
    Integer firstCategory;
    /**
     * 二级行业
     */
    Integer secondCategory;
    /**
     * 拷贝来源
     */
    Integer copySource;

    /**
     * 已存在的标签列表
     */
    List<Long> tagIds;

    /**
     * 新增的标签名
     */
    List<String> tagNames;

    /**
     * 0-从系统模板复制 1-直接复制
     */
    private Integer type;
}
