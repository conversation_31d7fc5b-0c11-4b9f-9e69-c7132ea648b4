package com.dl.magicvideo.biz.common.util;

import com.dl.magicvideo.biz.common.constant.Const;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 向上取整工具类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-16 20:13
 */
public class CeilingUtils {

    /**
     * 毫秒转分钟,向上取整
     *
     * @param mills
     * @return
     */
    public static Long millsToMinutes(Long mills) {
        if (Objects.isNull(mills)) {
            return Const.ZERO_LONG;
        }
        if (Const.ZERO_LONG.equals(mills)) {
            return Const.ZERO_LONG;
        }

        //计算时长。 除以1000转换成秒，再除以60，转换成分钟，再向上取整，得到取整后的分钟数。
        BigDecimal durationMinutes = new BigDecimal(mills)
                .divide(new BigDecimal(1000 * 60), 3, BigDecimal.ROUND_UP);
        return (long) Math.ceil(durationMinutes.doubleValue());
    }
}
