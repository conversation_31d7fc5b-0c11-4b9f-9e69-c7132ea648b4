package com.dl.magicvideo.web.controllers.internal.font;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.controller.param.AbstractPageParam;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.dal.font.po.PatternFontPO;
import com.dl.magicvideo.biz.manager.font.PatternFontManager;
import com.dl.magicvideo.biz.manager.font.bo.PatternFontPageBO;
import com.dl.magicvideo.biz.manager.font.dto.PatternFontDTO;
import com.dl.magicvideo.web.controllers.AbstractController;
import com.dl.magicvideo.web.controllers.internal.font.convert.PatternFontInternalConvert;
import com.dl.magicvideo.web.controllers.internal.font.param.PatternFontInternalDetailParam;
import com.dl.magicvideo.web.controllers.internal.font.param.PatternFontInternalPageParam;
import com.dl.magicvideo.web.controllers.internal.font.param.PatternFontInternalParam;
import com.dl.magicvideo.web.controllers.internal.font.vo.PatternFontInternalPageVO;
import com.dl.magicvideo.web.controllers.internal.font.vo.PatternFontInternalVO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/visual/internal/patternfont/")
public class PatternFontInternalController extends AbstractController {

    @Autowired
    private PatternFontManager patternFontManager;

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private OperatorUtil operatorUtil;

    @PostMapping("/list")
    public ResultModel<List<PatternFontInternalVO>> list(){
        List<PatternFontDTO> patternFontDTOList = patternFontManager.fontList(null);
        return ResultModel.success(patternFontDTOList.stream().map(PatternFontInternalConvert::cnvPatternFontDTO2InternalVO).collect(
                Collectors.toList()));
    }

    @PostMapping("/detail")
    public ResultModel<PatternFontInternalVO> detail(@RequestBody PatternFontInternalDetailParam param){
        PatternFontDTO dto = patternFontManager.detail(param.getBizId());
        return ResultModel.success(PatternFontInternalConvert.cnvPatternFontDTO2InternalVO(dto));
    }

    @PostMapping("/page")
    public ResultPageModel<PatternFontInternalPageVO> page(@RequestBody PatternFontInternalPageParam param){
        PatternFontPageBO bo = new PatternFontPageBO();
        bo.setName(param.getName());
        bo.setType(param.getType());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        IPage<PatternFontDTO> resp = patternFontManager.fontPage(bo);
        return pageQueryModel(resp,resp.getRecords().stream().map(PatternFontInternalConvert::cnvPatternFontDTO2InternalPageVO).collect(
                Collectors.toList()));
    }

    @PostMapping("/add")
    public ResultModel<String> add(@RequestBody PatternFontInternalParam param){
        operatorUtil.init(param.getUserId(), null,null,null);
        PatternFontPO po = new PatternFontPO();
        po.setBizId(hostTimeIdg.generateId().longValue());
        po.setName(param.getName());
        po.setFontType(param.getFontType());
        po.setStyles(param.getStyles());
        po.setCoverImg(param.getCoverImg());
        patternFontManager.save(po);
        return ResultModel.success(po.getBizId().toString());
    }

    @PostMapping("/update")
    public ResultModel<Boolean> update(@RequestBody PatternFontInternalParam param){
        operatorUtil.init(param.getUserId(), null,null,null);
        LambdaUpdateWrapper<PatternFontPO> wrapper = Wrappers.lambdaUpdate(
                PatternFontPO.class);
        wrapper.eq(PatternFontPO::getBizId,param.getBizId()).eq(PatternFontPO::getIsDeleted, Const.ZERO);
        wrapper.set(StringUtils.isNotBlank(param.getName()),PatternFontPO::getName,param.getName())
                .set(StringUtils.isNotBlank(param.getCoverImg()),PatternFontPO::getCoverImg,param.getCoverImg())
                .set(StringUtils.isNotBlank(param.getStyles()),PatternFontPO::getStyles,param.getStyles())
                .set(Objects.nonNull(param.getFontType()),PatternFontPO::getFontType,param.getFontType())
                .set(PatternFontPO::getModifyBy,param.getUserId());
        return ResultModel.success(patternFontManager.update(wrapper));
    }

    @PostMapping("/delete")
    public ResultModel<Boolean> deleted(@RequestBody PatternFontInternalParam param) {
        operatorUtil.init(param.getUserId(), null,null,null);
        LambdaUpdateWrapper<PatternFontPO> wrapper = Wrappers.lambdaUpdate(PatternFontPO.class);
        wrapper.eq(PatternFontPO::getBizId, param.getBizId()).eq(PatternFontPO::getIsDeleted, Const.ZERO);
        wrapper.set(StringUtils.isNotBlank(param.getName()), PatternFontPO::getName, param.getName())
                .set(PatternFontPO::getIsDeleted, Const.ONE);
        return ResultModel.success(patternFontManager.update(wrapper));
    }
}
