package com.dl.magicvideo.biz.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class OpenApiSignUtil {

    public static void main(String[] args) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String originJson = "{\"jobStatus\":2,\"extUserId\":\"1067\",\"synthUpdated\":1700723540000,\"jobId\":\"1199740838355463391\",\"synthStarted\":1700723265000,\"jobResult\":{\"duration\":127805,\"coverUrl\":\"https://dl-prod-1314522657.cos.ap-shanghai.myqcloud.com/visual/videos/1199740838355463391.jpg\",\"videoUrl\":\"https://dl-prod-1314522657.cos.ap-shanghai.myqcloud.com/visual/videos/1199740838355463391.mp4\",\"dmTotalCeilingMinutes\":1},\"name\":\"轻编辑20231123\"}";
        JSONObject jsonObject = JSON.parseObject(originJson);
        String json = JSON.toJSONString(jsonObject, SerializerFeature.MapSortField);
        System.out.println(json);
        String sign = OpenApiSignUtil.getSignature("CSXCDZH", "1668553515913572358", timestamp, json);
        System.out.println("生成的签名:" + sign);
    }

    /**
     * 获取签名串
     *
     * @param accessKey ak
     * @param secretKey sk
     * @param timestamp 时间戳
     * @param param     JSON字符串参数，按参数名的ASII码排序
     * @return 签名串
     */
    public static String getSignature(String accessKey, String secretKey, String timestamp, String param) {
        if (StringUtils.isAnyBlank(accessKey, secretKey, timestamp)) {
            log.error("鉴权参数不可为空, accessKey={},secretKey={},timestamp={}", accessKey, secretKey, timestamp);
            throw new IllegalArgumentException();
        }
        String needSignatureStr = secretKey + "$" + timestamp + "$" + accessKey + "$" + param;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(needSignatureStr.getBytes("UTF-8"));
            byte[] summery = md5.digest();
            StringBuilder md5StrBuilder = new StringBuilder();
            for (byte aSummery : summery) {
                if (Integer.toHexString(255 & aSummery).length() == 1) {
                    md5StrBuilder.append("0").append(Integer.toHexString(255 & aSummery));
                } else {
                    md5StrBuilder.append(Integer.toHexString(255 & aSummery));
                }
            }
            return md5StrBuilder.toString();
        } catch (Exception e) {
            log.error("获取签名串失败", e);
            return "";
        }
    }

    /**
     * @param accessKey
     * @param secretKey
     * @param bizParam  map类型的业务业务参数名
     * @return 返回构造好的带有签名和参数的url
     */
    public static String getSignUrl(String accessKey, String secretKey, Map<String, String> bizParam) {
        if (Objects.isNull(bizParam) || bizParam.size() == 0) {
            bizParam = new HashMap<>();
        }
        String currentTime = String.valueOf(System.currentTimeMillis());
        if (Objects.isNull(bizParam.get("timestamp"))) {
            bizParam.put("timestamp", currentTime);
        }
        //添加access_key
        bizParam.put("access_key", accessKey);
        //排序后的参数字符串
        String sortParam = sortParamToString(bizParam);
        //MD5加密环节
        String sign = getSignature(accessKey, secretKey, currentTime, sortParam);
        //构造url后续参数
        String url = getUrlParamsByMap(bizParam);
        String signUrl = url.concat("&signature=").concat(sign);
        return signUrl;
    }

    private static String sortParamToString(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(params) && params.size() > 0) {
            params.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(paramEntry -> {
                sb.append(paramEntry.getKey()).append("=").append(paramEntry.getValue()).append("#");
            });
        }
        String res = sb.toString();
        return res;
    }

    /**
     * 将map转换成url
     *
     * @param map
     * @return
     */
    private static String getUrlParamsByMap(Map<String, String> map) {
        if (map == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            sb.append(entry.getKey() + "=" + entry.getValue());
            sb.append("&");
        }
        String s = sb.toString();
        if (s.endsWith("&")) {
            s = StringUtils.substringBeforeLast(s, "&");
        }
        return s;
    }
}
