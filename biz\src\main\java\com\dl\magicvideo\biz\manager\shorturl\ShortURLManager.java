package com.dl.magicvideo.biz.manager.shorturl;


import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.magicvideo.biz.dal.shorturl.po.ShortURLPO;
import com.dl.magicvideo.biz.manager.shorturl.dto.ShortURLDTO;

/**
 * 短链管理器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-06-15 15:09
 */
public interface ShortURLManager extends IService<ShortURLPO> {

    /**
     * 生成短网址
     *
     * @param targetURL  原来的长网址 ，需要携带http协议或https协议
     * @param expireTime 过期时间 单位：分钟   0-表示永久有效
     * @param tenantCode 租户编码
     * @return 包括域名   比如https://dl2022.cn/xxxxx
     */
    ShortURLDTO createShortURL(String targetURL, int expireTime, String tenantCode);

    /**
     * 查询原地址
     *
     * @param code
     * @return
     */
    ShortURLDTO getTargetURL(String code);

}
