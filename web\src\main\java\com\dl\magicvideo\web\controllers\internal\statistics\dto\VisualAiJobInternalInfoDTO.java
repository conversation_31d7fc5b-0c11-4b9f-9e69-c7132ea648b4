package com.dl.magicvideo.web.controllers.internal.statistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-14 20:02
 */
@Data
public class VisualAiJobInternalInfoDTO implements Serializable {
    private static final long serialVersionUID = 2242418784258681162L;

    @ApiModelProperty("任务id")
    private String jobId;

    @ApiModelProperty("视频合成任务id")
    private String produceJobId;

    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("ai任务类型，1-数字人，2-TTS")
    private Integer aiJobType;

    @ApiModelProperty("任务状态 0-未执行，1-执行中，2-执行成功，3-执行失败")
    private Integer jobStatus;

    @ApiModelProperty("创建时间")
    private Date createDt;

    @ApiModelProperty("创建人id")
    private String createBy;

    @ApiModelProperty("创建人名称")
    private String creatorName;

    @ApiModelProperty("时长，单位：ms")
    private Long duration;

    @ApiModelProperty("向上取整的分钟数")
    private Long ceilingMinutes;

    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty("创建来源描述")
    private String createSourceDesc;

    @ApiModelProperty("字符数-目前只针对tts和数字人的tts")
    private Integer textLength;
}
