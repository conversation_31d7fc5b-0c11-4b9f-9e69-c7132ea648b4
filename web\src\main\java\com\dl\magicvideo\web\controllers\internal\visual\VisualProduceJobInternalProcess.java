package com.dl.magicvideo.web.controllers.internal.visual;

import cn.hutool.json.JSONUtil;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.basicservice.dto.AdmTenantInfoDTO;
import com.dl.magicvideo.biz.common.constant.Const;
import com.dl.magicvideo.biz.common.util.OperatorUtil;
import com.dl.magicvideo.biz.manager.basicservice.TenantInfoManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobContextBO;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceJobSearchBO;
import com.dl.magicvideo.biz.manager.visual.bo.TtsParamBO;
import com.dl.magicvideo.biz.manager.visual.dto.VisualProduceJobDTO;
import com.dl.magicvideo.biz.manager.visual.enums.VisualProduceJobSourceEnum;
import com.dl.magicvideo.web.controllers.internal.visual.convert.VisualProduceJobInternalConvert;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualInternalTTsParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualProduceJobGenerateParam;
import com.dl.magicvideo.web.controllers.internal.visual.param.VisualProduceJobInternalPageParam;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualCreateProduceJobInternalVO;
import com.dl.magicvideo.web.controllers.internal.visual.vo.VisualProduceJobInternalPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class VisualProduceJobInternalProcess {

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private VisualProduceJobManager visualProduceJobManager;
    @Resource
    private TenantInfoManager tenantInfoManager;

    @Autowired
    private HostTimeIdg hostTimeIdg;

    public ResultModel<VisualCreateProduceJobInternalVO> generate(VisualProduceJobGenerateParam param) {
        log.info("内部调用合成视频参数 param = " + JSONUtil.toJsonStr(param));
        operatorUtil.init(Long.valueOf(param.getUserId()), param.getUserName(), param.getTenantCode(),
                param.getTenantName());
        AdmTenantInfoDTO tenantInfo = tenantInfoManager.getTenantInfo(param.getTenantCode());

        Long templateId = Long.valueOf(param.getTemplateId());
        String replaceData = param.getReplaceData();
        Long batchId = hostTimeIdg.generateId().longValue();
        VisualInternalTTsParam internalTTsParam = param.getVisualTTsParam();
        TtsParamBO ttsParam = null;
        if (Objects.nonNull(internalTTsParam)) {
            ttsParam = new TtsParamBO();
            ttsParam.setVoiceName(internalTTsParam.getVoiceName());
            ttsParam.setChannel(internalTTsParam.getChannel());
            ttsParam.setVolume(internalTTsParam.getVolume());
            ttsParam.setSpeed(internalTTsParam.getSpeed());
            ttsParam.setPitch(internalTTsParam.getPitch());
        }
        Long jobId = visualProduceJobManager.doProduce(
                ProduceJobContextBO.builder().templateId(templateId).replaceData(replaceData).batchId(batchId)
                        .source(VisualProduceJobSourceEnum.INTERNAL_PRODUCE.getCode()).ttsParam(ttsParam)
                        .dmProduceMode(tenantInfo.getDmProduceMode()).build());
        log.info("内部调用视频合成成功 jobId = " + jobId);
        VisualCreateProduceJobInternalVO result = new VisualCreateProduceJobInternalVO();
        result.setJobId(jobId.toString());
        return ResultModel.success(result);
    }

    public ResultPageModel<VisualProduceJobInternalPageVO> page(VisualProduceJobInternalPageParam param) {
        ProduceJobSearchBO searchBO = new ProduceJobSearchBO();
        searchBO.setTenantCode(param.getTenantCode());
        searchBO.setStatusList(param.getStatusList());
        searchBO.setEncludeDeleted(Const.ONE.equals(param.getIncludeDeleted()));
        searchBO.setStartTime(param.getStartTime());
        searchBO.setEndTime(param.getEndTime());
        searchBO.setPageSize(param.getPageSize());
        searchBO.setPageIndex(param.getPageIndex());

        ResponsePageQueryDO<List<VisualProduceJobDTO>> pageResultDTO = visualProduceJobManager.pageQuery(searchBO);
        ResultPageModel<VisualProduceJobInternalPageVO> resultPageModel = new ResultPageModel<>();
        resultPageModel.setPageSize(pageResultDTO.getPageSize());
        resultPageModel.setPageIndex(pageResultDTO.getPageIndex());
        resultPageModel.setTotalPage(pageResultDTO.getTotalPage());
        resultPageModel.setTotal(pageResultDTO.getTotal());
        if (CollectionUtils.isEmpty(pageResultDTO.getDataResult())) {
            return resultPageModel;
        }
        resultPageModel.setDataResult(pageResultDTO.getDataResult().stream()
                .map(VisualProduceJobInternalConvert::cnvVisualProduceJobDTO2InternalPageVO)
                .collect(Collectors.toList()));
        return resultPageModel;
    }
}
