package com.dl.magicvideo.biz.manager.visual.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.magicvideo.biz.common.enums.BatchStatusE;
import com.dl.magicvideo.biz.common.enums.JobStatusE;
import com.dl.magicvideo.biz.dal.visual.VisualProduceBatchMapper;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceBatchPO;
import com.dl.magicvideo.biz.dal.visual.po.VisualProduceJobPO;
import com.dl.magicvideo.biz.manager.visual.VisualProduceBatchManager;
import com.dl.magicvideo.biz.manager.visual.VisualProduceJobManager;
import com.dl.magicvideo.biz.manager.visual.bo.ProduceBatchSearchBO;
import com.dl.magicvideo.biz.manager.visual.dto.ProduceBatchCancelMsgDTO;
import com.dl.magicvideo.biz.manager.visual.dto.ProduceBatchDTO;
import com.dl.magicvideo.biz.mq.DlChannels;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【visual_produce_batch(作业批次表)】的数据库操作Service实现
 * @createDate 2023-06-19 11:37:08
 */
@Slf4j
@Service
public class VisualProduceBatchManagerImpl extends ServiceImpl<VisualProduceBatchMapper, VisualProduceBatchPO>
        implements VisualProduceBatchManager {
    @Autowired
    private VisualProduceJobManager visualProduceJobManager;

    @Autowired
    private DlChannels dlChannels;

    @Override
    public ResponsePageQueryDO<List<ProduceBatchDTO>> pageQuery(ProduceBatchSearchBO bo) {
        ResponsePageQueryDO<List<ProduceBatchDTO>> response = new ResponsePageQueryDO<>();
        LambdaQueryWrapper<VisualProduceBatchPO> queryWrapper = Wrappers.lambdaQuery(VisualProduceBatchPO.class);
        queryWrapper.ge(Objects.nonNull(bo.getStartTime()), VisualProduceBatchPO::getCreateDt, bo.getStartTime())
                .le(Objects.nonNull(bo.getEndTime()), VisualProduceBatchPO::getCreateDt, bo.getEndTime())
                .eq(Objects.nonNull(bo.getPlanId()), VisualProduceBatchPO::getPlanId, bo.getPlanId())
                .in(CollectionUtils.isNotEmpty(bo.getStatusList()), VisualProduceBatchPO::getStatus, bo.getStatusList())
                .in(CollectionUtils.isNotEmpty(bo.getTenantCodeList()), VisualProduceBatchPO::getTenantCode, bo.getTenantCodeList())
                .orderByDesc(VisualProduceBatchPO::getCreateDt);

        IPage<VisualProduceBatchPO> pageResult = baseMapper.selectPage(convert(bo), queryWrapper);
        List<VisualProduceBatchPO> data = pageResult.getRecords();
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        List<ProduceBatchDTO> dtos = new ArrayList<>();
        dtos.addAll(data.stream().map(t -> {
            ProduceBatchDTO dto = new ProduceBatchDTO();
            dto.setBatchId(t.getBatchId());
            dto.setBatchName(t.getBatchName());
            dto.setCreateBy(t.getCreateBy());
            dto.setCreateDt(t.getCreateDt());
            dto.setModifyDt(t.getModifyDt());
            dto.setModifyBy(t.getModifyBy());
            dto.setStatus(t.getStatus());
            dto.setTenantCode(t.getTenantCode());
            dto.setTenantName(t.getTenantName());
            dto.setCreatorName(t.getCreatorName());
            dto.setJobNumSuccess(t.getJobNumSuccess());
            dto.setJobNumTotal(t.getJobNumTotal());
            return dto;
        }).collect(Collectors.toList()));
        response.setPageIndex(pageResult.getCurrent());
        response.setPageSize(pageResult.getSize());
        response.setTotal(pageResult.getTotal());
        response.setDataResult(dtos);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long batchId) {
        VisualProduceBatchPO batch = lambdaQuery().eq(VisualProduceBatchPO::getBatchId, batchId).one();
        Assert.isTrue(Objects.equals(batch.getStatus(), BatchStatusE.QUEUED.getCode()), "只能取消状态为排队中的批次");

        boolean updateBatch = lambdaUpdate().eq(VisualProduceBatchPO::getBatchId, batchId).eq(VisualProduceBatchPO::getStatus, BatchStatusE.QUEUED.getCode()).set(VisualProduceBatchPO::getStatus, BatchStatusE.CANCELED.getCode()).update();
        if(updateBatch) {
            visualProduceJobManager.lambdaUpdate().eq(VisualProduceJobPO::getBatchId, batchId).set(VisualProduceJobPO::getStatus, JobStatusE.CANCELED.getCode()).update();
        }
        sendMsg(batch);
    }

    private void sendMsg(VisualProduceBatchPO input){
        log.info("准备进行发送取消produceBatch消息，input = {}", JSONUtil.toJsonStr(input));
        try {
            ProduceBatchCancelMsgDTO target = new ProduceBatchCancelMsgDTO();
            BeanUtils.copyProperties(input,target);
            Message message = MessageBuilder.withPayload(target).build();
            boolean sendMsgResult = dlChannels.visualproducebatchcancel().send(message, 1000L);
            log.info("发送取消生产批次消息成功 message = {}，sendMsgResul = {}",JSONUtil.toJsonStr(message),JSONUtil.toJsonStr(sendMsgResult));
        }catch (Exception e){
            log.info("发送取消生产批次消息异常 input = {}",JSONUtil.toJsonStr(input));
        }
    }
}




