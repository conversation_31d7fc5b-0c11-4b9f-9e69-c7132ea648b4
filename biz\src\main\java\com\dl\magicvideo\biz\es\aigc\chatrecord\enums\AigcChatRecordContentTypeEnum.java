package com.dl.magicvideo.biz.es.aigc.chatrecord.enums;


import java.util.Objects;

/**
 * aigc聊天记录内容类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 15:13
 */
public enum AigcChatRecordContentTypeEnum {

    //内容类型，1-文本 2-pdf 3-doc 4-xlsx 5-ppt 6-txt 7-图片 8-视频合成成功 9-视频合成失败 10-视频合成中
    //11-热点事件题材提问 12-热点事件题材回答 13-文本转视频
    TEXT(1, "文本"),
    PDF(2, "pdf"),
    DOC(3, "doc"),
    XLSX(4, "xlsx"),
    PPT(5, "ppt"),
    TXT_FILE(6, "txt"),
    IMG(7, "图片"),
    PRODUCE_SUCCESS(8, "视频合成成功"),
    PRODUCE_FAIL(9, "视频合成失败"),
    PRODUCE_ING(10,"视频合成中"),
    HOT_EVENT_SUBJECT_MATTER_ASK(11,"热点事件题材提问"),
    HOT_EVENT_SUBJECT_MATTER_ANSWER(12,"热点事件题材回答"),
    TEXT_TO_VIDEO(13,"文本转视频");

    private Integer type;

    private String desc;

    AigcChatRecordContentTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static AigcChatRecordContentTypeEnum parse(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (AigcChatRecordContentTypeEnum contentTypeEnum : AigcChatRecordContentTypeEnum.values()) {
            if (contentTypeEnum.getType().equals(type)) {
                return contentTypeEnum;
            }

        }
        return null;
    }
}
