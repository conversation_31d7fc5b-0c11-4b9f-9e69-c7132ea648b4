package com.dl.magicvideo.biz.client.aiservice;

import com.dl.aiservice.share.aichat.AiFileContentAndTitleRespDTO;
import com.dl.aiservice.share.aichat.AiMultiChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatRequestDTO;
import com.dl.aiservice.share.aichat.AiSingleChatResponseDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManSceneVoiceRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManScenesQueryDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualManVoiceRequestDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceBaseInfoDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceListQueryDTO;
import com.dl.aiservice.share.digitalasset.DaVirtualVoiceRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManAggregationRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManBaseInfoDTO;
import com.dl.aiservice.share.digitalman.DigitalManComposeRequestDTO;
import com.dl.aiservice.share.digitalman.DigitalManComposeResponseDTO;
import com.dl.aiservice.share.digitalman.DmSubtitleRequestDTO;
import com.dl.aiservice.share.digitalman.DmSubtitleResponseDTO;
import com.dl.aiservice.share.media.dto.MediaProduceJobRequestDTO;
import com.dl.aiservice.share.media.dto.MediaProduceJobResponseDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleAndReviseRequestDTO;
import com.dl.aiservice.share.subtitle.dto.AsrSubtitleDTO;
import com.dl.aiservice.share.subtitle.dto.ReviseAsrRequestDTO;
import com.dl.aiservice.share.subtitle.dto.RevisedAsrResponseDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceParamDTO;
import com.dl.aiservice.share.videoproduce.VideoProduceResponseDTO;
import com.dl.aiservice.share.voiceclone.TTSProduceParamDTO;
import com.dl.aiservice.share.voiceclone.TTSResponseDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.magicvideo.biz.client.aiservice.intercepter.AiServiceAuthInterceptor;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.DataFile;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;

import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 * @describe: AiServiceClient
 * @author: zhousx
 * @date: 2023/3/16 16:31
 */
@BaseRequest(interceptor = AiServiceAuthInterceptor.class)
public interface AiServiceClient {

    /**
     * 视频合成
     *
     * @return
     */
    @Post("/video/produce/handle")
    ResultModel<VideoProduceResponseDTO> produce(@JSONBody VideoProduceParamDTO param);

    /**
     * 语音合成 TTS
     *
     * @return
     */
    @Post("/voice/clone/tts")
    ResultModel<TTSResponseDTO> tts(Integer channel, @JSONBody TTSProduceParamDTO param);

//    /**
//     * TTS音色列表
//     *
//     * @return
//     */
//    @Post("/digital/asset/voice/list")
//    ResultModel<List<DaVirtualVoiceDTO>> getGenericVoiceList(@JSONBody DaVirtualVoiceRequestDTO param);


    /**
     * 查询租户的声音列表
     *
     * @return
     */
    @Post("/digital/asset/voice/list/magic")
    ResultModel<List<DaVirtualVoiceDTO>> getGenericVoiceList(String tenantCode, @JSONBody DaVirtualVoiceRequestDTO param);

    /**
     * 数字人列表
     *
     * @return
     */
    @Post("/digital/asset/vm/list")
    ResultModel<List<DaVirtualManDTO>> getDigitalmanList(String tenantCode, @JSONBody DaVirtualManRequestDTO param);

    /**
     * 查询单个数字人基本信息
     *
     * @param bizId
     * @return
     */
    @Post("/digital/asset/vm/baseinfo")
    ResultModel<DigitalManBaseInfoDTO> digitalManBaseInfo(String tenantCode, @Query("bizId") Long bizId);

    /**
     * 查询单个数字人组合信息（数字人信息、数字人场景、数字人声音信息）
     *
     * @param tenantCode
     * @param requestDTO
     * @return
     */
    @Post("/digital/asset/vm/aggregationinfo")
    ResultModel<DigitalManAggregationInfoDTO> aggregationinfo(String tenantCode,
            @JSONBody DigitalManAggregationRequestDTO requestDTO);

    /**
     * 数字人场景列表
     *
     * @return
     */
    @Post("/digital/asset/vm/scene/list")
    ResultModel<List<DaVirtualManScenesDTO>> getDigitalmanSceneList(String tenantCode,
            @JSONBody DaVirtualManRequestDTO param);

    /**
     * 数字人视频合成
     *
     * @return
     */
    @Post("/digital/videoCreate")
    ResultModel<DigitalManComposeResponseDTO> videoCreate(Integer channel, @JSONBody DigitalManComposeRequestDTO param);

    /**
     * 数字人字幕
     *
     * @return
     */
    @Deprecated
    @Post("/digital/gensubtitle")
    ResultModel<DmSubtitleResponseDTO> generateDmSubtitle(Integer channel, @JSONBody DmSubtitleRequestDTO param);

    /**
     * 任务详情
     *
     * @return
     */
    @Post("/media/jobdetail")
    ResultModel<List<MediaProduceJobResponseDTO>> getJobDetail(@JSONBody MediaProduceJobRequestDTO param);

    /**
     * 音频识别——音频文件
     *
     * @param file
     * @return
     */
    @Post(url = "/subtitle/asrfile", contentType = "multipart/form-data")
    ResultModel<List<AsrSubtitleDTO>> asrFile(@Query("channel") Integer channel, @DataFile("file") File file,
                                              @Query("sentenceMaxLength") Integer sentenceMaxLength);

    /**
     * 字幕校验
     *
     * @param requestDTO
     * @return
     */
    @Post("/subtitle/asrrevise")
    ResultModel<RevisedAsrResponseDTO> asrRevise(@JSONBody ReviseAsrRequestDTO requestDTO);

    /**
     * 音频识别音频地址并字幕校验
     *
     * @param requestDTO
     * @return
     */
    @Post("/subtitle/asrandrevise")
    ResultModel<RevisedAsrResponseDTO> asrAndRevise(@JSONBody AsrSubtitleAndReviseRequestDTO requestDTO);

    /**
     * 音频识别音频文件并字幕校验
     *
     * @return
     */
    @Post(url = "/subtitle/asrfileandrevise", contentType = "multipart/form-data")
    ResultModel<RevisedAsrResponseDTO> asrFileAndRevise(@Query("channel") Integer channel, @DataFile("file") File file,
            @Query("sentenceMaxLength") Integer sentenceMaxLength, @Query("originalScript") String originalScript);

    /**
     * 数字人场景列表
     *
     * @return
     */
    @Post("/digital/asset/vm/scene/listbybizids")
    ResultModel<List<DaVirtualManScenesDTO>> getDigitalmanSceneListByBizIds(String tenantCode,
            @JSONBody DaVirtualManRequestDTO param);

    /**
     * 根据数字人场景id列表和渠道列表查询数字人场景列表
     *
     * @param tenantCode
     * @param param
     * @return
     */
    @Post("/digital/asset/vm/scene/listbysceneids")
    ResultModel<List<DaVirtualManScenesDTO>> vmSceneListBySceneIds(String tenantCode,
            @JSONBody DaVirtualManScenesQueryDTO param);

    /**
     * ai对话-单条对话
     *
     * @param tenantCode
     * @param requestDTO
     * @return
     */
    @Post("/aichat/singlechat")
    ResultModel<AiSingleChatResponseDTO> singleChat(String tenantCode,@JSONBody AiSingleChatRequestDTO requestDTO);

    /**
     * 流式地与ai单条对话
     * <p>
     *
     * @param tenantCode
     * @param requestDTO
     * @return
     */
    @Post("/aichat/stream/singlechatstream")
    InputStream singlechatstream(String tenantCode, @JSONBody AiSingleChatRequestDTO requestDTO);

    /**
     * 提取文件内容和标题
     *
     * @return
     */
    @Post(url = "/aichat/extractfilecontentandtitle", contentType = "multipart/form-data")
    ResultModel<AiFileContentAndTitleRespDTO> extractFileContentAndTitle(String tenantCode, @DataFile("file") File file,
            @Query("presupposeText") String presupposeText, @Query("userId") Long userId, @Query("model") String model,
            @Query("respMaxToken") Integer respMaxToken);

    /**
     * 查询单个数字人场景的指定声音信息
     *
     * @param requestDTO
     * @return
     */
    @Post("/digital/asset/vm/scene/specificvoice")
    ResultModel<DaVirtualVoiceDTO> vmSceneSpecificVoice(@JSONBody DaVirtualManSceneVoiceRequestDTO requestDTO);

    /**
     * 查询单个数字人的指定声音信息
     *
     * @param requestDTO
     * @return
     */
    @Post("/digital/asset/vm/specificvoice")
    ResultModel<DaVirtualVoiceDTO> vmSpecificVoice(@JSONBody DaVirtualManVoiceRequestDTO requestDTO);

    /**
     * 多条对话，最后的响应以流式返回
     *
     * @param tenantCode
     * @param requestDTO
     * @return
     */
    @Post("/aichat/stream/multichatfinalstream")
    InputStream multiChatFinalStream(String tenantCode, @JSONBody AiMultiChatRequestDTO requestDTO);

    /**
     * 查询声音列表
     *
     * @param requestDTO
     * @return
     */
    @Post("/digital/voice/listquery")
    ResultModel<List<DaVirtualVoiceDTO>> voiceListQuery(@JSONBody DaVirtualVoiceRequestDTO requestDTO);

    /**
     * 根据厂商+声音编码列表查询声音列表
     *
     * @param queryDTO
     * @return
     */
    @Post("/digital/voice/listbychannelandvoicekeylist")
    ResultModel<List<DaVirtualVoiceBaseInfoDTO>> listByChannelAndVoiceKeyList(@JSONBody DaVirtualVoiceListQueryDTO queryDTO);

}
