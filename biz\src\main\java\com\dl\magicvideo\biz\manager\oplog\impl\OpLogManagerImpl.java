package com.dl.magicvideo.biz.manager.oplog.impl;

import com.dl.framework.common.model.ResultPageModel;
import com.dl.magicvideo.biz.client.basicservice.OpLogClient;
import com.dl.magicvideo.biz.client.basicservice.dto.OpLogDTO;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogAddReq;
import com.dl.magicvideo.biz.client.basicservice.param.OpLogPageReq;
import com.dl.magicvideo.biz.manager.oplog.OpLogManager;
import com.dl.magicvideo.biz.manager.oplog.consts.OpLogConsts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:54
 */
@Component
public class OpLogManagerImpl implements OpLogManager {

    @Autowired
    private OpLogClient opLogClient;

    @Override
    public void newOpLog(OpLogAddReq req) {
        req.setBizCode(OpLogConsts.BIZ_CODE);
        opLogClient.newOpLog(req);
    }

    @Override
    public ResultPageModel<OpLogDTO> page(OpLogPageReq req) {
        req.setBizCode(OpLogConsts.BIZ_CODE);
        return opLogClient.page(req);
    }
}
