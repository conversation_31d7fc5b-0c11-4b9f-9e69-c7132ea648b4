package com.dl.magicvideo.biz.manager.aigc.chatrecord.bo;

import cn.easyes.annotation.IndexField;
import com.dl.framework.common.bo.PageQueryDO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-03-20 15:01
 */
@Data
public class AigcChatRecordSearchBO extends PageQueryDO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 租户编号
     */
    private String tenantCode;

    /**
     * 查询在此记录id之后的记录
     */
    private Long afterRecordId;

    /**
     * 查询发送时间在此之后的记录
     */
    private Date afterSendDt;

}
