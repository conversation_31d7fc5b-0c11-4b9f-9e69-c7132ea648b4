package com.dl.magicvideo.web.controllers.internal.subjectmatter.helper;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMaterialPO;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterPO;
import com.dl.magicvideo.biz.dal.subjectmatter.po.SubjectMatterStockPO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterSaveBO;
import com.dl.magicvideo.biz.manager.subjectmatter.bo.SubjectMatterStockSaveBO;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.param.InternalSubjectMatterSaveParam;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.param.InternalSubjectMatterStockParam;
import com.dl.magicvideo.web.controllers.internal.subjectmatter.vo.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-01-31 14:45
 */
public class InternalSubjectMatterHelper {

    public static SubjectMatterSaveBO cnvSubjectMatterSaveParam2BO(InternalSubjectMatterSaveParam input) {
        SubjectMatterSaveBO result = new SubjectMatterSaveBO();
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setImgUrl(input.getImgUrl());
        result.setExcelUrl(input.getExcelUrl());
        result.setLevel(input.getLevel());
        result.setParentId(input.getParentId());
        result.setOperatorName(input.getOperatorName());
        result.setOperatorId(input.getOperatorId());
        result.setJsonUrl(input.getJsonUrl());
        return result;
    }

    public static List<SubjectMatterStockSaveBO> cnvSubjectMatterStockSaveParams2BOs(
            List<InternalSubjectMatterStockParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return Collections.emptyList();
        }
        return paramList.stream().map(param -> {
            SubjectMatterStockSaveBO bo = new SubjectMatterStockSaveBO();
            bo.setStockCode(param.getStockCode());
            bo.setStockShortName(param.getStockShortName());
            return bo;
        }).collect(Collectors.toList());
    }

    public static InternalSubjectMatterDetailVO buildSubjectMatterDetailVO(SubjectMatterPO subjectMatterPO,
            List<SubjectMatterStockPO> stockRelPOList) {
        InternalSubjectMatterDetailVO result = new InternalSubjectMatterDetailVO();
        fillSubjectMatterVO(subjectMatterPO, result);

        List<InternalSubjectMatterStockVO> stockVOList = cnvSubjectMatterStockPO2VOs(stockRelPOList);
        result.setStockList(stockVOList);
        return result;
    }

    public static InternalSubjectMatterEditVO buildSubjectMatterEditVO(SubjectMatterPO subjectMatterPO,
            List<SubjectMaterialPO> materialPOList) {
        InternalSubjectMatterEditVO result = new InternalSubjectMatterEditVO();
        fillSubjectMatterVO(subjectMatterPO, result);

        List<InternalSubjectMaterialVO> materialVOList = cnvSubjectMaterialPO2VOs(materialPOList);
        result.setMaterialList(materialVOList);
        return result;
    }


    public static InternalSubjectMatterVO cnvSubjectMatterPO2VO(SubjectMatterPO input){
        InternalSubjectMatterVO result = new InternalSubjectMatterVO();
        fillSubjectMatterVO(input, result);
        return result;
    }

    public static InternalSubjectMatterVO fillSubjectMatterVO(SubjectMatterPO input, InternalSubjectMatterVO result) {
        result.setBizId(input.getBizId());
        result.setName(input.getName());
        result.setTitle(input.getTitle());
        result.setIntro(input.getIntro());
        result.setExcelUrl(input.getExcelUrl());
        result.setImgUrl(input.getImgUrl());
        result.setXmindUrl(input.getXmindUrl());
        result.setXmindStr(input.getXmindJson());
        result.setPrompt(input.getPrompt());
        result.setLevel(input.getLevel());
        result.setParentId(input.getParentId());
        result.setIsHaveChild(input.getIsHaveChild());
        result.setJsonUrl(input.getJsonUrl());
        result.setCreatorName(input.getCreatorName());
        result.setModifyName(input.getModifyName());
        result.setCreateBy(input.getCreateBy());
        result.setModifyBy(input.getModifyBy());
        result.setModifyDt(input.getModifyDt());
        return result;
    }

    public static List<InternalSubjectMatterStockVO> cnvSubjectMatterStockPO2VOs(
            List<SubjectMatterStockPO> stockRelPOList) {
        if (CollectionUtils.isEmpty(stockRelPOList)) {
            return Collections.emptyList();
        }
        return stockRelPOList.stream().map(param -> {
            InternalSubjectMatterStockVO bo = new InternalSubjectMatterStockVO();
            bo.setStockCode(param.getStockCode());
            bo.setStockShortName(param.getStockShortName());
            return bo;
        }).collect(Collectors.toList());
    }

    public static List<InternalSubjectMaterialVO> cnvSubjectMaterialPO2VOs(
            List<SubjectMaterialPO> materialPOList) {
        if (CollectionUtils.isEmpty(materialPOList)){
            return Collections.emptyList();
        }

        return materialPOList.stream().map(param -> {
            InternalSubjectMaterialVO bo = new InternalSubjectMaterialVO();
            bo.setBizId(param.getBizId());
            bo.setMatterId(param.getMatterId());
            bo.setUrl(param.getUrl());
            return bo;
        }).collect(Collectors.toList());
    }
}
