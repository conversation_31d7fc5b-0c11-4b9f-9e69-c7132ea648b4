package com.dl.magicvideo.biz.common.util;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 资源中心调用工具
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-14 17:29
 */
@Component
public class RcInvokeUtil {

    private static ThreadLocal<Map<String, String>> headerHolder = new ThreadLocal<>();

    public void init(Map<String, String> headerMap) {
        headerHolder.set(headerMap);
    }

    public Map<String, String> getHeader() {
        return headerHolder.get();
    }

    public void remove() {
        headerHolder.remove();
    }
}
